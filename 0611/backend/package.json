{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.7.1", "devDependencies": {"@nestjs/cli": "^11.0.5", "@nestjs/common": "^11.0.12", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.12", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.12", "@nestjs/swagger": "^11.1.0", "@nestjs/typeorm": "^11.0.0", "@types/express": "^5.0.1", "@types/node": "^22.14.0", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "redis": "^4.7.0", "ts-node": "^10.9.2", "typeorm": "^0.3.21", "typescript": "^5.8.2"}, "dependencies": {"@nestjs/throttler": "^6.4.0", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "joi": "^17.13.3", "multer": "1.4.5-lts.2", "uuid": "^11.1.0"}}