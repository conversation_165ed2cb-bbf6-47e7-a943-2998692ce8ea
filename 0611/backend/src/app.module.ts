/**
 * 应用程序根模块
 * 
 * @description
 * 这是整个NestJS应用的根模块，负责整合所有子模块和全局配置。
 * 主要功能包括：
 * 1. 配置数据库连接
 * 2. 加载环境变量
 * 3. 注册全局模块
 * 4. 集成各个功能模块
 * 
 * @module AppModule
 */

import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
import { TasksModule } from './modules/tasks/tasks.module';
import logger from '@/lib/utils/logger';

/**
 * 数据库配置工厂函数
 * 
 * @param configService - 配置服务实例，用于读取环境变量
 * @returns TypeORM配置对象
 */
const typeOrmConfig = (configService: ConfigService) => ({
  type: 'postgres' as const,
  host: configService.get('DB_HOST'),
  port: configService.get('DB_PORT'),
  username: configService.get('DB_USERNAME'),
  password: configService.get('DB_PASSWORD'),
  database: configService.get('DB_DATABASE'),
  entities: [__dirname + '/**/*.entity{.ts,.js}'],
  synchronize: configService.get('NODE_ENV') === 'development', // 仅在开发环境启用自动同步
  logging: configService.get('NODE_ENV') === 'development',
});

@Module({
  imports: [
    // 配置模块 - 加载和验证环境变量
    ConfigModule.forRoot({
      isGlobal: true, // 全局可用
      envFilePath: '.env',
    }),

    // 数据库模块 - 配置TypeORM连接
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: typeOrmConfig,
      inject: [ConfigService],
    }),

    // 限流模块 - 防止DOS攻击
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: 60, // 时间窗口（秒）
        limit: 100, // 在时间窗口内的最大请求数
        throttlers: [{
          ttl: 60,
          limit: 100,
        }],
      }),
      inject: [ConfigService],
    }),

    // 功能模块
    // AuthModule,
    // UsersModule,
    // CustomersModule,
    TasksModule,
    // VideoCallsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 在构造函数中可以进行一些初始化操作
    logger.log(`Application running in ${this.configService.get('NODE_ENV')} mode`);
  }
} 