/**
 * 应用配置模块
 * 
 * @description
 * 该模块负责管理应用的所有配置项，包括：
 * 1. 环境变量的加载和验证
 * 2. 配置项的类型定义
 * 3. 默认值的设置
 * 4. 配置项的分类管理
 * 
 * @module Configuration
 */

import { registerAs } from '@nestjs/config';
import * as Jo<PERSON> from 'joi';

/**
 * 应用配置接口
 * @interface AppConfig
 */
export interface AppConfig {
  port: number;
  nodeEnv: string;
}

/**
 * 数据库配置接口
 * @interface DatabaseConfig
 */
export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
}

/**
 * JWT配置接口
 * @interface JwtConfig
 */
export interface JwtConfig {
  secret: string;
  expiresIn: string;
}

/**
 * Redis配置接口
 * @interface RedisConfig
 */
export interface RedisConfig {
  host: string;
  port: number;
}

/**
 * 存储配置接口
 * @interface StorageConfig
 */
export interface StorageConfig {
  type: 'local' | 's3' | 'oss';
  path: string;
}

/**
 * 应用配置
 * @const appConfig
 */
export const appConfig = registerAs('app', () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',
}));

/**
 * 数据库配置
 * @const dbConfig
 */
export const dbConfig = registerAs('database', () => ({
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT, 10) || 5432,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
}));

/**
 * JWT配置
 * @const jwtConfig
 */
export const jwtConfig = registerAs('jwt', () => ({
  secret: process.env.JWT_SECRET,
  expiresIn: process.env.JWT_EXPIRES_IN || '1d',
}));

/**
 * Redis配置
 * @const redisConfig
 */
export const redisConfig = registerAs('redis', () => ({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT, 10) || 6379,
}));

/**
 * 存储配置
 * @const storageConfig
 */
export const storageConfig = registerAs('storage', () => ({
  type: process.env.STORAGE_TYPE || 'local',
  path: process.env.STORAGE_PATH || './uploads',
}));

/**
 * 环境变量验证模式
 * @const validationSchema
 * 
 * @description
 * 使用Joi库验证环境变量的格式和类型
 * 如果验证失败，应用将无法启动
 */
export const validationSchema = Joi.object({
  // 应用配置验证
  PORT: Joi.number().default(3000),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),

  // 数据库配置验证
  DB_HOST: Joi.string().required(),
  DB_PORT: Joi.number().default(5432),
  DB_USERNAME: Joi.string().required(),
  DB_PASSWORD: Joi.string().required(),
  DB_DATABASE: Joi.string().required(),

  // JWT配置验证
  JWT_SECRET: Joi.string().required(),
  JWT_EXPIRES_IN: Joi.string().default('1d'),

  // Redis配置验证
  REDIS_HOST: Joi.string().default('localhost'),
  REDIS_PORT: Joi.number().default(6379),

  // 存储配置验证
  STORAGE_TYPE: Joi.string().valid('local', 's3', 'oss').default('local'),
  STORAGE_PATH: Joi.string().default('./uploads'),
}); 