/**
 * 应用程序入口文件
 * 
 * @description
 * 这是NestJS应用的引导文件，负责创建和配置应用实例。
 * 主要功能包括：
 * 1. 创建NestJS应用实例
 * 2. 配置全局验证管道
 * 3. 配置跨域访问(CORS)
 * 4. 设置Swagger API文档
 * 5. 启动HTTP服务器
 * 
 * @file main.ts
 * <AUTHOR> Name
 * @version 1.0.0
 */

// 导入polyfill以解决crypto未定义的问题
require('../polyfill');

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import logger from '@/lib/utils/logger';

/**
 * 应用程序引导函数
 * 
 * @description
 * 负责初始化和启动NestJS应用
 * 
 * @async
 * @function bootstrap
 * @returns {Promise<void>}
 */
async function bootstrap() {
  // 创建NestJS应用实例
  const app = await NestFactory.create(AppModule);

  /**
   * 配置全局验证管道
   * transform: 自动转换请求数据类型
   * whitelist: 自动删除未定义的属性
   * forbidNonWhitelisted: 当出现未定义的属性时抛出错误
   */
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  /**
   * 配置CORS
   * 允许跨域请求，便于前端开发
   */
  app.enableCors({
    origin: process.env.NODE_ENV === 'development' ? '*' : process.env.ALLOWED_ORIGINS,
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  });

  /**
   * 配置Swagger文档
   * 提供API文档和测试界面
   * 访问地址: http://localhost:${port}/api
   */
  const config = new DocumentBuilder()
    .setTitle('5G Web API')
    .setDescription('5G Web Application API Documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('auth', '认证相关接口')
    .addTag('users', '用户管理接口')
    .addTag('customers', '客户管理接口')
    .addTag('tasks', '任务管理接口')
    .addTag('video-calls', '视频通话接口')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // 启动应用服务器
  const port = process.env.PORT || 3000;
  await app.listen(port);
  logger.debug(`
    ################################################
    🚀 Server listening on port: ${port}
    🌍 Environment: ${process.env.NODE_ENV}
    📑 API Documentation: http://localhost:${port}/api
    ################################################
  `);
}

// 启动应用
bootstrap().catch(err => {
  logger.error('Application failed to start:', err);
  process.exit(1);
}); 