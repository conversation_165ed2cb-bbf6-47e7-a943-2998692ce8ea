import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Task } from './entities/task.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private tasksRepository: Repository<Task>,
  ) {}

  async create(createTaskDto: CreateTaskDto & { createdById?: string, hasAttachment?: boolean }): Promise<Task> {
    const task = this.tasksRepository.create(createTaskDto);
    return this.tasksRepository.save(task);
  }

  async findAll(query: any): Promise<{ data: Task[], total: number }> {
    const { page = 1, limit = 10, status, customerId } = query;
    const skip = (page - 1) * limit;
    
    const queryBuilder = this.tasksRepository.createQueryBuilder('task')
      .leftJoinAndSelect('task.customer', 'customer')
      .leftJoinAndSelect('task.createdBy', 'createdBy');
    
    if (status) {
      queryBuilder.andWhere('task.status = :status', { status });
    }
    
    if (customerId) {
      queryBuilder.andWhere('task.customerId = :customerId', { customerId });
    }
    
    const [data, total] = await queryBuilder
      .orderBy('task.createdAt', 'DESC')
      .skip(skip)
      .take(limit)
      .getManyAndCount();
    
    return { data, total };
  }

  async findOne(id: string): Promise<Task> {
    const task = await this.tasksRepository.findOne({
      where: { id },
      relations: ['customer', 'createdBy'],
    });
    
    if (!task) {
      throw new NotFoundException(`Task with ID ${id} not found`);
    }
    
    return task;
  }

  async update(id: string, updateTaskDto: UpdateTaskDto & { hasAttachment?: boolean }): Promise<Task> {
    const task = await this.findOne(id);
    
    // Handle attachments properly
    if (updateTaskDto.attachments) {
      // If new attachments were uploaded, add them to existing ones
      if (task.attachments && Array.isArray(task.attachments)) {
        updateTaskDto.attachments = [...task.attachments, ...updateTaskDto.attachments];
      }
    } else {
      // Keep existing attachments if none were uploaded
      updateTaskDto.attachments = task.attachments;
    }
    
    const updatedTask = this.tasksRepository.merge(task, updateTaskDto);
    return this.tasksRepository.save(updatedTask);
  }

  async remove(id: string): Promise<void> {
    const task = await this.findOne(id);
    await this.tasksRepository.remove(task);
  }
}
