import { Controller, Get, Post, Body, Param, Put, Delete, Query, UseInterceptors, UploadedFile, UploadedFiles, Req } from '@nestjs/common';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { v4 as uuidv4 } from 'uuid';

@Controller('tasks')
export class TasksController {
  constructor(private readonly tasksService: TasksService) {}

  @Post()
  @UseInterceptors(
    FilesInterceptor('attachments', 10, {
      storage: diskStorage({
        destination: './uploads/tasks',
        filename: (req, file, callback) => {
          const uniqueSuffix = uuidv4();
          const ext = extname(file.originalname);
          callback(null, `${uniqueSuffix}${ext}`);
        },
      }),
    }),
  )
  async create(@Body() createTaskDto: CreateTaskDto, @UploadedFiles() files, @Req() req) {
    const fileData = files?.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      path: file.path
    }));
    
    return this.tasksService.create({
      ...createTaskDto,
      attachments: fileData,
      hasAttachment: fileData && fileData.length > 0,
      createdById: req.user?.id,
    });
  }

  @Get()
  findAll(@Query() query) {
    return this.tasksService.findAll(query);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.tasksService.findOne(id);
  }

  @Put(':id')
  @UseInterceptors(
    FilesInterceptor('attachments', 10, {
      storage: diskStorage({
        destination: './uploads/tasks',
        filename: (req, file, callback) => {
          const uniqueSuffix = uuidv4();
          const ext = extname(file.originalname);
          callback(null, `${uniqueSuffix}${ext}`);
        },
      }),
    }),
  )
  update(@Param('id') id: string, @Body() updateTaskDto: UpdateTaskDto, @UploadedFiles() files) {
    const fileData = files?.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      path: file.path
    }));
    
    return this.tasksService.update(id, {
      ...updateTaskDto,
      attachments: fileData,
      hasAttachment: fileData && fileData.length > 0,
    });
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.tasksService.remove(id);
  }
}
