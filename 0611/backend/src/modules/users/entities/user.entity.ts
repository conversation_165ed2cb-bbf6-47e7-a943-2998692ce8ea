import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255, unique: true })
  username: string;

  @Column({ length: 255, select: false })
  password: string;

  @Column({ length: 255, nullable: true })
  name: string;

  @Column({ default: 'active' })
  status: string;

  @Column({ default: 'user' })
  role: string;

  @Column({ type: 'json', nullable: true })
  permissions: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
