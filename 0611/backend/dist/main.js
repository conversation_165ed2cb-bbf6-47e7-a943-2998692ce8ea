import logger from '@/lib/utils/logger';

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    app.enableCors({
        origin: process.env.NODE_ENV === 'development' ? '*' : process.env.ALLOWED_ORIGINS,
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
        credentials: true,
    });
    const config = new swagger_1.DocumentBuilder()
        .setTitle('5G Web API')
        .setDescription('5G Web Application API Documentation')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('auth', '认证相关接口')
        .addTag('users', '用户管理接口')
        .addTag('customers', '客户管理接口')
        .addTag('tasks', '任务管理接口')
        .addTag('video-calls', '视频通话接口')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api', app, document);
    const port = process.env.PORT || 3000;
    await app.listen(port);
    logger.debug(`
    ################################################
    🚀 Server listening on port: ${port}
    🌍 Environment: ${process.env.NODE_ENV}
    📑 API Documentation: http://localhost:${port}/api
    ################################################
  `);
}
bootstrap().catch(err => {
    logger.error('Application failed to start:', err);
    process.exit(1);
});
//# sourceMappingURL=main.js.map