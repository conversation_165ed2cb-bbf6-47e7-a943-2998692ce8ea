import logger from '@/lib/utils/logger';

if (typeof global.crypto === 'undefined') {
    const crypto = require('crypto');
    Object.defineProperty(global, 'crypto', {
        value: {
            ...crypto,
            randomUUID: crypto.randomUUID || (() => crypto.randomBytes(16).toString('hex'))
        },
        writable: false,
        configurable: false,
        enumerable: false
    });
    logger.log('Crypto polyfill has been applied');
}
//# sourceMappingURL=polyfill.js.map