import * as Jo<PERSON> from 'joi';
export interface AppConfig {
    port: number;
    nodeEnv: string;
}
export interface DatabaseConfig {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
}
export interface JwtConfig {
    secret: string;
    expiresIn: string;
}
export interface RedisConfig {
    host: string;
    port: number;
}
export interface StorageConfig {
    type: 'local' | 's3' | 'oss';
    path: string;
}
export declare const appConfig: (() => {
    port: number;
    nodeEnv: string;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    port: number;
    nodeEnv: string;
}>;
export declare const dbConfig: (() => {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
}>;
export declare const jwtConfig: (() => {
    secret: string;
    expiresIn: string;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    secret: string;
    expiresIn: string;
}>;
export declare const redisConfig: (() => {
    host: string;
    port: number;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    host: string;
    port: number;
}>;
export declare const storageConfig: (() => {
    type: string;
    path: string;
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    type: string;
    path: string;
}>;
export declare const validationSchema: Joi.ObjectSchema<any>;
