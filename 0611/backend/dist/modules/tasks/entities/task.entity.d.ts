import { User } from '../../users/entities/user.entity';
import { Customer } from '../../customers/entities/customer.entity';
export declare class Task {
    id: string;
    title: string;
    description: string;
    status: string;
    customer: Customer;
    customerId: string;
    businessType: string;
    hasAttachment: boolean;
    attachments: any;
    createdAt: Date;
    updatedAt: Date;
    createdBy: User;
    createdById: string;
}
