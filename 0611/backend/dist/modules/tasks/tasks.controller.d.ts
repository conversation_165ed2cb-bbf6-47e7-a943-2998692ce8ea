import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
export declare class TasksController {
    private readonly tasksService;
    constructor(tasksService: TasksService);
    create(createTaskDto: CreateTaskDto, files: any, req: any): Promise<import("./entities/task.entity").Task>;
    findAll(query: any): Promise<{
        data: import("./entities/task.entity").Task[];
        total: number;
    }>;
    findOne(id: string): Promise<import("./entities/task.entity").Task>;
    update(id: string, updateTaskDto: UpdateTaskDto, files: any): Promise<import("./entities/task.entity").Task>;
    remove(id: string): Promise<void>;
}
