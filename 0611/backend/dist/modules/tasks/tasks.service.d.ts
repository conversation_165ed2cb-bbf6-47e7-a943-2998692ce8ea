import { Repository } from 'typeorm';
import { Task } from './entities/task.entity';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
export declare class TasksService {
    private tasksRepository;
    constructor(tasksRepository: Repository<Task>);
    create(createTaskDto: CreateTaskDto & {
        createdById?: string;
        hasAttachment?: boolean;
    }): Promise<Task>;
    findAll(query: any): Promise<{
        data: Task[];
        total: number;
    }>;
    findOne(id: string): Promise<Task>;
    update(id: string, updateTaskDto: UpdateTaskDto & {
        hasAttachment?: boolean;
    }): Promise<Task>;
    remove(id: string): Promise<void>;
}
