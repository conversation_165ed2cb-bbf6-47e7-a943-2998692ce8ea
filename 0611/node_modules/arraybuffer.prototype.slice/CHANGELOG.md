# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.4](https://github.com/es-shims/ArrayBuffer.prototype.slice/compare/v1.0.3...v1.0.4) - 2024-12-15

### Commits

- [actions] split out node 10-20, and 20+ [`5e59635`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/5e59635c948994b613c544b4e94fe93536d6b213)
- [Deps] update `call-bind`, `es-abstract`, `get-intrinsic` [`0afbb59`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/0afbb5942574055a8a3afdf0f69f60313e3f1af2)
- [<PERSON>] update `@es-shims/api`, `auto-changelog`, `es-value-fixtures`, `object-inspect`, `tape` [`d76caf4`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/d76caf4880ef358cf5384a6eb29d0f2f67e4d3a8)
- [Deps] update `call-bind`, `es-abstract`, `es-errors`, `get-intrinsic` [`2b374a2`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/2b374a2d4078d9122ac30bdecd8a8fae740aa74c)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `object-inspect`, `tape` [`a24fd39`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/a24fd39370d891ea3439f9d7a1c42183c0aa1bf7)
- [Tests] replace `aud` with `npm audit` [`c704fb0`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/c704fb0b1651ceb56b609a5b4646a993fc868219)
- [Deps] remove unused dependency [`0274b32`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/0274b32ab1d345fbf70745a79dd7a44f844179bb)
- [Dev Deps] add missing peer dep [`673f754`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/673f754797dd0c40801cca2f32ff08b372448c6b)

## [v1.0.3](https://github.com/es-shims/ArrayBuffer.prototype.slice/compare/v1.0.2...v1.0.3) - 2024-02-04

### Commits

- [Deps] update `array-buffer-byte-length`, `call-bind`, `define-properties`, `es-abstract`, `get-intrinsic`, `is-array-buffer` [`d9b6859`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/d9b68591ff509613d0dfc4036539ba4e0dc34931)
- [Dev Deps] update `aud`, `npmignore`, `object-inspect`, `tape` [`38cb58d`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/38cb58dfa3f3c8b11bfb2144f8e7cc74dd461f5e)
- [Refactor] use `es-errors` where possible, so things that only need those do not need `get-intrinsic` [`5c07bef`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/5c07befd134cae93ad5f9ab307ff67691ff5155b)

## [v1.0.2](https://github.com/es-shims/ArrayBuffer.prototype.slice/compare/v1.0.1...v1.0.2) - 2023-09-05

### Commits

- [Deps] update `es-abstract` [`a9ab0d2`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/a9ab0d2551bb301b740e333ea3795fad23fcbe40)
- [Dev Deps] update `tape` [`6b24af5`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/6b24af585dc9176c8ac3fd482cb1d5257e550a09)
- [Fix] move `es-abstract` to runtime deps [`63a8397`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/63a8397623d7749856f6392ae93bf87152c3916c)

## [v1.0.1](https://github.com/es-shims/ArrayBuffer.prototype.slice/compare/v1.0.0...v1.0.1) - 2023-07-11

### Commits

- [Fix] node &lt; 0.11 has an own nonconfigurable `slice` property; use it [`554823c`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/554823c92ce16d6b7184a7d0ccfe315b663584d7)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `es-abstract`, `tape` [`53b0421`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/53b04217048c645306597e2cfc55adb69c384146)
- [Deps] update `define-properties`, `get-intrinsic` [`4966b02`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/4966b02bc25ac006709b29ca370b9f6e159f723a)

## v1.0.0 - 2023-07-09

### Commits

- Initial implementation, tests, readme [`36b4b5e`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/36b4b5eedfa225c3086e9453b9db0088c299640a)
- Initial commit [`51499df`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/51499dfac7d8c67c2928cb47363a4de7ff17904a)
- npm init [`8ec604e`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/8ec604e7d3ef8d4c27376b09645f779c2244b08f)
- Only apps should have lockfiles [`ac54435`](https://github.com/es-shims/ArrayBuffer.prototype.slice/commit/ac54435161d4415e2122e3a682499f3a6df2f6de)
