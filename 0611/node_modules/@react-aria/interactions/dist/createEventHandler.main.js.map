{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC,GAQM,SAAS,0CAA6C,OAAmC;IAC9F,IAAI,CAAC,SACH,OAAO;IAGT,IAAI,wBAAwB;IAC5B,OAAO,CAAC;QACN,IAAI,QAAsB;YACxB,GAAG,CAAC;YACJ;gBACE,EAAE,cAAc;YAClB;YACA;gBACE,OAAO,EAAE,kBAAkB;YAC7B;YACA;gBACE,IAAI,uBACF,QAAQ,KAAK,CAAC;qBAEd,wBAAwB;YAE5B;YACA;gBACE,wBAAwB;YAC1B;YACA;gBACE,OAAO;YACT;QACF;QAEA,QAAQ;QAER,IAAI,uBACF,EAAE,eAAe;IAErB;AACF", "sources": ["packages/@react-aria/interactions/src/createEventHandler.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BaseEvent} from '@react-types/shared';\nimport {SyntheticEvent} from 'react';\n\n/**\n * This function wraps a React event handler to make stopPropagation the default, and support continuePropagation instead.\n */\nexport function createEventHandler<T extends SyntheticEvent>(handler?: (e: BaseEvent<T>) => void): ((e: T) => void) | undefined {\n  if (!handler) {\n    return undefined;\n  }\n\n  let shouldStopPropagation = true;\n  return (e: T) => {\n    let event: BaseEvent<T> = {\n      ...e,\n      preventDefault() {\n        e.preventDefault();\n      },\n      isDefaultPrevented() {\n        return e.isDefaultPrevented();\n      },\n      stopPropagation() {\n        if (shouldStopPropagation) {\n          console.error('stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.');\n        } else {\n          shouldStopPropagation = true;\n        }\n      },\n      continuePropagation() {\n        shouldStopPropagation = false;\n      },\n      isPropagationStopped() {\n        return shouldStopPropagation;\n      }\n    };\n\n    handler(event);\n\n    if (shouldStopPropagation) {\n      e.stopPropagation();\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "createEventHandler.main.js.map"}