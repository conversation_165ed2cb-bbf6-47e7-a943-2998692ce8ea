{"mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;AAMM,MAAM;IA2BX,qBAA8B;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB;IAC1C;IAEA,iBAAuB;QACrB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,WAAW,CAAC,cAAc;IACjC;IAEA,kBAAwB;QACtB,IAAI,CAAC,WAAW,CAAC,eAAe;QAChC,IAAI,CAAC,oBAAoB,GAAG,IAAM;IACpC;IAEA,uBAAgC;QAC9B,OAAO;IACT;IAEA,UAAU,CAAC;IAhCX,YAAY,IAAY,EAAE,WAAuB,CAAE;QACjD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG,YAAY,MAAM;QAChC,IAAI,CAAC,aAAa,GAAG,YAAY,aAAa;QAC9C,IAAI,CAAC,aAAa,GAAG,YAAY,aAAa;QAC9C,IAAI,CAAC,OAAO,GAAG,YAAY,OAAO;QAClC,IAAI,CAAC,UAAU,GAAG,YAAY,UAAU;QACxC,IAAI,CAAC,gBAAgB,GAAG,YAAY,gBAAgB;QACpD,IAAI,CAAC,UAAU,GAAG,YAAY,UAAU;QACxC,IAAI,CAAC,SAAS,GAAG,YAAY,SAAS;QACtC,IAAI,CAAC,SAAS,GAAG,YAAY,SAAS;QACtC,IAAI,CAAC,IAAI,GAAG;IACd;AAqBF;AAEO,SAAS,0CAAwC,MAA4C;IAClG,IAAI,WAAW,CAAA,GAAA,mBAAK,EAAE;QACpB,WAAW;QACX,UAAU;IACZ;IAEA,mDAAmD;IAEnD,CAAA,GAAA,qCAAc,EAAE;QACd,MAAM,QAAQ,SAAS,OAAO;QAC9B,OAAO;YACL,IAAI,MAAM,QAAQ,EAAE;gBAClB,MAAM,QAAQ,CAAC,UAAU;gBACzB,MAAM,QAAQ,GAAG;YACnB;QACF;IACF,GAAG,EAAE;IAEL,IAAI,eAAe,CAAA,GAAA,oCAAa,EAAE,CAAC;QACjC,mBAAA,6BAAA,OAAS;IACX;IAEA,wDAAwD;IACxD,OAAO,CAAA,GAAA,wBAAU,EAAE,CAAC;QAClB,wGAAwG;QACxG,sGAAsG;QACtG,6FAA6F;QAC7F,qGAAqG;QACrG,IACE,EAAE,MAAM,YAAY,qBACpB,EAAE,MAAM,YAAY,oBACpB,EAAE,MAAM,YAAY,uBACpB,EAAE,MAAM,YAAY,mBACpB;YACA,SAAS,OAAO,CAAC,SAAS,GAAG;YAE7B,IAAI,SAAS,EAAE,MAAM;YACrB,IAAI,gBAA2D,CAAC;gBAC9D,SAAS,OAAO,CAAC,SAAS,GAAG;gBAE7B,IAAI,OAAO,QAAQ,EACjB,uEAAuE;gBACvE,aAAa,IAAI,0CAAoB,QAAQ;gBAG/C,qEAAqE;gBACrE,IAAI,SAAS,OAAO,CAAC,QAAQ,EAAE;oBAC7B,SAAS,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACpC,SAAS,OAAO,CAAC,QAAQ,GAAG;gBAC9B;YACF;YAEA,OAAO,gBAAgB,CAAC,YAAY,eAAe;gBAAC,MAAM;YAAI;YAE9D,SAAS,OAAO,CAAC,QAAQ,GAAG,IAAI,iBAAiB;gBAC/C,IAAI,SAAS,OAAO,CAAC,SAAS,IAAI,OAAO,QAAQ,EAAE;wBACjD;qBAAA,6BAAA,SAAS,OAAO,CAAC,QAAQ,cAAzB,iDAAA,2BAA2B,UAAU;oBACrC,IAAI,kBAAkB,WAAW,SAAS,aAAa,GAAG,OAAO,SAAS,aAAa;oBACvF,OAAO,aAAa,CAAC,IAAI,WAAW,QAAQ;wBAAC,eAAe;oBAAe;oBAC3E,OAAO,aAAa,CAAC,IAAI,WAAW,YAAY;wBAAC,SAAS;wBAAM,eAAe;oBAAe;gBAChG;YACF;YAEA,SAAS,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ;gBAAC,YAAY;gBAAM,iBAAiB;oBAAC;iBAAW;YAAA;QAC5F;IACF,GAAG;QAAC;KAAa;AACnB;AAEO,IAAI,4CAAmB;AAOvB,SAAS,0CAAa,MAA+B;IAC1D,uEAAuE;IACvE,MAAO,UAAU,CAAC,CAAA,GAAA,iCAAU,EAAE,QAC5B,SAAS,OAAO,aAAa;IAG/B,IAAI,SAAS,CAAA,GAAA,oCAAa,EAAE;IAC5B,IAAI,gBAAgB,OAAO,QAAQ,CAAC,aAAa;IACjD,IAAI,CAAC,iBAAiB,kBAAkB,QACtC;IAGF,4CAAmB;IACnB,IAAI,eAAe;IACnB,IAAI,SAAS,CAAC;QACZ,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAChC,EAAE,wBAAwB;IAE9B;IAEA,IAAI,aAAa,CAAC;QAChB,IAAI,EAAE,MAAM,KAAK,iBAAiB,cAAc;YAC9C,EAAE,wBAAwB;YAE1B,qEAAqE;YACrE,6CAA6C;YAC7C,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC5B,eAAe;gBACf,CAAA,GAAA,2CAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,IAAI,UAAU,CAAC;QACb,IAAI,EAAE,MAAM,KAAK,UAAU,cACzB,EAAE,wBAAwB;IAE9B;IAEA,IAAI,YAAY,CAAC;QACf,IAAI,EAAE,MAAM,KAAK,UAAU,cAAc;YACvC,EAAE,wBAAwB;YAE1B,IAAI,CAAC,cAAc;gBACjB,eAAe;gBACf,CAAA,GAAA,2CAAoB,EAAE;gBACtB;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,QAAQ,QAAQ;IACxC,OAAO,gBAAgB,CAAC,YAAY,YAAY;IAChD,OAAO,gBAAgB,CAAC,WAAW,WAAW;IAC9C,OAAO,gBAAgB,CAAC,SAAS,SAAS;IAE1C,IAAI,UAAU;QACZ,qBAAqB;QACrB,OAAO,mBAAmB,CAAC,QAAQ,QAAQ;QAC3C,OAAO,mBAAmB,CAAC,YAAY,YAAY;QACnD,OAAO,mBAAmB,CAAC,WAAW,WAAW;QACjD,OAAO,mBAAmB,CAAC,SAAS,SAAS;QAC7C,4CAAmB;QACnB,eAAe;IACjB;IAEA,IAAI,MAAM,sBAAsB;IAChC,OAAO;AACT", "sources": ["packages/@react-aria/interactions/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {FocusableElement} from '@react-types/shared';\nimport {focusWithoutScrolling, getOwnerWindow, isFocusable, useEffectEvent, useLayoutEffect} from '@react-aria/utils';\nimport {FocusEvent as ReactFocusEvent, useCallback, useRef} from 'react';\n\nexport class SyntheticFocusEvent<Target = Element> implements ReactFocusEvent<Target> {\n  nativeEvent: FocusEvent;\n  target: EventTarget & Target;\n  currentTarget: EventTarget & Target;\n  relatedTarget: Element;\n  bubbles: boolean;\n  cancelable: boolean;\n  defaultPrevented: boolean;\n  eventPhase: number;\n  isTrusted: boolean;\n  timeStamp: number;\n  type: string;\n\n  constructor(type: string, nativeEvent: FocusEvent) {\n    this.nativeEvent = nativeEvent;\n    this.target = nativeEvent.target as EventTarget & Target;\n    this.currentTarget = nativeEvent.currentTarget as EventTarget & Target;\n    this.relatedTarget = nativeEvent.relatedTarget as Element;\n    this.bubbles = nativeEvent.bubbles;\n    this.cancelable = nativeEvent.cancelable;\n    this.defaultPrevented = nativeEvent.defaultPrevented;\n    this.eventPhase = nativeEvent.eventPhase;\n    this.isTrusted = nativeEvent.isTrusted;\n    this.timeStamp = nativeEvent.timeStamp;\n    this.type = type;\n  }\n\n  isDefaultPrevented(): boolean {\n    return this.nativeEvent.defaultPrevented;\n  }\n\n  preventDefault(): void {\n    this.defaultPrevented = true;\n    this.nativeEvent.preventDefault();\n  }\n\n  stopPropagation(): void {\n    this.nativeEvent.stopPropagation();\n    this.isPropagationStopped = () => true;\n  }\n\n  isPropagationStopped(): boolean {\n    return false;\n  }\n\n  persist() {}\n}\n\nexport function useSyntheticBlurEvent<Target = Element>(onBlur: (e: ReactFocusEvent<Target>) => void) {\n  let stateRef = useRef({\n    isFocused: false,\n    observer: null as MutationObserver | null\n  });\n\n  // Clean up MutationObserver on unmount. See below.\n   \n  useLayoutEffect(() => {\n    const state = stateRef.current;\n    return () => {\n      if (state.observer) {\n        state.observer.disconnect();\n        state.observer = null;\n      }\n    };\n  }, []);\n\n  let dispatchBlur = useEffectEvent((e: SyntheticFocusEvent<Target>) => {\n    onBlur?.(e);\n  });\n\n  // This function is called during a React onFocus event.\n  return useCallback((e: ReactFocusEvent<Target>) => {\n    // React does not fire onBlur when an element is disabled. https://github.com/facebook/react/issues/9142\n    // Most browsers fire a native focusout event in this case, except for Firefox. In that case, we use a\n    // MutationObserver to watch for the disabled attribute, and dispatch these events ourselves.\n    // For browsers that do, focusout fires before the MutationObserver, so onBlur should not fire twice.\n    if (\n      e.target instanceof HTMLButtonElement ||\n      e.target instanceof HTMLInputElement ||\n      e.target instanceof HTMLTextAreaElement ||\n      e.target instanceof HTMLSelectElement\n    ) {\n      stateRef.current.isFocused = true;\n\n      let target = e.target;\n      let onBlurHandler: EventListenerOrEventListenerObject | null = (e) => {\n        stateRef.current.isFocused = false;\n\n        if (target.disabled) {\n          // For backward compatibility, dispatch a (fake) React synthetic event.\n          dispatchBlur(new SyntheticFocusEvent('blur', e as FocusEvent));\n        }\n\n        // We no longer need the MutationObserver once the target is blurred.\n        if (stateRef.current.observer) {\n          stateRef.current.observer.disconnect();\n          stateRef.current.observer = null;\n        }\n      };\n\n      target.addEventListener('focusout', onBlurHandler, {once: true});\n\n      stateRef.current.observer = new MutationObserver(() => {\n        if (stateRef.current.isFocused && target.disabled) {\n          stateRef.current.observer?.disconnect();\n          let relatedTargetEl = target === document.activeElement ? null : document.activeElement;\n          target.dispatchEvent(new FocusEvent('blur', {relatedTarget: relatedTargetEl}));\n          target.dispatchEvent(new FocusEvent('focusout', {bubbles: true, relatedTarget: relatedTargetEl}));\n        }\n      });\n\n      stateRef.current.observer.observe(target, {attributes: true, attributeFilter: ['disabled']});\n    }\n  }, [dispatchBlur]);\n}\n\nexport let ignoreFocusEvent = false;\n\n/**\n * This function prevents the next focus event fired on `target`, without using `event.preventDefault()`.\n * It works by waiting for the series of focus events to occur, and reverts focus back to where it was before.\n * It also makes these events mostly non-observable by using a capturing listener on the window and stopping propagation.\n */\nexport function preventFocus(target: FocusableElement | null) {\n  // The browser will focus the nearest focusable ancestor of our target.\n  while (target && !isFocusable(target)) {\n    target = target.parentElement;\n  }\n\n  let window = getOwnerWindow(target);\n  let activeElement = window.document.activeElement as FocusableElement | null;\n  if (!activeElement || activeElement === target) {\n    return;\n  }\n  \n  ignoreFocusEvent = true;\n  let isRefocusing = false;\n  let onBlur = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusOut = (e: FocusEvent) => {\n    if (e.target === activeElement || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      // If there was no focusable ancestor, we don't expect a focus event.\n      // Re-focus the original active element here.\n      if (!target && !isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n  \n  let onFocus = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n    }\n  };\n\n  let onFocusIn = (e: FocusEvent) => {\n    if (e.target === target || isRefocusing) {\n      e.stopImmediatePropagation();\n\n      if (!isRefocusing) {\n        isRefocusing = true;\n        focusWithoutScrolling(activeElement);\n        cleanup();\n      }\n    }\n  };\n\n  window.addEventListener('blur', onBlur, true);\n  window.addEventListener('focusout', onFocusOut, true);\n  window.addEventListener('focusin', onFocusIn, true);\n  window.addEventListener('focus', onFocus, true);\n\n  let cleanup = () => {\n    cancelAnimationFrame(raf);\n    window.removeEventListener('blur', onBlur, true);\n    window.removeEventListener('focusout', onFocusOut, true);\n    window.removeEventListener('focusin', onFocusIn, true);\n    window.removeEventListener('focus', onFocus, true);\n    ignoreFocusEvent = false;\n    isRefocusing = false;\n  };\n\n  let raf = requestAnimationFrame(cleanup);\n  return cleanup;\n}\n"], "names": [], "version": 3, "file": "utils.main.js.map"}