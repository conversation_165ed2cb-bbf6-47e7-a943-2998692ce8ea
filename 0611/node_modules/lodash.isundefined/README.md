# lodash.isundefined v3.0.1

The [modern build](https://github.com/lodash/lodash/wiki/Build-Differences) of [lodash’s](https://lodash.com/) `_.isUndefined` exported as a [Node.js](http://nodejs.org/)/[io.js](https://iojs.org/) module.

## Installation

Using npm:

```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isundefined
```

In Node.js/io.js:

```js
var isUndefined = require('lodash.isundefined');
```

See the [documentation](https://lodash.com/docs#isUndefined) or [package source](https://github.com/lodash/lodash/blob/3.0.1-npm-packages/lodash.isundefined) for more details.
