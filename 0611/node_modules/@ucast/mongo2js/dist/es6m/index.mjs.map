{"version": 3, "file": "index.mjs", "sources": ["../../src/factory.ts"], "sourcesContent": ["import { createTranslatorFactory, ParsingInstruction, Condition, ITSELF } from '@ucast/core';\nimport {\n  MongoQuery,\n  MongoQueryParser,\n  MongoQueryFieldOperators,\n  allParsingInstructions,\n  defaultParsers\n} from '@ucast/mongo';\nimport {\n  createJsInterpreter,\n  allInterpreters,\n  JsInterpreter,\n  JsInterpretationOptions,\n  compare\n} from '@ucast/js';\n\ntype ThingFilter<T> = {\n  (object: T): boolean\n  ast: Condition\n};\n\ninterface HasToJSON {\n  toJSON(): unknown\n}\n\nfunction toPrimitive(value: unknown) {\n  if (value === null || typeof value !== 'object') {\n    return value;\n  }\n\n  if (value instanceof Date) {\n    return value.getTime();\n  }\n\n  if (value && typeof (value as HasToJSON).toJSON === 'function') {\n    return (value as HasToJSON).toJSON();\n  }\n\n  return value;\n}\n\nconst comparePrimitives: typeof compare = (a, b) => compare(toPrimitive(a), toPrimitive(b));\n\nexport interface FactoryOptions extends JsInterpretationOptions {\n  forPrimitives: boolean\n}\n\nexport type Filter = <\n  T = Record<string, unknown>,\n  Q extends MongoQuery<T> = MongoQuery<T>\n>(query: Q) => ThingFilter<T>;\n\nexport type PrimitiveMongoQuery<T> = MongoQueryFieldOperators<T> & Partial<{\n  $and: MongoQueryFieldOperators<T>[],\n  $or: MongoQueryFieldOperators<T>[],\n  $nor: MongoQueryFieldOperators<T>[]\n}>;\nexport type PrimitiveFilter = <\n  T,\n  Q extends PrimitiveMongoQuery<T> = PrimitiveMongoQuery<T>\n>(query: Q) => ThingFilter<T>;\n\ntype FilterType<T extends { forPrimitives?: true }> = T['forPrimitives'] extends true\n  ? PrimitiveFilter\n  : Filter;\n\nexport function createFactory<\n  T extends Record<string, ParsingInstruction<any, any>>,\n  I extends Record<string, JsInterpreter<any>>,\n  P extends { forPrimitives?: true }\n>(instructions: T, interpreters: I, options?: Partial<FactoryOptions> & P): FilterType<P> {\n  const parser = new MongoQueryParser(instructions);\n  const interpret = createJsInterpreter(interpreters, {\n    compare: comparePrimitives,\n    ...options\n  });\n\n  if (options && options.forPrimitives) {\n    const params = { field: ITSELF };\n    const parse = parser.parse;\n    parser.setParse(query => parse(query, params));\n  }\n\n  return createTranslatorFactory(parser.parse, interpret) as any;\n}\n\nexport const guard = createFactory(allParsingInstructions, allInterpreters);\n\nconst compoundOperators = ['$and', '$or'] as const;\nconst allPrimitiveParsingInstructions = compoundOperators.reduce((instructions, name) => {\n  instructions[name] = { ...instructions[name], type: 'field' } as any;\n  return instructions;\n}, {\n  ...allParsingInstructions,\n  $nor: {\n    ...allParsingInstructions.$nor,\n    type: 'field',\n    parse: defaultParsers.compound\n  }\n});\n\nexport const squire = createFactory(allPrimitiveParsingInstructions, allInterpreters, {\n  forPrimitives: true\n});\nexport const filter = guard; // TODO: remove in next major version\n"], "names": ["toPrimitive", "value", "Date", "getTime", "toJSON", "comparePrimitives", "a", "b", "compare", "createFactory", "instructions", "interpreters", "options", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interpret", "createJsInterpreter", "forPrimitives", "params", "field", "ITSELF", "parse", "set<PERSON><PERSON><PERSON>", "query", "createTranslatorFactory", "guard", "allParsingInstructions", "allInterpreters", "squire", "reduce", "name", "type", "$nor", "defaultParsers", "compound", "filter"], "mappings": "8TAyBA,SAASA,EAAYC,UACL,OAAVA,GAAmC,iBAAVA,EACpBA,EAGLA,aAAiBC,KACZD,EAAME,UAGXF,GAAgD,mBAA/BA,EAAoBG,OAC/BH,EAAoBG,SAGvBH,EAGT,MAAMI,EAAoC,CAACC,EAAGC,IAAMC,EAAQR,EAAYM,GAAIN,EAAYO,IAyBjF,SAASE,EAIdC,EAAiBC,EAAiBC,SAC5BC,EAAS,IAAIC,EAAiBJ,GAC9BK,EAAYC,EAAoBL,iBACpCH,QAASH,GACNO,OAGDA,GAAWA,EAAQK,cAAe,OAC9BC,EAAS,CAAEC,MAAOC,GAClBC,EAAQR,EAAOQ,MACrBR,EAAOS,SAASC,GAASF,EAAME,EAAOL,WAGjCM,EAAwBX,EAAOQ,MAAON,SAGlCU,EAAQhB,EAAciB,EAAwBC,GAe9CC,EAASnB,EAbI,CAAC,OAAQ,OACuBoB,OAAO,CAACnB,EAAcoB,KAC9EpB,EAAaoB,oBAAapB,EAAaoB,IAAOC,KAAM,UAC7CrB,oBAEJgB,GACHM,sBACKN,EAAuBM,MAC1BD,KAAM,QACNV,MAAOY,EAAeC,cAI2CP,EAAiB,CACpFV,eAAe,IAEJkB,EAASV"}