!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@ucast/core"),require("@ucast/mongo"),require("@ucast/js")):"function"==typeof define&&define.amd?define(["exports","@ucast/core","@ucast/mongo","@ucast/js"],t):t(((e="undefined"!=typeof globalThis?globalThis:e||self).ucast=e.ucast||{},e.ucast.mongo2js={}),e.ucast.core,e.ucast.mongo,e.ucast.js)}(this,(function(e,t,n,r){"use strict";function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function o(e){return null===e||"object"!=typeof e?e:e instanceof Date?e.getTime():e&&"function"==typeof e.toJSON?e.toJSON():e}var f=function(e,t){return r.compare(o(e),o(t))};function c(e,o,c){var i=new n.MongoQueryParser(e),a=r.createJsInterpreter(o,u({compare:f},c));if(c&&c.forPrimitives){var s={field:t.ITSELF},l=i.parse;i.setParse((function(e){return l(e,s)}))}return t.createTranslatorFactory(i.parse,a)}var i=c(n.allParsingInstructions,r.allInterpreters),a=c(["$and","$or"].reduce((function(e,t){return e[t]=u({},e[t],{type:"field"}),e}),u({},n.allParsingInstructions,{$nor:u({},n.allParsingInstructions.$nor,{type:"field",parse:n.defaultParsers.compound})})),r.allInterpreters,{forPrimitives:!0}),s=i;Object.keys(t).forEach((function(n){"default"!==n&&Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})})),Object.keys(n).forEach((function(t){"default"!==t&&Object.defineProperty(e,t,{enumerable:!0,get:function(){return n[t]}})})),Object.keys(r).forEach((function(t){"default"!==t&&Object.defineProperty(e,t,{enumerable:!0,get:function(){return r[t]}})})),e.createFactory=c,e.filter=s,e.guard=i,e.squire=a,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=index.js.map
