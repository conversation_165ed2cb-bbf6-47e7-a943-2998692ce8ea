{"name": "@ucast/mongo2js", "version": "1.4.0", "description": "**************:stalniy/ucast.git", "sideEffects": false, "main": "dist/es6c/index.js", "module": "dist/es5m/index.js", "es2015": "dist/es6m/index.mjs", "legacy": "dist/umd/index.js", "typings": "dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/es6m/index.mjs", "require": "./dist/es6c/index.js"}}, "scripts": {"build.types": "tsc", "prebuild": "rm -rf dist/* && npm run build.types", "build": "rollup -c ../../rollup.config.js -n ucast.mongo2js -g @ucast/core:ucast.core,@ucast/js:ucast.js,@ucast/mongo:ucast.mongo", "lint": "eslint --ext .js,.ts src/", "test": "mocha -r ts-node/register spec/*", "coverage": "nyc -n src npm run test && nyc report --reporter=lcov", "prerelease": "npm run lint && npm test && NODE_ENV=production npm run build", "release": "semantic-release -e ../../semantic-release.js"}, "publishConfig": {"access": "public"}, "files": ["dist", "NOTICE", "*.d.ts"], "repository": {"type": "git", "url": "git+https://github.com/stalniy/ucast.git"}, "keywords": ["mongo", "conditions", "query", "builder", "ast"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/stalniy/ucast/issues"}, "homepage": "https://github.com/stalniy/ucast/tree/master/packages/mongo2js#readme", "devDependencies": {"@babel/core": "^7.12.3", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-transform-typescript": "^7.12.1", "@babel/preset-env": "^7.12.1", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-node-resolve": "^8.4.0", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@semantic-release/github": "^7.1.1", "@semantic-release/npm": "^7.0.6", "@types/chai": "^4.2.14", "@types/chai-spies": "^1.0.2", "@types/mocha": "^7.0.2", "@typescript-eslint/eslint-plugin": "^3.10.1", "chai": "^4.2.0", "chai-spies": "^1.0.0", "eslint": "^7.12.1", "eslint-config-airbnb-base": "^14.2.0", "eslint-config-airbnb-typescript": "^8.0.2", "eslint-plugin-import": "^2.22.1", "mocha": "^8.2.0", "nyc": "^15.1.0", "rollup": "^2.33.1", "rollup-plugin-terser": "^6.1.0", "semantic-release": "^17.4.7", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dependencies": {"@ucast/core": "^1.6.1", "@ucast/js": "^3.0.0", "@ucast/mongo": "^2.4.0"}}