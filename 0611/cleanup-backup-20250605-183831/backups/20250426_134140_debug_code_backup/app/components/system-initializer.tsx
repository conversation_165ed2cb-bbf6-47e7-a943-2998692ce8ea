"use client"

import { useEffect } from "react"

/**
 * 系统初始化组件
 * 在客户端加载时执行系统初始化操作
 */
export function SystemInitializer() {
  useEffect(() => {
    const initSystem = async () => {
      try {
        // 调用初始化API
        const response = await fetch("/api/init")
        const data = await response.json()

        if (data.success) {
          console.log("系统初始化成功")

          // 初始化权限系统
          try {
            const permResponse = await fetch("/api/admin/refresh-permissions", {
              method: "POST",
              headers: { "Content-Type": "application/json" }
            })
            const permData = await permResponse.json()

            if (permData.success) {
              console.log("权限系统初始化成功")
            } else {
              console.warn("权限系统初始化失败:", permData.message)
            }
          } catch (permError) {
            console.warn("权限系统初始化请求失败:", permError)
            // 不阻止系统继续运行
          }
        } else {
          console.error("系统初始化失败:", data.message)
        }
      } catch (error) {
        console.error("系统初始化请求失败:", error)
      }
    }

    initSystem()
  }, [])

  // 这个组件不渲染任何内容
  return null
}
