"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"

interface RejectDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (reason: string) => void
  accountName: string
}

export function RejectDialog({ isOpen, onClose, onConfirm, accountName }: RejectDialogProps) {
  const [reason, setReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleConfirm = async () => {
    if (!reason.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onConfirm(reason)
      setReason("")
      onClose()
    } catch (error) {
      console.error("拒绝操作失败:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>拒绝用户审核</DialogTitle>
          <DialogDescription>
            您正在拒绝用户 <span className="font-semibold">{accountName}</span> 的审核申请。请提供拒绝原因，该原因将通过邮件和系统通知发送给用户。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            placeholder="请输入拒绝原因..."
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="min-h-[100px]"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            取消
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!reason.trim() || isSubmitting}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {isSubmitting ? "提交中..." : "确认拒绝"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
