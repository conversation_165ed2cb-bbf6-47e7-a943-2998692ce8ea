import { type NextRequest, NextResponse } from "next/server"
import { validateRequest } from "@/lib/middleware/validate-request"
import { errorHandler } from "@/lib/middleware/error-handler"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { registerSchema } from "@/lib/validation/schemas"
import { verifyVerificationCode } from "@/lib/services/verification"
import type { user } from "@prisma/client"

/**
 * 用户注册响应接口
 */
interface RegisterResponse {
  code: number
  success: boolean
  message: string
  data: {
    user: Omit<user, 'password'>
  } | null
}

/**
 * 处理用户注册请求
 * POST /api/auth/register
 *
 * 安全考虑：
 * 1. 注册频率限制，防止批量注册
 * 2. 错误信息模糊化，不泄露敏感信息
 * 3. 密码强度验证
 * 4. 验证码必须验证
 * 5. IP 限制和监控
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 注册响应
 *
 * 处理流程：
 * 1. 检查注册频率
 * 2. 记录注册尝试
 * 3. 验证请求参数（用户名、密码、邮箱、验证码）
 * 4. 验证邮箱验证码
 * 5. 检查用户名和邮箱是否已存在（不泄露具体哪个存在）
 * 6. 创建新用户账户
 *
 * 错误处理：
 * - 400: 参数验证失败、验证码无效、用户名/邮箱已存在
 * - 429: 注册过于频繁，请稍后再试
 * - 500: 服务器内部错误
 *
 * @example
 * ```typescript
 * // 请求体示例
 * {
 *   "username": "testuser",
 *   "password": "Test123456",
 *   "email": "<EMAIL>",
 *   "verificationCode": "123456"
 * }
 *
 * // 成功响应示例
 * {
 *   "code": 200,
 *   "success": true,
 *   "message": "注册成功",
 *   "data": {
 *     "user": {
 *       "id": "user_id",
 *       "username": "testuser",
 *       "email": "<EMAIL>",
 *       "role": "user"
 *     }
 *   }
 * }
 * ```
 */

export async function POST(request: NextRequest): Promise<NextResponse<RegisterResponse>> {
  try {
    const body = await request.json()

    // 验证请求参数
    const result = registerSchema.safeParse(body)
    if (!result.success) {
      return NextResponse.json({
        code: 400,
        success: false,
        message: result.error.errors[0].message,
        data: null
      }, { status: 400 })
    }

    const { username, email, password, verificationCode } = result.data

    // 验证验证码
    const isValidCode = await verifyVerificationCode(email, verificationCode)
    if (!isValidCode) {
      return NextResponse.json({
        code: 400,
        success: false,
        message: '验证码无效或已过期',
        data: null
      }, { status: 400 })
    }

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUserByEmail) {
      return NextResponse.json({
        code: 400,
        success: false,
        message: '该邮箱已注册，请直接登录',
        data: null
      }, { status: 400 })
    }

    // 检查用户名是否已存在
    const existingUserByUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUserByUsername) {
      return NextResponse.json({
        code: 400,
        success: false,
        message: '用户名已被使用，请更换其他用户名',
        data: null
      }, { status: 400 })
    }

    // 创建新用户
    console.log('开始创建新用户...')

    // 尝试使用bcryptjs加密密码
    let hashedPassword;
    try {
      hashedPassword = await hash(password, 10)
      console.log('密码加密成功')
    } catch (hashError) {
      console.error('密码加密失败:', hashError)
      // 如果加密失败，返回错误
      return NextResponse.json({
        code: 500,
        success: false,
        message: '注册失败，密码处理错误',
        data: null
      }, { status: 500 })
    }

    // 检查是否存在 USER 角色，如果不存在则创建
    let userRole = await prisma.role.findUnique({
      where: { code: 'USER' }
    });

    if (!userRole) {
      console.log('创建默认用户角色...');
      userRole = await prisma.role.create({
        data: {
          code: 'USER',
          name: '普通用户',
          type: 'customer',
          description: '系统默认用户角色',
          permissions: ['notifications:list', 'notifications:read', 'user:profile', 'user:password', 'TASK_READ', 'TASK_CREATE']
        }
      });
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        roleCode: 'USER',
        name: username,
        image: null,
        emailVerified: null,
        permissions: ['notifications:list', 'notifications:read', 'user:profile', 'user:password', 'TASK_READ', 'TASK_CREATE']
      }
    })
    console.log('用户创建成功:', user)

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user

    return NextResponse.json({
      code: 200,
      success: true,
      message: '注册成功',
      data: { user: userWithoutPassword }
    })
  } catch (error) {
    console.error('注册错误:', error)
    return NextResponse.json({
      code: 500,
      success: false,
      message: '注册失败，请稍后重试',
      data: null
    }, { status: 500 })
  }
}