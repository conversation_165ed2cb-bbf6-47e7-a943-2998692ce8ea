/**
 * 导出客户列表API
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 导出客户列表
 *
 * @route GET /api/customers/export
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "导出客户列表API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 })
    }

    // 获取导出格式
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'csv'

    // 查询客户列表（非管理员用户）
    const customers = await prisma.user.findMany({
      where: {
        roleCode: {
          not: 'ADMIN'
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        roleCode: true,
        status: true,
        balance: true,
        creditLimit: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        createdBy: {
          select: {
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 格式化数据
    const formattedData = customers.map(customer => ({
      用户名: customer.username,
      邮箱: customer.email,
      昵称: customer.name || '',
      电话: customer.phone || '',
      角色: customer.role.name,
      账户类型: customer.role.type === 'system' ? '管理员' : (customer.role.type === 'enterprise' ? '企业用户' : '个人用户'),
      状态: customer.status === 'active' ? '正常' : (customer.status === 'inactive' ? '停用' : '待审核'),
      余额: customer.balance || 0,
      授信额度: customer.creditLimit || 0,
      认证状态: customer.verificationStatus === 'approved' ? '已认证' :
               (customer.verificationStatus === 'rejected' ? '已拒绝' :
               (customer.verificationStatus === 'pending' ? '待审核' : '未认证')),
      认证类型: customer.verificationType === 'personal' ? '个人认证' :
               (customer.verificationType === 'enterprise' ? '企业认证' : '未认证'),
      创建者: customer.createdBy ? (customer.createdBy.name || customer.createdBy.username) : '系统',
      创建时间: customer.createdAt.toLocaleString('zh-CN'),
      更新时间: customer.updatedAt.toLocaleString('zh-CN')
    }))

    // 根据格式生成导出内容
    let content = ''
    let contentType = ''
    let filename = `客户列表_${new Date().toISOString().split('T')[0]}`

    if (format === 'csv') {
      // 生成CSV格式
      const headers = Object.keys(formattedData[0]).join(',')
      const rows = formattedData.map(row =>
        Object.values(row).map(value =>
          typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value
        ).join(',')
      ).join('\n')

      content = `${headers}\n${rows}`
      contentType = 'text/csv;charset=utf-8'
      filename += '.csv'
    } else if (format === 'excel') {
      // 简单模拟Excel格式（实际应使用专门的库）
      const headers = Object.keys(formattedData[0]).join('\t')
      const rows = formattedData.map(row =>
        Object.values(row).join('\t')
      ).join('\n')

      content = `${headers}\n${rows}`
      contentType = 'application/vnd.ms-excel;charset=utf-8'
      filename += '.xls'
    } else {
      return NextResponse.json({
        success: false,
        message: '不支持的导出格式'
      }, { status: 400 })
    }

    // 设置响应头
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Content-Disposition', `attachment; filename="${filename}"`)

    return new NextResponse(content, {
      status: 200,
      headers
    })
  } catch (error) {
    console.error("导出客户列表错误:", error)
    return NextResponse.json({
      success: false,
      message: '导出客户列表失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
