/**
 * 客户授信额度API
 * 处理客户授信额度的操作
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 更新客户授信额度
 *
 * @route POST /api/customers/[id]/limit/credit
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('客户授信API接收到的客户ID:', customerId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "客户授信额度API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const adminId = admin.id;

    // 获取请求体
    const body = await request.json()
    const { creditLimit, operationType = "set", remarks } = body

    if (creditLimit === undefined) {
      return NextResponse.json({
        success: false,
        message: '授信额度不能为空'
      }, { status: 400 })
    }

    if (!remarks) {
      return NextResponse.json({
        success: false,
        message: '备注不能为空'
      }, { status: 400 })
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: customerId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 计算新的授信额度
    let newCreditLimit = parseFloat(creditLimit.toString())
    const currentCreditLimit = user.creditLimit || 0

    // 根据操作类型计算新的授信额度
    if (operationType === "add") {
      // 增加授信额度
      newCreditLimit = currentCreditLimit + newCreditLimit
    } else if (operationType === "subtract") {
      // 减少授信额度
      newCreditLimit = currentCreditLimit - newCreditLimit

      // 确保授信额度不会变成负数
      if (newCreditLimit < 0) {
        newCreditLimit = 0
      }
    }

    // 更新用户授信额度
    const updatedUser = await prisma.user.update({
      where: { id: customerId },
      data: { creditLimit: newCreditLimit }
    })

    // 记录授信额度变更日志
    try {
      // 记录到授信额度日志表
      await prisma.$queryRaw`
        INSERT INTO user_credit_logs (
          id,
          "userId",
          "adminId",
          "oldCreditLimit",
          "newCreditLimit",
          remarks,
          "createdAt"
        ) VALUES (
          gen_random_uuid(),
          ${customerId},
          ${adminId},
          ${user.creditLimit},
          ${newCreditLimit},
          ${remarks},
          NOW()
        )
      `

      // 同时记录到余额交易表，便于在余额记录中显示
      let transactionType = "credit_set"
      if (operationType === "add") {
        transactionType = "credit_add"
      } else if (operationType === "subtract") {
        transactionType = "credit_subtract"
      }

      await prisma.balanceTransaction.create({
        data: {
          userId: customerId,
          amount: 0, // 授信额度变动不影响余额
          balanceAfter: user.balance || 0,
          creditLimitChange: newCreditLimit - (user.creditLimit || 0),
          creditLimitAfter: newCreditLimit,
          type: transactionType,
          paymentMethod: "system",
          remarks: remarks,
          adminId: adminId
        }
      })
    } catch (error) {
      console.error('创建授信额度日志失败:', error)
      // 继续执行，不中断流程
    }

    return NextResponse.json({
      success: true,
      message: '用户授信额度已更新',
      data: {
        id: updatedUser.id,
        creditLimit: updatedUser.creditLimit,
        newCreditLimit: updatedUser.creditLimit,
        oldCreditLimit: user.creditLimit
      }
    })
  } catch (error: any) {
    console.error('更新用户授信额度错误:', error)
    return NextResponse.json({
      success: false,
      message: '更新用户授信额度失败',
      error: error.message
    }, { status: 500 })
  }
}

/**
 * 获取客户授信额度
 *
 * @route GET /api/customers/[id]/limit/credit
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('客户授信API接收到的客户ID:', customerId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "客户授信额度API");
    if (response) {
      return response;
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: customerId },
      select: {
        id: true,
        username: true,
        name: true,
        creditLimit: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: '获取用户授信额度成功',
      data: {
        creditLimit: user.creditLimit,
        user: {
          id: user.id,
          username: user.username,
          name: user.name
        }
      }
    })
  } catch (error: any) {
    console.error('获取用户授信额度错误:', error)
    return NextResponse.json({
      success: false,
      message: '获取用户授信额度失败',
      error: error.message
    }, { status: 500 })
  }
}
