/**
 * 客户详情API
 * 处理单个客户相关的请求
 */

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { UserCacheService } from "@/app/lib/user-cache";
import { getProvinceName, getCityName, getDistrictName } from '@/app/lib/data/area-data-adapter';

// 格式化并返回客户数据
function formatAndReturnCustomer(user: any, customerId: string, byUsername: boolean) {
  // 检查用户是否存在
  if (!user) {
    console.error('用户不存在');
    return NextResponse.json({
      success: false,
      message: '客户不存在',
      debug: {
        requestedId: customerId,
        byUsername
      }
    }, { status: 404 });
  }

  // 格式化客户数据
  const { password, ...userData } = user;

  // 转换为前端需要的格式
  const formattedCustomer = {
    id: user.id,
    name: user.name || user.username,
    loginName: user.username,
    email: user.email,
    phone: user.phone || '',
    accountType: user.role?.type || 'personal',
    status: user.status,
    createdAt: user.createdAt.toISOString(),
    updatedAt: user.updatedAt.toISOString(),
    industry: user.industry || '',
    province: user.province || '',
    city: user.city || '',
    district: user.district || '',
    address: user.address || '',
    description: user.description || '',
    balance: user.balance || 0,
    creditLimit: user.creditLimit || 0,
    feeRate: '0%', // 默认费率，实际应从费率表中获取
    disabledAt: user.disabledAt ? user.disabledAt.toISOString() : undefined,
    disableReason: user.disableReason || undefined,
    // 认证状态
    personalVerification: user.verification?.type === 'personal' ? user.verification?.status : undefined,
    enterpriseVerification: user.verification?.type === 'enterprise' ? user.verification?.status : undefined,
  };

  return NextResponse.json({
    success: true,
    message: '获取客户详情成功',
    data: formattedCustomer
  });
}

/**
 * 获取单个客户详情
 *
 * @route GET /api/customers/[id]
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = params.id;
    try {
      customerId = decodeURIComponent(params.id);
    } catch (e) {
      console.error('解码ID时出错:', e);
      // 如果解码失败，使用原始ID
    }

    // 去除URL中可能的特殊字符
    customerId = customerId.trim();

    // 检查是否有byUsername参数
    const searchParams = request.nextUrl.searchParams;
    let byUsername = searchParams.get('byUsername') === 'true';

    console.log('API接收到的客户ID:', customerId, '是否按用户名查询:', byUsername);

    // 如果是ID包含特殊字符，尝试清理
    if (customerId.includes('%') || customerId.includes('+')) {
      try {
        customerId = decodeURIComponent(customerId);
        console.log('再次解码后的ID:', customerId);
      } catch (e) {
        console.error('解码ID时出错:', e);
      }
    }

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "获取客户详情API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 });
    }

    const adminId = admin.id;

    // 检查用户是否存在
    console.log('尝试查询客户，ID:', customerId);

    // 先尝试直接查询
    let customer = null;

    // 先尝试从缓存中获取用户ID
    let cachedUserId = null;

    // 如果有byUsername参数，先从缓存中查找用户ID
    if (byUsername) {
      cachedUserId = UserCacheService.getUserIdByUsername(customerId);
      if (cachedUserId) {
        console.log('从缓存中找到用户ID:', cachedUserId, '用户名:', customerId);
        // 如果找到缓存的用户ID，将customerId替换为缓存的ID
        customerId = cachedUserId;
        // 将byUsername设置为false，使用ID查询
        byUsername = false;
      }
    }

    // 如果有byUsername参数且缓存中没有找到，则使用用户名查询
    if (byUsername) {
      try {
        console.log('按用户名查询:', customerId);

        // 尝试精确匹配和不区分大小写匹配
        customer = await prisma.user.findFirst({
          where: {
            OR: [
              { username: customerId },
              { username: { equals: customerId, mode: 'insensitive' } }
            ]
          },
        include: {
          role: {
            select: {
              name: true,
              type: true
            }
          },

          verification: {
            select: {
              id: true,
              type: true,
              status: true
            }
          }
        }
      });

      if (customer) {
        console.log('通过用户名直接查询找到用户:', customer.id);
        return formatAndReturnCustomer(customer, customerId, byUsername);
      }
    } catch (err) {
      console.error('使用用户名直接查询时出错:', err);
    }
  }

    // 如果没有byUsername参数或者用户名查询失败，尝试使用ID查询
    if (!customer) {
      // 先尝试从缓存中获取用户信息
      const cachedUser = UserCacheService.getUser(customerId);
      if (cachedUser) {
        console.log('从缓存中找到用户信息:', cachedUser.id);
        customer = cachedUser;
      } else {
        try {
          console.log('尝试使用ID查询:', customerId);
          customer = await prisma.user.findUnique({
            where: { id: customerId },
            include: {
              role: {
                select: {
                  name: true,
                  type: true
                }
              },

              verification: {
                select: {
                  id: true,
                  type: true,
                  status: true
                }
              }
            }
          });

          // 如果找到用户，缓存用户信息
          if (customer) {
            console.log('通过ID查询找到用户:', customer.id);

            // 缓存用户信息和用户名到ID的映射
            UserCacheService.setUser(customer.id, customer);
            if (customer.username) {
              UserCacheService.setUserIdByUsername(customer.username, customer.id);
            }
          }
        } catch (err) {
          console.error('查询用户时出错:', err);
        }
      }
    }

    // 如果直接查询失败，尝试使用username查询
    // 这里优先使用username查询，因为前端传递的可能就是用户名
    if (!customer) {
      try {
        console.log('尝试使用username查询:', customerId);

        // 尝试精确匹配用户名
        customer = await prisma.user.findFirst({
          where: { username: customerId },
          include: {
            role: {
              select: {
                name: true,
                type: true
              }
            },

            verification: {
              select: {
                id: true,
                type: true,
                status: true
              }
            }
          }
        });

        if (customer) {
          console.log('通过精确匹配的username找到用户:', customer.id);

          // 缓存用户信息和用户名到ID的映射
          UserCacheService.setUser(customer.id, customer);
          UserCacheService.setUserIdByUsername(customer.username, customer.id);

          return formatAndReturnCustomer(customer, customerId, byUsername);
        }

        // 如果精确匹配失败，尝试模糊匹配
        console.log('精确匹配失败，尝试模糊匹配:', customerId);
        customer = await prisma.user.findFirst({
          where: {
            OR: [
              { username: { equals: customerId, mode: 'insensitive' } },
              { username: { contains: customerId, mode: 'insensitive' } },
              { username: { startsWith: customerId, mode: 'insensitive' } }
            ]
          },
          include: {
            role: {
              select: {
                name: true,
                type: true
              }
            },

            verification: {
              select: {
                id: true,
                type: true,
                status: true
              }
            }
          }
        });

        if (customer) {
          console.log('通过模糊匹配的username找到用户:', customer.id);
          return formatAndReturnCustomer(customer, customerId, byUsername);
        }
      } catch (err) {
        console.error('使用username查询用户时出错:', err);
      }
    }

    // 如果还是没找到用户，尝试使用邮箱查询
    if (!customer && customerId.includes('@')) {
      try {
        console.log('尝试使用邮箱查询:', customerId);
        customer = await prisma.user.findFirst({
          where: { email: customerId },
          include: {
            role: {
              select: {
                name: true,
                type: true
              }
            },

            verification: {
              select: {
                id: true,
                type: true,
                status: true
              }
            }
          }
        });

        if (customer) {
          console.log('通过邮箱找到用户:', customer.id);
          return formatAndReturnCustomer(customer, customerId, byUsername);
        }
      } catch (err) {
        console.error('使用邮箱查询用户时出错:', err);
      }
    }

    // 如果所有方法都失败，尝试查询所有用户并返回调试信息
    if (!customer) {
      console.log('未找到客户，尝试查询所有用户');

      // 查询所有用户，限制数量以避免响应过大
      const allUsers = await prisma.user.findMany({
        select: { id: true, username: true, email: true },
        take: 20
      });
      console.log('数据库中的用户示例:', allUsers);

      // 尝试模糊查询
      console.log('尝试模糊查询，查找包含该ID的用户');
      let fuzzyUsers: any[] = [];
      try {
        fuzzyUsers = await prisma.user.findMany({
          where: {
            id: {
              contains: customerId.replace(/[^a-zA-Z0-9]/g, '')
            }
          },
          select: { id: true, username: true }
        });
        console.log('模糊查询结果:', fuzzyUsers);

        // 如果没有找到，尝试使用邮箱查询
        if (fuzzyUsers.length === 0 && customerId.includes('@')) {
          console.log('尝试使用邮箱查询:', customerId);
          const emailUser = await prisma.user.findFirst({
            where: { email: customerId },
            select: { id: true, username: true }
          });
          if (emailUser) {
            fuzzyUsers = [emailUser];
            console.log('通过邮箱找到用户:', emailUser.id);
          }
        }
      } catch (err) {
        console.error('模糊查询时出错:', err);
      }

      // 如果模糊查询找到了用户，使用第一个结果
      if (fuzzyUsers.length > 0) {
        let foundUserId = fuzzyUsers[0].id;
        console.log('找到可能匹配的用户:', foundUserId);

        // 如果是完全匹配，优先使用
        const exactMatch = fuzzyUsers.find(u => u.id === customerId || u.username === customerId);
        if (exactMatch) {
          console.log('找到完全匹配的用户:', exactMatch.id);
          // 更新foundUserId为完全匹配的ID
          foundUserId = exactMatch.id;
        }

        // 重新查询完整用户信息
        try {
          const foundUser = await prisma.user.findUnique({
            where: { id: foundUserId },
            include: {
              role: {
                select: {
                  name: true,
                  type: true
                }
              },

              verification: {
                select: {
                  id: true,
                  type: true,
                  status: true
                }
              }
            }
          });

          if (foundUser) {
            console.log('成功找到用户:', foundUser.id);
            // 使用找到的用户替代原来的customer
            return formatAndReturnCustomer(foundUser, customerId, byUsername);
          }
        } catch (err) {
          console.error('查询模糊匹配用户时出错:', err);
        }
      }

      // 返回更详细的调试信息
      return NextResponse.json({
        success: false,
        message: '客户不存在',
        debug: {
          requestedId: customerId,
          allUsers: allUsers.map(u => ({ id: u.id, username: u.username, email: u.email })),
          fuzzyResults: fuzzyUsers.map(u => ({ id: u.id, username: u.username })),
          searchMethod: 'username'
        }
      }, { status: 404 });
    }

    // 使用原始客户数据
    if (customer) {
      return formatAndReturnCustomer(customer, customerId, byUsername);
    } else {
      return NextResponse.json({
        success: false,
        message: '客户不存在',
        debug: {
          requestedId: customerId,
          byUsername
        }
      }, { status: 404 });
    }
  } catch (error) {
    console.error("获取客户详情错误:", error);

    // 添加更详细的错误信息
    let errorMessage = '获取客户详情失败';
    let errorDetails = '';

    if (error instanceof Error) {
      errorMessage += ': ' + error.message;
      errorDetails = error.stack || '';
    } else if (typeof error === 'string') {
      errorMessage += ': ' + error;
    } else {
      errorMessage += ': 未知错误';
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      debug: {
        errorDetails,
        requestedId: params.id
      }
    }, { status: 500 });
  }
}

/**
 * 更新客户信息
 *
 * @route PUT /api/customers/[id]
 * @access 需要管理员权限
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = params.id;
    try {
      customerId = decodeURIComponent(params.id);
    } catch (e) {
      console.error('解码ID时出错:', e);
      // 如果解码失败，使用原始ID
    }

    // 去除URL中可能的特殊字符
    customerId = customerId.trim();

    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "更新客户信息API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 });
    }

    // 获取管理员ID，但不使用

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: customerId },
      include: {
        role: true
      }
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '客户不存在'
      }, { status: 404 });
    }

    // 获取请求体
    const body = await request.json();

    // 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: customerId },
      data: {
        name: body.name,
        email: body.email,
        // 手机号字段在数据库中存在但不在类型定义中，使用类型断言
        ...(body.phone ? { phone: body.phone } : {}),
        // 使用类型断言处理其他字段
        ...(body.industry ? { industry: body.industry } : {}),
        ...(body.province ? { province: body.province } : {}),
        ...(body.city ? { city: body.city } : {}),
        ...(body.district ? { district: body.district } : {}),
        ...(body.address ? { address: body.address } : {})
      },
      include: {
        role: {
          select: {
            name: true,
            type: true
          }
        }
      }
    });

    // 格式化并返回客户数据
    // 移除敏感信息
    delete (updatedUser as any).password;

    // 转换为前端需要的格式
    const formattedCustomer = {
      id: updatedUser.id,
      name: updatedUser.name || updatedUser.username,
      loginName: updatedUser.username,
      email: updatedUser.email,
      phone: (updatedUser as any).phone || '',
      accountType: (updatedUser as any).role?.type || 'personal',
      status: (updatedUser as any).status || 'active',
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
      industry: (updatedUser as any).industry || '',
      province: (updatedUser as any).province || '',
      city: (updatedUser as any).city || '',
      district: (updatedUser as any).district || '',
      address: (updatedUser as any).address || '',
      description: (updatedUser as any).description || '',
      balance: (updatedUser as any).balance || 0,
      creditLimit: (updatedUser as any).creditLimit || 0
    };

    return NextResponse.json({
      success: true,
      message: '更新客户信息成功',
      data: formattedCustomer
    });
  } catch (error) {
    console.error("更新客户信息错误:", error);
    return NextResponse.json({
      success: false,
      message: '更新客户信息失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
