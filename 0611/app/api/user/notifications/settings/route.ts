import { NextRequest } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "../../../../../lib/unified-auth-service"

export async function GET(request: NextRequest) {
  try {
    // 使用统一认证服务获取用户
    const user = await UnifiedAuthService.getCurrentUser(request, "通知设置API")
    if (!user) {
      return Response.json({ success: false, message: "未认证" }, { status: 401 })
    }

    console.log("获取用户通知设置:", user.id)

    const settings = await prisma.userNotificationSettings.findUnique({
      where: { userId: user.id },
    })

    if (!settings) {
      console.log("用户没有通知设置，创建默认设置")
      // 如果没有设置记录，创建默认设置
      const defaultSettings = await prisma.userNotificationSettings.create({
        data: {
          userId: user.id,
          emailEnabled: true,
          smsEnabled: false, // 默认关闭短信通知
          appEnabled: true,
          types: ["SYSTEM", "SECURITY", "TASK"]
        }
      })
      console.log("创建的默认设置:", defaultSettings)
      return Response.json({ success: true, data: defaultSettings })
    }

    console.log("获取到的用户通知设置:", settings)
    return Response.json({ success: true, data: settings })
  } catch (error) {
    console.error("获取通知设置失败:", error)
    return Response.json({ success: false, message: "获取通知设置失败" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // 使用统一认证服务获取用户
    const user = await UnifiedAuthService.getCurrentUser(request, "更新通知设置API")
    if (!user) {
      return Response.json({ success: false, message: "未认证" }, { status: 401 })
    }

    const body = await request.json()
    const { emailEnabled, smsEnabled, appEnabled, types } = body

    console.log("更新通知设置:", { userId: user.id, emailEnabled, smsEnabled, appEnabled, types })

    // 先检查是否存在设置记录
    const existingSettings = await prisma.userNotificationSettings.findUnique({
      where: { userId: user.id },
    })

    let settings;
    if (existingSettings) {
      // 更新现有设置
      settings = await prisma.userNotificationSettings.update({
        where: { userId: user.id },
        data: {
          emailEnabled: emailEnabled !== undefined ? emailEnabled : existingSettings.emailEnabled,
          smsEnabled: smsEnabled !== undefined ? smsEnabled : existingSettings.smsEnabled,
          appEnabled: appEnabled !== undefined ? appEnabled : existingSettings.appEnabled,
          types: types || existingSettings.types
        }
      })
    } else {
      // 创建新设置
      settings = await prisma.userNotificationSettings.create({
        data: {
          userId: user.id,
          emailEnabled: emailEnabled !== undefined ? emailEnabled : true,
          smsEnabled: smsEnabled !== undefined ? smsEnabled : false,
          appEnabled: appEnabled !== undefined ? appEnabled : true,
          types: types || ["SYSTEM", "SECURITY", "TASK"]
        }
      })
    }

    console.log("通知设置已更新:", settings)
    return Response.json({ success: true, data: settings })
  } catch (error) {
    console.error("更新通知设置失败:", error)
    return Response.json({ success: false, message: "更新通知设置失败" }, { status: 500 })
  }
}