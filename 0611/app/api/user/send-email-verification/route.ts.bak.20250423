import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { emailService } from "@/lib/services/email"
import { generateVerificationCode, storeVerificationCode } from "@/lib/services/verification"
// 直接在这里实现 verifyToken 函数
import * as jose from 'jose';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

async function verifyToken(token: string) {
  try {
    console.log('开始验证令牌:', token.substring(0, 20) + '...');
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    console.log('令牌验证成功, 载荷:', payload);
    return payload;
  } catch (error) {
    console.error('令牌验证失败:', error);
    throw error;
  }
}
import { AuthService } from "@/lib/auth-service"
import { cookies } from "next/headers"

const CODE_EXPIRY = 5 * 60 // 5分钟

/**
 * 发送验证码响应接口
 */
interface SendVerificationResponse {
  code: number
  success: boolean
  message: string
}

/**
 * 验证码请求验证 schema
 */
const sendVerificationSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
})

/**
 * 处理发送验证码请求
 * POST /api/user/send-email-verification
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 发送验证码响应
 */
export async function POST(request: NextRequest): Promise<NextResponse<SendVerificationResponse>> {
  try {
    console.log('收到发送邮箱验证码请求')

    // 验证用户是否已登录
    console.log('检查请求头:', request.headers)

    // 尝试从请求头中获取token
    let token = request.headers.get('Authorization')?.replace('Bearer ', '');

    // 如果请求头中没有token，尝试从Cookie中获取
    if (!token) {
      const cookieStore = cookies()
      token = cookieStore.get('auth_token')?.value || cookieStore.get('token')?.value
    }

    console.log('获取到的token:', token ? token.substring(0, 20) + '...' : '不存在')

    if (!token) {
      console.log('未找到认证令牌')
      return NextResponse.json({
        code: 401,
        success: false,
        message: '未登录或会话已过期'
      }, { status: 401 })
    }

    // 验证令牌
    let decoded;
    try {
      decoded = await verifyToken(token);
    } catch (error) {
      console.log('令牌验证失败:', error)
      return NextResponse.json({
        code: 401,
        success: false,
        message: '无效的认证令牌'
      }, { status: 401 })
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId as string }
    })

    if (!user) {
      console.log('用户不存在:', decoded.userId)
      return NextResponse.json({
        code: 401,
        success: false,
        message: '用户不存在'
      }, { status: 401 })
    }

    console.log('当前用户:', user.id, user.username)

    const body = await request.json()
    console.log('请求体:', body)

    // 验证请求参数
    const result = sendVerificationSchema.safeParse(body)
    if (!result.success) {
      console.log('参数验证失败:', result.error.errors)
      return NextResponse.json({
        code: 400,
        success: false,
        message: result.error.errors[0].message
      }, { status: 400 })
    }

    const { email } = result.data
    console.log('参数验证通过:', { email })

    // 检查邮箱是否已被其他用户注册
    console.log('检查邮箱是否已被其他用户注册...')
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        id: { not: user.id }
      }
    })

    if (existingUser) {
      console.log('邮箱已被其他用户注册:', existingUser.id)
      return NextResponse.json({
        code: 400,
        success: false,
        message: '该邮箱已被其他用户注册'
      }, { status: 400 })
    }

    // 生成验证码
    console.log('生成验证码...')
    const code = generateVerificationCode()
    console.log('验证码生成成功:', code)

    // 保存验证码到 Redis
    console.log('保存验证码到 Redis...')
    try {
      await storeVerificationCode(email, code, "email_change" as "register" | "reset" | "email_change")
      console.log('验证码保存成功')
    } catch (error) {
      console.error('验证码保存失败:', error)
      return NextResponse.json({
        code: 500,
        success: false,
        message: '验证码保存失败，请稍后重试'
      }, { status: 500 })
    }

    // 发送验证码邮件
    console.log('发送验证码邮件...')
    try {
      await emailService.sendEmailChangeVerification(email, code)
      console.log('验证码邮件发送成功')
    } catch (error) {
      console.error('验证码邮件发送失败:', error)
      return NextResponse.json({
        code: 500,
        success: false,
        message: '验证码邮件发送失败，请稍后重试'
      }, { status: 500 })
    }

    return NextResponse.json({
      code: 200,
      success: true,
      message: '验证码已发送'
    })
  } catch (error) {
    console.error('发送验证码失败:', error)
    return NextResponse.json({
      code: 500,
      success: false,
      message: '发送验证码失败，请稍后重试'
    }, { status: 500 })
  }
}
