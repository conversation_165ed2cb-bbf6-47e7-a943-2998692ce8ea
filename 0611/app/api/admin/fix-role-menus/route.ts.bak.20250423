/**
 * 修复角色和菜单的关联关系
 */

import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthService } from "@/lib/auth-service"

export async function POST() {
  try {
    const user = await AuthService.getCurrentUser()
    if (!user || user.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "需要管理员权限" },
        { status: 403 }
      )
    }

    // 获取当前的角色-菜单关联
    const roleMenus = await prisma.$queryRaw`
      SELECT "A", "B" FROM "_RoleMenus"
    `

    console.log("当前的角色-菜单关联:", roleMenus)

    // 检查是否需要修复
    let needsFix = false
    let correctRelations = 0
    let incorrectRelations = 0

    for (const rm of roleMenus as any[]) {
      // 检查A是否为菜单ID
      const menuA = await prisma.menu.findUnique({
        where: { id: rm.A }
      })

      // 检查B是否为角色ID
      const roleB = await prisma.role.findUnique({
        where: { id: rm.B }
      })

      if (menuA && roleB) {
        // A是菜单ID，B是角色ID，这是错误的关系
        needsFix = true
        incorrectRelations++
      } else {
        // 检查反向关系
        const roleA = await prisma.role.findUnique({
          where: { id: rm.A }
        })

        const menuB = await prisma.menu.findUnique({
          where: { id: rm.B }
        })

        if (roleA && menuB) {
          // A是角色ID，B是菜单ID，这是正确的关系
          correctRelations++
        }
      }
    }

    console.log(`检查结果: 正确关系 ${correctRelations} 个, 错误关系 ${incorrectRelations} 个`)

    if (!needsFix) {
      return NextResponse.json({
        success: true,
        message: "角色和菜单的关联关系正常，无需修复",
        stats: { correctRelations, incorrectRelations }
      })
    }

    // 创建临时表存储正确的关联关系
    await prisma.$executeRaw`
      CREATE TEMP TABLE temp_role_menus (
        "roleId" TEXT,
        "menuId" TEXT
      )
    `

    // 将当前关联关系反转后插入临时表
    for (const rm of roleMenus as any[]) {
      await prisma.$executeRaw`
        INSERT INTO temp_role_menus ("roleId", "menuId")
        VALUES (${rm.B}, ${rm.A})
      `
    }

    // 删除当前的关联关系
    await prisma.$executeRaw`
      DELETE FROM "_RoleMenus"
    `

    // 插入正确的关联关系
    const tempRoleMenus = await prisma.$queryRaw`
      SELECT * FROM temp_role_menus
    `

    for (const rm of tempRoleMenus as any[]) {
      await prisma.$executeRaw`
        INSERT INTO "_RoleMenus" ("A", "B")
        VALUES (${rm.roleId}, ${rm.menuId})
      `
    }

    // 删除临时表
    await prisma.$executeRaw`
      DROP TABLE temp_role_menus
    `

    return NextResponse.json({
      success: true,
      message: "角色和菜单的关联关系已修复",
      count: (tempRoleMenus as any[]).length,
      stats: { correctRelations, incorrectRelations, fixed: (tempRoleMenus as any[]).length }
    })
  } catch (error) {
    console.error("修复角色和菜单关联关系失败:", error)
    return NextResponse.json(
      { success: false, error: "修复角色和菜单关联关系失败" },
      { status: 500 }
    )
  }
}
