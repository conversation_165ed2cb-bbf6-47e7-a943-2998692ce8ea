import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取管理员用户列表
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[管理员用户API:${requestId}] 获取管理员用户列表`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);
    if (response) {
      logger.error(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以查看用户列表",
        requestId
      }, { status: 403 })
    }

    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    // 获取所有管理员用户
    const users = await prisma.user.findMany({
      where: {
        roleCode: 'ADMIN'
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        roleCode: true,
        status: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: users,
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 获取管理员用户列表失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取管理员用户列表失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 创建管理员用户
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[管理员用户API:${requestId}] 创建管理员用户`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);    if (response) {
      console.log(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以创建用户",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    console.log(`[管理员用户API:${requestId}] 请求体:`, body)

    // 验证必要字段
    if (!body.username || !body.email || !body.password || !body.roleCode) {
      return NextResponse.json({
        success: false,
        message: "缺少必要字段: username, email, password, roleCode",
        requestId
      }, { status: 400 })
    }

    // 检查用户名是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: body.username },
          { email: body.email }
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: "用户名或邮箱已存在",
        requestId
      }, { status: 400 })
    }

    // 检查角色是否存在
    const role = await prisma.role.findFirst({
      where: { code: body.roleCode }
    })

    if (!role) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 400 })
    }

    // 加密密码
    const hashedPassword = await hash(body.password, 10)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username: body.username,
        email: body.email,
        password: hashedPassword,
        name: body.name,
        roleCode: body.roleCode,
        status: body.status || 'active'
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        roleCode: true,
        status: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return NextResponse.json({
      success: true,
      data: user,
      message: '用户创建成功',
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 创建用户失败:`, error)
    return NextResponse.json({
      success: false,
      message: "创建用户失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
