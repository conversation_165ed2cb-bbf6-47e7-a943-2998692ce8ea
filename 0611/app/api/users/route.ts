import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { checkPermission } from "@/lib/casbin/enforcer";
import logger from '@/lib/utils/logger';

/**
 * 获取用户列表
 *
 * @route GET /api/users
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取用户列表API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    const userId = user.id;

    // 检查是否为通知选择器请求
    const reqUrl = new URL(request.url);
    const isForNotification = reqUrl.searchParams.get('for') === 'notification';

    // 如果是通知选择器请求，则允许访问基本用户列表
    // 否则检查完整权限
    if (!isForNotification) {
      const hasPermission = await checkPermission(request, 'users:read');
      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          message: '无权限查看用户列表'
        }, { status: 403 });
      }
    }

    // 获取查询参数
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const search = url.searchParams.get('search') || '';

    // 计算分页
    const skip = (page - 1) * pageSize;

    // 构建查询条件
    const where: any = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' as any } },
        { email: { contains: search, mode: 'insensitive' as any } },
        { nickname: { contains: search, mode: 'insensitive' as any } }
      ]
    } : {};

    // 查询用户总数
    const total = await prisma.user.count({ where });

    // 查询用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        image: true,
        roleCode: true,
        status: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        role: {
          select: {
            name: true,
            code: true
          }
        }
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' }
    });

    // 处理日期格式
    const formattedUsers = users.map(user => ({
      ...user,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt?.toISOString(),
      lastLoginAt: user.lastLoginAt?.toISOString()
    }));

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    return NextResponse.json({
      success: true,
      message: '获取用户列表成功',
      data: {
        users: formattedUsers,
        pagination: {
          page,
          pageSize,
          total,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error("获取用户列表错误:", error);
    return NextResponse.json({
      success: false,
      message: '获取用户列表失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}
