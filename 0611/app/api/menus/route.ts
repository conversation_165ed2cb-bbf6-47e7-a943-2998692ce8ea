import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取菜单列表
 * 支持树形结构和扁平结构
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[菜单API:${requestId}] 获取菜单列表`)

  // 设置响应头，禁止缓存
  const headers = {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "菜单API");
    if (response) {
      logger.error(`[菜单API:${requestId}] 未授权访问`)
      return response;
    }

    console.log(`[菜单API:${requestId}] 管理员用户ID: ${admin.id}`)

    // 获取所有菜单
    const menus = await prisma.menu.findMany({
      orderBy: {
        order: 'asc'
      }
    })

    console.log(`[菜单API:${requestId}] 找到 ${menus.length} 个菜单`)

    // 构建菜单树
    const buildMenuTree = (items: any[], parentId: string | null = null) => {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          ...item,
          children: buildMenuTree(items, item.id)
        }))
    }

    // 构建树形结构
    const menuTree = buildMenuTree(menus)

    return NextResponse.json({
      success: true,
      data: menuTree,
      requestId,
      timestamp: Date.now() // 添加时间戳，确保每次响应都不同
    }, { headers })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 获取菜单列表失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取菜单列表失败",
      requestId,
      timestamp: Date.now()
    }, { status: 500, headers })
  }
}

/**
 * 创建新菜单
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 创建新菜单`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "菜单API");    if (response) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return response;
    }

    // 获取请求体
    const body = await request.json()

    // 生成唯一的菜单代码，如果没有提供
    if (!body.code) {
      body.code = `menu-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    }

    // 创建菜单
    const menu = await prisma.menu.create({
      data: {
        name: body.name,
        path: body.path,
        icon: body.icon,
        code: body.code,
        visible: body.visible !== undefined ? body.visible : true,
        order: body.order || 0,
        parentId: body.parentId || null
      }
    })

    // 将菜单添加到管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    if (adminRole) {
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          menus: {
            connect: { id: menu.id }
          }
        }
      })

      // 使用jCasbin添加权限
      await CasbinService.addPermissionForRole('ADMIN', `menu:${menu.code}`, 'view')
    }

    return NextResponse.json({
      success: true,
      data: menu,
      message: '菜单创建成功',
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 创建菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "创建菜单失败",
      requestId
    }, { status: 500 })
  }
}
