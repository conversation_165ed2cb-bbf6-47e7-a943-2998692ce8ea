/**
 * 系统日志导出API
 * 提供系统日志的导出功能
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { SystemLogService } from "@/lib/system-log-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { checkPermission } from "@/lib/casbin/enforcer"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

// 导出系统日志
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "系统日志导出API");
    if (response) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(request, "logs:view")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限访问" }, { status: 403 })
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const keyword = searchParams.get("keyword") || ""
    const startDate = searchParams.get("startDate") ? new Date(searchParams.get("startDate") as string) : undefined
    const endDate = searchParams.get("endDate") ? new Date(searchParams.get("endDate") as string) : undefined
    const module = searchParams.get("module") || ""
    const action = searchParams.get("action") || ""
    const format = searchParams.get("format") || "json" // 新增：导出格式，默认为json

    // 构建查询条件
    const where: any = {}

    if (keyword) {
      where.OR = [
        { userId: { contains: keyword, mode: "insensitive" } },
        { action: { contains: keyword, mode: "insensitive" } },
        { module: { contains: keyword, mode: "insensitive" } },
        { resourceId: { contains: keyword, mode: "insensitive" } },
        { resourceType: { contains: keyword, mode: "insensitive" } },
        { ipAddress: { contains: keyword, mode: "insensitive" } },
      ]
    }

    if (startDate || endDate) {
      where.createdAt = {
        ...(startDate ? { gte: startDate } : {}),
        ...(endDate ? { lte: endDate } : {})
      }
    }

    if (module) {
      where.module = module
    }

    if (action) {
      where.action = action
    }

    // 查询日志记录（最多导出10000条）
    const logs = await prisma.system_log.findMany({
      where,
      orderBy: {
        createdAt: "desc"
      },
      take: 10000,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true,
            roleCode: true
          }
        }
      }
    })

    // 记录导出日志的操作
    await SystemLogService.log({
      userId: admin.id,
      action: "export",
      module: "logs",
      details: {
        query: {
          keyword,
          startDate,
          endDate,
          module,
          action
        },
        count: logs.length
      }
    })

    // 转换日志数据为JSON格式
    const data = logs.map(log => ({
      time: format(new Date(log.createdAt), "yyyy-MM-dd HH:mm:ss", { locale: zhCN }),
      user: log.user ? (log.user.name || log.user.username) : "未知用户",
      email: log.user?.email || "-",
      module: getModuleLabel(log.module),
      action: getActionLabel(log.action),
      resourceId: log.resourceId || "-",
      resourceType: log.resourceType || "-",
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      details: log.details ? JSON.stringify(log.details) : "-"
    }))

    // 根据格式返回不同类型的数据
    const headers = new Headers()
    const timestamp = format(new Date(), "yyyyMMdd_HHmmss")

    if (format === "csv") {
      // CSV格式
      const csvHeader = "\uFEFF时间,用户,邮箱,模块,操作,资源ID,资源类型,IP地址,用户代理,详情\n";
      const csvRows = data.map(item => {
        return `"${item.time}","${item.user}","${item.email}","${item.module}","${item.action}","${item.resourceId}","${item.resourceType}","${item.ipAddress}","${item.userAgent}","${item.details}"`;
      }).join("\n");

      headers.set("Content-Type", "text/csv;charset=utf-8");
      headers.set("Content-Disposition", `attachment; filename="系统日志_${timestamp}.csv"`);

      return new NextResponse(csvHeader + csvRows, {
        status: 200,
        headers
      });
    } else if (format === "excel") {
      // Excel格式 (实际上是CSV格式，但我们设置扩展名为xls以便于直接用Excel打开)
      const csvHeader = "\uFEFF时间,用户,邮箱,模块,操作,资源ID,资源类型,IP地址,用户代理,详情\n";
      const csvRows = data.map(item => {
        return `"${item.time}","${item.user}","${item.email}","${item.module}","${item.action}","${item.resourceId}","${item.resourceType}","${item.ipAddress}","${item.userAgent}","${item.details}"`;
      }).join("\n");

      headers.set("Content-Type", "application/vnd.ms-excel;charset=utf-8");
      headers.set("Content-Disposition", `attachment; filename="系统日志_${timestamp}.xls"`);

      return new NextResponse(csvHeader + csvRows, {
        status: 200,
        headers
      });
    } else {
      // 默认JSON格式
      headers.set("Content-Type", "application/json");
      headers.set("Content-Disposition", `attachment; filename="系统日志_${timestamp}.json"`);

      return NextResponse.json(data, {
        status: 200,
        headers
      });
    }
  } catch (error) {
    console.error("导出系统日志失败:", error)
    return NextResponse.json({ success: false, message: "导出系统日志失败" }, { status: 500 })
  }
}

// 获取模块标签
function getModuleLabel(moduleCode: string) {
  const moduleMap: Record<string, string> = {
    "user": "用户管理",
    "role": "角色管理",
    "customer": "客户管理",
    "rate": "费率管理",
    "notification": "通知管理",
    "settings": "系统设置",
    "auth": "认证授权",
    "logs": "系统日志"
  }

  return moduleMap[moduleCode] || moduleCode
}

// 获取操作标签
function getActionLabel(actionCode: string) {
  const actionMap: Record<string, string> = {
    "create": "创建",
    "update": "更新",
    "delete": "删除",
    "view": "查看",
    "login": "登录",
    "logout": "登出",
    "enable": "启用",
    "disable": "禁用",
    "recharge": "充值",
    "deduct": "扣费",
    "export": "导出"
  }

  return actionMap[actionCode] || actionCode
}




