import { NextResponse } from 'next/server';
import { prisma, Prisma } from '@/lib/prisma';
import { getToken } from '@/app/lib/token';
import { checkPermission } from '@/app/lib/auth';

/**
 * 批量标记通知为已读API
 *
 * 此API用于批量标记通知为已读，支持以下功能：
 * 1. 标记指定ID列表的通知为已读
 * 2. 标记所有通知为已读
 * 3. 标记指定类型的通知为已读
 * 4. 更新通知的阅读统计数据
 */

export async function POST(request: Request) {
  try {
    const token = await getToken();
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();
    const { notificationIds, all = false, type } = body;

    // 当前时间
    const now = new Date();

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: token.id },
      select: { roleCode: true }
    });

    const isAdmin = userRole?.roleCode === 'ADMIN';
    console.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    // 构建查询条件
    let whereCondition: any = {};

    if (all) {
      // 无论是管理员还是普通用户，都只能标记全局通知和发送给自己的通知为已读
      // 使用原始 SQL 查询来处理 JSONB 类型的 recipients 字段
      const notifications = await prisma.$queryRaw`
        SELECT n.id
        FROM notification n
        LEFT JOIN "user_notification" un
          ON n.id = un."notificationId"
          AND un."userId" = ${token.id}
          AND un.read = true
        WHERE
          n.status = 'published' AND
          (n."sendToAll" = true OR (n."sendToAll" = false AND n.recipients::text ILIKE ${'%' + token.id + '%'})) AND
          un.id IS NULL
      `;

      // 提取通知ID
      const notificationIds = (notifications as { id: string }[]).map(n => n.id);

      if (notificationIds.length === 0) {
        return NextResponse.json({
          success: false,
          message: '没有找到需要标记的未读通知'
        }, { status: 404 });
      }

      // 使用提取的ID构建查询条件
      whereCondition = {
        id: { in: notificationIds },
        status: 'published'
      };
      console.log(`只标记全局通知和发送给自己的通知为已读，找到 ${notificationIds.length} 条通知`);
    } else if (Array.isArray(notificationIds) && notificationIds.length > 0) {
      // 标记指定ID列表的通知为已读
      // 无论是管理员还是普通用户，都只能标记全局通知和发送给自己的通知为已读
      // 使用原始 SQL 查询来处理 JSONB 类型的 recipients 字段
      const filteredNotifications = await prisma.$queryRaw`
        SELECT n.id
        FROM notification n
        WHERE
          n.id IN (${Prisma.join(notificationIds)}) AND
          n.status = 'published' AND
          (n."sendToAll" = true OR (n."sendToAll" = false AND n.recipients::text ILIKE ${'%' + token.id + '%'}))
      `;

      // 提取通知ID
      const filteredIds = (filteredNotifications as { id: string }[]).map(n => n.id);

      if (filteredIds.length === 0) {
        return NextResponse.json({
          success: false,
          message: '没有找到需要标记的未读通知'
        }, { status: 404 });
      }

      // 使用提取的ID构建查询条件
      whereCondition = {
        id: { in: filteredIds },
        status: 'published'
      };
      console.log(`标记指定ID列表的通知为已读，找到 ${filteredIds.length} 条通知`);
    } else if (type) {
      // 标记指定类型的通知为已读
      // 无论是管理员还是普通用户，都只能标记全局通知和发送给自己的通知为已读
      // 使用原始 SQL 查询来处理 JSONB 类型的 recipients 字段
      const typeNotifications = await prisma.$queryRaw`
        SELECT n.id
        FROM notification n
        LEFT JOIN "notificationType" nt ON n."typeId" = nt.id
        LEFT JOIN "user_notification" un
          ON n.id = un."notificationId"
          AND un."userId" = ${token.id}
          AND un.read = true
        WHERE
          nt.code = ${type} AND
          n.status = 'published' AND
          (n."sendToAll" = true OR (n."sendToAll" = false AND n.recipients::text ILIKE ${'%' + token.id + '%'})) AND
          un.id IS NULL
      `;

      // 提取通知ID
      const typeIds = (typeNotifications as { id: string }[]).map(n => n.id);

      if (typeIds.length === 0) {
        return NextResponse.json({
          success: false,
          message: '没有找到需要标记的未读通知'
        }, { status: 404 });
      }

      // 使用提取的ID构建查询条件
      whereCondition = {
        id: { in: typeIds },
        status: 'published'
      };
      console.log(`标记指定类型的通知为已读，找到 ${typeIds.length} 条通知`);
    } else {
      return NextResponse.json({
        success: false,
        message: '请提供有效的通知ID列表、类型或设置all=true'
      }, { status: 400 });
    }

    // 查询符合条件的通知
    const notifications = await prisma.notification.findMany({
      where: whereCondition,
      select: {
        id: true,
        sendToAll: true,
        recipients: true,
        readCount: true,
        totalRecipients: true,
        allowBatchMarkRead: true
      }
    });

    console.log(`查询到 ${notifications.length} 条符合条件的通知`);

    // 过滤出允许批量标记已读的通知
    // 对于普通用户，我们不考虑allowBatchMarkRead属性，因为他们只能标记自己的通知
    const allowedNotifications = isAdmin
      ? notifications.filter(n => n.allowBatchMarkRead)
      : notifications;

    console.log(`允许批量标记的通知数量: ${allowedNotifications.length}`);

    if (allowedNotifications.length === 0) {
      return NextResponse.json({
        success: false,
        message: '没有找到可批量标记的通知'
      }, { status: 404 });
    }

    // 使用事务确保数据一致性
    const result = await prisma.$transaction(async (tx) => {
      let markedCount = 0;

      for (const notification of allowedNotifications) {
        // 检查用户是否有权限接收此通知
        let canReceive = notification.sendToAll;

        if (!canReceive && notification.recipients) {
          try {
            const recipients = typeof notification.recipients === 'string'
              ? JSON.parse(notification.recipients)
              : notification.recipients;

            canReceive = Array.isArray(recipients) && recipients.includes(token.id);
          } catch (e) {
            console.error('解析recipients错误:', e);
          }
        }

        if (!canReceive) {
          continue; // 跳过用户无权接收的通知
        }

        // 查找现有的用户通知记录
        const existingUserNotification = await tx.userNotification.findFirst({
          where: {
            userId: token.id,
            notificationId: notification.id
          }
        });

        let isNewRead = false;

        if (existingUserNotification) {
          // 如果记录存在且未读，更新它
          if (!existingUserNotification.read) {
            await tx.userNotification.update({
              where: {
                id: existingUserNotification.id
              },
              data: {
                read: true,
                readAt: now,
                viewCount: { increment: 1 },
                lastViewedAt: now
              }
            });
            isNewRead = true;
            markedCount++;
          }
        } else {
          // 如果记录不存在，创建新记录
          await tx.userNotification.create({
            data: {
              userId: token.id,
              notificationId: notification.id,
              read: true,
              readAt: now,
              viewCount: 1,
              lastViewedAt: now
            }
          });
          isNewRead = true;
          markedCount++;
        }

        // 如果是新的已读记录，更新通知的阅读统计
        if (isNewRead) {
          // 计算总接收者数
          let totalRecipients = notification.totalRecipients;

          // 如果总接收者数为0，重新计算
          if (totalRecipients === 0) {
            if (notification.sendToAll) {
              // 如果是全局通知，总接收者数为所有用户数
              totalRecipients = await tx.user.count();
            } else if (notification.recipients) {
              // 如果是指定用户通知，总接收者数为指定用户数
              try {
                const recipients = typeof notification.recipients === 'string'
                  ? JSON.parse(notification.recipients)
                  : notification.recipients;
                totalRecipients = Array.isArray(recipients) ? recipients.length : 1;
              } catch (e) {
                console.error('解析recipients错误:', e);
                totalRecipients = 1; // 默认值
              }
            }
          }

          // 更新通知的阅读统计
          const newReadCount = (notification.readCount || 0) + 1;
          const readRate = totalRecipients > 0 ? newReadCount / totalRecipients : 0;

          await tx.notification.update({
            where: { id: notification.id },
            data: {
              readCount: newReadCount,
              totalRecipients,
              readRate,
              lastReadAt: now
            }
          });
        }
      }

      return { markedCount };
    });

    return NextResponse.json({
      success: true,
      message: `成功标记${result.markedCount}条通知为已读`,
      data: {
        markedCount: result.markedCount,
        totalCount: allowedNotifications.length
      }
    }, { status: 200 });
  } catch (error) {
    console.error('批量标记通知为已读错误:', error);
    return NextResponse.json({
      success: false,
      message: '批量标记已读失败'
    }, { status: 500 });
  }
}
