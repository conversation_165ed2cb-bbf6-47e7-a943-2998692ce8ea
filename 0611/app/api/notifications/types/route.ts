import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import logger from '@/lib/utils/logger';

/**
 * 获取通知类型列表
 *
 * @route GET /api/notifications/types
 * @access 需要 notifications:view 权限
 */
export async function GET(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "获取通知类型列表API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    const userId = user.id;
    const roleCode = user.roleCode || 'user';

    // 检查用户是否有权限查看通知类型
    const hasViewPermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:view"
    );

    if (!hasViewPermission) {
      return NextResponse.json({
        success: false,
        message: '无权查看通知类型',
      }, { status: 403 });
    }

    // 从数据库获取通知类型
    const types = await prisma.notificationType.findMany({
      orderBy: {
        id: 'asc'
      },
      include: {
        children: true,
        parent: true
      }
    });

    // 构建类型树结构
    const rootTypes = types.filter(type => !type.parentId);
    const typeTree = rootTypes.map(type => ({
      ...type,
      children: types.filter(child => child.parentId === type.id)
    }));

    return NextResponse.json({
      success: true,
      message: '获取通知类型列表成功',
      data: typeTree,
    });
  } catch (error) {
    logger.error('获取通知类型列表失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取通知类型列表失败',
    }, { status: 500 });
  }
}

/**
 * 创建新通知类型
 *
 * @route POST /api/notifications/types
 * @access 需要 notifications:create 权限
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "创建通知类型API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    const userId = user.id;
    const roleCode = user.roleCode || 'user';

    // 检查用户是否有权限创建通知类型
    const hasCreatePermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:create"
    );

    if (!hasCreatePermission) {
      return NextResponse.json({
        success: false,
        message: '无权创建通知类型',
      }, { status: 403 });
    }

    // 获取请求体
    const body = await req.json();
    const { name, code, description, parentId } = body;

    // 验证必填字段
    if (!name || !code) {
      return NextResponse.json({
        success: false,
        message: '名称和代码不能为空',
      }, { status: 400 });
    }

    // 检查代码是否已存在
    const existingType = await prisma.notificationType.findUnique({
      where: { code }
    });

    if (existingType) {
      return NextResponse.json({
        success: false,
        message: '通知类型代码已存在',
      }, { status: 400 });
    }

    // 创建通知类型
    const newType = await prisma.notificationType.create({
      data: {
        name,
        code,
        description,
        parentId: parentId ? parseInt(parentId) : null
      }
    });

    return NextResponse.json({
      success: true,
      message: '创建通知类型成功',
      data: newType
    });
  } catch (error) {
    logger.error('创建通知类型失败:', error);
    return NextResponse.json({
      success: false,
      message: '创建通知类型失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}