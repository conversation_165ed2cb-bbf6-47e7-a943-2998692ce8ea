import { NextResponse } from "next/server"
import { writeFile } from "fs/promises"
import { join } from "path"
import { v4 as uuidv4 } from "uuid"

// 允许的视频类型
const ALLOWED_TYPES = [".mp4", ".avi", ".wmv", ".flv", ".mov"]
// 视频大小限制（50MB）
const MAX_SIZE = 50 * 1024 * 1024

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json(
        { error: "No file uploaded" },
        { status: 400 }
      )
    }

    // 获取文件扩展名
    const ext = `.${file.name.split(".").pop()?.toLowerCase()}`

    // 检查文件类型
    if (!ALLOWED_TYPES.includes(ext)) {
      return NextResponse.json(
        { error: "Unsupported video type" },
        { status: 400 }
      )
    }

    // 检查文件大小
    if (file.size > MAX_SIZE) {
      return NextResponse.json(
        { error: "Video size exceeds 50MB limit" },
        { status: 400 }
      )
    }

    // 生成唯一文件名
    const fileName = `${uuidv4()}${ext}`
    const uploadDir = join(process.cwd(), "public", "uploads", "videos")
    const filePath = join(uploadDir, fileName)

    // 将文件写入磁盘
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 返回视频URL
    const videoUrl = `/uploads/videos/${fileName}`
    return NextResponse.json({ url: videoUrl })
  } catch (error) {
    console.error("Video upload error:", error)
    return NextResponse.json(
      { error: "Failed to upload video" },
      { status: 500 }
    )
  }
} 