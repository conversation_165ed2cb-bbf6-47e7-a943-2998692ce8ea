import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"
import path from "path"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { processLoginBackground } from "@/lib/utils/imageProcessor"

// 允许的图片类型
const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp"
]

/**
 * 图片上传处理API
 * 接收图片文件，保存到public/uploads/images目录，并返回可访问URL
 *
 * @route POST /api/upload/image
 * @access private - 需要认证
 */
export async function POST(req: NextRequest) {
  try {
    // 尝试验证用户身份，但不强制要求
    // 这样系统设置页面也可以上传图片
    const { user } = await AuthMiddleware.tryAuth(req, "图片上传API");
    let isAuthenticated = !!user;

    if (isAuthenticated) {
      logger.log("用户已认证:", user.id);
    }

    // 注意：在生产环境中，应该根据实际需求决定是否允许未认证用户上传图片
    // 这里为了解决系统设置页面的问题，我们允许未认证的请求

    // 获取FormData
    const formData = await req.formData()
    const file = formData.get("image") as File

    if (!file) {
      return NextResponse.json(
        { success: false, message: "未找到上传文件", code: 400, data: null },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: `不支持的文件类型。允许的类型: ${ALLOWED_IMAGE_TYPES.join(", ")}`,
          code: 400,
          data: null
        },
        { status: 400 }
      )
    }

    // 从文件名中获取文件扩展名
    const fileExtension = file.name.split(".").pop() || "jpg"

    // 生成时间戳文件名
    const timestamp = new Date().getTime()
    const fileName = `${timestamp}.${fileExtension}`

    // 确保目录存在
    const uploadDir = path.join(process.cwd(), "public", "uploads", "images")
    try {
      if (!existsSync(uploadDir)) {
        logger.error(`创建上传目录: ${uploadDir}`)
        await mkdir(uploadDir, { recursive: true })
      }

      // 验证目录是否成功创建
      if (!existsSync(uploadDir)) {
        throw new Error(`无法创建上传目录: ${uploadDir}`)
      }
    } catch (dirError) {
      console.error("创建目录失败:", dirError)
      throw new Error(`创建上传目录失败: ${dirError.message}`)
    }

    // 保存文件路径
    const filePath = path.join(uploadDir, fileName)

    // 将文件保存到服务器
    try {
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)

      // 检查是否是登录页背景图片上传
      const isLoginBackground = formData.get("type") === "loginBackground";

      // 处理图片
      let processedBuffer: Buffer;
      if (isLoginBackground) {
        // 使用登录页背景图片处理函数
        logger.log("处理登录页背景图片...");        processedBuffer = await processLoginBackground(buffer, fileExtension);
        logger.log("登录页背景图片处理完成");
      } else {
        // 使用原始图片
        processedBuffer = buffer;
      }

      // 保存处理后的图片
      await writeFile(filePath, processedBuffer);
      logger.log(`图片已保存到: ${filePath}`);
    } catch (writeError) {
      logger.error("写入文件失败:", writeError);
      throw new Error(`写入文件失败: ${writeError.message}`);
    }

    // 返回可访问的URL
    const fileUrl = `/uploads/images/${fileName}`

    return NextResponse.json({
      success: true,
      message: "图片上传成功",
      code: 200,
      data: {
        url: fileUrl
      }
    })
  } catch (error) {
    logger.error("图片上传失败:", error)

    // 提供更详细的错误信息
    let errorMessage = "服务器错误，图片上传失败";
    if (error instanceof Error) {
      errorMessage = `图片上传失败: ${error.message}`;
      logger.error("错误详情:", error.stack);
    }

    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
        code: 500,
        data: null,
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}