import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import * as jose from 'jose'
import { cookies } from "next/headers"
import { verify } from "jsonwebtoken"
import { existsSync } from "fs"
import path from "path"

// JWT密钥，与你的应用中使用的保持一致
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

// 验证JWT令牌
const verifyJWT = async (token: string) => {
  try {
    const { payload } = await jose.jwtVerify(token, JWT_SECRET)
    return payload
  } catch (error) {
    console.error("JWT验证失败:", error)
    return null
  }
}

// 允许的图片类型
const ALLOWED_IMAGE_TYPES = [
  "image/jpeg",
  "image/png",
  "image/gif",
  "image/webp"
]

/**
 * 图片上传处理API
 * 接收图片文件，保存到public/uploads/images目录，并返回可访问URL
 *
 * @route POST /api/upload/image
 * @access private - 需要认证
 */
export async function POST(req: NextRequest) {
  try {
    // 尝试验证用户身份，但不强制要求
    // 这样系统设置页面也可以上传图片
    const token = req.cookies.get("token")?.value
    let isAuthenticated = false

    if (token) {
      const decoded = await verifyJWT(token)
      if (decoded) {
        isAuthenticated = true
        console.log("用户已认证:", decoded)
      }
    }

    // 注意：在生产环境中，应该根据实际需求决定是否允许未认证用户上传图片
    // 这里为了解决系统设置页面的问题，我们允许未认证的请求

    // 获取FormData
    const formData = await req.formData()
    const file = formData.get("image") as File

    if (!file) {
      return NextResponse.json(
        { success: false, message: "未找到上传文件", code: 400, data: null },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: `不支持的文件类型。允许的类型: ${ALLOWED_IMAGE_TYPES.join(", ")}`,
          code: 400,
          data: null
        },
        { status: 400 }
      )
    }

    // 从文件名中获取文件扩展名
    const fileExtension = file.name.split(".").pop() || "jpg"

    // 生成时间戳文件名
    const timestamp = new Date().getTime()
    const fileName = `${timestamp}.${fileExtension}`

    // 确保目录存在
    const uploadDir = path.join(process.cwd(), "public", "uploads", "images")
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件路径
    const filePath = path.join(uploadDir, fileName)

    // 将文件保存到服务器
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    await writeFile(filePath, buffer)

    // 返回可访问的URL
    const fileUrl = `/uploads/images/${fileName}`

    return NextResponse.json({
      success: true,
      message: "图片上传成功",
      code: 200,
      data: {
        url: fileUrl
      }
    })
  } catch (error) {
    console.error("图片上传失败:", error)
    return NextResponse.json(
      { success: false, message: "服务器错误，图片上传失败", code: 500, data: null },
      { status: 500 }
    )
  }
}