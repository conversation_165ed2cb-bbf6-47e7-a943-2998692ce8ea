"use client"

import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { AlertCircle } from "lucide-react"

export default function ForbiddenPage() {
  const router = useRouter()

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <AlertCircle className="h-24 w-24 text-red-500" />
        </div>
        <h1 className="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          403
        </h1>
        <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
          权限不足
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          抱歉，您没有权限访问此页面。
        </p>
        <Button
          onClick={() => router.push("/dashboard")}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          返回首页
        </Button>
      </div>
    </div>
  )
} 