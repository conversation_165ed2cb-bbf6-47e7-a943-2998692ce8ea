'use client';

import { useState } from 'react';
import { testNotification } from '@/app/api/debug/notification-test/client';

export default function NotificationDebugPage() {
  const [userId, setUserId] = useState('');
  const [reason, setReason] = useState('测试禁用原因');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await testNotification(userId, reason, email);
      setResult(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">通知和邮件功能调试</h1>
      
      <form onSubmit={handleSubmit} className="mb-6 p-4 border rounded">
        <div className="mb-4">
          <label className="block mb-2">
            用户ID <span className="text-red-500">*</span>
            <input
              type="text"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              className="w-full p-2 border rounded mt-1"
              required
            />
          </label>
        </div>
        
        <div className="mb-4">
          <label className="block mb-2">
            禁用原因
            <input
              type="text"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full p-2 border rounded mt-1"
            />
          </label>
        </div>
        
        <div className="mb-4">
          <label className="block mb-2">
            测试邮箱地址
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 border rounded mt-1"
              placeholder="可选，用于直接测试邮件发送"
            />
          </label>
        </div>
        
        <button
          type="submit"
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? '测试中...' : '开始测试'}
        </button>
      </form>
      
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded text-red-700">
          <h2 className="font-bold mb-2">错误</h2>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-2">测试结果</h2>
          
          <div className="mb-4 p-4 border rounded">
            <h3 className="font-bold mb-2">用户信息</h3>
            <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(result.user, null, 2)}
            </pre>
          </div>
          
          <div className="mb-4 p-4 border rounded">
            <h3 className="font-bold mb-2">通知创建结果</h3>
            <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(result.notificationResult, null, 2)}
            </pre>
          </div>
          
          {result.directEmailResult && (
            <div className="mb-4 p-4 border rounded">
              <h3 className="font-bold mb-2">直接邮件发送结果</h3>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(result.directEmailResult, null, 2)}
              </pre>
            </div>
          )}
          
          <div className="mb-4 p-4 border rounded">
            <h3 className="font-bold mb-2">数据库检查</h3>
            
            <div className="mb-2">
              <h4 className="font-bold">通知类型</h4>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(result.databaseChecks.notificationTypes, null, 2)}
              </pre>
            </div>
            
            <div className="mb-2">
              <h4 className="font-bold">通知记录</h4>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(result.databaseChecks.notifications, null, 2)}
              </pre>
            </div>
            
            <div className="mb-2">
              <h4 className="font-bold">用户通知关联</h4>
              <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
                {JSON.stringify(result.databaseChecks.userNotifications, null, 2)}
              </pre>
            </div>
            
            {result.databaseChecks.emailQueue && (
              <div className="mb-2">
                <h4 className="font-bold">邮件队列</h4>
                <pre className="bg-gray-100 p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(result.databaseChecks.emailQueue, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
