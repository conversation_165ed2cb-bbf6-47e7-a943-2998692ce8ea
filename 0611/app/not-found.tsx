'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'

/**
 * 全局404页面
 *
 * 当用户访问不存在的页面时显示此页面
 * 它是Next.js的内置404处理机制的一部分
 *
 * @see https://nextjs.org/docs/app/api-reference/file-conventions/not-found
 */
export default function NotFound() {
  // 定义处理函数
  const handleGoBack = () => {
    window.history.back()
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="w-full max-w-md text-center">
        <h1 className="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          404
        </h1>
        <h2 className="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
          页面未找到
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        <div className="flex justify-center gap-4">
          <Button asChild>
            <Link href="/dashboard">
              返回首页
            </Link>
          </Button>
          <Button
            variant="outline"
            onClick={handleGoBack}
          >
            返回上一页
          </Button>
        </div>
      </div>
    </div>
  )
}
