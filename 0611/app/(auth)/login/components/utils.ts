/**
 * 登录页面工具函数
 */

// 生成随机动画延迟
export function getRandomDelay() {
  return Math.random() * 5 + 's'
}

// 生成随机位置
export function getRandomPosition() {
  return `${Math.random() * 80 + 10}%`
}

// 生成粒子
export function generateParticles(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    x: getRandomPosition(),
    y: getRandomPosition(),
    size: Math.random() * 3 + 1,
    delay: getRandomDelay(),
  }))
}

// 生成数据线
export function generateDataLines(count: number) {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    startX: getRandomPosition(),
    delay: getRandomDelay(),
    width: Math.random() * 100 + 50,
    opacity: Math.random() * 0.3 + 0.1,
  }))
}

// 检查密码强度
export function checkPasswordStrength(password: string) {
  const hasLowerCase = /[a-z]/.test(password)
  const hasUpperCase = /[A-Z]/.test(password)
  const hasNumber = /[0-9]/.test(password)
  const hasSpecialChar = /[^A-Za-z0-9]/.test(password)
  const isLongEnough = password.length >= 8

  // 计算强度分数 (0-5)
  let score = 0
  if (hasLowerCase) score++
  if (hasUpperCase) score++
  if (hasNumber) score++
  if (hasSpecialChar) score++
  if (isLongEnough) score++

  return {
    score,
    hasLowerCase,
    hasUpperCase,
    hasNumber,
    hasSpecialChar,
    isLongEnough
  }
}
