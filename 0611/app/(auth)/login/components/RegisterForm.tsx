"use client"

import React, { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { UserIcon, MailIcon, KeyIcon } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { RegisterErrors, LoginPageSettings } from "./types"
import { Footer } from "./Footer"
import { PasswordInput } from "@/components/password-input"
import { usePasswordRules } from "@/hooks/use-password-rules"
import { checkPasswordStrengthClient, PasswordStrengthResult } from "@/lib/password-rules"

interface RegisterFormProps {
  onTabChange: (tab: "login" | "register") => void;
  setShowDialog: (show: boolean) => void;
  setDialogContent: (content: { title: string; description: string; action: (() => void) | null }) => void;
  setUsername: (username: string) => void;
  settings: LoginPageSettings;
}

export function RegisterForm({ onTabChange, setShowDialog, setDialogContent, setUsername, settings }: RegisterFormProps) {
  // 注册表单状态
  const [registerUsername, setRegisterUsername] = useState("")
  const [registerPassword, setRegisterPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [email, setEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 获取密码规则
  const { rules: passwordRules, isLoading: isLoadingRules } = usePasswordRules()

  // 密码强度检测状态
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrengthResult>({
    score: 0,
    hasLowerCase: false,
    hasUpperCase: false,
    hasNumber: false,
    hasSpecialChar: false,
    isLongEnough: false,
    isValid: false
  })

  // 注册表单的错误状态
  const [registerErrors, setRegisterErrors] = useState<RegisterErrors>({
    username: "",
    email: "",
    verificationCode: "",
    password: "",
    confirmPassword: ""
  })

  // 处理密码强度检测
  useEffect(() => {
    if (registerPassword) {
      setPasswordStrength(checkPasswordStrengthClient(registerPassword, passwordRules))
    } else {
      setPasswordStrength({
        score: 0,
        hasLowerCase: false,
        hasUpperCase: false,
        hasNumber: false,
        hasSpecialChar: false,
        isLongEnough: false,
        isValid: false
      })
    }
  }, [registerPassword, passwordRules])

  // 处理倒计时
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // 处理注册表单提交
  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 重置错误
    setRegisterErrors({
      username: "",
      email: "",
      verificationCode: "",
      password: "",
      confirmPassword: ""
    })

    // 表单验证
    let hasError = false

    if (!registerUsername) {
      setRegisterErrors(prev => ({ ...prev, username: "请输入用户名" }))
      hasError = true
    } else if (registerUsername.length < 3) {
      setRegisterErrors(prev => ({ ...prev, username: "用户名至少需要3个字符" }))
      hasError = true
    }

    if (!email) {
      setRegisterErrors(prev => ({ ...prev, email: "请输入邮箱" }))
      hasError = true
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      setRegisterErrors(prev => ({ ...prev, email: "请输入有效的邮箱地址" }))
      hasError = true
    }

    if (!verificationCode) {
      setRegisterErrors(prev => ({ ...prev, verificationCode: "请输入验证码" }))
      hasError = true
    } else if (verificationCode.length !== 6) {
      setRegisterErrors(prev => ({ ...prev, verificationCode: "验证码应为6位数字" }))
      hasError = true
    }

    if (!registerPassword) {
      setRegisterErrors(prev => ({ ...prev, password: "请输入密码" }))
      hasError = true
    } else if (!passwordStrength.isValid) {
      setRegisterErrors(prev => ({ ...prev, password: "密码不符合要求" }))
      hasError = true
    }

    if (!confirmPassword) {
      setRegisterErrors(prev => ({ ...prev, confirmPassword: "请确认密码" }))
      hasError = true
    } else if (confirmPassword !== registerPassword) {
      setRegisterErrors(prev => ({ ...prev, confirmPassword: "两次输入的密码不一致" }))
      hasError = true
    }

    if (hasError) {
      return
    }

    // 继续原有的注册逻辑
    setIsLoading(true)
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: registerUsername,
          password: registerPassword,
          email,
          verificationCode,
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 显示成功提示，并在3秒后自动切换到登录页
        toast({
          title: "✨ 注册成功",
          description: (
            <div className="space-y-2">
              <p>您的账号已创建成功！</p>
              <p className="text-sm text-muted-foreground">系统将在3秒后自动跳转到登录页面...</p>
            </div>
          ),
          variant: "default",
          duration: 3000,
        })

        // 延迟3秒后切换到登录页
        setTimeout(() => {
          onTabChange("login")
          // 自动填充刚注册的用户名
          setUsername(registerUsername)
        }, 3000)
      } else {
        setDialogContent({
          title: "注册失败",
          description: data.message || "注册过程中发生错误，请稍后重试",
          action: null
        })
        setShowDialog(true)
      }
    } catch (error) {
      console.error("注册错误:", error)
      setDialogContent({
        title: "系统错误",
        description: error instanceof Error
          ? error.message
          : "注册过程中发生错误，请检查网络连接后重试",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsLoading(false)
    }
  }

  // 处理发送验证码
  const handleSendCode = async () => {
    // 邮箱为空检查
    if (!email) {
      setDialogContent({
        title: "邮箱未填写",
        description: "请输入邮箱地址",
        action: null
      })
      setShowDialog(true)
      return
    }

    // 邮箱格式检查
    if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
      setDialogContent({
        title: "邮箱格式错误",
        description: "请输入有效的邮箱地址",
        action: null
      })
      setShowDialog(true)
      return
    }

    setIsSendingCode(true)
    try {
      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "验证码已发送",
          description: "请查看您的邮箱，验证码有效期为10分钟",
        })

        // 设置倒计时
        setCountdown(60)
      } else {
        setDialogContent({
          title: "发送失败",
          description: data.message || "验证码发送失败，请稍后重试",
          action: null
        })
        setShowDialog(true)
      }
    } catch (error) {
      console.error("发送验证码错误:", error)
      setDialogContent({
        title: "网络错误",
        description: "请检查您的网络连接后重试",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsSendingCode(false)
    }
  }

  return (
    <form onSubmit={handleRegisterSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="register-username" className="text-gray-700 dark:text-gray-300">
          用户名
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <UserIcon className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="register-username"
            type="text"
            placeholder="请设置用户名"
            className={`pl-10 ${registerErrors.username ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={registerUsername}
            onChange={(e) => setRegisterUsername(e.target.value)}
            disabled={isLoading}
          />
        </div>
        {registerErrors.username && (
          <p className="text-sm text-red-500">{registerErrors.username}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="register-email" className="text-gray-700 dark:text-gray-300">
          邮箱
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <MailIcon className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="register-email"
            type="email"
            placeholder="请输入邮箱"
            className={`pl-10 ${registerErrors.email ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>
        {registerErrors.email && (
          <p className="text-sm text-red-500">{registerErrors.email}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="verification-code" className="text-gray-700 dark:text-gray-300">
          验证码
        </Label>
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <KeyIcon className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              id="verification-code"
              type="text"
              placeholder="请输入验证码"
              className={`pl-10 ${registerErrors.verificationCode ? 'border-red-500 focus:ring-red-500' : ''}`}
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <Button
            type="button"
            variant="outline"
            className="whitespace-nowrap"
            onClick={handleSendCode}
            disabled={isSendingCode || countdown > 0 || isLoading}
          >
            {countdown > 0 ? `${countdown}秒后重试` : "获取验证码"}
          </Button>
        </div>
        {registerErrors.verificationCode && (
          <p className="text-sm text-red-500">{registerErrors.verificationCode}</p>
        )}
      </div>

      <PasswordInput
        id="register-password"
        label="密码"
        value={registerPassword}
        onChange={setRegisterPassword}
        placeholder="请设置密码"
        error={registerErrors.password}
        disabled={isLoading}
        showStrengthIndicator={true}
        rules={passwordRules}
      />

      <PasswordInput
        id="confirm-password"
        label="确认密码"
        value={confirmPassword}
        onChange={setConfirmPassword}
        placeholder="请再次输入密码"
        error={registerErrors.confirmPassword}
        disabled={isLoading}
        showStrengthIndicator={false}
      />

      <Button
        type="submit"
        className="w-full bg-blue-600 hover:bg-blue-700"
        disabled={isLoading}
      >
        {isLoading ? "注册中..." : "注册"}
      </Button>

      <div className="text-center mt-4">
        <span className="text-gray-600 dark:text-gray-400 text-sm">
          已有账号？
        </span>{" "}
        <Button
          type="button"
          variant="link"
          className="text-sm text-blue-600 dark:text-blue-400 p-0 h-auto"
          onClick={() => onTabChange("login")}
        >
          立即登录
        </Button>
      </div>

      {/* 页脚信息 */}
      <div className="mt-8">
        <Footer settings={settings} />
      </div>
    </form>
  )
}
