"use client"

import React from "react"
import { LoginPageSettings } from "./types"
import { UserIcon, LockIcon, MailIcon, SettingsIcon, BellIcon } from "lucide-react"

interface LeftContentProps {
  settings: LoginPageSettings;
}

// 根据图标名称返回对应的图标组件
function getIconComponent(iconName: string) {
  const iconStyle = "w-6 h-6 group-hover:scale-110 transition-transform";

  switch (iconName) {
    case 'user':
      return <UserIcon className={iconStyle} />;
    case 'lock':
      return <LockIcon className={iconStyle} />;
    case 'mail':
      return <MailIcon className={iconStyle} />;
    case 'settings':
      return <SettingsIcon className={iconStyle} />;
    case 'bell':
      return <BellIcon className={iconStyle} />;
    default:
      return <UserIcon className={iconStyle} />;
  }
}

export function LeftContent({ settings }: LeftContentProps) {
  return (
    <div className="relative h-full flex flex-col items-center justify-center text-white p-12">
      <div className="relative">
        <h1 className="text-4xl font-bold mb-4 animate-title-glow">
          {settings.title || "外呼管理系统"}
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/0 via-blue-500/10 to-blue-500/0 animate-title-shine" />
        </h1>
      </div>
      <p className="text-xl mb-8 text-center animate-fadeIn animation-delay-200">
        {settings.subtitle || "提升工作效率的得力助手"}
      </p>
      <div className="space-y-6 relative">
        {(settings.features || [
          { icon: "user", text: "专业的客户管理" },
          { icon: "lock", text: "安全的数据保护" },
          { icon: "mail", text: "高效的沟通工具" }
        ]).map((item, index) => (
          <div
            key={index}
            className="flex items-center space-x-4 animate-feature-slide"
            style={{ animationDelay: `${index * 200}ms` }}
          >
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-400/10 backdrop-blur-sm
                          flex items-center justify-center group hover:from-blue-500/30 hover:to-blue-400/20
                          transition-all duration-300 border border-blue-500/20">
              {getIconComponent(item.icon)}
              <div className="absolute inset-0 bg-grid-small opacity-20" />
            </div>
            <p className="text-lg text-blue-50">{item.text}</p>
          </div>
        ))}
      </div>
    </div>
  )
}
