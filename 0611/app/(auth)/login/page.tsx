"use client"

import logger from '@/lib/utils/logger';

/**
 * 登录页面组件
 * 提供用户登录和账户验证功能
 *
 * 特性：
 * - 用户名和密码验证
 * - 记住登录状态
 * - 错误提示
 * - 响应式设计
 * - 主题适配
 * - 背景图片自定义
 *
 * 状态管理：
 * - 登录表单状态
 * - 登录过程状态
 * - 错误信息状态
 *
 * 页面布局：
 * - 左侧：营销信息展示区
 * - 右侧：登录表单区域
 *
 * @example
 * ```tsx
 * // 访问登录页面
 * router.push("/login")
 * ```
 */

import React, { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { getSystemSettings } from "@/lib/api/index"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Logo } from "@/components/Logo"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"

// 导入组件
import { BackgroundEffects } from "./components/BackgroundEffects"
import { LeftContent } from "./components/LeftContent"
import { LoginForm } from "./components/LoginForm"
import { RegisterForm } from "./components/RegisterForm"
// Footer组件现在在LoginForm和RegisterForm中使用

// 导入类型
import { LoginPageSettings, DialogContent as DialogContentType } from "./components/types"

// 登录页面默认设置
const defaultSettings: LoginPageSettings = {
  backgroundImage: "/placeholder.svg?height=1080&width=1920",
  footerText: "",
  backgroundEffect: 'all',
  title: "外呼管理系统",
  subtitle: "提升工作效率的得力助手",
  features: [
    { icon: "user", text: "专业的客户管理" },
    { icon: "lock", text: "安全的数据保护" },
    { icon: "mail", text: "高效的沟通工具" }
  ]
}

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // 状态管理
  const [activeTab, setActiveTab] = useState<"login" | "register">("login")
  const [username, setUsername] = useState("")
  const [settings, setSettings] = useState<LoginPageSettings>(defaultSettings)
  const [showDialog, setShowDialog] = useState(false)
  const [dialogContent, setDialogContent] = useState<DialogContentType>({
    title: "",
    description: "",
    action: null
  })
  const [logoKey, setLogoKey] = useState(Date.now()) // 添加一个状态来强制重新渲染Logo

  // 初始化Tab状态并清除会话数据
  useEffect(() => {
    // 清除所有会话数据
    const clearAllSessionData = async () => {
      try {
        // 清除所有cookie
        document.cookie.split(';').forEach(function(c) {
          document.cookie = c.trim().split('=')[0] + '=;' + 'expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
        });

        // 调用NextAuth的登出API
        try {
          await fetch('/api/auth/signout', { method: 'POST' });
          logger.log('成功调用NextAuth登出API');
        } catch (signoutError) {
          logger.error('调用NextAuth登出API失败:', signoutError);
        }

        logger.log('登录页面加载时清除了所有会话数据');
      } catch (e) {
        logger.error('清除会话数据失败:', e);
      }
    };

    // 检查是否有clear参数
    const clear = searchParams.get("clear");
    if (clear) {
      clearAllSessionData();
    }

    // 设置Tab状态
    const tab = searchParams.get("tab")
    if (tab === "login" || tab === "register") {
      setActiveTab(tab)
    }
  }, [searchParams])

  // 不再需要从Zustand存储获取Logo URL，新的Logo组件会自动获取

  useEffect(() => {
    // 获取系统设置
    const fetchSettings = async () => {
      try {
        // 添加时间戳参数，确保不使用缓存
        const timestamp = Date.now()
        const response = await fetch(`/api/settings?t=${timestamp}`)
        const data = await response.json()

        if (data.success && data.data) {
          // 将系统设置转换为登录页面设置
          const loginPageData = data.data.loginPage as any || {};

          const newSettings: LoginPageSettings = {
            backgroundImage: loginPageData.backgroundImage || data.data.logo || settings.backgroundImage,
            footerText: data.data.footerText || settings.footerText,
            backgroundEffect: loginPageData.backgroundEffect || settings.backgroundEffect,
            title: loginPageData.title || data.data.siteName || settings.title,
            subtitle: loginPageData.subtitle || settings.subtitle,
            logo: data.data.logo || '/logo.png', // 直接使用API返回的Logo URL
            features: loginPageData.features || settings.features,
          };

          setSettings(newSettings)
          setLogoKey(timestamp) // 更新时间戳强制刷新
        }
      } catch (error) {
        logger.error('获取系统设置失败:', error);
      }
    }

    // 延迟执行，确保先获取Logo
    setTimeout(fetchSettings, 100);
  }, [])

  // 处理Tab切换
  const handleTabChange = (tab: "login" | "register") => {
    setActiveTab(tab)
    // 更新URL参数，但不刷新页面
    const url = new URL(window.location.href)
    url.searchParams.set("tab", tab)
    window.history.pushState({}, "", url.toString())
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* 左侧背景区域 */}
      <div className="flex-1 flex">
        <div className="hidden lg:block lg:w-1/2 relative overflow-hidden">
          <BackgroundEffects settings={settings} />
          <LeftContent settings={settings} />
        </div>

        {/* 右侧表单区域 */}
        <div className="w-full lg:w-1/2 bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
          <div className="w-full max-w-md px-8 py-12">
            {/* Logo和欢迎文字 */}
            <div className="text-center mb-8">
              {/* 使用新的Logo组件 */}
              <Logo size="large" className="mx-auto mb-4 animate-float" />
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white animate-title-glow">欢迎回来！</h2>
              <p className="text-gray-500 dark:text-gray-400 mt-2">创造属于您的快乐和梦想</p>
            </div>

            {/* 表单切换按钮 */}
            <div className="flex mb-6 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                className={`flex-1 py-2 rounded-md transition-all ${
                  activeTab === "login"
                    ? "bg-white dark:bg-gray-700 shadow-sm"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                }`}
                onClick={() => handleTabChange("login")}
              >
                登录
              </button>
              <button
                className={`flex-1 py-2 rounded-md transition-all ${
                  activeTab === "register"
                    ? "bg-white dark:bg-gray-700 shadow-sm"
                    : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                }`}
                onClick={() => handleTabChange("register")}
              >
                注册
              </button>
            </div>

            {/* 登录表单 */}
            {activeTab === "login" && (
              <LoginForm
                onTabChange={handleTabChange}
                setShowDialog={setShowDialog}
                setDialogContent={setDialogContent}
                settings={settings}
              />
            )}

            {/* 注册表单 */}
            {activeTab === "register" && (
              <RegisterForm
                onTabChange={handleTabChange}
                setShowDialog={setShowDialog}
                setDialogContent={setDialogContent}
                setUsername={setUsername}
                settings={settings}
              />
            )}
          </div>
        </div>

      </div>

      {/* 提示对话框 */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{dialogContent.title}</DialogTitle>
            <DialogDescription>
              {dialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              onClick={() => {
                setShowDialog(false)
                if (dialogContent.action) {
                  dialogContent.action()
                }
              }}
            >
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Toast通知 */}
      <Toaster />
    </div>
  )
}

// 添加全局样式
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style')
  styleSheet.textContent = `
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    @keyframes pulse-slow {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 0.3; }
    }

    @keyframes gradient {
      0% { opacity: 0.7; }
      50% { opacity: 0.5; }
      100% { opacity: 0.7; }
    }

    @keyframes title-glow {
      0%, 100% { text-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
      50% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
    }

    @keyframes title-shine {
      0% { transform: translateX(-100%); }
      50%, 100% { transform: translateX(100%); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes feature-slide {
      from { opacity: 0; transform: translateX(-20px); }
      to { opacity: 1; transform: translateX(0); }
    }

    @keyframes data-line {
      0% { transform: translateX(-100%); opacity: 0; }
      10%, 90% { opacity: 0.5; }
      100% { transform: translateX(100%); opacity: 0; }
    }

    .animation-delay-200 {
      animation-delay: 0.2s;
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .animate-pulse-slow {
      animation: pulse-slow 4s ease-in-out infinite;
    }

    .animate-gradient {
      animation: gradient 8s ease-in-out infinite;
    }

    .animate-title-glow {
      animation: title-glow 2s ease-in-out infinite;
    }

    .animate-title-shine {
      animation: title-shine 4s ease-in-out infinite;
    }

    .animate-fadeIn {
      animation: fadeIn 1s ease-out forwards;
    }

    .animate-feature-slide {
      animation: feature-slide 0.5s ease-out forwards;
    }

    .animate-data-line {
      animation: data-line 8s linear infinite;
    }

    .bg-grid {
      background-size: 50px 50px;
      background-image:
        linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    }

    .bg-grid-small {
      background-size: 20px 20px;
      background-image:
        linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    }
  `
  document.head.appendChild(styleSheet)
}