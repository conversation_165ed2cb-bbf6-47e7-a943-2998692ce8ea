"use client"

import { useState } from "react"
import { Plus, Edit, Trash, <PERSON>tings, Mail, Info, Heart } from "lucide-react"
import { FloatingActionButton } from "@/components/ui/floating-action-button"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function FloatingActionButtonExamplePage() {
  const [position, setPosition] = useState<"bottom-right" | "bottom-left" | "top-right" | "top-left" | "center-right" | "center-left">("bottom-right")
  const [variant, setVariant] = useState<"default" | "destructive" | "outline" | "secondary" | "ghost" | "link">("default")
  const [offsetSize, setOffsetSize] = useState<"sm" | "md" | "lg" | "xl" | "2xl">("md")
  
  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Floating Action Button Examples</h1>
      <p className="text-muted-foreground mb-8">
        This page demonstrates the Floating Action Button component with various configurations.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
        <Card>
          <CardHeader>
            <CardTitle>Position</CardTitle>
            <CardDescription>Change the position of the FAB on the screen</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={position} onValueChange={(value: any) => setPosition(value)}>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="bottom-right" id="position-1" />
                <Label htmlFor="position-1">Bottom Right</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="bottom-left" id="position-2" />
                <Label htmlFor="position-2">Bottom Left</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="top-right" id="position-3" />
                <Label htmlFor="position-3">Top Right</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="top-left" id="position-4" />
                <Label htmlFor="position-4">Top Left</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="center-right" id="position-5" />
                <Label htmlFor="position-5">Center Right</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="center-left" id="position-6" />
                <Label htmlFor="position-6">Center Left</Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Variant</CardTitle>
            <CardDescription>Change the visual style of the FAB</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={variant} onValueChange={(value: any) => setVariant(value)}>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="default" id="variant-1" />
                <Label htmlFor="variant-1">Default</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="destructive" id="variant-2" />
                <Label htmlFor="variant-2">Destructive</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="outline" id="variant-3" />
                <Label htmlFor="variant-3">Outline</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="secondary" id="variant-4" />
                <Label htmlFor="variant-4">Secondary</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="ghost" id="variant-5" />
                <Label htmlFor="variant-5">Ghost</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="link" id="variant-6" />
                <Label htmlFor="variant-6">Link</Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Offset Size</CardTitle>
            <CardDescription>Change the distance from the edge</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={offsetSize} onValueChange={(value: any) => setOffsetSize(value)}>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="sm" id="offset-1" />
                <Label htmlFor="offset-1">Small</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="md" id="offset-2" />
                <Label htmlFor="offset-2">Medium</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="lg" id="offset-3" />
                <Label htmlFor="offset-3">Large</Label>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <RadioGroupItem value="xl" id="offset-4" />
                <Label htmlFor="offset-4">Extra Large</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="2xl" id="offset-5" />
                <Label htmlFor="offset-5">2X Large</Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="examples" className="mb-8">
        <TabsList>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
        </TabsList>
        <TabsContent value="examples" className="mt-4">
          <h2 className="text-2xl font-bold mb-4">Example FABs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="relative p-6 h-60 flex items-center justify-center">
              <p className="text-center text-muted-foreground">Add action</p>
              <FloatingActionButton 
                icon={<Plus />} 
                tooltip="Add new item"
                position="bottom-right" 
                offsetSize="sm"
                variant="default"
                onClick={() => alert('Add button clicked')}
              />
            </Card>
            
            <Card className="relative p-6 h-60 flex items-center justify-center">
              <p className="text-center text-muted-foreground">Edit action</p>
              <FloatingActionButton 
                icon={<Edit />} 
                tooltip="Edit document"
                position="bottom-left" 
                offsetSize="md"
                variant="secondary"
                onClick={() => alert('Edit button clicked')}
              />
            </Card>
            
            <Card className="relative p-6 h-60 flex items-center justify-center">
              <p className="text-center text-muted-foreground">Delete action</p>
              <FloatingActionButton 
                icon={<Trash />} 
                tooltip="Delete item"
                position="top-right" 
                offsetSize="md"
                variant="destructive"
                onClick={() => alert('Delete button clicked')}
              />
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="usage" className="mt-4">
          <h2 className="text-2xl font-bold mb-4">Usage</h2>
          <pre className="p-4 bg-muted rounded-md overflow-auto">
{`import { FloatingActionButton } from "@/components/ui/floating-action-button"
import { Plus } from "lucide-react"

// Basic usage
<FloatingActionButton 
  icon={<Plus />} 
  tooltip="Add new item" 
  onClick={() => console.log('FAB clicked')} 
/>

// With custom position and style
<FloatingActionButton 
  icon={<Edit />} 
  position="bottom-left" 
  variant="secondary"
  offsetSize="lg"
  tooltip="Edit document" 
/>`}
          </pre>
        </TabsContent>
      </Tabs>
      
      {/* Live example that responds to the control settings */}
      <FloatingActionButton 
        icon={<Plus />} 
        tooltip="Configurable FAB"
        position={position}
        offsetSize={offsetSize}
        variant={variant}
        onClick={() => alert('Configurable FAB clicked')}
      />
    </div>
  )
} 