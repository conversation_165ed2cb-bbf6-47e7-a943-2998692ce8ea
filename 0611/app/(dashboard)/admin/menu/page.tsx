"use client"

import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { MenuFormDialog } from "@/components/menu/menu-form-dialog"
import { SortableTable } from "@/components/ui/sortable-table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { Menu } from "@/types/menu"

export default function MenuPage() {
  const { toast } = useToast()
  const [menus, setMenus] = useState<Menu[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingMenu, setEditingMenu] = useState<Menu | null>(null)

  const fetchMenus = async () => {
    try {
      const response = await fetch("/api/menus")
      if (!response.ok) throw new Error("Failed to fetch menus")
      const data = await response.json()
      setMenus(data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch menus",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    fetchMenus()
  }, [])

  const handleCreate = () => {
    setEditingMenu(null)
    setIsDialogOpen(true)
  }

  const handleEdit = (menu: Menu) => {
    setEditingMenu(menu)
    setIsDialogOpen(true)
  }

  const handleDelete = async (menu: Menu) => {
    try {
      const response = await fetch(`/api/menus/${menu.id}`, {
        method: "DELETE",
      })
      if (!response.ok) throw new Error("Failed to delete menu")
      toast({
        title: "Success",
        description: "Menu deleted successfully",
      })
      fetchMenus()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete menu",
        variant: "destructive",
      })
    }
  }

  const handleBatchDelete = async (menus: Menu[]) => {
    try {
      const response = await fetch("/api/menus/batch", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: menus.map((menu) => menu.id) }),
      })
      if (!response.ok) throw new Error("Failed to delete menus")
      toast({
        title: "Success",
        description: "Menus deleted successfully",
      })
      fetchMenus()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete menus",
        variant: "destructive",
      })
    }
  }

  const handleBatchUpdate = async (menus: Menu[], updates: Partial<Menu>) => {
    try {
      const response = await fetch("/api/menus/batch", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ids: menus.map((menu) => menu.id),
          updates,
        }),
      })
      if (!response.ok) throw new Error("Failed to update menus")
      toast({
        title: "Success",
        description: "Menus updated successfully",
      })
      fetchMenus()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update menus",
        variant: "destructive",
      })
    }
  }

  const handleReorder = async (menus: Menu[]) => {
    try {
      const response = await fetch("/api/menus/reorder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          items: menus.map((menu) => ({
            id: menu.id,
            order: menu.order,
          })),
        }),
      })
      if (!response.ok) throw new Error("Failed to reorder menus")
      toast({
        title: "Success",
        description: "Menus reordered successfully",
      })
      fetchMenus()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reorder menus",
        variant: "destructive",
      })
    }
  }

  const columns = [
    {
      key: "name",
      title: "Name",
    },
    {
      key: "path",
      title: "Path",
    },
    {
      key: "icon",
      title: "Icon",
    },
    {
      key: "order",
      title: "Order",
    },
    {
      key: "visible",
      title: "Visible",
      render: (menu: Menu) => (menu.visible ? "Yes" : "No"),
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Menu Management</h1>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" />
          Create Menu
        </Button>
      </div>
      <div className="mt-8">
        <SortableTable
          columns={columns}
          data={menus}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onReorder={handleReorder}
          onBatchDelete={handleBatchDelete}
          onBatchUpdate={handleBatchUpdate}
          getItemId={(menu: Menu) => menu.id}
        />
      </div>
      <MenuFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        menu={editingMenu || undefined}
        onSuccess={() => {
          setIsDialogOpen(false)
          fetchMenus()
        }}
      />
    </div>
  )
}