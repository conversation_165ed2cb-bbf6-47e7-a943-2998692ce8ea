"use client"

import { useEffect, useState } from "react"
import { PermissionDiagnostic } from "@/app/components/admin/permission-diagnostic"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"

export default function PermissionDiagnosticPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isAdmin, setIsAdmin] = useState(false)

  useEffect(() => {
    if (status === "authenticated") {
      // 检查用户是否是管理员
      const userRole = session?.user?.roleCode || ""
      setIsAdmin(userRole.toUpperCase() === "ADMIN")

      if (userRole.toUpperCase() !== "ADMIN") {
        // 如果不是管理员，重定向到首页
        router.push("/")
      }
    } else if (status === "unauthenticated") {
      // 如果未登录，重定向到登录页
      router.push("/auth/login")
    }
  }, [status, session, router])

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto py-10">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>访问被拒绝</AlertTitle>
          <AlertDescription>
            您没有权限访问此页面。此页面仅供管理员使用。
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">权限系统诊断</h1>

      <Tabs defaultValue="diagnostic">
        <TabsList className="mb-4">
          <TabsTrigger value="diagnostic">权限诊断</TabsTrigger>
          <TabsTrigger value="help">帮助信息</TabsTrigger>
        </TabsList>

        <TabsContent value="diagnostic">
          <PermissionDiagnostic />
        </TabsContent>

        <TabsContent value="help">
          <Card>
            <CardHeader>
              <CardTitle>权限诊断帮助</CardTitle>
              <CardDescription>
                了解如何使用权限诊断工具解决权限问题
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">什么是权限诊断？</h3>
                <p className="text-gray-600 mt-1">
                  权限诊断工具可以帮助您检测和修复系统中的权限配置问题，确保所有用户都能正确访问他们应该看到的功能。
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium">诊断项目说明</h3>
                <ul className="list-disc pl-5 mt-2 space-y-2">
                  <li>
                    <strong>USER 角色存在</strong> - 检查系统中是否存在普通用户角色
                  </li>
                  <li>
                    <strong>USER 角色权限完整</strong> - 检查普通用户角色是否拥有所有必要的基本权限
                  </li>
                  <li>
                    <strong>USER 角色菜单完整</strong> - 检查普通用户角色是否能看到所有必要的菜单项
                  </li>
                  <li>
                    <strong>用户角色分配</strong> - 检查所有非管理员用户是否都被正确分配了角色
                  </li>
                  <li>
                    <strong>jCasbin 策略一致性</strong> - 检查 jCasbin 权限策略是否与数据库中的权限配置一致
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium">如何修复问题</h3>
                <p className="text-gray-600 mt-1">
                  对于每个诊断出的问题，您可以点击对应的修复按钮进行修复。如果有多个问题，您也可以使用&quot;一键修复所有问题&quot;按钮一次性解决所有问题。
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium">注意事项</h3>
                <ul className="list-disc pl-5 mt-2">
                  <li>修复操作可能会影响系统中的权限配置，请确保您了解修复操作的影响</li>
                  <li>修复完成后，用户可能需要重新登录才能看到更改</li>
                  <li>如果问题仍然存在，请尝试重新诊断或联系系统管理员</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
