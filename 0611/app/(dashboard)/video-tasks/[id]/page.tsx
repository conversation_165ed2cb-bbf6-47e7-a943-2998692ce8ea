"use client"

import logger from '@/lib/utils/logger';

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "@/components/ui/use-toast"
import { VideoCallService } from "@/lib/api/video-call-service"
import { VIDEO_CALL_API_CONFIG } from "@/lib/api/config"
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"

export default function TaskDetailPage() {
  const params = useParams()
  const taskId = params.id as string

  // 使用视频外呼服务单例
  const { videoCallService } = require("@/lib/api/video-call-service")

  // 加载状态
  const [isLoading, setIsLoading] = useState(true)

  // 任务详情
  const [taskDetail, setTaskDetail] = useState<any>(null)

  // 任务统计数据
  const [taskStats, setTaskStats] = useState<any>(null)

  // 当前选中的标签页
  const [activeTab, setActiveTab] = useState("overview")

  // 通话记录详情
  const [selectedRecord, setSelectedRecord] = useState<any>(null)
  const [showRecordDetail, setShowRecordDetail] = useState(false)

  /**
   * 页面加载时获取任务详情
   */
  useEffect(() => {
    fetchTaskDetail()
  }, [taskId])

  /**
   * 获取任务详情
   */
  const fetchTaskDetail = async () => {
    setIsLoading(true)
    try {
      const detail = await videoCallService.getTaskDetail(taskId)
      setTaskDetail(detail)

      // 获取任务统计数据
      try {
        const stats = await videoCallService.getTaskStatistics([taskId])
        setTaskStats(stats)
      } catch (error) {
        logger.error("获取任务统计数据失败:", error)
        toast({
          title: "获取任务统计数据失败",
          description: "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("获取任务详情失败:", error)
      toast({
        title: "获取任务详情失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取任务状态中文名称
   */
  const getStatusName = (status: string) => {
    switch (status) {
      case "initialized":
        return "待启动"
      case "started":
        return "已启动"
      case "paused":
        return "已暂停"
      case "finished":
        return "已完成"
      case "stopped":
        return "已停止"
      default:
        return status
    }
  }

  /**
   * 获取外呼类型中文名称
   */
  const getCallTypeName = (type: number) => {
    switch (type) {
      case 1:
        return "视频通知"
      case 2:
        return "视频互动"
      case 3:
        return "语音互动"
      default:
        return `未知类型(${type})`
    }
  }

  // 准备图表数据
  const prepareChartData = () => {
    if (!taskStats) return null

    // 接通方式数据
    const connectionTypeData = [
      { name: "视频接通", value: taskStats.callonStatisticData.callStatisticData.videoCallonCount },
      { name: "语音接通", value: taskStats.callonStatisticData.callStatisticData.audioCallonCount },
      { name: "未接通", value: taskStats.callonStatisticData.callStatisticData.callonNotCount },
    ]

    // 意图分布数据
    const intentionData =
      taskStats.intentStatisticData?.map((item: any) => ({
        name: item.intentLevel,
        value: item.totalCount,
      })) || []

    // 挂断分布数据
    const hangupData =
      taskStats.positionStatisticData?.map((item: any) => ({
        name: `轮次${item.position}`,
        视频: item.videoCount,
        语音: item.audioCount,
      })) || []

    return {
      connectionTypeData,
      intentionData,
      hangupData,
    }
  }

  const chartData = prepareChartData()
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8", "#82ca9d"]

  return (
    <DashboardShell>
      <DashboardHeader
        heading={`任务详情: ${taskDetail?.name || taskId}`}
        text="查看5G视频外呼任务的详细信息和统计数据"
      />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : taskDetail ? (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">任务ID</p>
                  <p>{taskDetail.taskId}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">任务名称</p>
                  <p>{taskDetail.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">任务状态</p>
                  <p>{getStatusName(taskDetail.status)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">外呼类型</p>
                  <p>{getCallTypeName(taskDetail.callType)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">创建人</p>
                  <p>{taskDetail.creator}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">创建时间</p>
                  <p>{taskDetail.createdAt}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">设定开始时间</p>
                  <p>{taskDetail.settingStartTime}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">实际开始时间</p>
                  <p>{taskDetail.startedAt || "未开始"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">完成时间</p>
                  <p>{taskDetail.endedAt || "未完成"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">导入数量</p>
                  <p>{taskDetail.importNumber}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">有效数量</p>
                  <p>{taskDetail.deliverNumber}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">已呼数量</p>
                  <p>{taskDetail.calledCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>内容信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">视频内容</p>
                  <p>{taskDetail.videoBotName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">视频内容ID</p>
                  <p>{taskDetail.videoBotId}</p>
                </div>
                {taskDetail.audioBotName && (
                  <>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">语音分流内容</p>
                      <p>{taskDetail.audioBotName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">语音分流内容ID</p>
                      <p>{taskDetail.audioBotId}</p>
                    </div>
                  </>
                )}
                <div>
                  <p className="text-sm font-medium text-muted-foreground">线路名称</p>
                  <p>{taskDetail.trunkName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">并发数</p>
                  <p>{taskDetail.concurrency}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>短信配置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {taskDetail.messageTemplateName ? (
                  <>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">文本短信模板</p>
                      <p>{taskDetail.messageTemplateName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">文本短信模板ID</p>
                      <p>{taskDetail.messageTemplateId}</p>
                    </div>
                  </>
                ) : (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">文本短信</p>
                    <p>未配置</p>
                  </div>
                )}

                {taskDetail.videoMessageTemplateName ? (
                  <>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">视频短信模板</p>
                      <p>{taskDetail.videoMessageTemplateName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">视频短信模板ID</p>
                      <p>{taskDetail.videoMessageTemplateId}</p>
                    </div>
                  </>
                ) : (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">视频短信</p>
                    <p>未配置</p>
                  </div>
                )}

                {taskDetail.flashMessageTemplateName ? (
                  <>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">闪信模板</p>
                      <p>{taskDetail.flashMessageTemplateName}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">闪信模板ID</p>
                      <p>{taskDetail.flashMessageTemplateId}</p>
                    </div>
                  </>
                ) : (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">闪信</p>
                    <p>未配置</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {taskStats && (
            <Card>
              <CardHeader>
                <CardTitle>统计分析</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="overview">概览</TabsTrigger>
                    <TabsTrigger value="connection">接通分析</TabsTrigger>
                    <TabsTrigger value="intention">意图分析</TabsTrigger>
                    <TabsTrigger value="hangup">挂断分析</TabsTrigger>
                    <TabsTrigger value="message">短信分析</TabsTrigger>
                    <TabsTrigger value="callRecords">通话记录</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">外呼总数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.callTotalCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">接通总数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.callonCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">接通率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.callStatisticData.callonRate * 100).toFixed(2)}%
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">视频接通数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.videoCallonCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">语音接通数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.audioCallonCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">平均通话时长</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.avgTotalHoldingTime.toFixed(2)}秒
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">完播数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.callStatisticData.playFinishCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">完播率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.callStatisticData.playFinishRate * 100).toFixed(2)}%
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">互动率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.callStatisticData.interactionRate * 100).toFixed(2)}%
                        </p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="connection">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">接通方式分布</h3>
                        {chartData && (
                          <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                              <Pie
                                data={chartData.connectionTypeData}
                                cx="50%"
                                cy="50%"
                                labelLine={true}
                                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(2)}%`}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                              >
                                {chartData.connectionTypeData.map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                              </Pie>
                              <Tooltip />
                              <Legend />
                            </PieChart>
                          </ResponsiveContainer>
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-4">接通趋势</h3>
                        {taskStats.callonTrendStatisticData && (
                          <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={taskStats.callonTrendStatisticData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="hour" />
                              <YAxis />
                              <Tooltip />
                              <Legend />
                              <Bar dataKey="videoCallonCount" name="视频接通" fill="#0088FE" />
                              <Bar dataKey="audioCallonCount" name="语音接通" fill="#00C49F" />
                            </BarChart>
                          </ResponsiveContainer>
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="intention">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">意图分布</h3>
                        {chartData && (
                          <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                              <Pie
                                data={chartData.intentionData}
                                cx="50%"
                                cy="50%"
                                labelLine={true}
                                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(2)}%`}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                              >
                                {chartData.intentionData.map((entry: any, index: number) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                              </Pie>
                              <Tooltip />
                              <Legend />
                            </PieChart>
                          </ResponsiveContainer>
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-4">意图触发详情</h3>
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>意图类型</TableHead>
                                <TableHead>触发次数</TableHead>
                                <TableHead>占比</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {taskStats.intentTouchStatisticData?.totalList?.map((item: any, index: number) => (
                                <TableRow key={index}>
                                  <TableCell>{item.intentTouchName || item.intentTouchType}</TableCell>
                                  <TableCell>{item.count}</TableCell>
                                  <TableCell>{(item.rate * 100).toFixed(2)}%</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="hangup">
                    <div className="grid grid-cols-1 gap-6">
                      <div>
                        <h3 className="text-lg font-medium mb-4">挂断分布</h3>
                        {chartData && (
                          <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={chartData.hangupData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="name" />
                              <YAxis />
                              <Tooltip />
                              <Legend />
                              <Bar dataKey="视频" fill="#0088FE" />
                              <Bar dataKey="语音" fill="#00C49F" />
                            </BarChart>
                          </ResponsiveContainer>
                        )}
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-4">5秒挂断分布</h3>
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>挂断秒数</TableHead>
                                <TableHead>视频数量</TableHead>
                                <TableHead>语音数量</TableHead>
                                <TableHead>总数</TableHead>
                                <TableHead>占比</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {taskStats.hangup5SecondsData?.map((item: any, index: number) => (
                                <TableRow key={index}>
                                  <TableCell>{item.hangupSeconds}秒</TableCell>
                                  <TableCell>{item.videoCount}</TableCell>
                                  <TableCell>{item.audioCount}</TableCell>
                                  <TableCell>{item.totalCount}</TableCell>
                                  <TableCell>{(item.totalRate * 100).toFixed(2)}%</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="message">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信发送总数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.messageStatisticData.smsSendCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信发送成功数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.messageStatisticData.smsSendSuccessCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信接收数</h3>
                        <p className="text-3xl font-bold">
                          {taskStats.callonStatisticData.messageStatisticData.smsReceivedCount}
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信发送成功率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.messageStatisticData.smsSendSuccessRate * 100).toFixed(2)}%
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信接收率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.messageStatisticData.smsReceivedRate * 100).toFixed(2)}%
                        </p>
                      </div>
                      <div className="bg-muted/20 p-4 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">短信点击率</h3>
                        <p className="text-3xl font-bold">
                          {(taskStats.callonStatisticData.messageStatisticData.smsUrlAccessRate * 100).toFixed(2)}%
                        </p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-lg font-medium mb-4">短信类型分布</h3>
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>短信类型</TableHead>
                              <TableHead>发送数</TableHead>
                              <TableHead>发送成功数</TableHead>
                              <TableHead>接收数</TableHead>
                              <TableHead>成功率</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow>
                              <TableCell>文本短信</TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.textSmsSendCount}
                              </TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.textSmsSendSuccessCount}
                              </TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.textSmsReceivedCount}
                              </TableCell>
                              <TableCell>
                                {(
                                  taskStats.callonStatisticData.messageStatisticData.textSmsSendSuccessRate * 100
                                ).toFixed(2)}
                                %
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell>视频短信</TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.videoSmsSendCount}
                              </TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.videoSmsSendSuccessCount}
                              </TableCell>
                              <TableCell>
                                {taskStats.callonStatisticData.messageStatisticData.videoSmsReceivedCount}
                              </TableCell>
                              <TableCell>
                                {(
                                  taskStats.callonStatisticData.messageStatisticData.videoSmsSendSuccessRate * 100
                                ).toFixed(2)}
                                %
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="callRecords">
                    <div className="space-y-6">
                      {/* 通话记录列表 */}
                      <div>
                        <h3 className="text-lg font-medium mb-4">通话记录列表</h3>
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>手机号码</TableHead>
                                <TableHead>接通状态</TableHead>
                                <TableHead>接通类型</TableHead>
                                <TableHead>通话时长</TableHead>
                                <TableHead>意向等级</TableHead>
                                <TableHead>开始时间</TableHead>
                                <TableHead>结束时间</TableHead>
                                <TableHead>操作</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {taskStats?.callRecords?.map((record: any, index: number) => (
                                <TableRow key={index}>
                                  <TableCell>{record.phone}</TableCell>
                                  <TableCell>
                                    {record.isConnected === 2 ? (
                                      <span className="text-green-600">已接通</span>
                                    ) : (
                                      <span className="text-red-600">未接通</span>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    {record.connectType === 1 ? "视频接通" : record.connectType === 2 ? "语音接通" : "-"}
                                  </TableCell>
                                  <TableCell>{record.holdingTime || 0}秒</TableCell>
                                  <TableCell>
                                    <span
                                      className={`px-2 py-1 rounded-full text-xs ${
                                        record.intention === "A"
                                          ? "bg-green-100 text-green-800"
                                          : record.intention === "B"
                                          ? "bg-blue-100 text-blue-800"
                                          : record.intention === "C"
                                          ? "bg-yellow-100 text-yellow-800"
                                          : record.intention === "D"
                                          ? "bg-orange-100 text-orange-800"
                                          : "bg-gray-100 text-gray-800"
                                      }`}
                                    >
                                      {record.intention || "-"}
                                    </span>
                                  </TableCell>
                                  <TableCell>{record.callStartTime || "-"}</TableCell>
                                  <TableCell>{record.callEndTime || "-"}</TableCell>
                                  <TableCell>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        setSelectedRecord(record);
                                        setShowRecordDetail(true);
                                      }}
                                    >
                                      详情
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))}
                              {(!taskStats?.callRecords || taskStats.callRecords.length === 0) && (
                                <TableRow>
                                  <TableCell colSpan={8} className="text-center py-4">
                                    暂无通话记录
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      </div>

                      {/* 通话记录详情对话框 */}
                      {showRecordDetail && selectedRecord && (
                        <Dialog open={showRecordDetail} onOpenChange={setShowRecordDetail}>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>通话记录详情</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">手机号码</p>
                                  <p>{selectedRecord.phone}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">客户名称</p>
                                  <p>{selectedRecord.userName || "-"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">接通状态</p>
                                  <p>
                                    {selectedRecord.isConnected === 2 ? (
                                      <span className="text-green-600">已接通</span>
                                    ) : (
                                      <span className="text-red-600">未接通</span>
                                    )}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">接通类型</p>
                                  <p>
                                    {selectedRecord.connectType === 1
                                      ? "视频接通"
                                      : selectedRecord.connectType === 2
                                      ? "语音接通"
                                      : "-"}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">通话时长</p>
                                  <p>{selectedRecord.holdingTime || 0}秒</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">意向等级</p>
                                  <p>
                                    <span
                                      className={`px-2 py-1 rounded-full text-xs ${
                                        selectedRecord.intention === "A"
                                          ? "bg-green-100 text-green-800"
                                          : selectedRecord.intention === "B"
                                          ? "bg-blue-100 text-blue-800"
                                          : selectedRecord.intention === "C"
                                          ? "bg-yellow-100 text-yellow-800"
                                          : selectedRecord.intention === "D"
                                          ? "bg-orange-100 text-orange-800"
                                          : "bg-gray-100 text-gray-800"
                                      }`}
                                    >
                                      {selectedRecord.intention || "-"}
                                    </span>
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">开始时间</p>
                                  <p>{selectedRecord.callStartTime || "-"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">接通时间</p>
                                  <p>{selectedRecord.callOnTime || "-"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">结束时间</p>
                                  <p>{selectedRecord.callEndTime || "-"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">等待时间</p>
                                  <p>{selectedRecord.waitingTime || 0}秒</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">振铃时间</p>
                                  <p>{selectedRecord.ringingDuration || 0}秒</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">挂断方</p>
                                  <p>{selectedRecord.hangupBy === "1" ? "被叫挂断" : "主叫挂断"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">挂断节点</p>
                                  <p>{selectedRecord.hungupNode || "-"}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">完播率</p>
                                  <p>{selectedRecord.completionRate || 0}%</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">总交互轮次</p>
                                  <p>{selectedRecord.totalInteractNum || 0}</p>
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-muted-foreground">有效交互轮次</p>
                                  <p>{selectedRecord.effectiveInteractNum || 0}</p>
                                </div>
                              </div>

                              {/* 通话文本 */}
                              {selectedRecord.callDetailText && selectedRecord.callDetailText.length > 0 && (
                                <div className="mt-4">
                                  <h3 className="text-lg font-medium mb-2">通话文本</h3>
                                  <div className="border rounded-md p-4 max-h-60 overflow-y-auto">
                                    {selectedRecord.callDetailText.map((text: any, index: number) => (
                                      <div key={index} className="mb-2">
                                        {text.AI && (
                                          <div className="flex items-start mb-1">
                                            <div className="bg-blue-100 rounded-lg p-2 max-w-[80%]">
                                              <p className="text-sm font-medium text-blue-800">AI:</p>
                                              <p className="text-sm">{text.AI}</p>
                                            </div>
                                          </div>
                                        )}
                                        {text.CUST && (
                                          <div className="flex items-start justify-end mb-1">
                                            <div className="bg-gray-100 rounded-lg p-2 max-w-[80%]">
                                              <p className="text-sm font-medium text-gray-800">客户:</p>
                                              <p className="text-sm">{text.CUST}</p>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* 对话标签 */}
                              {selectedRecord.conversationLabels && selectedRecord.conversationLabels.length > 0 && (
                                <div className="mt-4">
                                  <h3 className="text-lg font-medium mb-2">对话标签</h3>
                                  <div className="flex flex-wrap gap-2">
                                    {selectedRecord.conversationLabels.map((label: string, index: number) => (
                                      <span
                                        key={index}
                                        className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                                      >
                                        {label}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* 意图标签 */}
                              {selectedRecord.intentLevelAndLabels && selectedRecord.intentLevelAndLabels.length > 0 && (
                                <div className="mt-4">
                                  <h3 className="text-lg font-medium mb-2">意图标签</h3>
                                  <div className="space-y-2">
                                    {selectedRecord.intentLevelAndLabels.map((item: any, index: number) => (
                                      <div key={index} className="border rounded-md p-3">
                                        <p className="font-medium mb-1">
                                          意图等级: <span className="font-normal">{item.intention}</span>
                                        </p>
                                        <div className="flex flex-wrap gap-2">
                                          {item.labels.map((label: string, labelIndex: number) => (
                                            <span
                                              key={labelIndex}
                                              className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                                            >
                                              {label}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* 录音链接 */}
                              {selectedRecord.fullAudioUrl && (
                                <div className="mt-4">
                                  <h3 className="text-lg font-medium mb-2">录音</h3>
                                  <audio controls className="w-full">
                                    <source src={selectedRecord.fullAudioUrl} type="audio/mpeg" />
                                    您的浏览器不支持音频播放
                                  </audio>
                                </div>
                              )}
                            </div>
                            <DialogFooter>
                              <Button onClick={() => setShowRecordDetail(false)}>关闭</Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-lg font-medium">未找到任务</p>
              <p className="text-muted-foreground">无法获取任务ID为 {taskId} 的任务详情</p>
              <Button className="mt-4" onClick={() => window.history.back()}>
                返回
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </DashboardShell>
  )
}