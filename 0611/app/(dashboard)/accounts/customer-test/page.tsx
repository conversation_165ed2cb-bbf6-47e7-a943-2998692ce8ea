"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { CustomerType } from "@/app/types/customer"

export default function CustomerTestPage() {
  const router = useRouter()
  const [customers, setCustomers] = useState<CustomerType[]>([])
  const [loading, setLoading] = useState(true)

  // 加载客户数据
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const response = await fetch('/api/customers', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          },
          credentials: 'include'
        })

        if (!response.ok) {
          throw new Error('获取客户列表失败')
        }

        const data = await response.json()
        if (data.success && data.data) {
          setCustomers(data.data)
        } else {
          setCustomers([])
        }
      } catch (error) {
        console.error('获取客户列表失败:', error)
        toast({
          title: "获取数据失败",
          description: "无法获取客户列表数据，请稍后重试",
          variant: "destructive"
        })
        setCustomers([])
      } finally {
        setLoading(false)
      }
    }

    fetchCustomers()
  }, [])

  return (
    <DashboardShell>
      <DashboardHeader heading="客户测试页面">
        <Button variant="outline" asChild>
          <Link href="/accounts/customer">返回客户列表</Link>
        </Button>
      </DashboardHeader>

      <Card>
        <CardHeader>
          <CardTitle>客户列表 - 测试不同ID格式</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">加载中...</div>
          ) : customers.length > 0 ? (
            <div className="space-y-4">
              {customers.map((customer) => (
                <div key={customer.id} className="border p-4 rounded-md">
                  <h3 className="font-bold">{customer.name || customer.username}</h3>
                  <p>ID: {customer.id}</p>
                  <p>用户名: {customer.username}</p>
                  <p>邮箱: {customer.email}</p>
                  <div className="flex space-x-2 mt-2">
                    <Button size="sm" asChild>
                      <Link href={`/accounts/customer/${customer.username}`}>
                        通过用户名访问
                      </Link>
                    </Button>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/accounts/customer/${customer.id}`}>
                        通过ID访问
                      </Link>
                    </Button>
                    <Button size="sm" variant="secondary" asChild>
                      <Link href={`/accounts/customer/${customer.email}`}>
                        通过邮箱访问
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">没有找到客户数据</div>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  )
}
