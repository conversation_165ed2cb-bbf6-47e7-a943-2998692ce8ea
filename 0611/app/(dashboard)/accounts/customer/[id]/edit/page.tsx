"use client"

import logger from '@/lib/utils/logger';

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Loader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { provinces, getCitiesByProvince, getDistrictsByCity } from "@/app/lib/data/area-data-adapter"

interface CustomerData {
  id: string
  name: string
  username: string
  email: string
  phone: string
  accountType: string
  industry: string
  address: string
  description: string
  status: string
  province?: string
  city?: string
  district?: string
}

export default function CustomerEditPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const customerId = params.id

  const [customer, setCustomer] = useState<CustomerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editData, setEditData] = useState({
    name: "",
    email: "",
    phone: "",
    accountType: "enterprise",
    industry: "",
    address: "",
    description: "",
    province: "",
    city: "",
    district: ""
  })

  // 删除contact字段，因为后端没有这个字段

  useEffect(() => {
    async function fetchCustomer() {
      try {
        setLoading(true)
        const response = await fetch(`/api/customers/${customerId}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error('获取客户信息失败')
        }

        const data = await response.json()

        if (data.success && data.data) {
          setCustomer(data.data)
          // 解析地址字段，提取省市区信息
          let province = '';
          let city = '';
          let district = '';
          let detailAddress = '';

          // 直接使用服务器返回的省市区信息
          province = data.data.province || '';
          city = data.data.city || '';
          district = data.data.district || '';
          detailAddress = data.data.address || '';

          setEditData({
            name: data.data.name || '',
            email: data.data.email || '',
            phone: data.data.phone || '',
            accountType: data.data.accountType || 'enterprise',
            industry: data.data.industry || '',
            address: detailAddress,
            description: data.data.description || '',
            province: data.data.province || '',
            city: data.data.city || '',
            district: data.data.district || ''
          })
        } else {
          throw new Error(data.message || '获取客户信息失败')
        }
      } catch (error) {
        logger.error('获取客户信息错误:', error)
        toast({
          title: "获取客户信息失败",
          description: error instanceof Error ? error.message : '获取客户信息时发生错误',
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchCustomer()
  }, [customerId])

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="加载客户信息">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">正在加载客户信息...</p>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  // 如果找不到客户，显示错误信息
  if (!customer && !loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="客户不存在">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground mb-4">未找到ID为 {customerId} 的客户信息</p>
            <Button onClick={() => router.push("/accounts/customer")}>返回客户列表</Button>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  // 提交编辑
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setSaving(true)

      // 打印请求数据，便于调试
      logger.log('发送编辑请求，数据:', editData);

      // 确保使用用户的实际ID而不是用户名
      const userId = customer?.id;
      logger.log('使用用户ID:', userId);

      if (!userId) {
        throw new Error('无法获取用户ID，请刷新页面重试');
      }

      const response = await fetch(`/api/customers/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          name: editData.name,
          email: editData.email,
          phone: editData.phone,
          industry: editData.industry,
          province: editData.province,
          city: editData.city,
          district: editData.district,
          address: editData.address,
          description: editData.description
        })
      })

      if (!response.ok) {
        throw new Error('更新客户信息失败')
      }

      const data = await response.json()

      if (data.success) {
        toast({
          title: "更新成功",
          description: "客户信息已成功更新",
          variant: "success"
        })
        router.push(`/accounts/customer/${customerId}`)
      } else {
        throw new Error(data.message || '更新客户信息失败')
      }
    } catch (error) {
      console.error('更新客户信息错误:', error)
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : '更新客户信息时发生错误',
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={`编辑 ${customer.name} 的账户信息`}>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
      </DashboardHeader>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>编辑客户信息</CardTitle>
            <CardDescription>修改客户账户的基本信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">账户名称</Label>
                <Input
                  id="name"
                  value={editData.name}
                  onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                  required
                />
              </div>
              {/* 联系人字段已移除，因为后端API不支持 */}
              <div className="space-y-2">
                <Label htmlFor="email">邮箱</Label>
                <Input
                  id="email"
                  type="email"
                  value={editData.email}
                  onChange={(e) => setEditData({ ...editData, email: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">手机号</Label>
                <Input
                  id="phone"
                  value={editData.phone}
                  onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="accountType">账户类型</Label>
                <Input
                  id="accountType"
                  value={editData.accountType === 'enterprise' ? '企业账户' : '个人账户'}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground">账户类型不可直接修改，需要通过认证类型变更来实现</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="industry">行业</Label>
                <Input
                  id="industry"
                  value={editData.industry}
                  onChange={(e) => setEditData({ ...editData, industry: e.target.value })}
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="location">所在地区</Label>
                <div className="grid grid-cols-3 gap-3">
                  <Select
                    value={editData.province || ''}
                    onValueChange={(value) => setEditData({ ...editData, province: value, city: '', district: '' })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择省份" />
                    </SelectTrigger>
                    <SelectContent>
                      {provinces.map((province) => (
                        <SelectItem key={province.value} value={province.value}>
                          {province.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={editData.city || ''}
                    onValueChange={(value) => setEditData({ ...editData, city: value, district: '' })}
                    disabled={!editData.province}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择城市" />
                    </SelectTrigger>
                    <SelectContent>
                      {editData.province && getCitiesByProvince(editData.province).map((city) => (
                        <SelectItem key={city.value} value={city.value}>
                          {city.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select
                    value={editData.district || ''}
                    onValueChange={(value) => setEditData({ ...editData, district: value })}
                    disabled={!editData.city}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择区县" />
                    </SelectTrigger>
                    <SelectContent>
                      {editData.province && editData.city &&
                        getDistrictsByCity(editData.province, editData.city).map((district) => (
                          <SelectItem key={district.value} value={district.value}>
                            {district.label}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">详细地址</Label>
                <Input
                  id="address"
                  value={editData.address}
                  onChange={(e) => setEditData({ ...editData, address: e.target.value })}
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">描述</Label>
                <Textarea
                  id="description"
                  value={editData.description}
                  onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                  className="min-h-[100px]"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              取消
            </Button>
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                "保存"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </DashboardShell>
  )
}

