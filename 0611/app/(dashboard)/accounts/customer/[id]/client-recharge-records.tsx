"use client"

import { useEffect, useState } from "react"
import { Card } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"
import { format, isWithinInterval, startOfDay, endOfDay } from "date-fns"
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Download, AlertCircle, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import { DateRange } from "react-day-picker"



interface ClientRechargeRecordsProps {
  customerId: string
}

export default function ClientRechargeRecords({ customerId }: ClientRechargeRecordsProps) {
  const [records, setRecords] = useState<any[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalRecords, setTotalRecords] = useState(0)
  const [dateRange, setDateRange] = useState<{ from: Date; to?: Date } | undefined>()
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(false)
  const [type, setType] = useState<string | undefined>(undefined)
  const itemsPerPage = 10

  const fetchRechargeRecords = async (page = 1) => {
    try {
      setLoading(true)
      // 构建查询参数
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', itemsPerPage.toString())

      if (type && type !== 'all') {
        params.append('type', type)
      }

      if (dateRange?.from) {
        params.append('startDate', dateRange.from.toISOString())
      }

      if (dateRange?.to) {
        params.append('endDate', dateRange.to.toISOString())
      }

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      // 直接使用ID查询余额记录
      const apiUrl = `/api/customers/${customerId}/recharge-records?${params.toString()}`
      console.log('使用的余额记录API URL:', apiUrl)
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('获取余额记录失败')
      }

      const data = await response.json()
      console.log('余额记录API响应:', data)

      if (data.success && data.data) {
        // 处理API返回的数据结构
        if (data.data.transactions) {
          setRecords(data.data.transactions)
          setTotalPages(data.data.pagination.totalPages)
          setTotalRecords(data.data.pagination.total)
          setCurrentPage(data.data.pagination.page)
        } else {
          // 兼容旧的API结构
          setRecords(data.data)
          setTotalPages(1)
          setTotalRecords(data.data.length)
        }
      } else {
        setRecords([])
        setTotalPages(1)
        setTotalRecords(0)
      }
    } catch (error) {
      console.error('获取余额记录失败:', error)
      setRecords([])
      setTotalPages(1)
      setTotalRecords(0)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRechargeRecords(1)
  }, [customerId, dateRange, searchTerm, type])

  // 处理分页变化
  const handlePageChange = (page: number) => {
    fetchRechargeRecords(page)
  }

  const exportData = async (format: 'csv' | 'excel') => {
    try {
      toast({
        title: `正在导出${format === 'csv' ? 'CSV' : 'Excel'}文件`,
        description: "请稍候...",
      })

      // 获取所有交易记录用于导出
      const response = await fetch(`/api/customers/${customerId}/recharge-records?page=1&limit=1000${type ? `&type=${type}` : ''}${dateRange?.from ? `&startDate=${dateRange.from.toISOString()}` : ''}${dateRange?.to ? `&endDate=${dateRange.to.toISOString()}` : ''}${searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : ''}`)

      if (!response.ok) {
        throw new Error('获取数据失败')
      }

      const data = await response.json()
      const allRecords = data.data.transactions

      if (!allRecords || allRecords.length === 0) {
        throw new Error('没有可导出的交易记录')
      }

      // 准备导出数据
      const headers = ["交易时间", "交易类型", "交易金额", "交易后余额", "授信额度变动", "支付方式", "操作人", "备注"]

      // 准备行数据
      const rows = allRecords.map(record => {
        const typeText =
          record.type === 'recharge' ? '充值' :
          record.type === 'deduct' ? '扣费' :
          record.type === 'refund' ? '退款' :
          record.type === 'credit_add' ? '授信额度增加' :
          record.type === 'credit_subtract' ? '授信额度减少' :
          record.type === 'credit_set' ? '授信额度调整' : '其他'

        return [
          new Date(record.createdAt).toLocaleString('zh-CN'),
          typeText,
          record.amount,
          record.balanceAfter,
          record.creditLimitChange || 0,
          record.paymentMethod || '-',
          record.admin?.name || '-',
          `"${(record.remarks || '').replace(/"/g, '""')}"`
        ]
      })

      // 根据格式生成内容
      let fileContent = ''
      let mimeType = ''

      if (format === 'csv') {
        // CSV格式
        fileContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
        mimeType = 'text/csv;charset=utf-8'
      } else {
        // Excel格式 (简单CSV，但使用xls扩展名)
        fileContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
        mimeType = 'application/vnd.ms-excel'
      }

      // 创建Blob对象
      const blob = new Blob([fileContent], { type: mimeType })
      const url = URL.createObjectURL(blob)

      // 创建下载链接并触发下载
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `客户余额记录_${new Date().toISOString().slice(0, 10)}.${format === 'excel' ? 'xls' : format}`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 释放URL对象
      URL.revokeObjectURL(url)

      toast({
        title: `${format === 'csv' ? 'CSV' : 'Excel'}文件导出成功`,
        description: "文件已保存到您的下载文件夹",
        variant: "success",
      })

    } catch (error) {
      console.error(`导出余额记录错误:`, error)
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-4 mb-6">
        <h3 className="text-lg font-medium text-blue-800">交易筛选</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* 交易类型选择 */}
          <Select
            value={type || 'all'}
            onValueChange={(value) => {
              setType(value === 'all' ? undefined : value)
            }}
          >
            <SelectTrigger className="w-full border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
              <SelectValue placeholder="交易类型" />
            </SelectTrigger>
            <SelectContent className="border-blue-100 shadow-lg">
              <SelectItem value="all" className="py-2">全部类型</SelectItem>
              <SelectItem value="recharge" className="py-2 text-blue-600">充值</SelectItem>
              <SelectItem value="deduct" className="py-2 text-amber-600">扣费</SelectItem>
              <SelectItem value="refund" className="py-2 text-green-600">退款</SelectItem>
              <SelectItem value="credit_add" className="py-2 text-green-600">授信额度增加</SelectItem>
              <SelectItem value="credit_subtract" className="py-2 text-orange-600">授信额度减少</SelectItem>
              <SelectItem value="credit_set" className="py-2 text-blue-600">授信额度调整</SelectItem>
            </SelectContent>
          </Select>

          {/* 日期选择 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start text-left font-normal border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
                <CalendarIcon className="mr-2 h-5 w-5 text-blue-600" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "yyyy-MM-dd")} ~{" "}
                      {format(dateRange.to, "yyyy-MM-dd")}
                    </>
                  ) : (
                    format(dateRange.from, "yyyy-MM-dd")
                  )
                ) : (
                  <span>选择日期范围</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0 border-blue-100 shadow-lg" align="start">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={(range) => {
                  setDateRange(range || { from: undefined, to: undefined })
                }}
                initialFocus
              />
              <div className="p-3 border-t border-blue-100">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setDateRange(undefined)
                  }}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                >
                  清除日期筛选
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* 搜索框 */}
          <div className="relative">
            <Input
              placeholder="搜索描述、操作人或金额..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border-blue-100 hover:bg-blue-50 py-5 rounded-lg pl-10"
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </div>
          </div>
        </div>

        <div className="flex justify-between items-center">
          {loading && (
            <div className="flex items-center text-blue-500">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span>加载中...</span>
            </div>
          )}
          <div className="flex-1"></div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg">
                <Download className="mr-2 h-4 w-4 text-white" />
                导出数据
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="border-blue-100 shadow-lg">
              <DropdownMenuItem onClick={() => exportData('csv')} className="py-2 hover:bg-blue-50">
                <svg className="mr-2 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                导出为CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => exportData('excel')} className="py-2 hover:bg-blue-50">
                <svg className="mr-2 h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                导出为Excel
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {loading && records.length === 0 ? (
        <div className="py-8 text-center text-muted-foreground">正在加载余额记录...</div>
      ) : records.length === 0 ? (
        <div className="py-8 text-center text-muted-foreground">没有找到余额记录</div>
      ) : (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>时间</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>金额</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>描述</TableHead>
              <TableHead>操作人</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {records.map((record) => (
              <TableRow key={record.id}>
                <TableCell>{new Date(record.createdAt).toLocaleString('zh-CN')}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={`font-medium px-3 py-1.5 rounded-full text-sm ${record.type === 'recharge' ? 'bg-green-100 text-green-800 hover:bg-green-100 border-green-200' :
                              record.type === 'deduct' ? 'bg-red-100 text-red-800 hover:bg-red-100 border-red-200' :
                              record.type === 'refund' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100 border-yellow-200' :
                              record.type === 'credit_add' ? 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100 border-emerald-200' :
                              record.type === 'credit_subtract' ? 'bg-orange-100 text-orange-800 hover:bg-orange-100 border-orange-200' :
                              record.type === 'credit_set' ? 'bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200' :
                              'bg-gray-100 text-gray-800 border-gray-200'}`}
                  >
                    {record.type === 'recharge' ? '充值' :
                     record.type === 'deduct' ? '扣费' :
                     record.type === 'refund' ? '退款' :
                     record.type === 'credit_add' ? '授信额度增加' :
                     record.type === 'credit_subtract' ? '授信额度减少' :
                     record.type === 'credit_set' ? '授信额度调整' : '其他'}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className={record.amount > 0 ? "text-green-600" : "text-red-600"}>
                    {record.amount > 0 ? "+" : ""}¥{Math.abs(record.amount).toLocaleString()}
                  </span>
                </TableCell>
                <TableCell>
                  <span className="text-green-600">成功</span>
                </TableCell>
                <TableCell>{record.remarks || record.description || '-'}</TableCell>
                <TableCell>{record.admin?.name || record.operator || '-'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            显示 {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, totalRecords)} 条，共 {totalRecords} 条
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || loading}
            >
              <ChevronLeft className="h-4 w-4" />
              上一页
            </Button>
            <div className="text-sm">
              第 {currentPage} 页，共 {totalPages} 页
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || loading}
            >
              下一页
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}