"use client"

import logger from '@/lib/utils/logger';

/**
 * 仪表板布局组件
 * 为仪表板页面提供统一的布局结构，包括侧边栏、顶部导航和页脚
 *
 * 特性：
 * - 响应式设计，支持移动端和桌面端
 * - 可折叠侧边栏，状态保存在 localStorage
 * - 用户角色切换（管理员/客户）
 * - 主题切换
 * - 通知中心集成
 * - 用户菜单（个人资料、账户设置等）
 *
 * 状态管理：
 * - 侧边栏折叠状态
 * - 用户类型和信息
 * - 未读通知数量
 *
 * @example
 * ```tsx
 * // 仪表板页面会自动被包裹在这个布局中
 * <DashboardLayout>
 *   <YourDashboardPage />
 * </DashboardLayout>
 * ```
 */

// 添加全局类型声明
 declare global {
  interface Window {
    __isLoadingUserInfo?: boolean;
  }
}

import React, { useState, useEffect, createContext, useContext, useCallback } from "react"
import { usePathname, useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { Toaster } from "react-hot-toast"
import { useMediaQuery } from "@/hooks/use-media-query"
import { Sidebar } from "@/components/ui/sidebar/sidebar"
import { SidebarItems } from "@/components/ui/sidebar/sidebar-items"
import { AuthSidebar } from "@/components/auth-sidebar"
import { MenuUpdateListener } from "@/components/menu-update-listener"
import { BellIcon } from "@/components/icons"
import { SidebarProvider, useSidebar } from "@/components/ui/sidebar/use-sidebar"
import type { User } from "@/types/user"
import { SimpleTooltip } from "@/components/ui/simple-tooltip"
import { ThemeSwitcher } from "@/components/theme-switcher"
import { NotificationButton } from "@/components/shared/notification-button"
import { EnhancedUserAvatar } from "@/components/enhanced-user-avatar"
// 使用新的自管理状态的通知按钮组件

import { DashboardContext, DashboardContextValue, MenuItem } from "./hooks/use-dashboard"

// 路径映射函数
function getCorrectPath(path: string): string {
  if (!path || path === "#") return "#";
  // 如果路径不以"/"开头，添加"/"
  return path.startsWith("/") ? path : `/${path}`;
}

// 图标映射函数 - 返回字符串图标名称
function getMenuIcon(iconName?: string): string | undefined {
  if (!iconName) return undefined;

  // 返回图标名称供 getIconByName 使用
  // 这里不直接返回组件，而是返回图标名称字符串
  return iconName;
}



interface DashboardLayoutProps {
  /** 子组件，将被包裹在仪表板布局中 */
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const isMobile = useMediaQuery("(max-width: 768px)")

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userInfo, setUserInfo] = useState<User | null>(null)
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])

  // 使用 NextAuth 会话
  const { data: session, status } = useSession()

  // 监听会话状态变化
  useEffect(() => {
    if (status === 'loading') {
      // 会话正在加载中，保持加载状态
      setIsLoading(true);
    } else if (status === 'authenticated' && session?.user) {
      // 如果有有效会话，直接使用会话中的用户信息
      logger.log('使用NextAuth会话中的用户信息:', session.user);
      setUserInfo(session.user as User);
      setIsLoading(false);
    } else if (status === 'unauthenticated') {
      // 如果没有有效会话，重定向到登录页
      logger.log('未认证，重定向到登录页');
      router.push(`/login?returnUrl=${encodeURIComponent(pathname)}`);
    }
  }, [session, status, router, pathname])

  return (
    <SidebarProvider>
      <DashboardLayoutContent
        isLoading={isLoading}
        error={error}
        userInfo={userInfo}
        menuItems={menuItems}
        setIsLoading={setIsLoading}
        setError={setError}
        setUserInfo={setUserInfo}
        setMenuItems={setMenuItems}
        pathname={pathname}
        router={router}
        isMobile={isMobile}
        status={status}
      >
        {children}
      </DashboardLayoutContent>
    </SidebarProvider>
  );
}

// 内部组件，可以安全使用useSidebar
function DashboardLayoutContent({
  children,
  isLoading,
  error,
  userInfo,
  menuItems,
  setIsLoading,
  setError,
  setUserInfo,
  setMenuItems,
  pathname,
  router,
  isMobile,
  status
}: {
  children: React.ReactNode;
  isLoading: boolean;
  error: string | null;
  userInfo: User | null;
  menuItems: MenuItem[];
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setError: React.Dispatch<React.SetStateAction<string | null>>;
  setUserInfo: React.Dispatch<React.SetStateAction<User | null>>;
  setMenuItems: React.Dispatch<React.SetStateAction<MenuItem[]>>;
  pathname: string;
  router: any;
  isMobile: boolean;
  status: string;
}) {
  // 使用useSidebar钩子
  const sidebar = useSidebar();
  const toggleSidebar = sidebar.toggleSidebar;
  // 使用useCallback包装fetchUserInfo函数，避免不必要的重新创建
  const fetchUserInfo = useCallback(async () => {
    // 防止重复请求 - 使用静态变量跟踪状态
    if (window.__isLoadingUserInfo) {
      logger.log('已经在加载中，跳过重复请求');
      return;
    }

    // 设置全局标志
    window.__isLoadingUserInfo = true;

    try {
      setIsLoading(true)
      setError(null)
      logger.debug("开始获取用户信息...")

      // 先尝试使用 NextAuth 会话
      try {
        const session = await fetch('/api/auth/session');
        const sessionData = await session.json();

        if (sessionData && sessionData.user) {
          logger.log('从 NextAuth 获取到用户信息:', sessionData.user);
          setUserInfo(sessionData.user);
          setIsLoading(false);
          // 如果有 NextAuth 会话，不需要继续处理
          window.__isLoadingUserInfo = false;
          return;
        }
      } catch (e) {
        logger.error('从 NextAuth 获取用户信息失败:', e);
        // 如果 NextAuth 失败，回退到使用原有的方式
      }

      // 尝试从 sessionStorage 获取基本用户信息
      try {
        const userDisplayStr = sessionStorage.getItem('user_display')
        if (userDisplayStr) {
          const userDisplay = JSON.parse(userDisplayStr)
          // 先使用基本信息填充用户信息，减少空白闪烁
          setUserInfo({
            id: userDisplay.id,
            username: userDisplay.username,
            name: userDisplay.name,
            email: userDisplay.email,
            image: userDisplay.image,
            role: {
              name: userDisplay.roleName,
              type: userDisplay.roleType
            }
          } as User)

          // 减少加载状态的显示时间
          setIsLoading(false)
        }
      } catch (e) {
        logger.error('从 sessionStorage 获取用户信息失败:', e)
        // 可能是无痕模式下的存储问题，继续处理
      }

      // 从 sessionStorage 获取用户信息
      let sessionId = null;
      let userInfoFromSession = null;

      try {
        // 尝试从 sessionStorage 获取用户信息
        const userInfoStr = sessionStorage.getItem('user_info');
        if (userInfoStr) {
          userInfoFromSession = JSON.parse(userInfoStr);
          logger.log('从 sessionStorage 获取到用户信息:', userInfoFromSession);
        }

        // 尝试从 cookie 获取会话 ID
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'session_id') {
            sessionId = value;
            logger.log('从 cookie 获取到会话 ID:', sessionId);
            break;
          }
        }

        // 验证会话 ID 与用户信息中的会话 ID 是否匹配
        if (sessionId && userInfoFromSession && sessionId !== userInfoFromSession.sessionId) {
          logger.warn('会话 ID 不匹配，可能是会话已过期或被篡改');
          // 清除不匹配的会话数据
          sessionStorage.removeItem('user_info');
          userInfoFromSession = null;
        }
      } catch (e) {
        logger.error('获取用户会话信息失败:', e);
      }

      // 使用 no-cache 选项避免缓存问题
      // 添加时间戳参数避免缓存
      const timestamp = Date.now();
      const userId = userInfoFromSession?.id;

      // 构建请求 URL 和头信息
      const requestUrl = `/api/auth/current-user?t=${timestamp}${userId ? `&userId=${userId}` : ''}`;
      const headers = {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
      };

      // 如果有会话 ID，添加到请求头
      if (sessionId) {
        headers['X-Session-ID'] = sessionId;
      }

      // 如果有用户信息，添加用户 ID 和用户名到请求头
      if (userInfoFromSession) {
        headers['X-User-ID'] = userInfoFromSession.id;
        headers['X-Username'] = userInfoFromSession.username;
      }

      const res = await fetch(requestUrl, {
        credentials: 'include',
        headers,
        cache: "no-store"
      })

      logger.error(`发送用户信息请求，时间戳: ${timestamp}${userId ? ', 用户ID: ' + userId : ''}`)

      if (!res.ok) {
        console.error(`获取用户信息失败: ${res.status} ${res.statusText}`)
        setError(`无法获取用户信息 (${res.status})`)
        // 添加时间戳参数，避免缓存问题
        router.push(`/login?t=${Date.now()}`)
        return
      }

      const data = await res.json()
      console.log("获取到的用户数据:", data)

      if (!data || !data.success || !data.data) {
        console.error("获取到的用户数据格式不正确:", data)
        setError("无效的用户数据")
        // 添加时间戳参数，避免缓存问题
        router.push(`/login?t=${Date.now()}`)
        return
      }

      // 打印服务器返回的用户信息
      console.log('服务器返回的用户信息:', data.data)
      console.log('用户ID:', data.data.id)
      console.log('用户名:', data.data.username)
      console.log('角色信息:', data.data.role)

      // 如果有会话信息，存储到 sessionStorage
      if (data.data.session) {
        logger.log('收到会话信息:', data.data.session);
        try {
          // 将会话信息存储到 sessionStorage
          sessionStorage.setItem('session_info', JSON.stringify(data.data.session));

          // 更新用户信息中的会话 ID
          const userInfoStr = sessionStorage.getItem('user_info');
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            userInfo.sessionId = data.data.session.id;
            sessionStorage.setItem('user_info', JSON.stringify(userInfo));
          } else {
            // 如果没有用户信息，创建新的用户信息
            const userInfo = {
              id: data.data.id,
              username: data.data.username,
              name: data.data.name || data.data.username,
              email: data.data.email,
              roleName: data.data.role?.name || '普通用户',
              roleType: data.data.role?.type || 'user',
              image: data.data.image,
              sessionId: data.data.session.id,
              loginTime: new Date().toISOString()
            };
            sessionStorage.setItem('user_info', JSON.stringify(userInfo));
          }

          // 将会话 ID 存储到 cookie
          document.cookie = `session_id=${data.data.session.id}; path=/; max-age=86400`;

          logger.log('会话信息已存储');
        } catch (e) {
          logger.error('存储会话信息失败:', e);
        }
      }

      // 更新用户信息
      setUserInfo(data.data)

      // 更新 sessionStorage 中的用户信息
      try {
        // 先获取当前的用户信息
        const currentUserInfo = sessionStorage.getItem('user_display')
        let userDisplayInfo = {
          id: data.data.id,
          username: data.data.username,
          name: data.data.name || data.data.username,
          email: data.data.email,
          roleName: data.data.role?.name || '普通用户',
          roleType: data.data.role?.type || 'user',
          image: data.data.image
        }

        // 如果当前有用户信息，对比是否一致
        if (currentUserInfo) {
          const parsedInfo = JSON.parse(currentUserInfo)
          if (parsedInfo.username !== data.data.username) {
            logger.error('检测到用户信息不一致，当前:', parsedInfo.username, '服务器:', data.data.username)
          }
        }

        // 尝试清除并重新设置用户信息
        try {
          sessionStorage.removeItem('user_display')
          sessionStorage.setItem('user_display', JSON.stringify(userDisplayInfo))
          console.log('更新后的用户显示信息:', userDisplayInfo)
        } catch (storageError) {
          console.log('存储用户信息失败，可能是无痕模式:', storageError)
          // 无痕模式下存储失败不影响核心功能
        }
      } catch (e) {
        console.error('更新 sessionStorage 中的用户信息失败:', e)
      }

      // 处理菜单数据
      if (data.data && data.data.role && data.data.role.menus && Array.isArray(data.data.role.menus)) {
        // 转换菜单数据
        const convertMenus = (menus: any[]): MenuItem[] => {
          return menus
            .filter(menu => menu.visible)
            .sort((a, b) => a.order - b.order)
            .map(menu => ({
              title: menu.name,
              href: getCorrectPath(menu.path),
              icon: getMenuIcon(menu.icon),
              customIconUrl: menu.customIconUrl || menu.iconUrl, // 添加对自定义图标URL的支持
              ...(menu.children && menu.children.length > 0
                ? {
                    children: convertMenus(menu.children)
                  }
                : {})
            }))
        }

        // 转换数据库中的菜单
        const dbMenus = convertMenus(data.data.role.menus)

        // 如果是管理员角色，添加额外的菜单项
        if (data.data.roleCode === 'ADMIN' || data.data.roleCode === 'admin') {
          // 添加任务管理相关菜单
          const taskMenu = dbMenus.find(item => item.title === '任务管理')
          if (!taskMenu) {
            dbMenus.push({
              title: '任务管理',
              href: '/tasks',
              icon: 'task',
              children: [
                {
                  title: '任务创建',
                  href: '/tasks/upload',
                  icon: 'upload'
                },
                {
                  title: '任务详情',
                  href: '/tasks/details',
                  icon: 'details'
                }
              ]
            })
          }

          // 添加系统设置相关菜单
          const settingsMenu = dbMenus.find(item => item.title === '系统设置')
          if (settingsMenu) {
            // 确保系统设置菜单有子菜单数组
            if (!settingsMenu.children) {
              settingsMenu.children = []
            }

            // 添加角色管理菜单
            if (!settingsMenu.children.some(item => item.title === '角色管理')) {
              settingsMenu.children.push({
                title: '角色管理',
                href: '/settings/roles',
                icon: 'role'
              })
            }

            // 添加菜单管理菜单
            if (!settingsMenu.children.some(item => item.title === '菜单设置')) {
              settingsMenu.children.push({
                title: '菜单设置',
                href: '/settings?tab=menu',
                icon: 'menu'
              })
            }
          } else {
            // 如果没有系统设置菜单，创建一个
            dbMenus.push({
              title: '系统设置',
              href: '/settings',
              icon: 'settings',
              children: [
                {
                  title: '角色管理',
                  href: '/settings/roles',
                  icon: 'role'
                },
                {
                  title: '菜单设置',
                  href: '/settings?tab=menu',
                  icon: 'menu'
                }
              ]
            })
          }
        }

        // 按照顺序排序菜单
        dbMenus.sort((a, b) => {
          const orderMap: Record<string, number> = {
            '仪表盘': 10,
            '任务管理': 20,
            '客户管理': 30,
            '费率管理': 40,
            '通知中心': 50,
            '网站设置': 100
          }

          const orderA = orderMap[a.title] || 999
          const orderB = orderMap[b.title] || 999
          return orderA - orderB
        })

        // 移除不需要显示的菜单项
        const filteredMenus = dbMenus.filter(menu => {
          // 移除用户中心
          if (menu.title === '用户中心') return false;

          // 移除账户管理
          if (menu.title === '账户管理') return false;

          // 不再隐藏系统管理菜单
          // if (menu.title === '系统管理') return false;

          return true;
        });
        setMenuItems(filteredMenus)
      } else {
        logger.warn("用户菜单数据不可用:", data.data?.role?.menus)
        setMenuItems([])
      }
    } catch (err) {
      console.error("获取用户信息时出错:", err)
      setError(err instanceof Error ? err.message : "未知错误")
      router.push("/login")
    } finally {
      // 延迟设置加载状态为完成，确保数据已完全加载
      setTimeout(() => {
        setIsLoading(false)
        // 重置全局标志
        window.__isLoadingUserInfo = false
      }, 500)
    }
  }, [])

  // 在组件挂载时获取用户信息和未读通知数量
  useEffect(() => {
    // 尝试使用原有方式获取用户信息
    fetchUserInfo();  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 移除status依赖，避免错误

  // 单独的useEffect用于头像更新事件
  useEffect(() => {
    // 监听头像更新事件
    const handleAvatarUpdate = (event: CustomEvent) => {
      logger.log('收到头像更新事件:', event.detail);
      if (event.detail && event.detail.imageUrl) {
        // 使用函数式更新，避免闭包捕获旧的userInfo
        setUserInfo(prevUserInfo => {
          if (!prevUserInfo) return null;
          logger.log('更新头像从', prevUserInfo.image, '到', event.detail.imageUrl);
          return {
            ...prevUserInfo,
            image: event.detail.imageUrl
          };
        });
      }
    };

    // 添加事件监听器
    logger.log('添加头像更新事件监听器');
    window.addEventListener('avatar-updated', handleAvatarUpdate as EventListener);

    // 清理函数
    return () => {
      logger.log('移除头像更新事件监听器');
      window.removeEventListener('avatar-updated', handleAvatarUpdate as EventListener);
    };
  }, []); // 不依赖于userInfo，避免循环更新

  // 检查是否需要强制刷新页面
  useEffect(() => {
    // 检查是否存在force_refresh cookie
    const checkForceRefresh = () => {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'force_refresh' && value === 'true') {
          logger.log('检测到强制刷新cookie，将刷新页面');

          // 删除cookie
          document.cookie = 'force_refresh=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

          // 刷新页面
          window.location.reload();
          return;
        }
      }
    };

    // 初始检查
    checkForceRefresh();

    // 设置定期检查
    const intervalId = setInterval(checkForceRefresh, 2000);

    // 清理函数
    return () => {
      clearInterval(intervalId);
    };
  }, []);

  // 添加系统设置状态但使用缓存/延迟加载
  const [systemSettings, setSystemSettings] = useState({
    siteName: "外呼管理系统",
    logo: "/logo.png"
  });

  // 使用useEffect清理，避免与其他钩子冲突
  useEffect(() => {
    // 延迟加载系统设置，避免与用户认证冲突
    let isMounted = true;
    const fetchSystemSettings = async () => {
      try {
        // 加载系统设置前检查组件是否仍然挂载
        if (!isMounted) return;

        const response = await fetch('/api/settings');
        const data = await response.json();

        // 再次检查组件是否仍然挂载
        if (!isMounted) return;

        if (data.success && data.data) {
          setSystemSettings({
            siteName: data.data.siteName || "外呼管理系统",
            logo: data.data.logo || "/logo.png"
          });
        }
      } catch (error) {
        logger.error("获取系统设置失败:", error);
      }
    };

    // 用户信息加载完成后再加载系统设置
    if (!isLoading && userInfo) {
      // 延迟加载系统设置，避免与初始渲染冲突
      const timer = setTimeout(() => {
        fetchSystemSettings();
      }, 1000);

      return () => {
        clearTimeout(timer);
        isMounted = false;
      };
    }

    return () => {
      isMounted = false;
    };
  }, [isLoading, userInfo]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-red-500">
          <h3 className="text-lg font-bold">加载失败</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // 上下文值
  const contextValue: DashboardContextValue = {
    userInfo,
    menuItems,
    isCollapsed: !sidebar.open, // 使用SidebarProvider的open状态
    toggleCollapsed: toggleSidebar, // 使用SidebarProvider的toggleSidebar
    isMobileView: isMobile
  };

  // 确保菜单项不为空
  const displayMenuItems = menuItems && menuItems.length > 0 ? menuItems : [
    {
      title: '仪表盘',
      href: '/dashboard',
      icon: 'DashboardOutlined'
    },
    {
      title: '任务管理',
      href: '/tasks',
      icon: 'ProjectOutlined',
      children: [
        {
          title: '任务创建',
          href: '/tasks/upload',
          icon: 'upload'
        },
        {
          title: '任务详情',
          href: '/tasks/details',
          icon: 'details'
        }
      ]
    },
    {
      title: '客户管理',
      href: '/customer-management',
      icon: 'TeamOutlined',
      children: [
        {
          title: '客户账户',
          href: '/customers',
          icon: 'Users'
        },
        {
          title: '费率管理',
          href: '/rates',
          icon: 'DollarOutlined'
        }
      ]
    },
    {
      title: '通知中心',
      href: '/notifications',
      icon: 'BellOutlined'
    },
    {
      title: '网站设置',
      href: '/settings',
      icon: 'SettingOutlined',
      children: [
        {
          title: '基本信息',
          href: '/settings?tab=basic',
          icon: 'InfoCircleOutlined'
        },
        {
          title: '主题设置',
          href: '/settings?tab=theme',
          icon: 'BgColorsOutlined'
        },
        {
          title: '功能开关',
          href: '/settings?tab=features',
          icon: 'ControlOutlined'
        },
        {
          title: '安全设置',
          href: '/settings?tab=security',
          icon: 'SafetyOutlined'
        },
        {
          title: '登录页设置',
          href: '/settings?tab=loginPage',
          icon: 'LoginOutlined'
        },
        {
          title: '菜单设置',
          href: '/settings?tab=menu',
          icon: 'menu'
        },
        {
          title: '角色管理',
          href: '/settings/roles',
          icon: 'Shield'
        },
        {
          title: '系统日志',
          href: '/settings/logs',
          icon: 'FileOutlined'
        }
      ]
    }
  ];

  return (
    <DashboardContext.Provider value={contextValue}>
      <div className="flex min-h-screen w-full">
        {/* 侧边栏 - 使用新的AuthSidebar组件 */}
        <AuthSidebar
          siteName={systemSettings.siteName}
          logoUrl={systemSettings.logo}
          isCollapsed={!sidebar.open}
          isMobile={isMobile}
          onToggleSidebar={toggleSidebar}
        />

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col min-h-screen">
          {/* 顶部导航栏 */}
          <header className="h-14 border-b border-border px-4 flex items-center justify-between sticky top-0 z-30 backdrop-blur-md bg-background/80 supports-[backdrop-filter]:bg-background/60 transition-all">
            <div className="flex items-center gap-2">
              {/* 移动端菜单按钮 */}
              {isMobile && (
                <button
                  className="p-2 rounded hover:bg-accent/80"
                  onClick={toggleSidebar}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-5 w-5"
                  >
                    <line x1="3" y1="12" x2="21" y2="12" />
                    <line x1="3" y1="6" x2="21" y2="6" />
                    <line x1="3" y1="18" x2="21" y2="18" />
                  </svg>
                </button>
              )}
              <h1 className="text-lg font-semibold">
                {pathname === "/"
                  ? ""
                  : (pathname.split("/").pop() === "dashboard" || pathname.includes("dashboard"))
                    ? ""
                    : pathname.split("/").pop()}
              </h1>
            </div>

            <div className="flex items-center gap-4">
              {/* 通知按钮 - 使用自管理状态的组件 */}
              <NotificationButton />

              {/* 主题切换 */}
              <SimpleTooltip content="切换主题">
                <ThemeSwitcher />
              </SimpleTooltip>

              {/* 用户信息下拉菜单 */}
              <div className="relative">
                <SimpleTooltip content={userInfo?.name || userInfo?.username || "用户账户"}>
                  <div
                    className="flex items-center gap-2 cursor-pointer"
                    onClick={() => {
                      const menu = document.getElementById("user-menu");
                      if (menu) {
                        menu.classList.toggle("hidden");
                      }
                    }}
                  >
                    <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                      <EnhancedUserAvatar
                        src={userInfo?.image}
                        alt={userInfo?.name || userInfo?.username || "用户头像"}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <span className="text-sm hidden md:inline-block">
                      {userInfo?.username || userInfo?.name || "用户"}
                    </span>
                  </div>
                </SimpleTooltip>

                {/* 用户下拉菜单 */}
                <div
                  id="user-menu"
                  className="absolute right-0 mt-2 w-48 bg-card text-card-foreground border border-border rounded-md shadow-lg hidden z-50"
                >
                  <div className="py-1">
                    <a
                      href="/user/profile"
                      onClick={(e) => {
                        e.preventDefault();
                        router.push('/user/profile');
                        // 隐藏菜单
                        const menu = document.getElementById("user-menu");
                        if (menu) {
                          menu.classList.add("hidden");
                        }
                      }}
                      className="block px-4 py-2 text-sm hover:bg-accent"
                    >
                      个人资料
                    </a>
                    <a
                      href="/user?tab=password"
                      onClick={(e) => {
                        e.preventDefault();
                        router.push('/user?tab=password');
                        // 隐藏菜单
                        const menu = document.getElementById("user-menu");
                        if (menu) {
                          menu.classList.add("hidden");
                        }
                      }}
                      className="block px-4 py-2 text-sm hover:bg-accent"
                    >
                      修改密码
                    </a>
                    <hr className="my-1 border-border" />
                    <button
                      onClick={() => {
                        // 使用NextAuth的signOut方法退出登录
                        if (window.confirm("确定要退出登录吗？")) {
                          // 导入并使用signOut
                          import('next-auth/react').then(({ signOut }) => {
                            signOut({ callbackUrl: '/login' });
                          });
                        }
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-destructive hover:bg-accent"
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </header>

          {/* 页面内容 */}
          <main className="flex-1 p-4 overflow-hidden">
            {children}
          </main>
        </div>

        {/* Toast通知 */}
        <Toaster position="top-right" />

        {/* 菜单更新监听器 - 确保在每个页面都能监听菜单更新事件 */}
        <MenuUpdateListener />
      </div>
    </DashboardContext.Provider>
  );
}