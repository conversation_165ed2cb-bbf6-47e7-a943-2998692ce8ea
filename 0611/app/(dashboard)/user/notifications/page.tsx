"use client"

import { useState, useEffect } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, Upload, Lock, Search, Filter, CheckCircle, CalendarIcon } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
// 修改导入部分，添加对URL查询参数的支持
import { useRouter, useSearchParams } from "next/navigation"
import { toast } from "@/components/ui/use-toast"

// 示例通知数据 - 更多通知数据用于测试分页
const allNotifications = [
  {
    id: 1,
    title: "系统更新",
    message: "系统已更新到最新版本 v2.3.0，包含多项功能改进和性能优化。请刷新页面以体验新功能。",
    time: "2023-07-15 10:23:45",
    read: false,
    type: "system",
  },
  {
    id: 2,
    title: "新任务分配",
    message: "您有一个新的外呼任务已分配，任务编号 #T-2023-0715，包含150个联系人。请尽快处理。",
    time: "2023-07-15 09:45:12",
    read: false,
    type: "task",
  },
  {
    id: 3,
    title: "账户安全提醒",
    message: "您的账户在新设备上登录，IP地址: ***********，位置: 北京市。如非本人操作，请立即修改密码并联系管理员。",
    time: "2023-07-14 16:30:22",
    read: true,
    type: "security",
  },
  {
    id: 4,
    title: "任务完成",
    message: "任务 #T-2023-0710 已完成，共处理联系人120个，成功率85%。详细报告已生成，可在任务管理中查看。",
    time: "2023-07-14 14:15:36",
    read: true,
    type: "task",
  },
  {
    id: 5,
    title: "系统维护通知",
    message: "系统将于今晚22:00-23:00进行维护，期间可能出现短暂服务中断。请提前做好相关工作安排。",
    time: "2023-07-14 10:05:18",
    read: true,
    type: "system",
  },
  {
    id: 6,
    title: "账户信息更新",
    message: "您的账户信息已更新，包括联系电话和邮箱地址。如非本人操作，请立即联系管理员。",
    time: "2023-07-13 17:42:51",
    read: true,
    type: "security",
  },
  {
    id: 7,
    title: "新功能上线",
    message: "外呼系统新增数据分析功能，可实时查看外呼效果和转化率。点击查看详情了解更多。",
    time: "2023-07-13 11:20:33",
    read: true,
    type: "system",
  },
  {
    id: 8,
    title: "任务提醒",
    message: "您有一个任务 #T-2023-0708 即将到期，请尽快处理或申请延期。",
    time: "2023-07-12 15:10:27",
    read: true,
    type: "task",
  },
  {
    id: 9,
    title: "密码即将过期",
    message: "您的账户密码将在7天后过期，请及时修改密码以确保账户安全。",
    time: "2023-07-12 09:35:14",
    read: true,
    type: "security",
  },
  {
    id: 10,
    title: "系统性能优化",
    message: "系统已完成性能优化，外呼响应速度提升30%，数据加载时间减少50%。",
    time: "2023-07-11 14:28:42",
    read: true,
    type: "system",
  },
  {
    id: 11,
    title: "外呼任务报表",
    message: "您的外呼任务月度报表已生成，请在任务管理中查看详细数据分析。",
    time: "2023-07-10 16:35:48",
    read: true,
    type: "task",
  },
  {
    id: 12,
    title: "账户登录异常",
    message: "检测到您的账户有异常登录尝试，已自动阻止。如有疑问，请联系系统管理员。",
    time: "2023-07-10 08:15:22",
    read: true,
    type: "security",
  },
  {
    id: 13,
    title: "新用户指南",
    message: "我们更新了系统用户指南，点击查看以了解最新功能操作方法。",
    time: "2023-07-09 11:42:33",
    read: true,
    type: "system",
  },
  {
    id: 14,
    title: "任务效率提升",
    message: "您的外呼任务效率已提升15%，继续保持良好工作状态！",
    time: "2023-07-08 15:30:27",
    read: true,
    type: "task",
  },
  {
    id: 15,
    title: "API密钥更新",
    message: "为提高系统安全性，您的API访问密钥已自动更新。请在开发设置中查看新的密钥。",
    time: "2023-07-07 09:20:14",
    read: true,
    type: "security",
  },
  {
    id: 16,
    title: "系统数据备份",
    message: "系统将在今晚进行数据备份，可能导致系统响应略有延迟。请知悉。",
    time: "2023-07-06 17:35:42",
    read: true,
    type: "system",
  },
  {
    id: 17,
    title: "外呼任务统计",
    message: "上周外呼任务统计报告已生成，共完成外呼1250次，转化率达到35%。",
    time: "2023-07-05 14:25:18",
    read: true,
    type: "task",
  },
  {
    id: 18,
    title: "账户验证确认",
    message: "您的账户安全验证已完成，账户安全状态良好。",
    time: "2023-07-04 10:15:30",
    read: true,
    type: "security",
  },
  {
    id: 19,
    title: "客户满意度调查",
    message: "系统已添加客户满意度调查功能，您可以在外呼任务完成后收集客户反馈。",
    time: "2023-07-03 16:40:22",
    read: true,
    type: "system",
  },
  {
    id: 20,
    title: "任务模板更新",
    message: "系统已更新外呼任务模板，提供更多自定义选项，让您的外呼更加高效。",
    time: "2023-07-02 13:50:45",
    read: true,
    type: "task",
  },
  {
    id: 21,
    title: "网络安全公告",
    message: "IT部门发布网络安全公告，请勿点击可疑链接或下载未知附件。",
    time: "2023-07-01 09:30:14",
    read: true,
    type: "security",
  },
  {
    id: 22,
    title: "系统界面优化",
    message: "系统界面进行了优化，提供更好的用户体验和视觉效果。",
    time: "2023-06-30 15:20:36",
    read: true,
    type: "system",
  },
  {
    id: 23,
    title: "任务优先级调整",
    message: "您有3个任务的优先级已被调整，请查看任务管理页面了解详情。",
    time: "2023-06-29 11:45:58",
    read: true,
    type: "task",
  },
  {
    id: 24,
    title: "二次验证设置提醒",
    message: "建议您启用账户二次验证功能，提高账户安全性。",
    time: "2023-06-28 14:10:20",
    read: true,
    type: "security",
  },
  {
    id: 25,
    title: "系统更新计划",
    message: "系统将于下周进行版本更新，添加多项新功能。敬请期待！",
    time: "2023-06-27 16:35:42",
    read: true,
    type: "system",
  },
]

export default function UserNotificationsPage() {
  const [activeTab, setActiveTab] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [notifications, setNotifications] = useState(allNotifications)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(5)
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // 修改组件内部，增加对URL查询参数的处理
  const router = useRouter()
  const searchParams = useSearchParams()

  // 根据类型筛选通知
  const getFilteredNotifications = () => {
    return notifications.filter((notification) => {
      // 标签筛选
      const tabMatch =
        activeTab === "all" ||
        (activeTab === "unread" && !notification.read) ||
        (activeTab === "system" && notification.type === "system") ||
        (activeTab === "task" && notification.type === "task") ||
        (activeTab === "security" && notification.type === "security")

      // 搜索筛选
      const searchMatch =
        searchTerm === "" ||
        notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.message.toLowerCase().includes(searchTerm.toLowerCase())

      // 类型筛选
      const typeMatch = typeFilter === "all" || notification.type === typeFilter

      // 日期范围筛选
      let dateMatch = true
      if (dateRange.from || dateRange.to) {
        const notificationDate = new Date(notification.time)

        if (dateRange.from && dateRange.to) {
          dateMatch = notificationDate >= dateRange.from && notificationDate <= dateRange.to
        } else if (dateRange.from) {
          dateMatch = notificationDate >= dateRange.from
        } else if (dateRange.to) {
          dateMatch = notificationDate <= dateRange.to
        }
      }

      return tabMatch && searchMatch && typeMatch && dateMatch
    })
  }

  const filteredNotifications = getFilteredNotifications()

  // 计算总页数
  const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage)

  // 获取当前页的通知
  const currentNotifications = filteredNotifications.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // 修改useEffect添加URL参数处理逻辑
  useEffect(() => {
    // 处理URL参数
    const filterParam = searchParams.get("filter")
    if (filterParam === "unread") {
      setActiveTab("unread")
    } else if (filterParam === "system") {
      setActiveTab("system")
    } else if (filterParam === "task") {
      setActiveTab("task")
    } else if (filterParam === "security") {
      setActiveTab("security")
    }

    // 当筛选条件改变时，重置到第一页
    setCurrentPage(1)
  }, [activeTab, searchTerm, typeFilter, dateRange, searchParams])

  // 标记通知为已读
  const markAsRead = (id: number) => {
    setNotifications(
      notifications.map((notification) => (notification.id === id ? { ...notification, read: true } : notification)),
    )
  }

  // 增强标记全部已读功能，添加成功提示
  const markAllAsRead = () => {
    setNotifications(notifications.map((notification) => ({ ...notification, read: true })))

    toast({
      title: "所有通知已标记为已读",
      description: "您的所有通知已成功标记为已读状态",
      variant: "success",
    })
  }

  // 增强标记当前页已读功能，添加成功提示
  const markCurrentPageAsRead = () => {
    const hasUnread = currentNotifications.some((n) => !n.read)
    if (!hasUnread) return

    setNotifications(
      notifications.map((notification) => {
        if (currentNotifications.some((n) => n.id === notification.id) && !notification.read) {
          return { ...notification, read: true }
        }
        return notification
      }),
    )

    toast({
      title: "当前页通知已标记为已读",
      description: `已成功标记当前页的未读通知为已读状态`,
      variant: "success",
    })
  }

  // 重置筛选条件
  const resetFilters = () => {
    setSearchTerm("")
    setTypeFilter("all")
    setDateRange({ from: undefined, to: undefined })
    setCurrentPage(1)
  }

  // 获取通知类型的图标和颜色
  const getNotificationStyle = (type: string) => {
    switch (type) {
      case "system":
        return {
          bgColor: "bg-blue-100 dark:bg-blue-900",
          textColor: "text-blue-600 dark:text-blue-300",
          icon: <Bell className="h-5 w-5 text-blue-600 dark:text-blue-300" />,
        }
      case "task":
        return {
          bgColor: "bg-green-100 dark:bg-green-900",
          textColor: "text-green-600 dark:text-green-300",
          icon: <Upload className="h-5 w-5 text-green-600 dark:text-green-300" />,
        }
      case "security":
        return {
          bgColor: "bg-red-100 dark:bg-red-900",
          textColor: "text-red-600 dark:text-red-300",
          icon: <Lock className="h-5 w-5 text-red-600 dark:text-red-300" />,
        }
      default:
        return {
          bgColor: "bg-gray-100 dark:bg-gray-800",
          textColor: "text-gray-600 dark:text-gray-300",
          icon: <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300" />,
        }
    }
  }

  // 获取未读通知数量
  const getUnreadCount = (type = "all") => {
    return notifications.filter((notification) => !notification.read && (type === "all" || notification.type === type))
      .length
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="通知中心" text="查看和管理您的所有系统通知" />

      <Card className="mt-6 border-blue-200 dark:border-blue-800 shadow-md">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 rounded-t-lg border-b border-blue-100 dark:border-blue-800">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <CardTitle className="text-blue-700 dark:text-blue-300">通知列表</CardTitle>
              <CardDescription className="text-blue-600 dark:text-blue-400">
                您有 {getUnreadCount()} 条未读通知
              </CardDescription>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索通知..."
                  className="pl-8 w-[200px] border-blue-200 dark:border-blue-800"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="border-blue-200 dark:border-blue-800">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "yyyy-MM-dd")} - {format(dateRange.to, "yyyy-MM-dd")}
                        </>
                      ) : (
                        format(dateRange.from, "yyyy-MM-dd")
                      )
                    ) : (
                      <span>日期范围</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="range"
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range) {
                        setDateRange({
                          from: range.from,
                          to: range.to || undefined
                        })
                      } else {
                        setDateRange({ from: undefined, to: undefined })
                      }
                    }}
                    initialFocus
                  />
                  <div className="p-3 border-t border-border">
                    <Button variant="ghost" size="sm" onClick={() => setDateRange({ from: undefined, to: undefined })}>
                      清除日期筛选
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[130px] border-blue-200 dark:border-blue-800">
                  <SelectValue placeholder="筛选类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="system">系统通知</SelectItem>
                  <SelectItem value="task">任务通知</SelectItem>
                  <SelectItem value="security">安全通知</SelectItem>
                </SelectContent>
              </Select>
              {getUnreadCount() > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={markAllAsRead}
                  className="border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  全部标为已读
                </Button>
              )}
              {currentNotifications.some((n) => !n.read) && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={markCurrentPageAsRead}
                  className="border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  标记当前页已读
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300"
              >
                <Filter className="mr-2 h-4 w-4" />
                重置筛选
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="border-b border-blue-100 dark:border-blue-800">
              <TabsList className="bg-transparent h-auto p-0">
                <TabsTrigger
                  value="all"
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 rounded-none px-4 py-3"
                >
                  全部
                  {getUnreadCount() > 0 && <Badge className="ml-2 bg-blue-600">{getUnreadCount()}</Badge>}
                </TabsTrigger>
                <TabsTrigger
                  value="unread"
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 rounded-none px-4 py-3"
                >
                  未读
                  {getUnreadCount() > 0 && <Badge className="ml-2 bg-blue-600">{getUnreadCount()}</Badge>}
                </TabsTrigger>
                <TabsTrigger
                  value="system"
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 rounded-none px-4 py-3"
                >
                  系统
                  {getUnreadCount("system") > 0 && (
                    <Badge className="ml-2 bg-blue-600">{getUnreadCount("system")}</Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger
                  value="task"
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 rounded-none px-4 py-3"
                >
                  任务
                  {getUnreadCount("task") > 0 && <Badge className="ml-2 bg-blue-600">{getUnreadCount("task")}</Badge>}
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-blue-600 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 rounded-none px-4 py-3"
                >
                  安全
                  {getUnreadCount("security") > 0 && (
                    <Badge className="ml-2 bg-blue-600">{getUnreadCount("security")}</Badge>
                  )}
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value={activeTab} className="mt-0">
              <div className="divide-y divide-border">
                {currentNotifications.length > 0 ? (
                  currentNotifications.map((notification) => {
                    const { bgColor, textColor, icon } = getNotificationStyle(notification.type)
                    return (
                      <div
                        key={notification.id}
                        className={`p-4 flex items-start gap-4 ${!notification.read ? "bg-blue-50/50 dark:bg-blue-900/20" : ""} hover:bg-muted/50 cursor-pointer`}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div className={`rounded-full p-3 ${bgColor} flex-shrink-0`}>{icon}</div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4
                              className={`font-medium ${!notification.read ? "text-foreground" : "text-muted-foreground"}`}
                            >
                              {notification.title}
                              {!notification.read && (
                                <span className="inline-block w-2 h-2 rounded-full bg-blue-500 ml-2"></span>
                              )}
                            </h4>
                            <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">
                              {notification.time}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                          <div className="flex items-center mt-2">
                            <Badge variant="outline" className={`${bgColor} ${textColor} border-none text-xs`}>
                              {notification.type === "system"
                                ? "系统通知"
                                : notification.type === "task"
                                  ? "任务通知"
                                  : "安全通知"}
                            </Badge>
                            {!notification.read && <Badge className="ml-2 bg-blue-600 text-xs">未读</Badge>}
                          </div>
                        </div>
                      </div>
                    )
                  })
                ) : (
                  <div className="p-8 text-center text-muted-foreground">没有找到匹配的通知</div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {/* 分页控件 */}
          {filteredNotifications.length > 0 && (
            <div className="p-4 flex flex-col sm:flex-row items-center justify-between gap-4 border-t border-border">
              <div className="text-sm text-muted-foreground">
                显示 {filteredNotifications.length} 条通知中的 {(currentPage - 1) * itemsPerPage + 1} -{" "}
                {Math.min(currentPage * itemsPerPage, filteredNotifications.length)} 条
              </div>

              <div className="flex items-center gap-4">
                <Select
                  value={String(itemsPerPage)}
                  onValueChange={(value) => {
                    setItemsPerPage(Number(value))
                    setCurrentPage(1) // 重置到第一页
                  }}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="每页条数" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5条</SelectItem>
                    <SelectItem value="10">10条</SelectItem>
                    <SelectItem value="15">15条</SelectItem>
                    <SelectItem value="20">20条</SelectItem>
                  </SelectContent>
                </Select>

                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>

                    {/* 计算要显示哪些页码 */}
                    {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                      let pageNumber
                      if (totalPages <= 5) {
                        // 总页数少于5，显示所有页码
                        pageNumber = i + 1
                      } else if (currentPage <= 3) {
                        // 当前页靠近开始，显示1-5
                        pageNumber = i + 1
                      } else if (currentPage >= totalPages - 2) {
                        // 当前页靠近结束，显示最后5页
                        pageNumber = totalPages - 4 + i
                      } else {
                        // 显示当前页及其前后2页
                        pageNumber = currentPage - 2 + i
                      }

                      return (
                        <PaginationItem key={i}>
                          <PaginationLink
                            onClick={() => setCurrentPage(pageNumber)}
                            isActive={currentPage === pageNumber}
                            className="cursor-pointer"
                          >
                            {pageNumber}
                          </PaginationLink>
                        </PaginationItem>
                      )
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardShell>
  )
}

