"use client"

import logger from '@/lib/utils/logger';

import { useEffect } from "react"
import "../process-polyfill"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Loader2 } from "lucide-react"
import { useNotificationSettings } from "../hooks/useNotificationSettings"

export function NotificationSettings() {
  const {
    notificationSettings,
    setNotificationSettings,
    savingNotificationSettings,
    fetchNotificationSettings,
    handleChannelChange,
    saveNotificationSettings,
    handleBalanceAlertThresholdChange,
    handleBalanceAlertChannelChange
  } = useNotificationSettings()

  useEffect(() => {
    fetchNotificationSettings()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-4xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">通知设置</CardTitle>
        <CardDescription>管理您接收通知的方式和偏好</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="max-w-2xl mx-auto space-y-8">
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-lg font-medium text-blue-800">邮件通知</Label>
              <p className="text-sm text-blue-700">
                通过邮件接收系统通知，及时了解重要信息
              </p>
            </div>
            <Switch
              checked={notificationSettings.emailEnabled}
              onCheckedChange={(checked) => {
                const saveSettings = async () => {
                  try {
                    const success = await handleChannelChange('email', checked)
                    if (!success) {
                      // 恢复原来的设置
                      setNotificationSettings(prev => ({
                        ...prev,
                        emailEnabled: !checked
                      }));
                    }
                  } catch (error: any) {
                    logger.error('保存通知设置失败:', error);
                  }
                };
                saveSettings();
              }}
              disabled={savingNotificationSettings}
              className="data-[state=checked]:bg-blue-600"
            />
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-100 flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Label className="text-lg font-medium text-yellow-800">短信通知</Label>
                <span className="text-xs px-2 py-1 rounded-full bg-yellow-200 text-yellow-800 font-medium">
                  即将上线
                </span>
              </div>
              <p className="text-sm text-yellow-700">
                通过短信接收重要通知，确保重要信息不遗漏
              </p>
            </div>
            <Switch
              checked={false}
              disabled={true}
              className="opacity-50"
            />
          </div>

          <div className="bg-green-50 p-6 rounded-lg border border-green-100 flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-lg font-medium text-green-800">应用内通知</Label>
              <p className="text-sm text-green-700">
                在系统内接收通知，便于实时查看和处理
              </p>
            </div>
            <Switch
              checked={notificationSettings.appEnabled}
              onCheckedChange={(checked) => {
                const saveSettings = async () => {
                  try {
                    const success = await handleChannelChange('app', checked)
                    if (!success) {
                      // 恢复原来的设置
                      setNotificationSettings(prev => ({
                        ...prev,
                        appEnabled: !checked
                      }));
                    }
                  } catch (error: any) {
                    logger.error('保存通知设置失败:', error);
                  }
                };
                saveSettings();
              }}
              disabled={savingNotificationSettings}
              className="data-[state=checked]:bg-green-600"
            />
          </div>

          <Separator className="my-8" />

          <div className="mb-4">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">余额预警设置</h3>
            <p className="text-sm text-gray-600 mb-6">
              当账户余额低于预警阈值时，系统将通过您选择的方式发送通知，帮助您及时了解账户余额状况
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-red-50 p-6 rounded-lg border border-red-100">
              <div className="flex items-start gap-3 mb-4">
                <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
                <div>
                  <h4 className="text-base font-medium text-red-800">重要提示</h4>
                  <p className="text-sm text-red-700 mt-1">
                    设置余额预警可以帮助您避免因余额不足导致任务中断。建议设置合理的预警阈值，确保有足够的时间进行充值。
                  </p>
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <div className="space-y-2">
                  <Label htmlFor="threshold" className="text-red-800">预警阈值（元）</Label>
                  <Input
                    id="threshold"
                    type="number"
                    min="0"
                    step="0.01"
                    value={notificationSettings.balanceAlertThreshold}
                    onChange={(e) => handleBalanceAlertThresholdChange(parseFloat(e.target.value))}
                    className="max-w-sm border-red-200 focus:border-red-400 focus:ring-red-400"
                  />
                  <p className="text-sm text-red-600">
                    当账户余额低于此金额时，系统将发送预警通知
                  </p>
                </div>

                <div className="space-y-4 mt-6">
                  <div className="flex items-center justify-between max-w-sm">
                    <div className="space-y-0.5">
                      <Label htmlFor="app-balance-alert" className="text-red-800">站内预警通知</Label>
                      <p className="text-sm text-red-600">
                        通过站内消息接收余额预警通知
                      </p>
                    </div>
                    <Switch
                      id="app-balance-alert"
                      checked={notificationSettings.balanceAlertApp}
                      onCheckedChange={(checked) => handleBalanceAlertChannelChange('app', checked)}
                      className="data-[state=checked]:bg-red-600"
                    />
                  </div>

                  <div className="flex items-center justify-between max-w-sm">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-balance-alert" className="text-red-800">邮件预警通知</Label>
                      <p className="text-sm text-red-600">
                        通过邮件接收余额预警通知
                      </p>
                    </div>
                    <Switch
                      id="email-balance-alert"
                      checked={notificationSettings.balanceAlertEmail}
                      onCheckedChange={(checked) => handleBalanceAlertChannelChange('email', checked)}
                      className="data-[state=checked]:bg-red-600"
                    />
                  </div>

                  <div className="flex items-center justify-between max-w-sm">
                    <div className="space-y-0.5">
                      <div className="flex items-center gap-2">
                        <Label htmlFor="sms-balance-alert" className="text-red-800">短信预警通知</Label>
                        <span className="text-xs px-2 py-1 rounded-full bg-yellow-200 text-yellow-800 font-medium">
                          即将上线
                        </span>
                      </div>
                      <p className="text-sm text-red-600">
                        通过短信接收余额预警通知
                      </p>
                    </div>
                    <Switch
                      id="sms-balance-alert"
                      checked={false}
                      disabled={true}
                      className="opacity-50"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {savingNotificationSettings && (
            <div className="flex items-center justify-center py-4 bg-gray-50 rounded-lg border border-gray-100 mt-6">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="ml-3 text-base font-medium text-blue-600">正在保存设置...</span>
            </div>
          )}

          <div className="mt-8 flex justify-end">
            <Button
              onClick={saveNotificationSettings}
              disabled={savingNotificationSettings}
              className="bg-blue-600 hover:bg-blue-700"
            >
              保存所有设置
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
