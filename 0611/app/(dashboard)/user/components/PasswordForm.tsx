"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import { usePasswordChange } from "../hooks/usePasswordChange"
import { PasswordInput } from "@/components/password-input"

export function PasswordForm() {
  const {
    currentPassword,
    setCurrentPassword,
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    showPassword,
    setShowPassword,
    changingPassword,
    passwordStrength,
    changePassword
  } = usePasswordChange()

  // 组件挂载时初始化
  useEffect(() => {
    // 如果需要初始化操作，可以在这里添加
  }, [])

  return (
    <Card className="border-none shadow-md max-w-4xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">修改密码</CardTitle>
        <CardDescription>更新您的账户密码，确保账户安全</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="max-w-md mx-auto grid gap-6">
          <div className="grid gap-3">
            <PasswordInput
              id="current-password"
              label="当前密码"
              value={currentPassword}
              onChange={setCurrentPassword}
              placeholder="请输入当前密码"
              disabled={changingPassword}
              showStrengthIndicator={false}
            />
          </div>
          <div className="grid gap-3">
            <PasswordInput
              id="new-password"
              label="新密码"
              value={newPassword}
              onChange={setNewPassword}
              placeholder="请输入新密码"
              disabled={changingPassword}
              showStrengthIndicator={true}
            />
          </div>
          <div className="grid gap-3">
            <PasswordInput
              id="confirm-password"
              label="确认新密码"
              value={confirmPassword}
              onChange={setConfirmPassword}
              placeholder="请再次输入新密码"
              disabled={changingPassword}
              showStrengthIndicator={false}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="bg-white border-t flex justify-end space-x-3 py-6">
        <Button
          variant="outline"
          disabled={changingPassword}
          className="px-6 py-2 text-gray-700 hover:bg-gray-100"
        >
          取消
        </Button>
        <Button
          onClick={changePassword}
          disabled={changingPassword || !passwordStrength.isValid || newPassword !== confirmPassword}
          className="bg-blue-600 hover:bg-blue-700 px-6 py-2 text-white font-medium"
        >
          {changingPassword ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              更新中...
            </>
          ) : (
            "更新密码"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
