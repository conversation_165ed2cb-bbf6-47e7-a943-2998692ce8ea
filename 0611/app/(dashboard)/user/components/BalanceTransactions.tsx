"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { AlertCircle, Calendar as CalendarIcon, Download, FileText, FileSpreadsheet, Loader2 } from "lucide-react"
import { format } from "date-fns"
import { useBalanceTransactions } from "../hooks/useBalanceTransactions"

export function BalanceTransactions() {
  const {
    balanceTransactions,
    loadingTransactions,
    transactionTypeFilter,
    setTransactionTypeFilter,
    transactionDateRange,
    setTransactionDateRange,
    fetchBalanceTransactions,
    handleTransactionFilterChange,
    handleTransactionPageChange,
    exportBalanceTransactions
  } = useBalanceTransactions()

  useEffect(() => {
    fetchBalanceTransactions()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-6xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">余额记录</CardTitle>
        <CardDescription>查看您的账户余额变动记录和交易明细</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="space-y-4 mb-6">
          {/* 搜索和筛选标题 */}
          <h3 className="text-lg font-medium text-blue-800">交易筛选</h3>

          {/* 筛选工具栏 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 交易类型选择 */}
            <Select
              value={transactionTypeFilter}
              onValueChange={(value) => {
                setTransactionTypeFilter(value)
                handleTransactionFilterChange()
              }}
            >
              <SelectTrigger className="w-full border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
                <SelectValue placeholder="交易类型" />
              </SelectTrigger>
              <SelectContent className="border-blue-100 shadow-lg">
                <SelectItem value="all" className="py-2">全部类型</SelectItem>
                <SelectItem value="recharge" className="py-2 text-blue-600">充值</SelectItem>
                <SelectItem value="deduct" className="py-2 text-amber-600">扣费</SelectItem>
                <SelectItem value="refund" className="py-2 text-green-600">退款</SelectItem>
                <SelectItem value="credit_add" className="py-2 text-green-600">授信额度增加</SelectItem>
                <SelectItem value="credit_subtract" className="py-2 text-orange-600">授信额度减少</SelectItem>
                <SelectItem value="credit_set" className="py-2 text-blue-600">授信额度调整</SelectItem>
                <SelectItem value="other" className="py-2 text-gray-600">其他</SelectItem>
              </SelectContent>
            </Select>

            {/* 日期选择 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-full justify-start text-left font-normal border-blue-100 hover:bg-blue-50 py-5 rounded-lg">
                  <CalendarIcon className="mr-2 h-5 w-5 text-blue-600" />
                  {transactionDateRange?.from ? (
                    transactionDateRange.to ? (
                      <>
                        {format(transactionDateRange.from, "yyyy-MM-dd")} ~{" "}
                        {format(transactionDateRange.to, "yyyy-MM-dd")}
                      </>
                    ) : (
                      format(transactionDateRange.from, "yyyy-MM-dd")
                    )
                  ) : (
                    <span>选择日期范围</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 border-blue-100 shadow-lg" align="start">
                <Calendar
                  mode="range"
                  selected={transactionDateRange}
                  onSelect={(range) => {
                    setTransactionDateRange(range || { from: undefined, to: undefined })
                    if (range?.from || range?.to) {
                      handleTransactionFilterChange()
                    }
                  }}
                  initialFocus
                />
                <div className="p-3 border-t border-blue-100">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setTransactionDateRange({ from: undefined, to: undefined })
                      handleTransactionFilterChange()
                    }}
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                  >
                    清除日期筛选
                  </Button>
                </div>
              </PopoverContent>
            </Popover>

            {/* 导出按钮 */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className="w-full bg-green-600 hover:bg-green-700 text-white py-5 rounded-lg">
                  <Download className="mr-2 h-5 w-5 text-white" />
                  导出数据
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="border-blue-100 shadow-lg">
                <DropdownMenuItem onClick={() => exportBalanceTransactions('csv')} className="py-2 hover:bg-blue-50">
                  <FileText className="mr-2 h-5 w-5 text-blue-600" />
                  导出为CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => exportBalanceTransactions('excel')} className="py-2 hover:bg-blue-50">
                  <FileSpreadsheet className="mr-2 h-5 w-5 text-green-600" />
                  导出为Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {loadingTransactions ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-blue-500">加载中...</span>
          </div>
        ) : balanceTransactions.transactions.length > 0 ? (
          <>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>交易时间</TableHead>
                  <TableHead>交易类型</TableHead>
                  <TableHead>交易金额</TableHead>
                  <TableHead>交易后余额</TableHead>
                  <TableHead>授信额度变动</TableHead>
                  <TableHead>支付方式</TableHead>
                  <TableHead>操作人</TableHead>
                  <TableHead>备注</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {balanceTransactions.transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{new Date(transaction.createdAt).toLocaleString('zh-CN')}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={`font-medium px-3 py-1.5 rounded-full text-sm ${transaction.type === 'recharge' ? 'bg-green-100 text-green-800 hover:bg-green-100 border-green-200' :
                                  transaction.type === 'deduct' ? 'bg-red-100 text-red-800 hover:bg-red-100 border-red-200' :
                                  transaction.type === 'refund' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100 border-yellow-200' :
                                  transaction.type === 'credit_add' ? 'bg-emerald-100 text-emerald-800 hover:bg-emerald-100 border-emerald-200' :
                                  transaction.type === 'credit_subtract' ? 'bg-orange-100 text-orange-800 hover:bg-orange-100 border-orange-200' :
                                  transaction.type === 'credit_set' ? 'bg-blue-100 text-blue-800 hover:bg-blue-100 border-blue-200' :
                                  'bg-gray-100 text-gray-800 border-gray-200'}`}
                      >
                        {transaction.type === 'recharge' ? '充值' :
                         transaction.type === 'deduct' ? '扣费' :
                         transaction.type === 'refund' ? '退款' :
                         transaction.type === 'credit_add' ? '授信额度增加' :
                         transaction.type === 'credit_subtract' ? '授信额度减少' :
                         transaction.type === 'credit_set' ? '授信额度调整' : '其他'}
                      </Badge>
                    </TableCell>
                    <TableCell className={transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                      {transaction.amount >= 0 ? '+' : ''}¥{transaction.amount.toFixed(2)}
                    </TableCell>
                    <TableCell>¥{transaction.balanceAfter.toFixed(2)}</TableCell>
                    <TableCell>
                      {transaction.creditLimitChange ? (
                        <span className={transaction.creditLimitChange > 0 ? 'text-green-600' : transaction.creditLimitChange < 0 ? 'text-red-600' : 'text-gray-600'}>
                          {transaction.creditLimitChange > 0 ? '+' : ''}¥{transaction.creditLimitChange.toFixed(2)}
                        </span>
                      ) : '-'}
                    </TableCell>
                    <TableCell>
                      {transaction.paymentMethod === 'bank' ? '银行转账' :
                       transaction.paymentMethod === 'alipay' ? '支付宝' :
                       transaction.paymentMethod === 'wechat' ? '微信支付' : '其他'}
                    </TableCell>
                    <TableCell>
                      {transaction.admin ? transaction.admin.name || transaction.admin.username : '系统'}
                    </TableCell>
                    <TableCell>{transaction.remarks || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {balanceTransactions.pagination.totalPages > 1 && (
              <div className="flex justify-center mt-4">
                <Pagination>
                  <PaginationContent>
                    {balanceTransactions.pagination.page > 1 && (
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handleTransactionPageChange(balanceTransactions.pagination.page - 1)}
                        />
                      </PaginationItem>
                    )}

                    {Array.from({ length: balanceTransactions.pagination.totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          isActive={page === balanceTransactions.pagination.page}
                          onClick={() => handleTransactionPageChange(page)}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                    {balanceTransactions.pagination.page < balanceTransactions.pagination.totalPages && (
                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handleTransactionPageChange(balanceTransactions.pagination.page + 1)}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-2" />
            <p>暂无交易记录</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
