"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { EnhancedAvatar } from "@/components/ui/enhanced-avatar"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { AvatarSelector } from "@/components/avatar-selector"
import { Loader2 } from "lucide-react"
import { useProfile } from "../hooks/useProfile"
import { provinces, getCitiesByProvince, getDistrictsByCity } from "@/app/lib/data/area-data-adapter"
import { EmailChangeForm } from "./EmailChangeForm"

export function ProfileForm() {
  const {
    userProfile,
    loading,
    saving,
    showAvatarSelector,
    setShowAvatarSelector,
    handleInputChange,
    saveProfile,
    fetchUserProfile
  } = useProfile()

  // 组件挂载时获取用户资料
  useEffect(() => {
    fetchUserProfile()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-4xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle>个人资料</CardTitle>
        <CardDescription>完善您的个人信息和联系方式</CardDescription>
      </CardHeader>
      <CardContent className="pt-6 bg-white">
        <div className="grid md:grid-cols-[250px_1fr] gap-8">
          {/* 左侧头像区域 */}
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <EnhancedAvatar
                src={userProfile.image || "/placeholder.svg"}
                alt={userProfile.name || userProfile.username || "用户头像"}
                fallback={userProfile.name?.charAt(0) || userProfile.username?.charAt(0) || "U"}
                className="h-32 w-32 cursor-pointer border-4 border-white shadow-lg"
                fallbackClassName="text-2xl font-bold"
                onClick={() => setShowAvatarSelector(true)}
              />
              <div className="absolute -bottom-2 -right-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="h-9 w-9 rounded-full bg-blue-500 text-white hover:bg-blue-600 border-0 p-0 shadow-md"
                  onClick={() => setShowAvatarSelector(true)}
                >
                  <span className="sr-only">更换头像</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    className="h-5 w-5"
                  >
                    <path d="M5.433 13.917l1.262-3.155A4 4 0 017.58 9.42l6.92-6.918a2.121 2.121 0 013 3l-6.92 6.918c-.383.383-.84.685-1.343.886l-3.154 1.262a.5.5 0 01-.65-.65z" />
                    <path d="M3.5 5.75c0-.69.56-1.25 1.25-1.25H10A.75.75 0 0010 3H4.75A2.75 2.75 0 002 5.75v9.5A2.75 2.75 0 004.75 18h9.5A2.75 2.75 0 0017 15.25V10a.75.75 0 00-1.5 0v5.25c0 .69-.56 1.25-1.25 1.25h-9.5c-.69 0-1.25-.56-1.25-1.25v-9.5z" />
                  </svg>
                </Button>
              </div>
            </div>

            <div className="text-center space-y-4">
              <div className="mb-2">
                <h3 className="text-xl font-semibold">{userProfile.name || userProfile.username}</h3>
                <p className="text-gray-500 mt-1">{userProfile.email}</p>
              </div>

              {/* 用户信息卡片 */}
              {/* 用户信息卡片 */}
              <div className="border border-blue-100 rounded-lg p-6 bg-white shadow-sm max-w-2xl mx-auto mb-6">
                {/* 用户信息标签 - 使用Flex布局代替Grid */}
                <div className="flex flex-wrap gap-4">
                  {/* 用户类型 */}
                  <div className="flex-1 min-w-[200px] flex items-center h-12 bg-purple-50 text-purple-700 border border-purple-200 rounded-lg py-2 px-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    <div className="whitespace-nowrap">用户类型: {userProfile.role?.name || '普通用户'}</div>
                  </div>

                  {/* 账户余额 */}
                  <div className="flex-1 min-w-[200px] flex items-center h-12 bg-amber-50 text-amber-700 border border-amber-200 rounded-lg py-2 px-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                    </svg>
                    <div className="whitespace-nowrap">账户余额: ¥{userProfile.balance ? userProfile.balance.toFixed(2) : '0.00'}</div>
                  </div>

                  {/* 授信额度 */}
                  <div className="flex-1 min-w-[200px] flex items-center h-12 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg py-2 px-4">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                      <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                    </svg>
                    <div className="whitespace-nowrap">授信额度: ¥{userProfile.creditLimit ? userProfile.creditLimit.toFixed(2) : '0.00'}</div>
                  </div>

                  {/* 认证状态 */}
                  <div className={`flex-1 min-w-[200px] flex items-center h-12 border rounded-lg py-2 px-4 ${userProfile.verificationType && userProfile.verificationType !== 'none' ?
                    (userProfile.verificationStatus === 'approved' ? 'bg-green-50 text-green-700 border-green-200' :
                    userProfile.verificationStatus === 'pending' ? 'bg-yellow-50 text-yellow-700 border-yellow-200' :
                    userProfile.verificationStatus === 'rejected' ? 'bg-red-50 text-red-700 border-red-200' :
                    'bg-gray-50 text-gray-700 border-gray-200') :
                    'bg-blue-50 text-blue-700 border-blue-200'}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <div className="whitespace-nowrap">
                      认证状态: {userProfile.verificationType && userProfile.verificationType !== 'none' ?
                        (userProfile.verificationType === 'personal' ? '个人' : '企业') +
                        (userProfile.verificationStatus === 'approved' ? '认证已通过' :
                         userProfile.verificationStatus === 'pending' ? '审核中' :
                         userProfile.verificationStatus === 'rejected' ? '认证已拒绝' : '未认证')
                        : '未提交认证'}
                    </div>
                  </div>

                  {/* 账户状态 */}
                  <div className={`flex-1 min-w-[200px] flex items-center h-12 border rounded-lg py-2 px-4 ${userProfile.status === "active" ? 'bg-green-50 text-green-700 border-green-200' : 'bg-red-50 text-red-700 border-red-200'}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      {userProfile.status === "active" ? (
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      ) : (
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      )}
                    </svg>
                    <div className="whitespace-nowrap">账户状态: {userProfile.status === "active" ? "正常" : "已禁用"}</div>
                  </div>
                </div>
              </div>


            </div>

            {showAvatarSelector && (
              <Dialog open={showAvatarSelector} onOpenChange={setShowAvatarSelector}>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>上传头像</DialogTitle>
                    <DialogDescription>
                      上传您自己的头像
                    </DialogDescription>
                  </DialogHeader>
                  <AvatarSelector onSelect={(url) => {
                    handleInputChange('image', url)
                    setShowAvatarSelector(false)
                  }} />
                </DialogContent>
              </Dialog>
            )}
          </div>

          {/* 右侧表单区域 */}
          <div className="grid gap-6 md:grid-cols-2">
            <div className="grid gap-3">
              <Label htmlFor="name" className="text-blue-700 font-medium">昵称</Label>
              <Input
                id="name"
                placeholder="请输入昵称"
                value={userProfile.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                disabled={loading || saving}
                className="border-blue-100 focus:border-blue-300"
              />
            </div>
            <div className="grid gap-3">
              <div className="flex justify-between items-center">
                <Label htmlFor="email" className="text-blue-700 font-medium">当前邮箱</Label>
                <EmailChangeForm
                  currentEmail={userProfile.email}
                  onSuccess={(newEmail) => {
                    // 更新本地状态
                    handleInputChange('email', newEmail)
                    // 刷新用户资料
                    fetchUserProfile()
                  }}
                />
              </div>
              <Input
                id="email"
                type="email"
                value={userProfile.email}
                disabled={true}
                className="bg-gray-50"
              />
            </div>

            <div className="grid gap-3">
              <Label htmlFor="phone" className="text-blue-700 font-medium">手机号码</Label>
              <Input
                id="phone"
                placeholder="请输入手机号码"
                value={userProfile.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={loading || saving}
                className="border-blue-100 focus:border-blue-300"
              />
            </div>

            <div className="grid gap-3">
              <Label htmlFor="wechat" className="text-blue-700 font-medium">微信号</Label>
              <Input
                id="wechat"
                placeholder="请输入微信号"
                value={userProfile.wechat || ''}
                onChange={(e) => handleInputChange('wechat', e.target.value)}
                disabled={loading || saving}
                className="border-blue-100 focus:border-blue-300"
              />
            </div>

            <div className="grid gap-3 md:col-span-2">
              <Label htmlFor="location" className="text-blue-700 font-medium">所在地区</Label>
              <div className="grid grid-cols-3 gap-3">
                <Select
                  value={userProfile.province || ''}
                  onValueChange={(value) => handleInputChange('province', value)}
                  disabled={loading || saving}
                >
                  <SelectTrigger className="w-full border-blue-100 focus:border-blue-300">
                    <SelectValue placeholder="选择省份" />
                  </SelectTrigger>
                  <SelectContent>
                    {provinces.map((province) => (
                      <SelectItem key={province.value} value={province.value}>
                        {province.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={userProfile.city || ''}
                  onValueChange={(value) => handleInputChange('city', value)}
                  disabled={!userProfile.province || loading || saving}
                >
                  <SelectTrigger className="w-full border-blue-100 focus:border-blue-300">
                    <SelectValue placeholder="选择城市" />
                  </SelectTrigger>
                  <SelectContent>
                    {userProfile.province && getCitiesByProvince(userProfile.province).map((city) => (
                      <SelectItem key={city.value} value={city.value}>
                        {city.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={userProfile.district || ''}
                  onValueChange={(value) => handleInputChange('district', value)}
                  disabled={!userProfile.city || loading || saving}
                >
                  <SelectTrigger className="w-full border-blue-100 focus:border-blue-300">
                    <SelectValue placeholder="选择区县" />
                  </SelectTrigger>
                  <SelectContent>
                    {userProfile.province && userProfile.city &&
                      getDistrictsByCity(userProfile.province, userProfile.city).map((district) => (
                        <SelectItem key={district.value} value={district.value}>
                          {district.label}
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-3 md:col-span-2">
              <Label htmlFor="address" className="text-blue-700 font-medium">详细地址</Label>
              <Textarea
                id="address"
                placeholder="请输入详细地址"
                value={userProfile.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
                disabled={loading || saving}
                className="min-h-[80px] border-blue-100 focus:border-blue-300"
              />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="bg-white border-t flex justify-end space-x-3 py-6">
        <Button
          variant="outline"
          disabled={saving}
          className="px-6 py-2 text-gray-700 hover:bg-gray-100"
        >
          取消
        </Button>
        <Button
          onClick={saveProfile}
          disabled={saving}
          className="bg-blue-600 hover:bg-blue-700 px-6 py-2 text-white font-medium"
        >
          {saving ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              保存中...
            </>
          ) : (
            "保存更改"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}