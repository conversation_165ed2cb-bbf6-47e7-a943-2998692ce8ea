"use client"

import logger from '@/lib/utils/logger';

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

export interface UserProfile {
  name: string
  email: string
  username: string
  image: string
  roleCode: string
  phone: string
  wechat: string
  province: string
  city: string
  district: string
  address: string
  balance: number
  creditLimit?: number
  status: string
  verificationStatus: string
  verificationType: string
  role: {
    name: string
    type: string
  }
}

export function useProfile() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showAvatarSelector, setShowAvatarSelector] = useState(false)

  const [userProfile, setUserProfile] = useState<UserProfile>({
    name: "",
    email: "",
    username: "",
    image: "",
    roleCode: "",
    phone: "",
    wechat: "",
    province: "",
    city: "",
    district: "",
    address: "",
    balance: 0,
    creditLimit: 0,
    status: "active",
    verificationStatus: "none",
    verificationType: "none",
    role: {
      name: "",
      type: ""
    }
  })

  // 获取用户资料
  const fetchUserProfile = async () => {
    try {
      setLoading(true)
      setError(null)

      // 尝试从 sessionStorage 获取基本用户信息
      try {
        const userDisplayStr = sessionStorage.getItem('user_display')
        if (userDisplayStr) {
          const userDisplay = JSON.parse(userDisplayStr)
          // 先使用基本信息填充表单，减少空白闪烁
          setUserProfile(prev => ({
            ...prev,
            name: userDisplay.name || prev.name,
            username: userDisplay.username || prev.username,
            email: userDisplay.email || prev.email,
            image: userDisplay.image || prev.image,
            role: {
              ...prev.role,
              name: userDisplay.roleName || prev.role.name,
              type: userDisplay.roleType || prev.role.type
            }
          }))
        }
      } catch (e) {
        logger.error('从 sessionStorage 获取用户信息失败:', e)
      }

      // 从 API 获取完整的用户资料
      const response = await fetch('/api/user/profile', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        cache: 'no-store'
      })

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Unauthorized, redirecting to login')
          router.push('/login')
          return
        }
        throw new Error(await response.text())
      }

      const data = await response.json()
      if (data.success) {
        setUserProfile(data.data)

        // 更新 sessionStorage 中的用户信息
        try {
          const userDisplayInfo = {
            id: data.data.id,
            username: data.data.username,
            name: data.data.name || data.data.username,
            email: data.data.email,
            roleName: data.data.role?.name || '普通用户',
            roleType: data.data.role?.type || 'user',
            image: data.data.image
          }
          sessionStorage.setItem('user_display', JSON.stringify(userDisplayInfo))
        } catch (e) {
          console.error('更新 sessionStorage 中的用户信息失败:', e)
        }

        return data.data
      } else {
        throw new Error(data.message || '获取用户信息失败')
      }
    } catch (error: any) {
      console.error('Error fetching user profile:', error)
      setError(error.message || '获取数据失败，请刷新页面重试')
      toast({
        title: "获取数据失败",
        description: error.message || "请刷新页面重试",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // 更新用户资料
  const saveProfile = async () => {
    try {
      setSaving(true)
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: userProfile.name,
          image: userProfile.image,
          phone: userProfile.phone,
          wechat: userProfile.wechat,
          province: userProfile.province,
          city: userProfile.city,
          district: userProfile.district,
          address: userProfile.address
        }),
      })

      const data = await response.json()
      if (data.success) {
        setUserProfile(data.data)

        // 更新 sessionStorage 中的用户信息
        try {
          // 从 sessionStorage 获取当前用户信息
          const userDisplayStr = sessionStorage.getItem('user_display')
          if (userDisplayStr) {
            const userDisplay = JSON.parse(userDisplayStr)
            // 更新可能变化的字段
            const updatedUserDisplay = {
              ...userDisplay,
              name: data.data.name || userDisplay.name,
              image: data.data.image || userDisplay.image
            }
            sessionStorage.setItem('user_display', JSON.stringify(updatedUserDisplay))
          }

          // 如果头像发生变化，触发头像更新事件并刷新会话
          if (data.data.image && data.data.image !== userProfile.image) {
            logger.log('从个人资料页面触发头像更新事件:', data.data.image);
            const avatarUpdateEvent = new CustomEvent('avatar-updated', {
              detail: { imageUrl: data.data.image }
            });
            window.dispatchEvent(avatarUpdateEvent);

            // 调用会话强制刷新API
            fetch('/api/auth/session/force-refresh', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include', // 确保包含cookie
            }).then(res => res.json())
              .then(refreshData => {
                logger.log('会话强制刷新响应:', refreshData);
                if (refreshData.success) {
                  logger.log('会话已标记为需要刷新');

                  // 刷新页面以应用新会话
                  setTimeout(() => {
                    window.location.reload();
                  }, 500);
                } else {
                  logger.error('会话强制刷新失败:', refreshData.message);
                }
              })
              .catch(err => {
                logger.error('会话强制刷新请求失败:', err);
              });
          }
        } catch (e) {
          logger.error('更新用户信息或触发事件失败:', e)
        }

        toast({
          title: "个人资料已更新",
          description: "您的个人资料信息已成功保存",
          variant: "success",
        })
        return true
      } else {
        throw new Error(data.message)
      }
    } catch (error: any) {
      toast({
        title: "更新个人资料失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return false
    } finally {
      setSaving(false)
    }
  }

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setUserProfile(prev => ({
      ...prev,
      [field]: value
    }))

    // 如果是头像变化，触发自定义事件
    if (field === 'image') {
      logger.log('从 handleInputChange 触发头像更新事件:', value);
      try {
        const avatarUpdateEvent = new CustomEvent('avatar-updated', {
          detail: { imageUrl: value }
        });
        window.dispatchEvent(avatarUpdateEvent);

        // 为确保事件被正确处理，添加一个延迟的第二次触发
        setTimeout(() => {
          const secondEvent = new CustomEvent('avatar-updated', {
            detail: { imageUrl: value }
          });
          window.dispatchEvent(secondEvent);
          logger.log('触发延迟头像更新事件');
        }, 500);

        // 调用会话强制刷新API
        fetch('/api/auth/session/force-refresh', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // 确保包含cookie
        }).then(res => res.json())
          .then(refreshData => {
            logger.log('会话强制刷新响应:', refreshData);
            if (refreshData.success) {
              logger.log('会话已标记为需要刷新');

              // 刷新页面以应用新会话
              setTimeout(() => {
                window.location.reload();
              }, 500);
            } else {
              logger.error('会话强制刷新失败:', refreshData.message);
            }
          })
          .catch(err => {
            logger.error('会话强制刷新请求失败:', err);
          });
      } catch (eventError) {
        logger.error('触发头像更新事件失败:', eventError);
      }
    }
  }

  return {
    userProfile,
    setUserProfile,
    loading,
    saving,
    error,
    showAvatarSelector,
    setShowAvatarSelector,
    fetchUserProfile,
    saveProfile,
    handleInputChange
  }
}
