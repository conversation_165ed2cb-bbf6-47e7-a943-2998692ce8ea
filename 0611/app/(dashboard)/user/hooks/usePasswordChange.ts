"use client"

import { useState, useEffect } from "react"
import { toast } from "@/components/ui/use-toast"
import { usePasswordRules } from "@/hooks/use-password-rules"
import { checkPasswordStrengthClient, PasswordStrengthResult } from "@/lib/password-rules"

export function usePasswordChange() {
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [changingPassword, setChangingPassword] = useState(false)
  const { rules: passwordRules } = usePasswordRules()
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrengthResult>({
    score: 0,
    isLongEnough: false,
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSpecialChar: false,
    isValid: false
  })

  // 检查密码强度
  useEffect(() => {
    if (newPassword) {
      setPasswordStrength(checkPasswordStrengthClient(newPassword, passwordRules))
    } else {
      setPasswordStrength({
        score: 0,
        isLongEnough: false,
        hasUpperCase: false,
        hasLowerCase: false,
        hasNumber: false,
        hasSpecialChar: false,
        isValid: false
      })
    }
  }, [newPassword, passwordRules])

  // 修改密码
  const changePassword = async () => {
    // 验证表单
    if (!currentPassword) {
      toast({
        title: "当前密码不能为空",
        description: "请输入您的当前密码",
        variant: "destructive",
      })
      return
    }

    if (!newPassword) {
      toast({
        title: "新密码不能为空",
        description: "请输入新密码",
        variant: "destructive",
      })
      return
    }

    if (!passwordStrength.isValid) {
      toast({
        title: "密码强度不足",
        description: "请确保密码符合所有安全要求",
        variant: "destructive",
      })
      return
    }

    if (newPassword !== confirmPassword) {
      toast({
        title: "密码不匹配",
        description: "新密码和确认密码不一致",
        variant: "destructive",
      })
      return
    }

    try {
      setChangingPassword(true)
      const response = await fetch('/api/user/password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword,
          newPassword,
        }),
      })

      const data = await response.json()
      if (data.success) {
        // 重置表单
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")

        toast({
          title: "密码已更新",
          description: "您的密码已成功修改",
          variant: "success",
        })
        return true
      } else {
        throw new Error(data.message)
      }
    } catch (error: any) {
      toast({
        title: "修改密码失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return false
    } finally {
      setChangingPassword(false)
    }
  }

  return {
    currentPassword,
    setCurrentPassword,
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    showPassword,
    setShowPassword,
    changingPassword,
    passwordStrength,
    changePassword
  }
}
