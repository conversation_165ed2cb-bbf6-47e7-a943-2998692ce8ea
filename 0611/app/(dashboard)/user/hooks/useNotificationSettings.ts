"use client"

import { useState } from "react"
import "../process-polyfill"
import { toast } from "@/components/ui/use-toast"

export interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  appEnabled: boolean
  types: string[]
  balanceAlertThreshold: number
  balanceAlertEmail: boolean
  balanceAlertSms: boolean
  balanceAlertApp: boolean
}

export function useNotificationSettings() {
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailEnabled: true,
    smsEnabled: false, // 默认关闭短信通知
    appEnabled: true,
    types: ["SYSTEM", "SECURITY", "TASK"],
    balanceAlertThreshold: 100, // 默认预警阈值100元
    balanceAlertEmail: true, // 默认开启邮件余额预警
    balanceAlertSms: false, // 默认关闭短信余额预警
    balanceAlertApp: true // 默认开启站内余额预警
  })

  const [savingNotificationSettings, setSavingNotificationSettings] = useState(false)

  // 获取通知设置
  const fetchNotificationSettings = async () => {
    try {
      // 获取通知设置
      const notificationResponse = await fetch('/api/user/notifications/settings')
      const notificationData = await notificationResponse.json()

      // 获取余额预警设置
      const balanceAlertResponse = await fetch('/api/user/balance/alert-settings')
      const balanceAlertData = await balanceAlertResponse.json()

      if (notificationData.success && balanceAlertData.success) {
        // 合并两个设置
        const mergedSettings = {
          ...notificationData.data,
          balanceAlertThreshold: balanceAlertData.data.threshold,
          balanceAlertEmail: balanceAlertData.data.enableEmail,
          balanceAlertSms: balanceAlertData.data.enableSms,
          balanceAlertApp: balanceAlertData.data.enableApp
        }

        setNotificationSettings(mergedSettings)
        return mergedSettings
      } else {
        throw new Error(notificationData.message || balanceAlertData.message || '获取通知设置失败')
      }
    } catch (error: any) {
      console.error('Error fetching notification settings:', error)
      toast({
        title: "获取通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return null
    }
  }

  // 保存通知设置
  const saveNotificationSettings = async () => {
    try {
      setSavingNotificationSettings(true)

      // 保存通知设置
      const notificationResponse = await fetch('/api/user/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailEnabled: notificationSettings.emailEnabled,
          smsEnabled: notificationSettings.smsEnabled,
          appEnabled: notificationSettings.appEnabled,
          types: notificationSettings.types
        }),
      })

      // 保存余额预警设置
      const balanceAlertResponse = await fetch('/api/user/balance/alert-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          threshold: notificationSettings.balanceAlertThreshold,
          enableEmail: notificationSettings.balanceAlertEmail,
          enableSms: notificationSettings.balanceAlertSms,
          enableApp: notificationSettings.balanceAlertApp
        }),
      })

      const notificationData = await notificationResponse.json()
      const balanceAlertData = await balanceAlertResponse.json()

      if (notificationData.success && balanceAlertData.success) {
        // 更新本地状态
        toast({
          title: "通知设置已更新",
          description: "您的通知偏好设置已成功保存",
          variant: "success",
        })
        return true
      } else {
        throw new Error(notificationData.message || balanceAlertData.message || '保存设置失败')
      }
    } catch (error: any) {
      toast({
        title: "更新通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return false
    } finally {
      setSavingNotificationSettings(false)
    }
  }

  // 处理通知类型变更
  const handleNotificationTypeChange = (type: string, checked: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      types: checked
        ? [...prev.types, type]
        : prev.types.filter(t => t !== type)
    }))
  }

  // 处理通知渠道变更
  const handleChannelChange = async (channel: 'email' | 'sms' | 'app', checked: boolean) => {
    try {
      setSavingNotificationSettings(true)

      // 更新本地状态
      const updatedSettings = {
        ...notificationSettings,
        [channel === 'email' ? 'emailEnabled' :
         channel === 'sms' ? 'smsEnabled' : 'appEnabled']: checked
      }

      // 调用API保存设置
      const response = await fetch('/api/user/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      })

      const data = await response.json()
      if (data.success) {
        setNotificationSettings(data.data)
        toast({
          title: "通知设置已更新",
          description: `${channel === 'email' ? '邮件' :
                        channel === 'sms' ? '短信' : '应用内'}通知已${checked ? '开启' : '关闭'}`,
          variant: "success",
        })
        return true
      } else {
        throw new Error(data.message)
      }
    } catch (error: any) {
      toast({
        title: "更新通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      // 恢复原来的设置
      return false
    } finally {
      setSavingNotificationSettings(false)
    }
  }

  // 处理余额预警阈值变更
  const handleBalanceAlertThresholdChange = (value: number) => {
    if (value >= 0) {
      setNotificationSettings(prev => ({
        ...prev,
        balanceAlertThreshold: value
      }))
    }
  }

  // 处理余额预警通知方式变更
  const handleBalanceAlertChannelChange = (channel: 'email' | 'sms' | 'app', checked: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      [channel === 'email' ? 'balanceAlertEmail' :
       channel === 'sms' ? 'balanceAlertSms' : 'balanceAlertApp']: checked
    }))
  }

  return {
    notificationSettings,
    setNotificationSettings,
    savingNotificationSettings,
    fetchNotificationSettings,
    saveNotificationSettings,
    handleNotificationTypeChange,
    handleChannelChange,
    handleBalanceAlertThresholdChange,
    handleBalanceAlertChannelChange
  }
}
