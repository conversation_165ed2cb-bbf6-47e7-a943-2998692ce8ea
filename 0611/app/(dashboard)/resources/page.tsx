"use client";

import { ResourceTable } from "@/components/resources/resource-table";
import { ResourceForm } from "@/components/resources/resource-form";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";

export default function ResourcesPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">资源管理</h1>
          <p className="text-muted-foreground">
            管理基于属性的访问控制(ABAC)资源
          </p>
        </div>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新建资源
        </Button>
      </div>

      <ResourceTable />

      <ResourceForm
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
      />
    </div>
  );
}