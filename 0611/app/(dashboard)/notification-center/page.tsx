"use client"

import React from "react"
import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Bell, Search, Plus, ChevronDown, ChevronUp, Calendar, Check, X, Settings } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// 定义通知类型
type NotificationType = "system" | "task" | "security" | "account"

// 通知数据结构
interface Notification {
  id: string
  title: string
  content: string
  type: NotificationType
  createdAt: string
  read: boolean
}

// 通知图标映射
const notificationIcons: Record<NotificationType, React.ReactNode> = {
  system: <Bell className="h-5 w-5 text-blue-500" />,
  task: <Bell className="h-5 w-5 text-green-500" />,
  security: <Bell className="h-5 w-5 text-red-500" />,
  account: <Bell className="h-5 w-5 text-amber-500" />,
}

// 获取通知样式
function getNotificationStyle(type: NotificationType) {
  switch (type) {
    case "system":
      return {
        icon: <Bell className="h-5 w-5" />,
        color: "text-blue-500",
        bg: "bg-blue-50 dark:bg-blue-950",
        border: "border-blue-200 dark:border-blue-800",
      }
    case "task":
      return {
        icon: <Bell className="h-5 w-5" />,
        color: "text-green-500",
        bg: "bg-green-50 dark:bg-green-950",
        border: "border-green-200 dark:border-green-800",
      }
    case "security":
      return {
        icon: <Bell className="h-5 w-5" />,
        color: "text-red-500",
        bg: "bg-red-50 dark:bg-red-950",
        border: "border-red-200 dark:border-red-800",
      }
    case "account":
      return {
        icon: <Bell className="h-5 w-5" />,
        color: "text-amber-500",
        bg: "bg-amber-50 dark:bg-amber-950",
        border: "border-amber-200 dark:border-amber-800",
      }
    default:
      return {
        icon: <Bell className="h-5 w-5" />,
        color: "text-gray-500",
        bg: "bg-gray-50 dark:bg-gray-900",
        border: "border-gray-200 dark:border-gray-800",
      }
  }
}

// 单个通知项组件
function NotificationItem({ notification }: { notification: Notification }) {
  const style = getNotificationStyle(notification.type)

  return (
    <div className={`relative p-4 border rounded-lg mb-3 ${style.bg} ${style.border}`}>
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 ${style.color}`}>{style.icon}</div>
        <div className="flex-grow min-w-0">
          <div className="flex items-center justify-between mb-1">
            <h4 className="font-medium text-sm line-clamp-1">{notification.title}</h4>
            <span className="text-xs text-gray-500 dark:text-gray-400 flex-shrink-0">
              {format(new Date(notification.createdAt), "MM.dd HH:mm")}
            </span>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-300">{notification.content}</p>
        </div>
      </div>
      {!notification.read && (
        <span className="absolute top-4 right-4 h-2 w-2 rounded-full bg-blue-500" />
      )}
    </div>
  )
}

// 通知列表组件
function NotificationList({ notifications }: { notifications: Notification[] }) {
  if (notifications.length === 0) {
    return (
      <div className="py-12 text-center text-gray-500 dark:text-gray-400">
        <Bell className="h-12 w-12 mx-auto mb-3 opacity-20" />
        <p>暂无通知</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {notifications.map((notification) => (
        <NotificationItem key={notification.id} notification={notification} />
      ))}
    </div>
  )
}

// 通知中心页面组件
export default function NotificationCenterPage() {
  const router = useRouter()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTab, setSelectedTab] = useState("all")
  const [expandedNotifications, setExpandedNotifications] = useState<string[]>([])
  const [isAdmin, setIsAdmin] = useState(true) // 模拟管理员权限
  const [date, setDate] = useState<Date | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取通知列表
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoading(true)
        setError(null)
        console.log('开始获取通知列表...')
        const response = await fetch('/api/notifications')

        if (!response.ok) {
          throw new Error(`获取通知列表失败: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        console.log('获取到的通知数据:', data)

        if (data.success && data.data && data.data.notifications) {
          setNotifications(data.data.notifications)
        } else {
          setError(data.message || '获取通知列表失败')
          console.error('获取通知列表失败:', data.message)
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : '获取通知列表出错')
        console.error('获取通知列表出错:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchNotifications()

    // 每30秒自动刷新一次
    const intervalId = setInterval(fetchNotifications, 30000)

    return () => clearInterval(intervalId)
  }, [])

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // 处理搜索
  const filteredNotifications = notifications.filter((notification) => {
    // 根据标签筛选
    if (selectedTab !== "all" && notification.type !== selectedTab) {
      return false
    }

    // 根据搜索词筛选
    if (
      searchTerm &&
      !notification.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !notification.content.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false
    }

    // 根据日期筛选
    if (date) {
      const notificationDate = new Date(notification.createdAt)
      return notificationDate.toDateString() === date.toDateString()
    }

    return true
  })

  // 计算总页数
  const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage)

  // 获取当前页的通知
  const currentNotifications = filteredNotifications.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // 页码变化时，重置展开状态
  useEffect(() => {
    setExpandedNotifications([])
  }, [currentPage])

  // 处理展开/收起通知
  const toggleExpand = (id: string) => {
    setExpandedNotifications((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]))
  }

  // 标记通知为已读
  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) => (notification.id === id ? { ...notification, read: true } : notification)),
    )
  }

  // 标记所有通知为已读
  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })))
  }

  // 跳转到发布通知页面
  const handlePublishNotification = () => {
    router.push("/notification-center/create")
  }

  // 跳转到通知类型管理页面
  const handleManageNotificationTypes = () => {
    router.push("/notification-center/types")
  }

  // 处理页码变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // 渲染分页控件
  const renderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <div className="flex justify-center items-center mt-6 space-x-2">
        <Button variant="outline" size="sm" onClick={() => handlePageChange(1)} disabled={currentPage === 1}>
          首页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          上一页
        </Button>

        <div className="flex items-center space-x-1">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              className="w-8 h-8 p-0"
              onClick={() => handlePageChange(page)}
            >
              {page}
            </Button>
          ))}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          下一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          末页
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      {loading && <div className="text-center py-4">加载中...</div>}
      {error && <div className="text-center py-4 text-red-500">错误: {error}</div>}

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">通知中心</h1>
          <p className="text-muted-foreground">查看和管理系统通知</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={markAllAsRead} className="flex items-center gap-1">
            <Check className="h-4 w-4" />
            标记全部为已读
          </Button>
          {isAdmin && (
            <>
              <Button variant="outline" onClick={handleManageNotificationTypes} className="flex items-center gap-1">
                <Settings className="h-4 w-4" />
                通知类型管理
              </Button>
              <Button onClick={handlePublishNotification} className="flex items-center gap-1">
                <Plus className="h-4 w-4" />
                发布通知
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-2 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索通知..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
              <Calendar className="mr-2 h-4 w-4" />
              {date ? format(date, "yyyy-MM-dd") : <span>选择日期筛选</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <CalendarComponent
              mode="single"
              selected={date || undefined}
              onSelect={(date) => date ? setDate(date) : setDate(null)}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        {date && (
          <Button variant="ghost" onClick={() => setDate(null)} className="h-8 w-8 p-0">
            <span className="sr-only">清除日期</span>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      <Tabs defaultValue="all" onValueChange={setSelectedTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="system">系统</TabsTrigger>
          <TabsTrigger value="task">任务</TabsTrigger>
          <TabsTrigger value="security">安全</TabsTrigger>
          <TabsTrigger value="account">账户</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="mt-0">
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-10">
                <Bell className="h-10 w-10 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">没有找到符合条件的通知</p>
              </CardContent>
            </Card>
          ) : (
            <>
              <div className="space-y-4">
                {currentNotifications.map((notification) => {
                  const style = getNotificationStyle(notification.type)
                  const isExpanded = expandedNotifications.includes(notification.id)

                  return (
                    <Card
                      key={notification.id}
                      className={cn("border-l-4 transition-all", style.bg, !notification.read ? "bg-muted/30" : "")}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div className="flex items-center gap-2">
                            {style.icon}
                            <CardTitle className="text-lg">{notification.title}</CardTitle>
                            {!notification.read && (
                              <Badge variant="secondary" className="ml-2">
                                新
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">{format(new Date(notification.createdAt), "MM.dd HH:mm")}</div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div
                          className={cn(
                            "transition-all overflow-hidden",
                            isExpanded ? "max-h-[500px]" : "max-h-[40px]",
                          )}
                        >
                          <p className="whitespace-pre-line">{notification.content}</p>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between pt-0">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpand(notification.id)}
                          className="text-xs"
                        >
                          {isExpanded ? (
                            <>
                              <ChevronUp className="h-4 w-4 mr-1" />
                              收起
                            </>
                          ) : (
                            <>
                              <ChevronDown className="h-4 w-4 mr-1" />
                              展开
                            </>
                          )}
                        </Button>
                        {!notification.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(notification.id)}
                            className="text-xs"
                          >
                            <Check className="h-4 w-4 mr-1" />
                            标记为已读
                          </Button>
                        )}
                      </CardFooter>
                    </Card>
                  )
                })}
              </div>

              {/* 分页控件 */}
              {renderPagination()}
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

