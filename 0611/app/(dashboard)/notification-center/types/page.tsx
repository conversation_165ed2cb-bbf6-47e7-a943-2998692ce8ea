"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Bell, Plus, Pencil, Trash2, ChevronRight, ChevronDown, Settings, AlertCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { toast } from "@/components/ui/use-toast"
import Link from "next/link"

// 通知类型接口
interface NotificationType {
  id: string;
  name: string;
  color: string;
  icon: string;
  enabled: boolean;
  description: string;
  priority: string;
  parentId?: string;
  children?: NotificationType[];
  code?: string;
}

// 模拟通知类型数据
const initialNotificationTypes: NotificationType[] = [
  {
    id: "system",
    name: "系统通知",
    color: "blue",
    icon: "Bell",
    enabled: true,
    description: "系统更新、维护和公告等通知",
    priority: "medium",
    children: [
      {
        id: "system_update",
        name: "系统更新",
        color: "blue",
        icon: "Bell",
        enabled: true,
        description: "系统版本更新和功能升级通知",
        priority: "medium",
        parentId: "system",
      },
      {
        id: "system_maintenance",
        name: "系统维护",
        color: "blue",
        icon: "Bell",
        enabled: true,
        description: "系统计划维护和临时停机通知",
        priority: "high",
        parentId: "system",
      },
    ],
  },
  {
    id: "task",
    name: "任务通知",
    color: "green",
    icon: "Bell",
    enabled: true,
    description: "任务分配、截止日期和完成提醒等通知",
    priority: "medium",
    children: [
      {
        id: "task_assignment",
        name: "任务分配",
        color: "green",
        icon: "Bell",
        enabled: true,
        description: "新任务分配通知",
        priority: "medium",
        parentId: "task",
      },
      {
        id: "task_deadline",
        name: "截止日期提醒",
        color: "amber",
        icon: "Bell",
        enabled: true,
        description: "任务截止日期临近提醒",
        priority: "high",
        parentId: "task",
      },
    ],
  },
  {
    id: "security",
    name: "安全警告",
    color: "red",
    icon: "Bell",
    enabled: true,
    description: "账户安全、异常登录和密码过期等警告",
    priority: "high",
    children: [
      {
        id: "security_login",
        name: "异常登录",
        color: "red",
        icon: "Bell",
        enabled: true,
        description: "检测到异常时间或地点的登录",
        priority: "high",
        parentId: "security",
      },
      {
        id: "security_password",
        name: "密码安全",
        color: "red",
        icon: "Bell",
        enabled: true,
        description: "密码过期或需要更改的提醒",
        priority: "medium",
        parentId: "security",
      },
    ],
  },
  {
    id: "account",
    name: "账户通知",
    color: "amber",
    icon: "Bell",
    enabled: true,
    description: "账户状态、权限变更和个人信息更新等通知",
    priority: "low",
    children: [
      {
        id: "account_status",
        name: "账户状态",
        color: "amber",
        icon: "Bell",
        enabled: true,
        description: "账户状态变更通知",
        priority: "medium",
        parentId: "account",
      },
      {
        id: "account_permission",
        name: "权限变更",
        color: "purple",
        icon: "Bell",
        enabled: true,
        description: "用户权限变更通知",
        priority: "low",
        parentId: "account",
      },
    ],
  },
]

// 可用的颜色选项
const colorOptions = [
  { value: "blue", label: "蓝色" },
  { value: "green", label: "绿色" },
  { value: "red", label: "红色" },
  { value: "amber", label: "琥珀色" },
  { value: "purple", label: "紫色" },
  { value: "pink", label: "粉色" },
  { value: "indigo", label: "靛蓝色" },
  { value: "gray", label: "灰色" },
]

// 可用的优先级选项
const priorityOptions = [
  { value: "low", label: "低" },
  { value: "medium", label: "中" },
  { value: "high", label: "高" },
]

export default function NotificationTypesPage() {
  const router = useRouter()
  const [notificationTypes, setNotificationTypes] = useState<NotificationType[]>([])
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentType, setCurrentType] = useState<NotificationType | null>(null)
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [hasPermission, setHasPermission] = useState(true) // 默认假设有权限，加载时会检查
  const [formData, setFormData] = useState<Partial<NotificationType>>({
    id: "",
    name: "",
    code: "",
    color: "blue",
    icon: "Bell",
    enabled: true,
    description: "",
    priority: "medium",
    parentId: undefined,
  })

  // 加载通知类型数据
  useEffect(() => {
    fetchNotificationTypes()
  }, [])

  // 获取通知类型数据
  const fetchNotificationTypes = async () => {
    try {
      setLoading(true)
      setError('')

      const response = await fetch('/api/notifications/types')

      if (!response.ok) {
        if (response.status === 403) {
          setHasPermission(false)
          throw new Error('无权访问通知类型管理')
        }
        throw new Error('获取通知类型失败')
      }

      const data = await response.json()

      if (data.success) {
        setNotificationTypes(data.data)
      } else {
        throw new Error(data.message || '获取通知类型失败')
      }
    } catch (error) {
      console.error('获取通知类型错误:', error)
      setError(error instanceof Error ? error.message : '获取通知类型失败')
    } finally {
      setLoading(false)
    }
  }

  // 切换展开/折叠状态
  const toggleExpand = (typeId: string) => {
    setExpandedItems((prev) => ({
      ...prev,
      [typeId]: !prev[typeId],
    }))
  }

  // 打开添加对话框
  const handleAddType = (parentId?: string) => {
    setFormData({
      id: "",
      name: "",
      code: "",
      color: parentId ? notificationTypes.find((t) => t.id === parentId)?.color || "blue" : "blue",
      icon: "Bell",
      enabled: true,
      description: "",
      priority: "medium",
      parentId,
    })
    setIsAddDialogOpen(true)
  }

  // 打开编辑对话框
  const handleEditType = (type: NotificationType) => {
    setCurrentType(type)
    setFormData({ ...type })
    setIsEditDialogOpen(true)
  }

  // 打开删除对话框
  const handleDeleteType = (type: NotificationType) => {
    setCurrentType(type)
    setIsDeleteDialogOpen(true)
  }

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // 处理选择框变化
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // 处理开关变化
  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, enabled: checked }))
  }

  // 添加新通知类型
  const handleAddSubmit = async () => {
    try {
      // 验证表单
      if (!formData.name || !formData.code) {
        toast({
          title: "验证失败",
          description: "名称和代码不能为空",
          variant: "destructive",
        })
        return
      }

      // 发送请求创建通知类型
      const response = await fetch('/api/notifications/types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          parentId: formData.parentId,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "成功",
          description: "通知类型创建成功",
        })

        // 刷新通知类型列表
        fetchNotificationTypes()

        // 如果有父类型，自动展开
        if (formData.parentId) {
          setExpandedItems((prev) => ({
            ...prev,
            ...(formData.parentId ? { [formData.parentId]: true } : {}),
          }))
        }

        // 关闭对话框
        setIsAddDialogOpen(false)

        // 重定向到通知列表页面
        router.push('/notifications')
      } else {
        throw new Error(data.message || '创建通知类型失败')
      }
    } catch (error) {
      console.error('创建通知类型错误:', error)
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : '创建通知类型失败',
        variant: "destructive",
      })
    }
  }

  // 更新通知类型
  const handleEditSubmit = async () => {
    try {
      // 验证表单
      if (!formData.name || !formData.code) {
        toast({
          title: "验证失败",
          description: "名称和代码不能为空",
          variant: "destructive",
        })
        return
      }

      if (!currentType) {
        throw new Error('当前编辑的通知类型不存在')
      }

      // 发送请求更新通知类型
      const response = await fetch(`/api/notifications/types/${currentType.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          code: formData.code,
          description: formData.description,
          parentId: formData.parentId,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "成功",
          description: "通知类型更新成功",
        })

        // 刷新通知类型列表
        fetchNotificationTypes()

        // 关闭对话框
        setIsEditDialogOpen(false)
      } else {
        throw new Error(data.message || '更新通知类型失败')
      }
    } catch (error) {
      console.error('更新通知类型错误:', error)
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : '更新通知类型失败',
        variant: "destructive",
      })
    }
  }

  // 删除通知类型
  const handleDeleteSubmit = async () => {
    try {
      if (!currentType) {
        throw new Error('当前删除的通知类型不存在')
      }

      // 发送请求删除通知类型
      const response = await fetch(`/api/notifications/types/${currentType.id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "成功",
          description: "通知类型删除成功",
        })

        // 刷新通知类型列表
        fetchNotificationTypes()

        // 关闭对话框
        setIsDeleteDialogOpen(false)
      } else {
        throw new Error(data.message || '删除通知类型失败')
      }
    } catch (error) {
      console.error('删除通知类型错误:', error)
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : '删除通知类型失败',
        variant: "destructive",
      })
    }
  }

  // 切换通知类型启用状态
  const toggleTypeEnabled = (type: NotificationType, enabled: boolean) => {
    if (type.parentId) {
      // 切换子类型
      setNotificationTypes((prev) =>
        prev.map((parentType) => {
          if (parentType.id === type.parentId) {
            return {
              ...parentType,
              children: parentType.children?.map((child) => (child.id === type.id ? { ...child, enabled } : child)) || [],
            }
          }
          return parentType
        }),
      )
    } else {
      // 切换顶级类型
      setNotificationTypes((prev) =>
        prev.map((parentType) => (parentType.id === type.id ? { ...parentType, enabled } : parentType)),
      )
    }
  }

  // 获取颜色类名
  const getColorClass = (color: string) => {
    switch (color) {
      case "blue":
        return "bg-blue-500"
      case "green":
        return "bg-green-500"
      case "red":
        return "bg-red-500"
      case "amber":
        return "bg-amber-500"
      case "purple":
        return "bg-purple-500"
      case "pink":
        return "bg-pink-500"
      case "indigo":
        return "bg-indigo-500"
      case "gray":
        return "bg-gray-500"
      default:
        return "bg-blue-500"
    }
  }

  // 获取优先级标签
  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "low":
        return "低"
      case "medium":
        return "中"
      case "high":
        return "高"
      default:
        return "中"
    }
  }

  // 渲染通知类型项
  const renderTypeItem = (type: NotificationType, isChild = false) => {
    const isExpanded = expandedItems[type.id]
    const hasChildren = type.children && type.children.length > 0

    return (
      <div key={type.id} className={`mb-2 ${isChild ? "ml-8" : ""}`}>
        <div className="border rounded-lg overflow-hidden">
          <div className="flex items-center p-4 bg-background">
            {hasChildren && (
              <Button variant="ghost" size="icon" className="mr-1 h-6 w-6" onClick={() => toggleExpand(type.id)}>
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            )}
            {!hasChildren && <div className="w-7"></div>}

            <div className={`w-5 h-5 rounded-full flex items-center justify-center ${getColorClass(type.color)} mr-2`}>
              <Bell className="h-3 w-3 text-white" />
            </div>

            <div className="flex-1">
              <div className="font-medium">{type.name}</div>
              <div className="text-sm text-muted-foreground">{type.description}</div>
            </div>

            <div className="flex items-center gap-1 ml-4">
              <div className="text-sm mr-2">
                <span className="text-muted-foreground mr-1">ID:</span>
                <code className="bg-muted px-1 rounded text-xs">{type.id}</code>
                <span className="text-muted-foreground mx-1">优先级:</span>
                <span>{getPriorityLabel(type.priority)}</span>
              </div>

              <Switch
                checked={type.enabled}
                onCheckedChange={(checked) => toggleTypeEnabled(type, checked)}
                className="mr-1"
              />

              <Button variant="ghost" size="icon" onClick={() => handleEditType(type)} className="h-8 w-8">
                <Pencil className="h-4 w-4" />
              </Button>

              <Button variant="ghost" size="icon" onClick={() => handleDeleteType(type)} className="h-8 w-8">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {!isChild && (
            <div className="border-t px-4 py-2 bg-muted/30 flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                {hasChildren ? `${type.children?.length || 0} 个子类型` : "无子类型"}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 text-xs flex items-center gap-1"
                onClick={() => handleAddType(type.id)}
              >
                <Plus className="h-3 w-3" />
                添加子类型
              </Button>
            </div>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-2">{type.children?.map((child) => renderTypeItem(child, true))}</div>
        )}
      </div>
    )
  }

  // 如果正在加载或没有权限，显示相应的提示
  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/notification-center">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">通知类型管理</h1>
          </div>
          <p className="text-muted-foreground">管理系统中的通知类型、颜色和优先级</p>
        </div>
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">正在加载通知类型数据...</p>
        </div>
      </div>
    )
  }

  if (!hasPermission) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/notifications">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">通知类型管理</h1>
          </div>
          <p className="text-muted-foreground">管理系统中的通知类型、颜色和优先级</p>
        </div>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>权限错误</AlertTitle>
          <AlertDescription>
            您没有权限访问通知类型管理。请联系系统管理员获取权限。
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/notifications">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-3xl font-bold tracking-tight">通知类型管理</h1>
          </div>
          <p className="text-muted-foreground">管理系统中的通知类型、颜色和优先级</p>
        </div>
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={fetchNotificationTypes}>重试</Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/notifications">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">通知类型管理</h1>
        </div>
        <p className="text-muted-foreground">管理系统中的通知类型、颜色和优先级</p>
      </div>

      <div className="flex justify-end mb-6">
        <Button onClick={() => handleAddType()} className="flex items-center gap-1">
          <Plus className="h-4 w-4" />
          添加通知类型
        </Button>
      </div>

      <div className="space-y-1">{notificationTypes.map((type) => renderTypeItem(type))}</div>

      {/* 添加通知类型对话框 */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{formData.parentId ? "添加子通知类型" : "添加通知类型"}</DialogTitle>
            <DialogDescription>
              {formData.parentId
                ? `为 "${notificationTypes.find((t) => t.id === formData.parentId)?.name}" 创建新的子通知类型`
                : "创建新的通知类型并设置其属性"}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                名称
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="例如：系统通知"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="code" className="text-right">
                代码
              </Label>
              <Input
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="例如：system（必填）"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                描述
              </Label>
              <Input
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
                placeholder="简要描述此通知类型的用途"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="color" className="text-right">
                颜色
              </Label>
              <Select value={formData.color} onValueChange={(value) => handleSelectChange("color", value)}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择颜色" />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded-full ${getColorClass(option.value)}`}></div>
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                默认优先级
              </Label>
              <Select value={formData.priority} onValueChange={(value) => handleSelectChange("priority", value)}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="enabled" className="text-right">
                启用
              </Label>
              <div className="col-span-3 flex items-center">
                <Switch id="enabled" checked={formData.enabled} onCheckedChange={handleSwitchChange} />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddSubmit}>添加</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑通知类型对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑通知类型</DialogTitle>
            <DialogDescription>修改通知类型的属性</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                名称
              </Label>
              <Input
                id="edit-name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-id" className="text-right">
                ID
              </Label>
              <Input
                id="edit-id"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                className="col-span-3"
                disabled
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-description" className="text-right">
                描述
              </Label>
              <Input
                id="edit-description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-color" className="text-right">
                颜色
              </Label>
              <Select value={formData.color} onValueChange={(value) => handleSelectChange("color", value)}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择颜色" />
                </SelectTrigger>
                <SelectContent>
                  {colorOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-4 h-4 rounded-full ${getColorClass(option.value)}`}></div>
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-priority" className="text-right">
                默认优先级
              </Label>
              <Select value={formData.priority} onValueChange={(value) => handleSelectChange("priority", value)}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-enabled" className="text-right">
                启用
              </Label>
              <div className="col-span-3 flex items-center">
                <Switch id="edit-enabled" checked={formData.enabled} onCheckedChange={handleSwitchChange} />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditSubmit}>保存</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除通知类型确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              {currentType?.children && currentType.children.length > 0
                ? `您确定要删除通知类型 "${currentType?.name}" 及其所有子类型吗？此操作无法撤销。`
                : `您确定要删除通知类型 "${currentType?.name}" 吗？此操作无法撤销。`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSubmit} className="bg-red-500 hover:bg-red-600">
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

