import { Skeleton } from "@/components/ui/skeleton"

export default function NotificationTypesLoading() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Skeleton className="h-10 w-64 mb-2" />
        <Skeleton className="h-5 w-96" />
      </div>

      <div className="flex justify-end mb-6">
        <Skeleton className="h-10 w-36" />
      </div>

      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index}>
            <div className="border rounded-lg overflow-hidden">
              <div className="flex items-center p-4 bg-background">
                <Skeleton className="h-6 w-6 mr-1" />
                <Skeleton className="h-5 w-5 rounded-full mr-2" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-40 mb-1" />
                  <Skeleton className="h-4 w-64" />
                </div>
                <div className="flex items-center gap-1 ml-4">
                  <Skeleton className="h-4 w-40 mr-2" />
                  <Skeleton className="h-5 w-10 rounded-full mr-1" />
                  <Skeleton className="h-8 w-8 rounded-md mr-1" />
                  <Skeleton className="h-8 w-8 rounded-md" />
                </div>
              </div>
              <div className="border-t px-4 py-2 bg-muted/30 flex justify-between items-center">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-7 w-24" />
              </div>
            </div>

            {index === 0 && (
              <div className="mt-2 ml-8 space-y-2">
                {Array.from({ length: 2 }).map((_, childIndex) => (
                  <div key={childIndex} className="border rounded-lg overflow-hidden">
                    <div className="flex items-center p-4 bg-background">
                      <div className="w-7"></div>
                      <Skeleton className="h-5 w-5 rounded-full mr-2" />
                      <div className="flex-1">
                        <Skeleton className="h-5 w-32 mb-1" />
                        <Skeleton className="h-4 w-56" />
                      </div>
                      <div className="flex items-center gap-1 ml-4">
                        <Skeleton className="h-4 w-40 mr-2" />
                        <Skeleton className="h-5 w-10 rounded-full mr-1" />
                        <Skeleton className="h-8 w-8 rounded-md mr-1" />
                        <Skeleton className="h-8 w-8 rounded-md" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

