"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { AlertCircle, ArrowLeft, Send } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Link from "next/link"
import { RichTextEditor } from "@/components/rich-text-editor"
import { toast } from "sonner"
import logger from "@/lib/utils/logger"

/**
 * 通知创建页面组件
 *
 * 该组件提供一个用于创建系统通知的表单界面，包含以下功能：
 * - 通知标题输入
 * - 通知类型选择（系统通知、任务通知、安全通知、账户通知）
 * - 优先级设置（低、中、高）
 * - 富文本内容编辑（使用 TipTap 编辑器）
 * - 表单验证
 * - 权限检查
 * - 提交状态管理
 *
 * @component
 */

export default function CreateNotificationPage() {
  // 路由实例，用于页面跳转
  const router = useRouter()

  // 表单状态管理
  const [title, setTitle] = useState("") // 通知标题
  const [content, setContent] = useState("") // 通知内容
  const [type, setType] = useState("system") // 通知类型
  const [priority, setPriority] = useState("medium") // 优先级
  const [isSubmitting, setIsSubmitting] = useState(false) // 提交状态
  const [error, setError] = useState("") // 错误信息
  const [isAdmin, setIsAdmin] = useState(false) // 管理员权限状态

  // 检查用户权限
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const response = await fetch('/api/auth/check-permission?resource=notifications&action=create');
        const data = await response.json();

        if (data.success) {
          setIsAdmin(data.hasPermission);
        } else {
          logger.error('检查权限失败:', data.message);
          setIsAdmin(false);
        }
      } catch (err) {
        logger.error('检查权限出错:', err);
        setIsAdmin(false);
      }
    };

    checkPermission();
  }, []);

  // 权限检查：如果不是管理员，显示无权限提示
  if (!isAdmin) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>无权限</AlertTitle>
          <AlertDescription>您没有发布通知的权限，请联系系统管理员。</AlertDescription>
        </Alert>
        <Button asChild>
          <Link href="/notification-center">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回通知中心
          </Link>
        </Button>
      </div>
    )
  }

  /**
   * 处理表单提交
   * @param {React.FormEvent} e - 表单提交事件
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 表单验证
    if (!title.trim()) {
      setError("请输入通知标题")
      return
    }

    if (!content.trim()) {
      setError("请输入通知内容")
      return
    }

    setIsSubmitting(true)
    setError("")

    try {
      // 发送API请求创建通知
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          content,
          type,
          priority,
          isGlobal: true, // 全局通知
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success('通知发布成功');
        // 成功后跳转回通知中心
        router.push("/notification-center");
      } else {
        setError(data.message || '发布通知失败，请重试');
        logger.error('发布通知失败:', data);
      }
    } catch (err) {
      logger.error('发布通知出错:', err);
      setError("发布通知失败，请重试");
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="container mx-auto py-6">
      {/* 页面标题区域 */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/notification-center">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">发布通知</h1>
        </div>
        <p className="text-muted-foreground">创建新的系统通知</p>
      </div>

      {/* 通知创建表单 */}
      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>通知信息</CardTitle>
            <CardDescription>填写通知详情，发布后将向所有用户推送此通知</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 错误提示 */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>错误</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 标题和类型选择 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">通知标题</Label>
                <Input
                  id="title"
                  placeholder="输入通知标题"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">通知类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择通知类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="system">系统通知</SelectItem>
                    <SelectItem value="task">任务通知</SelectItem>
                    <SelectItem value="security">安全通知</SelectItem>
                    <SelectItem value="account">账户通知</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* 优先级选择 */}
            <div className="space-y-2">
              <Label htmlFor="priority">优先级</Label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger id="priority">
                  <SelectValue placeholder="选择优先级" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">低</SelectItem>
                  <SelectItem value="medium">中</SelectItem>
                  <SelectItem value="high">高</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 富文本编辑器 */}
            <div className="space-y-2">
              <Label htmlFor="content">通知内容</Label>
              <RichTextEditor
                value={content}
                onChange={setContent}
                placeholder="请输入通知内容..."
              />
            </div>
          </CardContent>

          {/* 操作按钮 */}
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" asChild>
              <Link href="/notification-center">取消</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>处理中...</>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  发布通知
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}

