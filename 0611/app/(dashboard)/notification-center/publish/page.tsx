export default function PublishNotificationPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">发布通知</h1>
        <p className="text-muted-foreground">创建和发布系统通知</p>
      </div>

      <div className="border rounded-lg p-6 bg-card">
        <h2 className="text-xl font-semibold mb-4">通知发布表单</h2>
        <p>通知发布功能正在开发中...</p>
        <p className="mt-4">此页面将提供以下功能：</p>
        <ul className="list-disc pl-6 mt-2 space-y-1">
          <li>创建新通知</li>
          <li>使用富文本编辑器编辑通知内容</li>
          <li>设置通知优先级和状态</li>
          <li>预览通知效果</li>
        </ul>
      </div>
    </div>
  )
}

