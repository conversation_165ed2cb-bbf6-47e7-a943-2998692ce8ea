import React from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { EmailAuthCode } from "./email-auth-code"

interface EmailAuthCodeSectionProps {
  settings: any
  setSettings: (settings: any) => void
  setIsSaving: (value: boolean) => void
}

export function EmailAuthCodeSection({ settings, setSettings, setIsSaving }: EmailAuthCodeSectionProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor="emailPass">邮箱授权码</Label>
      <div className="flex gap-2">
        <Input
          id="emailPass"
          type="password"
          value={settings.emailSettings?.auth?.pass || ''}
          onChange={(e) => {
            // 如果输入框中有占位符但用户没有修改，不更新值
            if (settings.emailSettings?.auth?.pass === '******' && e.target.value === '******') {
              return;
            }

            setSettings(prev => ({
              ...prev,
              emailSettings: {
                ...prev.emailSettings,
                auth: {
                  ...prev.emailSettings?.auth,
                  pass: e.target.value
                }
              }
            }))
          }}
          placeholder="请输入邮箱授权码"
          className="flex-1"
        />
        <EmailAuthCode
          emailHost={settings.emailSettings?.host || ''}
          emailPort={settings.emailSettings?.port || 0}
          emailUser={settings.emailSettings?.auth?.user || ''}
          setIsSaving={setIsSaving}
          onSuccess={(data) => {
            setSettings(prev => ({
              ...prev,
              emailSettings: data
            }))
          }}
        />
      </div>
      <p className="text-sm text-gray-500">
        邮箱的授权码，非邮箱登录密码，需要在邮箱设置中获取
      </p>
      {settings.emailSettings?.auth?.pass === '******' && (
        <p className="text-sm text-blue-500 mt-1">
          您已经设置过授权码，如果需要修改，请点击&quot;安全修改&quot;按钮
        </p>
      )}
      <p className="text-sm text-amber-500 mt-1">
        <span className="font-semibold">安全提示：</span>授权码将安全存储，不会在前端显示实际内容。修改授权码需要邮箱验证。
      </p>
    </div>
  )
}
