"use client"

import React from "react"
import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import {
  ChevronRight,
  ChevronDown,
  Plus,
  Trash2,
  LayoutDashboard,
  Users,
  Settings,
  Upload,
  FileText,
  Phone,
  Bell,
  Mail,
  Calendar,
  Pencil,
  Shield,
  Lock,
} from "lucide-react"
import { useSession } from "next-auth/react"

// 可用图标列表
const availableIcons = [
  { name: "LayoutDashboard", icon: LayoutDashboard },
  { name: "Users", icon: Users },
  { name: "Settings", icon: Settings },
  { name: "Upload", icon: Upload },
  { name: "FileText", icon: FileText },
  { name: "Phone", icon: Phone },
  { name: "Bell", icon: Bell },
  { name: "Mail", icon: Mail },
  { name: "Calendar", icon: Calendar },
  { name: "Shield", icon: Shield },
  { name: "Lock", icon: Lock },
]

// 将数据库菜单项转换为前端格式
const convertDbMenuToUiMenu = (dbMenu) => {
  return {
    id: dbMenu.id,
    title: dbMenu.name,
    path: dbMenu.path,
    icon: dbMenu.icon || "FileText",
    isVisible: dbMenu.visible,
    hasChildren: dbMenu.children && dbMenu.children.length > 0,
    children: dbMenu.children ? dbMenu.children.map(child => ({
      id: child.id,
      title: child.name,
      path: child.path,
      isVisible: child.visible
    })) : [],
    order: dbMenu.order,
    code: dbMenu.code,
    parentId: dbMenu.parentId
  }
}

// 将前端菜单项转换为数据库格式
const convertUiMenuToDbMenu = (uiMenu, isChild = false, parentId = null) => {
  if (isChild) {
    return {
      id: uiMenu.id,
      name: uiMenu.title,
      path: uiMenu.path,
      visible: uiMenu.isVisible,
      parentId: parentId,
      // 为新菜单生成唯一代码
      code: uiMenu.code || `menu-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      order: uiMenu.order || 0
    }
  }

  return {
    id: uiMenu.id,
    name: uiMenu.title,
    path: uiMenu.path,
    icon: uiMenu.icon,
    visible: uiMenu.isVisible,
    // 为新菜单生成唯一代码
    code: uiMenu.code || `menu-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    order: uiMenu.order || 0,
    parentId: uiMenu.parentId || null
  }
}

export default function MenuSettingsPage() {
  const { data: session } = useSession()
  const [menuItems, setMenuItems] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [newItemDialogOpen, setNewItemDialogOpen] = useState(false)
  const [currentItem, setCurrentItem] = useState<any>(null)
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  // 添加子菜单编辑相关的状态和函数：
  const [childDialogOpen, setChildDialogOpen] = useState(false)
  const [currentParent, setCurrentParent] = useState<any>(null)
  const [currentChild, setCurrentChild] = useState<any>(null)
  const [isEditingChild, setIsEditingChild] = useState(false)
  const [saving, setSaving] = useState(false)

  // 从API加载菜单数据
  const loadMenuItems = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/menus')
      const result = await response.json()

      if (result.success) {
        // 将数据库格式转换为UI格式
        const uiMenuItems = result.data.map(convertDbMenuToUiMenu)
        setMenuItems(uiMenuItems)

        // 默认展开所有有子菜单的项
        const itemsWithChildren = uiMenuItems
          .filter(item => item.hasChildren)
          .map(item => item.id)
        setExpandedItems(itemsWithChildren)
      } else {
        toast.error('加载菜单失败: ' + result.message)
      }
    } catch (error) {
      console.error('加载菜单失败:', error)
      toast.error('加载菜单失败，请检查网络连接')
    } finally {
      setLoading(false)
    }
  }

  // 初始化时加载菜单数据
  useEffect(() => {
    loadMenuItems()
  }, [])

  // 切换展开/折叠状态
  const toggleExpand = (id: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setExpandedItems((prev) => {
      if (prev.includes(id)) {
        return prev.filter((itemId) => itemId !== id)
      } else {
        return [...prev, id]
      }
    })
  }

  // 编辑菜单项
  const handleEditItem = (item: any, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setCurrentItem({ ...item })
    setEditDialogOpen(true)
  }

  // 添加新菜单项
  const handleAddItem = () => {
    setCurrentItem({
      id: `new-${Date.now()}`,
      title: "",
      path: "",
      icon: "FileText",
      isVisible: true,
      hasChildren: false,
      children: [],
      order: menuItems.length + 1
    })
    setNewItemDialogOpen(true)
  }

  // 保存编辑的菜单项
  const handleSaveItem = async () => {
    if (!currentItem) return
    setSaving(true)

    try {
      let response
      const dbMenu = convertUiMenuToDbMenu(currentItem)

      if (editDialogOpen) {
        // 更新现有菜单
        response = await fetch(`/api/menus/${currentItem.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dbMenu)
        })
      } else if (newItemDialogOpen) {
        // 创建新菜单
        response = await fetch('/api/menus', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dbMenu)
        })
      }

      const result = await response.json()

      if (result.success) {
        toast.success(editDialogOpen ? '菜单更新成功' : '菜单创建成功')
        // 重新加载菜单数据
        await loadMenuItems()
      } else {
        toast.error(`操作失败: ${result.message}`)
      }
    } catch (error) {
      console.error('保存菜单失败:', error)
      toast.error('保存菜单失败，请重试')
    } finally {
      setSaving(false)
      setEditDialogOpen(false)
      setNewItemDialogOpen(false)
      setCurrentItem(null)
    }
  }

  // 删除菜单项
  const handleDeleteItem = async (id: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!confirm('确定要删除该菜单项吗？子菜单也将被删除。')) {
      return
    }

    try {
      const response = await fetch(`/api/menus/${id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('菜单删除成功')
        // 重新加载菜单数据
        await loadMenuItems()
      } else {
        toast.error(`删除失败: ${result.message}`)
      }
    } catch (error) {
      console.error('删除菜单失败:', error)
      toast.error('删除菜单失败，请重试')
    }
  }

  // 切换菜单项可见性
  const toggleItemVisibility = async (id: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const item = menuItems.find(item => item.id === id)
    if (!item) return

    try {
      const response = await fetch(`/api/menus/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ visible: !item.isVisible })
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`菜单已${!item.isVisible ? '显示' : '隐藏'}`)
        // 重新加载菜单数据
        await loadMenuItems()
      } else {
        toast.error(`操作失败: ${result.message}`)
      }
    } catch (error) {
      console.error('切换菜单可见性失败:', error)
      toast.error('操作失败，请重试')
    }
  }

  // 添加子菜单
  const handleAddChild = (parentId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const parent = menuItems.find((item) => item.id === parentId)
    if (parent) {
      setCurrentParent({ ...parent })
      setCurrentChild({
        id: `${parentId}-${Date.now()}`,
        title: "",
        path: "",
        isVisible: true,
        order: parent.children?.length ? parent.children.length + 1 : 1
      })
      setIsEditingChild(false)
      setChildDialogOpen(true)
    }
  }

  // 编辑子菜单
  const handleEditChild = (parentId: string, childId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const parent = menuItems.find((item) => item.id === parentId)
    if (parent) {
      const child = parent.children.find((c: any) => c.id === childId)
      if (child) {
        setCurrentParent({ ...parent })
        setCurrentChild({ ...child })
        setIsEditingChild(true)
        setChildDialogOpen(true)
      }
    }
  }

  // 保存子菜单
  const handleSaveChild = async () => {
    if (!currentParent || !currentChild) return
    setSaving(true)

    try {
      const dbMenu = convertUiMenuToDbMenu(currentChild, true, currentParent.id)
      let response

      if (isEditingChild) {
        // 更新现有子菜单
        response = await fetch(`/api/menus/${currentChild.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dbMenu)
        })
      } else {
        // 创建新子菜单
        response = await fetch('/api/menus', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(dbMenu)
        })
      }

      const result = await response.json()

      if (result.success) {
        toast.success(isEditingChild ? '子菜单更新成功' : '子菜单创建成功')
        // 重新加载菜单数据
        await loadMenuItems()

        // 确保展开父项
        if (!expandedItems.includes(currentParent.id)) {
          setExpandedItems(prev => [...prev, currentParent.id])
        }
      } else {
        toast.error(`操作失败: ${result.message}`)
      }
    } catch (error) {
      console.error('保存子菜单失败:', error)
      toast.error('保存子菜单失败，请重试')
    } finally {
      setSaving(false)
      setChildDialogOpen(false)
      // 延迟清除状态，避免在对话框关闭过程中状态变化导致的渲染问题
      setTimeout(() => {
        setCurrentChild(null)
        setIsEditingChild(false)
      }, 300)
    }
  }

  // 删除子菜单
  const handleDeleteChild = async (parentId: string, childId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!confirm('确定要删除该子菜单项吗？')) {
      return
    }

    try {
      const response = await fetch(`/api/menus/${childId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        toast.success('子菜单删除成功')
        // 重新加载菜单数据
        await loadMenuItems()
      } else {
        toast.error(`删除失败: ${result.message}`)
      }
    } catch (error) {
      console.error('删除子菜单失败:', error)
      toast.error('删除子菜单失败，请重试')
    }
  }

  // 切换子菜单可见性
  const toggleChildVisibility = async (parentId: string, childId: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const parent = menuItems.find(item => item.id === parentId)
    if (!parent || !parent.children) return

    const child = parent.children.find(child => child.id === childId)
    if (!child) return

    try {
      const response = await fetch(`/api/menus/${childId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ visible: !child.isVisible })
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`子菜单已${!child.isVisible ? '显示' : '隐藏'}`)
        // 重新加载菜单数据
        await loadMenuItems()
      } else {
        toast.error(`操作失败: ${result.message}`)
      }
    } catch (error) {
      console.error('切换子菜单可见性失败:', error)
      toast.error('操作失败，请重试')
    }
  }

  // 保存菜单设置
  const saveMenuSettings = async () => {
    try {
      // 更新角色菜单权限
      const response = await fetch('/api/menus/sync-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ roleCode: 'ADMIN' }) // 默认为管理员角色同步所有菜单权限
      })

      const result = await response.json()

      if (result.success) {
        toast.success('菜单设置已保存并同步到权限系统')
      } else {
        toast.error(`保存失败: ${result.message}`)
      }
    } catch (error) {
      console.error('保存菜单设置失败:', error)
      toast.error('保存菜单设置失败，请重试')
    }
  }

  // 渲染图标函数
  const renderIcon = (iconName: string) => {
    const iconConfig = availableIcons.find((icon) => icon.name === iconName)
    if (iconConfig) {
      const IconComponent = iconConfig.icon
      return <IconComponent className="h-4 w-4" />
    }
    return <LayoutDashboard className="h-4 w-4" />
  }

  // 获取图标背景颜色
  const getIconBgColor = (index: number) => {
    const colors = ["bg-blue-100", "bg-green-100", "bg-red-100", "bg-amber-100", "bg-purple-100"]
    return colors[index % colors.length]
  }

  // 获取图标文本颜色
  const getIconTextColor = (index: number) => {
    const colors = ["text-blue-700", "text-green-700", "text-red-700", "text-amber-700", "text-purple-700"]
    return colors[index % colors.length]
  }

  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="菜单设置" text="自定义左侧导航菜单" />
        <div className="space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="border rounded-md p-8 animate-pulse">
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </DashboardShell>
    )
  }

  // 检查用户权限
  const isAdmin = session?.user?.roleCode === 'ADMIN'

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading="菜单设置" text="自定义左侧导航菜单">
          {isAdmin && (
            <Button onClick={handleAddItem} disabled={saving}>
              <Plus className="mr-2 h-4 w-4" />
              添加菜单项
            </Button>
          )}
        </DashboardHeader>

        {!isAdmin ? (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-md">
            <p className="text-yellow-800 dark:text-yellow-400">只有管理员可以编辑菜单设置。</p>
          </div>
        ) : menuItems.length === 0 ? (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-md">
            <p className="text-blue-800 dark:text-blue-400">暂无菜单项，请点击“添加菜单项”按钮创建。</p>
          </div>
        ) : (
          <div className="space-y-4">
            {menuItems.map((item, index) => (
              <div key={item.id} className="border rounded-md overflow-hidden">
                <div className="flex items-center p-4">
                  {item.hasChildren && item.children.length > 0 ? (
                    <button
                      onClick={(e) => toggleExpand(item.id, e)}
                      className="mr-2"
                      aria-label={expandedItems.includes(item.id) ? "折叠" : "展开"}
                      type="button"
                    >
                      {expandedItems.includes(item.id) ? (
                        <ChevronDown className="h-5 w-5" />
                      ) : (
                        <ChevronRight className="h-5 w-5" />
                      )}
                    </button>
                  ) : (
                    <div className="w-7"></div>
                  )}
                  <div
                    className={`flex items-center justify-center w-8 h-8 rounded-full ${getIconBgColor(index)} ${getIconTextColor(index)} mr-3`}
                  >
                    {renderIcon(item.icon)}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{item.title}</div>
                    <div className="text-sm text-muted-foreground">路径: {item.path}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={item.isVisible}
                      onCheckedChange={(checked) => toggleItemVisibility(item.id, { preventDefault: () => {}, stopPropagation: () => {} } as any)}
                      disabled={saving}
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleEditItem(item, e)}
                      className="h-8 w-8"
                      type="button"
                      disabled={saving}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => handleDeleteItem(item.id, e)}
                      className="h-8 w-8"
                      type="button"
                      disabled={saving}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {item.hasChildren && item.children.length > 0 && !expandedItems.includes(item.id) && (
                  <div className="px-4 py-2 border-t bg-muted/20 text-sm text-muted-foreground">
                    {item.children.length} 个子菜单
                  </div>
                )}

                {expandedItems.includes(item.id) && (
                  <>
                    {item.children && item.children.length > 0 && (
                      <div className="border-t">
                        {item.children.map((child: any, childIndex: number) => (
                          <div key={child.id} className="p-4 pl-12 border-b last:border-b-0">
                            <div className="flex items-center">
                              <div
                                className={`flex items-center justify-center w-8 h-8 rounded-full ${getIconBgColor(index + childIndex + 1)} ${getIconTextColor(index + childIndex + 1)} mr-3`}
                              >
                                {renderIcon("FileText")}
                              </div>
                              <div className="flex-1">
                                <div className="font-medium">{child.title}</div>
                                <div className="text-sm text-muted-foreground">路径: {child.path}</div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Switch
                                  checked={child.isVisible}
                                  onCheckedChange={() => toggleChildVisibility(item.id, child.id, { preventDefault: () => {}, stopPropagation: () => {} } as any)}
                                  disabled={saving}
                                />
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => handleEditChild(item.id, child.id, e)}
                                  className="h-8 w-8"
                                  type="button"
                                  disabled={saving}
                                >
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => handleDeleteChild(item.id, child.id, e)}
                                  className="h-8 w-8"
                                  type="button"
                                  disabled={saving}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    <div className="p-2 pl-12 border-t bg-muted/10">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => handleAddChild(item.id, e)}
                        className="w-full justify-start"
                        type="button"
                        disabled={saving}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        添加子菜单
                      </Button>
                    </div>
                  </>
                )}
              </div>
            ))}

            <Button
              onClick={saveMenuSettings}
              className="bg-green-600 hover:bg-green-700"
              disabled={saving}
            >
              {saving ? '正在保存...' : '保存菜单设置'}
            </Button>
          </div>
        )}
      </DashboardShell>

      {/* 编辑菜单项对话框 */}
      <Dialog
        open={editDialogOpen}
        onOpenChange={(open) => {
          setEditDialogOpen(open)
          if (!open) {
            setTimeout(() => setCurrentItem(null), 300)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑菜单项</DialogTitle>
            <DialogDescription>修改菜单项的名称、路径和图标</DialogDescription>
          </DialogHeader>

          {currentItem && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="item-title" className="text-right">
                  菜单名称
                </Label>
                <Input
                  id="item-title"
                  value={currentItem.title}
                  onChange={(e) => setCurrentItem({ ...currentItem, title: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="item-path" className="text-right">
                  路径
                </Label>
                <Input
                  id="item-path"
                  value={currentItem.path}
                  onChange={(e) => setCurrentItem({ ...currentItem, path: e.target.value })}
                  placeholder="/dashboard"
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="item-icon" className="text-right">
                  图标
                </Label>
                <Select
                  value={currentItem.icon}
                  onValueChange={(value) => setCurrentItem({ ...currentItem, icon: value })}
                >
                  <SelectTrigger id="item-icon" className="col-span-3">
                    <SelectValue placeholder="选择图标" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableIcons.map((icon) => (
                      <SelectItem key={icon.name} value={icon.name}>
                        <div className="flex items-center gap-2">
                          {React.createElement(icon.icon, { className: "h-4 w-4" })}
                          <span>{icon.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="has-children" className="text-right">
                  有子菜单
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="has-children"
                    checked={currentItem.hasChildren}
                    onCheckedChange={(checked) => setCurrentItem({ ...currentItem, hasChildren: checked })}
                  />
                  <Label htmlFor="has-children">{currentItem.hasChildren ? "是" : "否"}</Label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)} type="button">
              取消
            </Button>
            <Button onClick={handleSaveItem} type="button">
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 添加菜单项对话框 */}
      <Dialog
        open={newItemDialogOpen}
        onOpenChange={(open) => {
          setNewItemDialogOpen(open)
          if (!open) {
            setTimeout(() => setCurrentItem(null), 300)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加菜单项</DialogTitle>
            <DialogDescription>添加新的菜单项到导航菜单</DialogDescription>
          </DialogHeader>

          {currentItem && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-item-title" className="text-right">
                  菜单名称
                </Label>
                <Input
                  id="new-item-title"
                  value={currentItem.title}
                  onChange={(e) => setCurrentItem({ ...currentItem, title: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-item-path" className="text-right">
                  路径
                </Label>
                <Input
                  id="new-item-path"
                  value={currentItem.path}
                  onChange={(e) => setCurrentItem({ ...currentItem, path: e.target.value })}
                  placeholder="/dashboard"
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-item-icon" className="text-right">
                  图标
                </Label>
                <Select
                  value={currentItem.icon}
                  onValueChange={(value) => setCurrentItem({ ...currentItem, icon: value })}
                >
                  <SelectTrigger id="new-item-icon" className="col-span-3">
                    <SelectValue placeholder="选择图标" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableIcons.map((icon) => (
                      <SelectItem key={icon.name} value={icon.name}>
                        <div className="flex items-center gap-2">
                          {React.createElement(icon.icon, { className: "h-4 w-4" })}
                          <span>{icon.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="new-has-children" className="text-right">
                  有子菜单
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="new-has-children"
                    checked={currentItem.hasChildren}
                    onCheckedChange={(checked) => setCurrentItem({ ...currentItem, hasChildren: checked })}
                  />
                  <Label htmlFor="new-has-children">{currentItem.hasChildren ? "是" : "否"}</Label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setNewItemDialogOpen(false)} type="button">
              取消
            </Button>
            <Button onClick={handleSaveItem} type="button">
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 子菜单编辑对话框 */}
      <Dialog
        open={childDialogOpen}
        onOpenChange={(open) => {
          setChildDialogOpen(open)
          if (!open) {
            setTimeout(() => {
              setCurrentChild(null)
              setIsEditingChild(false)
            }, 300)
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEditingChild ? "编辑子菜单" : "添加子菜单"}</DialogTitle>
            <DialogDescription>
              {isEditingChild ? "修改子菜单项的属性" : `为 "${currentParent?.title}" 添加新的子菜单项`}
            </DialogDescription>
          </DialogHeader>

          {currentChild && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="child-title" className="text-right">
                  菜单名称
                </Label>
                <Input
                  id="child-title"
                  value={currentChild.title}
                  onChange={(e) => setCurrentChild({ ...currentChild, title: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="child-path" className="text-right">
                  路径
                </Label>
                <Input
                  id="child-path"
                  value={currentChild.path}
                  onChange={(e) => setCurrentChild({ ...currentChild, path: e.target.value })}
                  placeholder="/dashboard/subpage"
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="child-visible" className="text-right">
                  可见
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="child-visible"
                    checked={currentChild.isVisible}
                    onCheckedChange={(checked) => setCurrentChild({ ...currentChild, isVisible: checked })}
                  />
                  <Label htmlFor="child-visible">{currentChild.isVisible ? "是" : "否"}</Label>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setChildDialogOpen(false)} type="button">
              取消
            </Button>
            <Button onClick={handleSaveChild} type="button">
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

