import type React from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import Link from "next/link"

interface SettingsLayoutProps {
  children: React.ReactNode
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  return (
    <DashboardShell>
      <DashboardHeader heading="系统设置" text="管理系统设置和外观" />
      <div className="space-y-6">
        <Tabs defaultValue="menu" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="appearance" asChild>
              <Link href="/settings">外观设置</Link>
            </TabsTrigger>
            <TabsTrigger value="menu" asChild>
              <Link href="/settings/menu">菜单设置</Link>
            </TabsTrigger>
            <TabsTrigger value="theme" asChild>
              <Link href="/settings">主题设置</Link>
            </TabsTrigger>
            <TabsTrigger value="performance" asChild>
              <Link href="/settings">性能优化</Link>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <Separator />
        <div className="space-y-4">{children}</div>
      </div>
    </DashboardShell>
  )
}

