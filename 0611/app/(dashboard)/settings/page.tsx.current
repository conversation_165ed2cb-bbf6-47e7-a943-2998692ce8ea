/**
 * 系统设置页面组件
 * 提供系统全局配置和个性化设置功能
 */

"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useTheme } from "next-themes"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "sonner"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"

// Icons
import { Icons } from "@/components/icons"
import {
  ChevronRight,
  ChevronDown,
  Plus,
  Trash2,
  LayoutDashboard,
  Users,
  Settings,
  Upload,
  FileText,
  Phone,
  Bell,
  Mail,
  Calendar,
  Pencil,
  UserRound,
  PanelRight,
  Save,
  PlusCircle,
  Edit,
} from "lucide-react"

/**
 * 系统设置接口
 */
interface SystemSettings {
  siteName: string
  logo: string
  footerText?: string
  description?: string
  keywords?: string
  applicationName?: string
  appleMobileWebAppTitle?: string
  theme: {
    primaryColor: string
    mode: "light" | "dark" | "system"
  }
  features: {
    enableRegistration: boolean
    enablePasswordReset: boolean
    enableNotifications: boolean
  }
  security?: {
    passwordMinLength?: number
    requireSpecialChar?: boolean
    requireNumber?: boolean
    requireUppercase?: boolean
    loginAttempts?: number
    sessionTimeout?: number
  }
  loginPage?: {
    backgroundImage?: string
    backgroundEffect?: "none" | "particles" | "grid" | "dataLines" | "all"
    title?: string
    subtitle?: string
    features?: Array<{
      icon: string
      text: string
    }>
  }
}

/**
 * 默认系统设置
 */
const defaultSettings: SystemSettings = {
  siteName: "外呼管理系统",
  logo: "/logo.png",
  footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
  description: "高效的外呼任务管理平台",
  keywords: "外呼,管理系统,任务管理",
  applicationName: "外呼管理系统",
  appleMobileWebAppTitle: "外呼系统",
  theme: {
    primaryColor: "#0284c7",
    mode: "system"
  },
  features: {
    enableRegistration: true,
    enablePasswordReset: true,
    enableNotifications: true
  },
  security: {
    passwordMinLength: 8,
    requireSpecialChar: true,
    requireNumber: true,
    requireUppercase: true,
    loginAttempts: 5,
    sessionTimeout: 30
  },
  loginPage: {
    backgroundImage: "/placeholder.svg?height=1080&width=1920",
    backgroundEffect: "all",
    title: "外呼管理系统",
    subtitle: "提升工作效率的得力助手",
    features: [
      { icon: "user", text: "专业的客户管理" },
      { icon: "lock", text: "安全的数据保护" },
      { icon: "mail", text: "高效的沟通工具" }
    ]
  }
}

// 可用图标列表 - 用于菜单设置
const availableIcons = [
  { name: "LayoutDashboard", icon: LayoutDashboard },
  { name: "Users", icon: Users },
  { name: "Settings", icon: Settings },
  { name: "Upload", icon: Upload },
  { name: "FileText", icon: FileText },
  { name: "Phone", icon: Phone },
  { name: "Bell", icon: Bell },
  { name: "Mail", icon: Mail },
  { name: "Calendar", icon: Calendar },
  { name: "UserRound", icon: UserRound },
  { name: "PanelRight", icon: PanelRight },
]

// 菜单项类型定义
interface MenuItem {
  id: string
  title: string
  path: string
  href: string
  icon: string
  isVisible: boolean
  hasChildren: boolean
  children: MenuItem[]
  order?: number
  customIconUrl?: string // 添加自定义图标URL字段
}

// 生成唯一ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 9)
}

// 默认菜单配置
const defaultMenuItems: MenuItem[] = []

// 渲染菜单图标
const renderIcon = (iconName: string, customIconUrl?: string) => {
  // 优先使用自定义图标URL
  if (customIconUrl) {
    return (
      <img
        src={customIconUrl}
        alt="菜单图标"
        className="h-4 w-4 object-contain"
      />
    );
  }

  const icon = availableIcons.find(i => i.name === iconName)
  if (icon) {
    return React.createElement(icon.icon, { className: "h-4 w-4" })
  }
  return <LayoutDashboard className="h-4 w-4" />
}

export default function SettingsPage() {
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [activeTab, setActiveTab] = useState("basic")
  const [settings, setSettings] = useState<SystemSettings>({
    siteName: "",
    logo: "",
    theme: {
      primaryColor: "#000000",
      mode: "system"
    },
    features: {
      enableRegistration: false,
      enablePasswordReset: false,
      enableNotifications: false
    },
    loginPage: {
      backgroundImage: "",
      backgroundEffect: "none",
      title: "",
      subtitle: "",
      features: []
    },
    security: {
      passwordMinLength: 8,
      requireSpecialChar: false,
      requireUppercase: false,
      requireNumber: false,
      loginAttempts: 5,
      sessionTimeout: 30
    }
  })
  const [menuItems, setMenuItems] = useState<MenuItem[]>(defaultMenuItems)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [currentItem, setCurrentItem] = useState<MenuItem | null>(null)
  const [currentParent, setCurrentParent] = useState<MenuItem | null>(null)
  const [currentChild, setCurrentChild] = useState<MenuItem | null>(null)
  const [draggingItemId, setDraggingItemId] = useState<string | null>(null)
  const [isLoadingMenus, setIsLoadingMenus] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [showDialog, setShowDialog] = useState(false)
  const [newItemDialogOpen, setNewItemDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [childDialogOpen, setChildDialogOpen] = useState(false)
  const [isEditingChild, setIsEditingChild] = useState(false)

  // 处理路由变化
  useEffect(() => {
    const handleRouteChange = (event: PopStateEvent) => {
      const newParams = new URLSearchParams(window.location.search)
      const newTabParam = newParams.get('tab')
      if (newTabParam && ['basic', 'theme', 'features', 'security', 'loginPage', 'menu', 'footer'].includes(newTabParam)) {
        setActiveTab(newTabParam)
      }
    }

    window.addEventListener('popstate', handleRouteChange)
    return () => window.removeEventListener('popstate', handleRouteChange)
  }, [])

  // 初始化菜单数据
  useEffect(() => {
    const loadMenus = async () => {
      try {
        setIsLoadingMenus(true)
        const response = await fetch('/api/menus')
        const data = await response.json()
        setMenuItems(data)
      } catch (error) {
        console.error('加载菜单失败:', error)
      } finally {
        setIsLoadingMenus(false)
      }
    }

    loadMenus()
  }, [])

  useEffect(() => {
    setMounted(true)
  }, [])

  // 使用正确的 Next.js 路由属性
  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search)
    const tabParam = searchParams.get('tab')
    if (tabParam) {
      setActiveTab(tabParam)
    }
  }, [window.location.search])

  useEffect(() => {
    const handlePopState = (event: PopStateEvent) => {
      const searchParams = new URLSearchParams(window.location.search)
      const tabParam = searchParams.get('tab')
      if (tabParam) {
        setActiveTab(tabParam)
      }
    }

    window.addEventListener('popstate', handlePopState)
    return () => window.removeEventListener('popstate', handlePopState)
  }, [window.location.search])

  const handleRouteChange = (tabParam: string) => {
    setActiveTab(tabParam)
    router.push(`/settings?tab=${tabParam}`)
  }`)
  }
  const [menuItems, setMenuItems] = useState<MenuItem[]>(defaultMenuItems)
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [currentItem, setCurrentItem] = useState<MenuItem | null>(null)
  const [currentParent, setCurrentParent] = useState<MenuItem | null>(null)
  const [currentChild, setCurrentChild] = useState<MenuItem | null>(null)
  const [isEditingChild, setIsEditingChild] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [newItemDialogOpen, setNewItemDialogOpen] = useState(false)
  const [childDialogOpen, setChildDialogOpen] = useState(false)

  // 处理基本设置信息变更
  const handleBasicInfoChange = (field: keyof SystemSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // 处理主题模式变更
  const handleThemeModeChange = (mode: "light" | "dark" | "system") => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...prev.theme,
        mode
      setActiveTab(tabParam)
    }

    // 添加路由变化监听
    const handleRouteChange = () => {
      const newParams = new URLSearchParams(window.location.search)
      const newTabParam = newParams.get('tab')
      if (newTabParam && ['basic', 'theme', 'features', 'security', 'loginPage', 'menu', 'footer'].includes(newTabParam)) {
        setActiveTab(newTabParam)
      }
    }

    // 添加事件监听
    window.addEventListener('popstate', handleRouteChange)

    // 清理函数
    return () => {
      window.removeEventListener('popstate', handleRouteChange)
    }
  }, [router])

  /**
   * 加载系统设置
   */
  useEffect(() => {
    async function loadSettings() {
      try {
        setIsLoading(true)
        const response = await fetch('/api/settings')
        const data = await response.json()

        if (data.success) {
          setSettings(data.data)
          // 同步主题设置
          setTheme(data.data.theme.mode)
        } else {
      toast({
            title: "加载失败",
            description: data.message || "无法加载系统设置",
            variant: "destructive",
      })
        }

        // 加载菜单项
        loadMenusFromApi()
      } catch (error) {
        console.error("加载系统设置失败:", error)
        toast({
          title: "加载失败",
          description: "无法连接到服务器，请检查网络连接",
        variant: "destructive",
      })
      } finally {
        setIsLoading(false)
      }
    }

    if (mounted) {
      loadSettings()
    }
  }, [mounted, setTheme])

  /**
   * 保存系统设置
   * @param showDialog 是否显示对话框，默认为true
   * @param autoRefresh 是否自动刷新页面，默认为false
   */
  const handleSave = async (showDialog: boolean = true, autoRefresh: boolean = false) => {
    try {
      setIsSaving(true)

      // 验证必填字段
      if (!settings.siteName) {
        toast({
          title: "验证失败",
          description: "网站名称不能为空",
          variant: "destructive",
        })
        return
      }

      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...settings,
          theme: settings.theme || defaultSettings.theme,
          features: settings.features || defaultSettings.features,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "保存失败")
      }

      const data = await response.json()

      if (data.success) {
        // 更新本地状态
        setSettings(data.data)

        // 如果需要自动刷新页面
        if (autoRefresh) {
          window.location.reload()
        }

        // 如果需要显示对话框
        if (showDialog) {
          setDialogContent({
            title: "✅ 保存成功",
            description: "系统设置已更新，部分设置可能需要刷新页面后生效。",
            action: () => {
              // 刷新页面以应用新设置
              window.location.reload()
            }
          })
          setShowDialog(true)
        }
      } else {
        throw new Error(data.message || "保存失败")
      }
    } catch (error) {
      console.error("保存系统设置失败:", error)
      // 显示错误对话框
      setDialogContent({
        title: "❌ 保存失败",
        description: error instanceof Error ? error.message : "无法保存系统设置",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsSaving(false)
    }
  }

  /**
   * 处理基本信息变更
   * @param field 变更的字段名
   * @param value 新的值
   */
  const handleBasicInfoChange = (field: keyof SystemSettings, value: string) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  /**
   * 处理Logo上传
   * @param event 文件上传事件
   */
  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "格式错误",
        description: "请上传JPG、PNG、GIF或WebP格式的图片",
        variant: "destructive",
      })
      return
    }

    // 验证文件大小，限制为2MB
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "文件过大",
        description: "Logo图片大小不能超过2MB",
        variant: "destructive",
      })
      return
    }

    // 不需要在客户端验证图片尺寸，因为服务器会自动处理成100x100px

    try {
      // 创建 FormData
      const formData = new FormData()
      formData.append('logo', file)

      // 显示上传中提示
      toast({
        title: "正在上传",
        description: "正在处理图片，请稍候...",
      })

      // 使用新的专用Logo上传API
      const response = await fetch('/api/upload/logo', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        handleBasicInfoChange('logo', data.url)
        toast({
          title: "上传成功",
          description: data.message || "Logo已更新并自动处理为100x100px",
        })

        // 自动保存设置
        handleSave(false, true)
      } else {
        throw new Error(data.message || "上传失败")
      }
    } catch (error) {
      console.error("上传Logo失败:", error)
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "无法上传Logo",
        variant: "destructive",
      })
    }
  }

  /**
   * 处理主题模式变更
   * @param mode 主题模式
   */
  const handleThemeModeChange = (mode: "light" | "dark" | "system") => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...prev.theme,
        mode
      }
    }))
    setTheme(mode)
  }

  /**
   * 处理主题颜色变更
   * @param color 主题颜色
   */
  const handleThemeColorChange = (color: string) => {
    setSettings(prev => ({
      ...prev,
      theme: {
        ...prev.theme,
        primaryColor: color
      }
    }))
  }

  /**
   * 处理功能开关变更
   * @param feature 功能名称
   * @param enabled 是否启用
   */
  const handleFeatureToggle = (feature: keyof SystemSettings['features'], enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [feature]: enabled
      }
    }))
  }

  /**
   * 处理登录页背景图片上传
   * @param event 文件上传事件
   */
  const handleLoginBackgroundUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "格式错误",
        description: "请上传JPG、PNG、GIF或WebP格式的图片",
        variant: "destructive",
      })
      return
    }

    // 验证文件大小，限制为5MB
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "文件过大",
        description: "背景图片大小不能超过5MB",
        variant: "destructive",
      })
      return
    }

    try {
      // 显示上传中提示
      toast({
        title: "正在上传",
        description: "正在处理图片，请稍候...",
      })

      // 创建 FormData
      const formData = new FormData()
      formData.append('image', file)

      // 使用图片上传API
      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        // 更新登录页设置
        setSettings(prev => ({
          ...prev,
          loginPage: {
            ...prev.loginPage,
            backgroundImage: data.data.url
          }
        }))

        toast({
          title: "上传成功",
          description: "登录页背景图片已更新",
        })

        // 不刷新页面，但要保存设置
        const saveResponse = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...settings,
            loginPage: {
              ...settings.loginPage,
              backgroundImage: data.data.url
            }
          }),
        })

        if (!saveResponse.ok) {
          const saveError = await saveResponse.json()
          throw new Error(saveError.message || "保存设置失败")
        }
      } else {
        throw new Error(data.message || "上传失败")
      }
    } catch (error) {
      console.error("上传背景图片失败:", error)
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "无法上传背景图片",
        variant: "destructive",
      })
    }
  }

  /**
   * 处理登录页设置变更
   * @param field 字段名
   * @param value 新值
   * @param autoSave 是否自动保存，默认为否
   */
  const handleLoginPageChange = (field: string, value: any, autoSave: boolean = false) => {
    setSettings(prev => ({
      ...prev,
      loginPage: {
        ...prev.loginPage,
        [field]: value
      }
    }))

    // 如果需要自动保存，则调用保存函数
    if (autoSave) {
      // 使用setTimeout确保状态已更新
      setTimeout(() => {
        handleSave(false, true)
      }, 0)
    }
  }

  // 创建新菜单项
  const createNewItem = () => {
    if (!currentItem) return

    const path = currentItem.path || '';

    // 确保所有必要的字段都有默认值
    const newItem: MenuItem = {
      id: generateId(),
      title: currentItem.title || '',
      path: path,
      href: path, // 确保设置href
      icon: currentItem.icon || 'LayoutDashboard',
      isVisible: currentItem.isVisible ?? true,
      hasChildren: currentItem.hasChildren ?? false,
      children: []
    }

    setMenuItems(prev => [...prev, newItem])
    setNewItemDialogOpen(false)
    setCurrentItem(null)

    toast({
      title: "菜单项创建成功",
      description: `菜单项"${newItem.title}"已添加到菜单列表中。`,
    })
  }

  // 添加子菜单项
  const addChildItem = () => {
    if (!currentChild || !currentParent) return

    const path = currentChild.path || '';

    const newChild: MenuItem = {
      id: generateId(),
      title: currentChild.title || '',
      path: path,
      href: path, // 确保设置href
      icon: currentChild.icon || 'LayoutDashboard',
      isVisible: currentChild.isVisible ?? true,
      hasChildren: false,
      children: []
    }

    setMenuItems(prev =>
      prev.map(item =>
        item.id === currentParent.id
          ? {
              ...item,
              hasChildren: true,
              children: [...item.children, newChild]
            }
          : item
      )
    )

    setChildDialogOpen(false)
    setCurrentChild(null)
    setCurrentParent(null)

    toast({
      title: "子菜单项添加成功",
      description: `子菜单项"${newChild.title}"已添加到"${currentParent.title}"下。`,
    })
  }

  // 处理菜单项拖拽开始
  const handleDragStart = (e: React.DragEvent, id: string) => {
    setDraggingItemId(id)
    e.dataTransfer.setData('text/plain', id)
    // 设置拖拽时的视觉效果
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '0.4'
    }
  }

  // 处理拖拽结束
  const handleDragEnd = (e: React.DragEvent) => {
    setDraggingItemId(null)
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.opacity = '1'
    }
  }

  // 处理拖拽进入目标区域
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.05)'
    }
  }

  // 处理离开目标区域
  const handleDragLeave = (e: React.DragEvent) => {
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = ''
    }
  }

  // 处理放置
  const handleDrop = (e: React.DragEvent, targetId: string) => {
    e.preventDefault()
    if (e.currentTarget instanceof HTMLElement) {
      e.currentTarget.style.backgroundColor = ''
    }

    const sourceId = e.dataTransfer.getData('text/plain')
    if (sourceId === targetId) return

    const updatedItems = [...menuItems]
    const sourceIndex = updatedItems.findIndex(item => item.id === sourceId)
    const targetIndex = updatedItems.findIndex(item => item.id === targetId)

    if (sourceIndex !== -1 && targetIndex !== -1) {
      // 获取源项目和目标项目的order值
      const sourceOrder = sourceIndex
      const targetOrder = targetIndex

      // 重新排序所有项目
      updatedItems.forEach((item, index) => {
        if (sourceIndex < targetIndex) {
          // 向下移动
          if (index === sourceIndex) {
            item.order = targetOrder
          } else if (index > sourceIndex && index <= targetIndex) {
            item.order = (item.order || index) - 1
          }
        } else {
          // 向上移动
          if (index === sourceIndex) {
            item.order = targetOrder
          } else if (index >= targetIndex && index < sourceIndex) {
            item.order = (item.order || index) + 1
          }
        }
      })

      // 按order属性排序
      const sortedItems = updatedItems.sort((a, b) => (a.order || 0) - (b.order || 0))
      setMenuItems(sortedItems)

      // 触发事件通知侧边栏更新
      window.dispatchEvent(new Event('menuItemsUpdated'))
    }
  }

  // 处理自定义图标上传
  const handleCustomIconUpload = async (file: File): Promise<string> => {
    // 如果没有文件，不处理
    if (!file) return ''

    try {
      // 使用FormData上传文件
      const formData = new FormData()
      formData.append('image', file)

      // 显示上传中提示
      toast({
        title: "正在上传",
        description: "正在处理图标，请稍候...",
      })

      // 调用图片上传API
      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "上传成功",
          description: "菜单图标已上传成功",
        })
        return data.data.url
      } else {
        throw new Error(data.message || "上传失败")
      }
    } catch (error) {
      console.error("上传图标失败:", error)
      toast({
        title: "上传失败",
        description: error instanceof Error ? error.message : "无法上传图标",
        variant: "destructive",
      })
      return ''
    }
  }

  // 保存菜单设置并立即使其生效
  const saveMenuAndApply = async (items: MenuItem[]) => {
    try {
      setIsSaving(true);

      // 确保所有菜单项都有有效的 href 和 path
      const validatedItems = items.map(item => {
        // 处理当前菜单项
        const validItem = {
          ...item,
          // 使用 path 作为 href 值，或者使用现有的 href，或者使用 "#" 作为默认值
          href: item.path || item.href || "#",
          path: item.path || item.href || "#" // 确保 path 有值
        };

        // 如果有子菜单，递归处理
        if (validItem.children && validItem.children.length > 0) {
          validItem.children = validItem.children.map(child => ({
            ...child,
            // 对每个子菜单项也做相同处理
            href: child.path || child.href || "#",
            path: child.path || child.href || "#"
          }));
        }

        return validItem;
      });

      // 调用API保存菜单设置
      const response = await fetch('/api/menus/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          menuItems: validatedItems
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '保存菜单设置失败');
      }

      // 触发菜单更新
      // 不使用自定义事件，而是直接刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 1000);

      toast({
        title: "✅ 保存成功",
        description: result.message || "菜单设置已更新并立即生效",
      });
    } catch (error) {
      console.error('保存菜单设置失败:', error);
      toast({
        title: "❌ 保存失败",
        description: error instanceof Error ? error.message : '保存菜单设置失败，请重试',
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }

  // 避免主题闪烁
  if (!mounted) {
    return null
  }

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icons.spinner className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold">系统设置</h1>
        <Button
          onClick={() => handleSave(true, true)}
          disabled={isSaving}
          className="flex items-center gap-2"
        >
          {isSaving ? (
            <>
              <Icons.spinner className="h-4 w-4 animate-spin" />
              <span>保存中...</span>
            </>
          ) : (
            <>
              <span>保存</span>
            </>
          )}
        </Button>
      </div>

      <Tabs defaultValue="basic" className="space-y-4" value={activeTab} onValueChange={(value) => setActiveTab(value)}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="theme">主题设置</TabsTrigger>
          <TabsTrigger value="features">功能开关</TabsTrigger>
          <TabsTrigger value="security">安全设置</TabsTrigger>
          <TabsTrigger value="loginPage">登录页</TabsTrigger>
          <TabsTrigger value="menu">菜单设置</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <Card>
            <CardHeader>
              <CardTitle>基本信息设置</CardTitle>
              <CardDescription>设置网站的基本信息，包括名称和Logo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="siteName">网站名称</Label>
                  <Input
                    id="siteName"
                    value={settings.siteName || ""}
                    onChange={(e) => handleBasicInfoChange("siteName", e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="logo">网站Logo</Label>
                  <div className="flex items-center gap-4">
                    <div className="relative w-24 h-24">
                      {settings.logo && (
                        <Image
                          src={settings.logo}
                          alt="网站Logo"
                          fill
                          className="object-contain"
                        />
                      )}
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => {
                        const input = document.createElement("input");
                        input.type = "file";
                        input.accept = "image/*";
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            handleLogoUpload(file);
                          }
                        };
                        input.click();
                      }}
                    >
                      上传Logo
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="theme">
          <Card>
            <CardHeader>
              <CardTitle>主题设置</CardTitle>
              <CardDescription>配置网站的主题颜色和模式</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="themeMode">主题模式</Label>
                  <Select
                    value={settings.theme?.mode}
                    onValueChange={(value) => handleThemeModeChange(value as "light" | "dark" | "system")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择主题模式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">浅色模式</SelectItem>
                      <SelectItem value="dark">深色模式</SelectItem>
                      <SelectItem value="system">跟随系统</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="primaryColor">主题色</Label>
                  <Input
                    type="color"
                    id="primaryColor"
                    value={settings.theme?.primaryColor || "#000000"}
                    onChange={(e) => handleThemeColorChange(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features">
          <Card>
            <CardHeader>
              <CardTitle>功能开关</CardTitle>
              <CardDescription>控制网站的功能特性</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="enableRegistration">注册功能</Label>
                  <Switch
                    id="enableRegistration"
                    checked={settings.features?.enableRegistration || false}
                    onCheckedChange={(checked) => handleFeatureToggle("enableRegistration", checked)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="enablePasswordReset">密码重置</Label>
                  <Switch
                    id="enablePasswordReset"
                    checked={settings.features?.enablePasswordReset || false}
                    onCheckedChange={(checked) => handleFeatureToggle("enablePasswordReset", checked)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="enableNotifications">通知功能</Label>
                  <Switch
                    id="enableNotifications"
                    checked={settings.features?.enableNotifications || false}
                    onCheckedChange={(checked) => handleFeatureToggle("enableNotifications", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>配置系统的安全选项</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">密码最小长度</Label>
                  <Input
                    type="number"
                    id="passwordMinLength"
                    value={settings.security?.passwordMinLength || ""}
                    onChange={(e) => handleBasicInfoChange("passwordMinLength", parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="requireSpecialChar">需要特殊字符</Label>
                  <Switch
                    id="requireSpecialChar"
                    checked={settings.security?.requireSpecialChar || false}
                    onCheckedChange={(checked) => handleBasicInfoChange("requireSpecialChar", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="loginPage">
          <Card>
            <CardHeader>
              <CardTitle>登录页设置</CardTitle>
              <CardDescription>自定义登录页的外观和内容</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="loginBackground">背景图片</Label>
                  <div className="flex items-center gap-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        const input = document.createElement("input");
                        input.type = "file";
                        input.accept = "image/*";
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            handleLoginBackgroundUpload(file);
                          }
                        };
                        input.click();
                      }}
                    >
                      上传背景图片
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="menu">
          <Card>
            <CardHeader>
              <CardTitle>菜单设置</CardTitle>
              <CardDescription>管理网站的导航菜单</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <h3 className="text-lg font-medium">菜单项</h3>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setNewItemDialogOpen(true);
                    }}
                  >
                    添加菜单项
                  </Button>
                </div>
                <div className="space-y-4">
                  {isLoadingMenus ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                    </div>
                  ) : (
                    menuItems.map((item) => (
                      <div
                        key={item.id}
                        className="border rounded-md overflow-hidden"
                        draggable
                        onDragStart={(e) => handleDragStart(e, item.id)}
                        onDragEnd={handleDragEnd}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, item.id)}
                      >
                        <div className="flex items-center p-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleExpand(item.id)}
                          >
                            {expandedItems.has(item.id) ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                          </Button>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-2">
                                {renderIcon(item.icon, item.customIconUrl)}
                                <span className="truncate">{item.title}</span>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCurrentItem(item);
                                  setEditDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        {expandedItems.has(item.id) && item.children.length > 0 && (
                          <div className="pl-8">
                            {item.children.map((child) => (
                              <div
                                key={child.id}
                                className="border rounded-md overflow-hidden"
                              >
                                <div className="flex items-center p-4">
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center gap-4">
                                      <div className="flex items-center gap-2">
                                        {renderIcon(child.icon, child.customIconUrl)}
                                        <span className="truncate">{child.title}</span>
                                      </div>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          setCurrentParent(item);
                                          setCurrentChild(child);
                                          setChildDialogOpen(true);
                                        }}
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
              保存中...
            </>
          ) : (
            "保存设置"
          )}
        </Button>
      </div>

      <Tabs defaultValue="basic" className="space-y-4" value={activeTab} onValueChange={(value) => {
        setActiveTab(value);
        // 更新URL参数，但不刷新页面
        const url = new URL(window.location.href);
        url.searchParams.set('tab', value);
        window.history.pushState({}, '', url);
      }}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="theme">主题设置</TabsTrigger>
          <TabsTrigger value="features">功能开关</TabsTrigger>
          <TabsTrigger value="security">安全设置</TabsTrigger>
          <TabsTrigger value="loginPage">登录页设置</TabsTrigger>
          <TabsTrigger value="menu">菜单设置</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
            <Card>
              <CardHeader>
              <CardTitle>基本信息设置</CardTitle>
              <CardDescription>
                设置网站的基本信息，包括名称和Logo
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 网站名称设置 */}
                <div className="space-y-2">
                <Label htmlFor="siteName">网站名称</Label>
                <Input
                  id="siteName"
                  value={settings.siteName}
                  onChange={(e) => handleBasicInfoChange('siteName', e.target.value)}
                  placeholder="请输入网站名称"
                />
                <p className="text-sm text-gray-500">
                  网站名称将显示在浏览器标签页和系统界面中
                </p>
                </div>

              {/* Logo设置 */}
                <div className="space-y-2">
                <Label htmlFor="logo">网站Logo</Label>
                <div className="flex items-center gap-4">
                  <div className="relative h-20 w-20 border rounded-lg overflow-hidden">
                    <Image
                      src={settings.logo}
                      alt="网站Logo"
                      fill
                      className="object-contain"
                    />
                </div>
                  <div className="flex flex-col gap-2">
                        <Input
                      id="logo"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => document.getElementById('logo')?.click()}
                      className="flex items-center gap-2"
                    >
                      <Icons.upload className="h-4 w-4" />
                      上传新Logo
                    </Button>
                    <p className="text-sm text-gray-500">
                      请上传图片，将自动处理为100x100px的正方形，大小不超过2MB，支持JPG、PNG、GIF和WebP格式
                    </p>
                      </div>
                    </div>
                </div>

              {/* 页脚文本设置 */}
                <div className="space-y-2">
                <Label htmlFor="footerText">页脚文本</Label>
                <textarea
                  id="footerText"
                  value={settings.footerText || ''}
                  onChange={(e) => handleBasicInfoChange('footerText', e.target.value)}
                  placeholder="请输入页脚文本"
                  rows={3}
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                />
                <p className="text-sm text-gray-500">
                  页脚文本将显示在网站底部，支持基本的HTML标签
                </p>
                </div>

              {/* 网站元数据设置 */}
              <div className="mt-6">
                <h3 className="text-lg font-medium mb-4">网站元数据设置</h3>
                <div className="space-y-4">
                  {/* 网站描述 */}
                  <div className="space-y-2">
                    <Label htmlFor="description">网站描述</Label>
                    <textarea
                      id="description"
                      value={settings.description || ''}
                      onChange={(e) => handleBasicInfoChange('description', e.target.value)}
                      placeholder="请输入网站描述"
                      rows={2}
                      className="flex min-h-[60px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                    />
                    <p className="text-sm text-gray-500">网站描述将显示在搜索引擎结果中</p>
                  </div>

                  {/* 关键词 */}
                  <div className="space-y-2">
                    <Label htmlFor="keywords">关键词</Label>
                    <Input
                      id="keywords"
                      value={settings.keywords || ''}
                      onChange={(e) => handleBasicInfoChange('keywords', e.target.value)}
                      placeholder="请输入关键词，用逗号分隔"
                    />
                    <p className="text-sm text-gray-500">关键词用于搜索引擎优化，多个关键词用逗号分隔</p>
                  </div>

                  {/* 应用名称 */}
                  <div className="space-y-2">
                    <Label htmlFor="applicationName">应用名称</Label>
                    <Input
                      id="applicationName"
                      value={settings.applicationName || ''}
                      onChange={(e) => handleBasicInfoChange('applicationName', e.target.value)}
                      placeholder="请输入应用名称"
                    />
                    <p className="text-sm text-gray-500">应用名称用于浏览器标签页和移动设备上的显示</p>
                  </div>

                  {/* iOS应用名称 */}
                  <div className="space-y-2">
                    <Label htmlFor="appleMobileWebAppTitle">iOS应用名称</Label>
                    <Input
                      id="appleMobileWebAppTitle"
                      value={settings.appleMobileWebAppTitle || ''}
                      onChange={(e) => handleBasicInfoChange('appleMobileWebAppTitle', e.target.value)}
                      placeholder="请输入iOS应用名称"
                    />
                    <p className="text-sm text-gray-500">
                      当用户将网站添加到iOS设备主屏幕时显示的名称，建议使用简短易记的名称，如"外呼系统"
                    </p>
                  </div>
                </div>
              </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="theme">
            <Card>
              <CardHeader>
                <CardTitle>主题设置</CardTitle>
                <CardDescription>
                自定义系统的主题外观，包括主题模式和颜色
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 主题模式设置 */}
              <div className="space-y-2">
                <Label>主题模式</Label>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant={settings.theme.mode === "light" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("light")}
                  >
                    <Icons.sun className="h-4 w-4 mr-2" />
                    明亮
                  </Button>
                  <Button
                    variant={settings.theme.mode === "dark" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("dark")}
                  >
                    <Icons.moon className="h-4 w-4 mr-2" />
                    暗黑
                  </Button>
                  <Button
                    variant={settings.theme.mode === "system" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => handleThemeModeChange("system")}
                  >
                    <Icons.laptop className="h-4 w-4 mr-2" />
                    跟随系统
                  </Button>
                </div>
                <p className="text-sm text-gray-500">
                  选择适合您的主题模式，可以随时切换
                </p>
              </div>

              {/* 主题颜色设置 */}
              <div className="space-y-2">
                <Label>主题颜色</Label>
                <div className="flex items-center gap-4">
                  <Input
                    type="color"
                    value={settings.theme.primaryColor}
                    onChange={(e) => handleThemeColorChange(e.target.value)}
                    className="w-20 h-10 p-1"
                  />
                  <Input
                    type="text"
                    value={settings.theme.primaryColor}
                    onChange={(e) => handleThemeColorChange(e.target.value)}
                    placeholder="#000000"
                    className="w-32"
                  />
                </div>
                <p className="text-sm text-gray-500">
                  选择系统的主题颜色，将应用于按钮、链接等元素
                </p>
              </div>
              </CardContent>
            </Card>
          </TabsContent>

        <TabsContent value="features">
            <Card>
              <CardHeader>
              <CardTitle>功能开关</CardTitle>
              <CardDescription>
                控制系统功能的开启和关闭
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 用户注册开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>用户注册</Label>
                  <p className="text-sm text-gray-500">
                    是否允许新用户注册账号
                  </p>
                    </div>
                    <Switch
                  checked={settings.features.enableRegistration}
                  onCheckedChange={(checked) => handleFeatureToggle('enableRegistration', checked)}
                    />
                  </div>

                  <Separator />

              {/* 密码重置开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>密码重置</Label>
                  <p className="text-sm text-gray-500">
                    是否允许用户重置密码
                  </p>
                    </div>
                <Switch
                  checked={settings.features.enablePasswordReset}
                  onCheckedChange={(checked) => handleFeatureToggle('enablePasswordReset', checked)}
                />
                  </div>

                  <Separator />

              {/* 系统通知开关 */}
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                  <Label>系统通知</Label>
                  <p className="text-sm text-gray-500">
                    是否启用系统通知功能
                    </p>
                  </div>
                <Switch
                  checked={settings.features.enableNotifications}
                  onCheckedChange={(checked) => handleFeatureToggle('enableNotifications', checked)}
                />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <Card>
              <CardHeader>
              <CardTitle>安全设置</CardTitle>
              <CardDescription>
                配置系统的安全选项，包括密码策略和登录限制
              </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
              {/* 密码最小长度 */}
                <div className="space-y-2">
                <Label htmlFor="passwordMinLength">密码最小长度</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="passwordMinLength"
                    type="number"
                    min="6"
                    max="20"
                    value={settings.security?.passwordMinLength || 8}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 6 && value <= 20) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            passwordMinLength: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">字符</span>
                </div>
                <p className="text-sm text-gray-500">
                  设置密码的最小长度要求，建议不少于8个字符
                </p>
                </div>

                <Separator />

              {/* 密码复杂度要求 */}
                <div className="space-y-4">
                <h3 className="text-sm font-medium">密码复杂度要求</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含特殊字符</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个特殊字符（如 @#$%）
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireSpecialChar || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireSpecialChar: checked
                        }
                      }))
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含数字</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个数字
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireNumber || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireNumber: checked
                        }
                      }))
                    }}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>要求包含大写字母</Label>
                    <p className="text-sm text-gray-500">
                      密码必须包含至少一个大写字母
                    </p>
                  </div>
                  <Switch
                    checked={settings.security?.requireUppercase || false}
                    onCheckedChange={(checked) => {
                      setSettings(prev => ({
                        ...prev,
                        security: {
                          ...prev.security,
                          requireUppercase: checked
                        }
                      }))
                    }}
                  />
                </div>
                </div>

                <Separator />

              {/* 登录尝试限制 */}
                <div className="space-y-2">
                <Label htmlFor="loginAttempts">登录尝试限制</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="loginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    value={settings.security?.loginAttempts || 5}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 3 && value <= 10) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            loginAttempts: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">次</span>
                </div>
                <p className="text-sm text-gray-500">
                  超过指定次数的失败登录尝试后，账户将被临时锁定
                </p>
                </div>

                <Separator />

              {/* 会话超时时间 */}
                <div className="space-y-2">
                <Label htmlFor="sessionTimeout">会话超时时间</Label>
                <div className="flex items-center gap-4">
                  <Input
                    id="sessionTimeout"
                    type="number"
                    min="5"
                    max="120"
                    value={settings.security?.sessionTimeout || 30}
                    onChange={(e) => {
                      const value = parseInt(e.target.value)
                      if (!isNaN(value) && value >= 5 && value <= 120) {
                        setSettings(prev => ({
                          ...prev,
                          security: {
                            ...prev.security,
                            sessionTimeout: value
                          }
                        }))
                      }
                    }}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">分钟</span>
                </div>
                <p className="text-sm text-gray-500">
                  用户无操作超过指定时间后，将自动退出登录
                </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="loginPage">
            <Card>
              <CardHeader>
                <CardTitle>登录页设置</CardTitle>
                <CardDescription>
                  自定义系统登录页的外观和内容
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 背景图片设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">背景图片</h3>
                  <div className="flex items-start gap-4">
                    <div className="w-1/3 aspect-video relative rounded-md overflow-hidden border">
                      <div
                        className="absolute inset-0 bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${settings.loginPage?.backgroundImage || defaultSettings.loginPage?.backgroundImage})`,
                        }}
                      />
                    </div>
                    <div className="flex-1 space-y-4">
                      <input
                        id="loginBackground"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleLoginBackgroundUpload}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => document.getElementById('loginBackground')?.click()}
                        className="flex items-center gap-2"
                      >
                        <Icons.upload className="h-4 w-4" />
                        上传新背景
                      </Button>
                      <p className="text-sm text-gray-500">
                        建议上传宽度为1920px的图片，大小不超过5MB，支持JPG、PNG、GIF和WebP格式
                      </p>
                    </div>
                  </div>
                </div>

                {/* 背景特效说明 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">背景特效</h3>
                  <p className="text-sm text-gray-500">
                    登录页面已内置背景特效，无需额外设置。
                  </p>
                </div>

                {/* 文字内容设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">文字内容</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="loginTitle">标题</Label>
                      <Input
                        id="loginTitle"
                        value={settings.loginPage?.title || ''}
                        onChange={(e) => handleLoginPageChange('title', e.target.value)}
                        onBlur={() => handleSave(false, true)}
                        placeholder="请输入登录页标题"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="loginSubtitle">副标题</Label>
                      <Input
                        id="loginSubtitle"
                        value={settings.loginPage?.subtitle || ''}
                        onChange={(e) => handleLoginPageChange('subtitle', e.target.value)}
                        onBlur={() => handleSave(false, true)}
                        placeholder="请输入登录页副标题"
                      />
                    </div>
                  </div>
                </div>

                {/* 特性列表设置 */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">特性列表</h3>
                  <div className="space-y-4">
                    {(settings.loginPage?.features || []).map((feature, index) => (
                      <div key={index} className="flex items-center gap-4">
                        <div className="w-1/4">
                          <Label htmlFor={`featureIcon${index}`}>图标</Label>
                          <Select
                            value={feature.icon}
                            onValueChange={(value) => {
                              const newFeatures = [...(settings.loginPage?.features || [])]
                              newFeatures[index] = { ...newFeatures[index], icon: value }
                              handleLoginPageChange('features', newFeatures, true)
                            }}
                          >
                            <SelectTrigger id={`featureIcon${index}`}>
                              <SelectValue placeholder="选择图标" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user">用户</SelectItem>
                              <SelectItem value="lock">锁定</SelectItem>
                              <SelectItem value="mail">邮件</SelectItem>
                              <SelectItem value="settings">设置</SelectItem>
                              <SelectItem value="bell">通知</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="w-1/2">
                          <Label htmlFor={`featureText${index}`}>文本</Label>
                          <Input
                            id={`featureText${index}`}
                            value={feature.text}
                            onChange={(e) => {
                              const newFeatures = [...(settings.loginPage?.features || [])]
                              newFeatures[index] = { ...newFeatures[index], text: e.target.value }
                              handleLoginPageChange('features', newFeatures, true)
                            }}
                            placeholder="请输入特性描述"
                          />
                        </div>
                        <div className="flex-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setIsEditingChild(true)
                              setCurrentChild(feature)
                              setChildDialogOpen(true)
                            }}
                          >
                            编辑
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const updatedFeatures = settings.loginPage?.features?.filter(
                                (f, i) => i !== index
                              ) || []
                              handleLoginPageChange('features', updatedFeatures, true)
                            }}
                          >
                            删除
                          </Button>
                        </div>
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const newFeatures = [...(settings.loginPage?.features || []), { icon: 'user', text: '新特性' }]
                        handleLoginPageChange('features', newFeatures, true)
                      }}
          </TabsContent>

          {/* 添加菜单设置选项卡 */}
          <TabsContent value="menu">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>菜单设置</CardTitle>
                  <CardDescription>
                    自定义左侧导航菜单的显示项目
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={loadMenusFromApi}
                    disabled={isLoadingMenus}
                    className="flex items-center gap-2"
                  >
                    {isLoadingMenus ? (
                      <>
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        加载中...
                      </>
                    ) : (
                      <>
                        <FileText className="h-4 w-4" />
                        从服务器加载菜单
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={() => saveMenuAndApply(menuItems)}
                    className="flex items-center gap-2"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                        正在保存...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        保存并应用菜单设置
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex justify-between pb-4 border-b">
                    <h3 className="text-lg font-medium">菜单项管理</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCurrentItem({
                          title: "",
                          path: "",
                          icon: "LayoutDashboard",
                          isVisible: true,
                          hasChildren: false
                        });
                        setNewItemDialogOpen(true);
                      }}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      添加菜单项
                    </Button>
                  </div>

                  {/* 菜单项列表 */}
                  <div className="space-y-4">
                    {isLoadingMenus ? (
                      // 加载中状态
                      <div className="flex items-center justify-center py-8">
                        <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        <span className="ml-3">正在加载菜单数据...</span>
                      </div>
                    ) : menuItems.length === 0 ? (
                      // 无菜单项状态
                      <div className="flex flex-col items-center justify-center py-8 text-center">
                        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium mb-2">暂无菜单项</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          点击“从服务器加载菜单”按钮加载数据库中的菜单项，或点击“添加菜单项”创建新的菜单项。
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={loadMenusFromApi}
                          className="flex items-center gap-2"
                        >
                          <FileText className="h-4 w-4" />
                          从服务器加载菜单
                        </Button>
                      </div>
                    ) : (
                      // 正常显示菜单项
                      menuItems.map((item, index) => (
                        <div
                          key={item.id}
                          className="border rounded-md overflow-hidden"
                          draggable
                          onDragStart={(e) => handleDragStart(e, item.id)}
                          onDragEnd={handleDragEnd}
                          onDragOver={(e) => handleDragOver(e)}
                          onDragLeave={(e) => handleDragLeave(e)}
                          onDrop={(e) => handleDrop(e, item.id)}
                        >
                          <div className="flex items-center p-4">
                            <Button
                            </div>
                          </div>

                          {/* 子菜单项 */}
                          {item.hasChildren && item.children.length > 0 && expandedItems.includes(item.id) && (
                            <div className="px-4 py-2 space-y-2 bg-slate-50 dark:bg-slate-900">
                              {item.children.map((child) => (
                                <div key={child.id} className="flex items-center p-2 rounded-md">
                                  <div className="flex items-center gap-2 flex-1">
                                    <div className="w-6 h-6 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center">
                                      {renderIcon(child.icon, child.customIconUrl)}
                                    </div>
                                    <div>
                                      <div className="font-medium">{child.title}</div>
                                      <div className="text-xs text-muted-foreground">路径: {child.path}</div>
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      // 设置为当前编辑项
                                      setCurrentParent(item);
                                      setCurrentChild({ ...child });
                                      setIsEditingChild(true);
                                      setChildDialogOpen(true);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))
                    )
                  </div>
                </div>

                {/* 对话框等保持不变 */}

              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 提示对话框 */}
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{dialogContent.title}</DialogTitle>
              <DialogDescription>
                {dialogContent.description}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                onClick={() => {
                  setShowDialog(false)
                  if (dialogContent.action) {
                    dialogContent.action()
                  }
                }}
              >
                确定
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* 编辑菜单项对话框 */}
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>编辑菜单项</DialogTitle>
              <DialogDescription>
                修改菜单项的标题、图标和路径等属性
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {/* 菜单项内容编辑表单将在此处实现 */}
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setEditDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit">保存更改</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    )
}

