"use client";

import { PolicyTable } from "@/components/policies/policy-table";
import { PolicyForm } from "@/components/policies/policy-form";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";

export default function PoliciesPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">策略管理</h1>
          <p className="text-muted-foreground">
            管理基于属性的访问控制(ABAC)策略
          </p>
        </div>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新建策略
        </Button>
      </div>

      <PolicyTable />

      <PolicyForm
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        onSubmit={() => {}}
        resources={[]}
        operations={[]}
      />
    </div>
  );
}