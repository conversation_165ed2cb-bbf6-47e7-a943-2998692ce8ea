"use client"

import { useState, useEffect } from "react"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { CalendarIcon, Plus, Trash, <PERSON>, <PERSON>, Send, Save, AlertCircle } from "lucide-react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

// 通知类型定义
interface Notification {
  id: number
  title: string
  content: string
  type: "system" | "task" | "security"
  priority: "low" | "medium" | "high"
  status: "draft" | "published" | "scheduled"
  publishDate: Date | null
  expiryDate: Date | null
  createdAt: Date
  updatedAt: Date
  sendToAll: boolean
  recipientGroups: string[]
}

// 示例通知数据
const initialNotifications: Notification[] = [
  {
    id: 1,
    title: "系统维护通知",
    content: "系统将于2023年8月15日22:00-23:00进行维护，期间可能出现短暂服务中断。",
    type: "system",
    priority: "high",
    status: "published",
    publishDate: new Date("2023-08-10"),
    expiryDate: new Date("2023-08-16"),
    createdAt: new Date("2023-08-08"),
    updatedAt: new Date("2023-08-08"),
    sendToAll: true,
    recipientGroups: ["all"],
  },
  {
    id: 2,
    title: "新功能上线预告",
    content: "外呼系统将于下周上线数据分析功能，敬请期待。",
    type: "system",
    priority: "medium",
    status: "draft",
    publishDate: null,
    expiryDate: null,
    createdAt: new Date("2023-08-07"),
    updatedAt: new Date("2023-08-07"),
    sendToAll: false,
    recipientGroups: ["admin", "manager"],
  },
]

// 用户组选项
const userGroups = [
  { id: "all", name: "所有用户" },
  { id: "admin", name: "管理员" },
  { id: "manager", name: "经理" },
  { id: "agent", name: "坐席人员" },
  { id: "customer", name: "客户" },
]

export default function NotificationsManagePage() {
  const [activeTab, setActiveTab] = useState("all")
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  // 当前编辑的通知
  const [currentNotification, setCurrentNotification] = useState<Notification>({
    id: 0,
    title: "",
    content: "",
    type: "system",
    priority: "medium",
    status: "draft",
    publishDate: null,
    expiryDate: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    sendToAll: true,
    recipientGroups: ["all"],
  })

  // 模拟从服务器加载数据
  useEffect(() => {
    // 在实际应用中，这里应该是从API获取数据
    setTimeout(() => {
      setNotifications(initialNotifications)
      setIsLoading(false)
    }, 500)
  }, [])

  // 根据标签筛选通知
  const getFilteredNotifications = () => {
    return notifications.filter((notification) => {
      if (activeTab === "all") return true
      if (activeTab === "draft") return notification.status === "draft"
      if (activeTab === "published") return notification.status === "published"
      if (activeTab === "scheduled") return notification.status === "scheduled"
      return true
    })
  }

  // 创建新通知
  const createNewNotification = () => {
    const newNotification: Notification = {
      id: Date.now(), // 使用时间戳作为临时ID
      title: "",
      content: "",
      type: "system",
      priority: "medium",
      status: "draft",
      publishDate: null,
      expiryDate: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      sendToAll: true,
      recipientGroups: ["all"],
    }

    setCurrentNotification(newNotification)
    setIsEditing(true)
  }

  // 编辑通知
  const editNotification = (notification: Notification) => {
    setCurrentNotification({ ...notification })
    setIsEditing(true)
  }

  // 预览通知
  const previewNotification = (notification: Notification) => {
    setCurrentNotification({ ...notification })
    setShowPreview(true)
  }

  // 保存通知
  const saveNotification = () => {
    const updatedNotification: Notification = {
      ...currentNotification,
      updatedAt: new Date(),
    }

    // 如果是新通知，添加到列表
    if (!notifications.some((n) => n.id === updatedNotification.id)) {
      setNotifications([...notifications, updatedNotification])
    } else {
      // 更新现有通知
      setNotifications(notifications.map((n) => (n.id === updatedNotification.id ? updatedNotification : n)))
    }

    setIsEditing(false)
    toast({
      title: "通知已保存",
      description: "通知已成功保存为草稿",
    })
  }

  // 发布通知
  const publishNotification = () => {
    const updatedNotification: Notification = {
      ...currentNotification,
      status: "published",
      publishDate: new Date(),
      updatedAt: new Date(),
    }

    // 更新通知列表
    if (!notifications.some((n) => n.id === updatedNotification.id)) {
      setNotifications([...notifications, updatedNotification])
    } else {
      setNotifications(notifications.map((n) => (n.id === updatedNotification.id ? updatedNotification : n)))
    }

    setIsEditing(false)
    toast({
      title: "通知已发布",
      description: "通知已成功发布，用户将立即收到此通知",
    })
  }

  // 删除通知
  const deleteNotification = (id: number) => {
    setNotifications(notifications.filter((n) => n.id !== id))
    toast({
      title: "通知已删除",
      description: "通知已成功删除",
    })
  }

  return (
    <DashboardShell>
      <DashboardHeader heading="通知管理" text="创建和管理系统通知，发布重要信息给用户">
        <Button onClick={createNewNotification}>
          <Plus className="mr-2 h-4 w-4" />
          创建通知
        </Button>
      </DashboardHeader>

      <div className="grid gap-8">
        {/* 通知列表 */}
        <Card>
          <CardHeader>
            <CardTitle>通知列表</CardTitle>
            <CardDescription>管理所有系统通知</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="draft">草稿</TabsTrigger>
                <TabsTrigger value="published">已发布</TabsTrigger>
                <TabsTrigger value="scheduled">计划发布</TabsTrigger>
              </TabsList>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>标题</TableHead>
                        <TableHead>类型</TableHead>
                        <TableHead>优先级</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>发布日期</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getFilteredNotifications().length > 0 ? (
                        getFilteredNotifications().map((notification) => (
                          <TableRow key={notification.id}>
                            <TableCell className="font-medium">{notification.title}</TableCell>
                            <TableCell>
                              {notification.type === "system" && "系统通知"}
                              {notification.type === "task" && "任务通知"}
                              {notification.type === "security" && "安全通知"}
                            </TableCell>
                            <TableCell>
                              {notification.priority === "low" && "低"}
                              {notification.priority === "medium" && "中"}
                              {notification.priority === "high" && "高"}
                            </TableCell>
                            <TableCell>
                              {notification.status === "draft" && "草稿"}
                              {notification.status === "published" && "已发布"}
                              {notification.status === "scheduled" && "计划发布"}
                            </TableCell>
                            <TableCell>
                              {notification.publishDate ? format(notification.publishDate, "yyyy-MM-dd") : "-"}
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button variant="outline" size="sm" onClick={() => editNotification(notification)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => previewNotification(notification)}>
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => deleteNotification(notification.id)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4">
                            没有找到通知
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* 编辑通知对话框 */}
      <Dialog open={isEditing} onOpenChange={setIsEditing}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{currentNotification.id === 0 ? "创建新通知" : "编辑通知"}</DialogTitle>
            <DialogDescription>填写通知详情并选择发布选项</DialogDescription>
          </DialogHeader>

          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">通知标题</Label>
                <Input
                  id="title"
                  value={currentNotification.title}
                  onChange={(e) => setCurrentNotification({ ...currentNotification, title: e.target.value })}
                  placeholder="输入通知标题"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">通知类型</Label>
                <Select
                  value={currentNotification.type}
                  onValueChange={(value: "system" | "task" | "security") =>
                    setCurrentNotification({ ...currentNotification, type: value })
                  }
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择通知类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="system">系统通知</SelectItem>
                    <SelectItem value="task">任务通知</SelectItem>
                    <SelectItem value="security">安全通知</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">优先级</Label>
                <Select
                  value={currentNotification.priority}
                  onValueChange={(value: "low" | "medium" | "high") =>
                    setCurrentNotification({ ...currentNotification, priority: value })
                  }
                >
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="选择优先级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">低</SelectItem>
                    <SelectItem value="medium">中</SelectItem>
                    <SelectItem value="high">高</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">状态</Label>
                <Select
                  value={currentNotification.status}
                  onValueChange={(value: "draft" | "published" | "scheduled") =>
                    setCurrentNotification({ ...currentNotification, status: value })
                  }
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="published">已发布</SelectItem>
                    <SelectItem value="scheduled">计划发布</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {currentNotification.status === "scheduled" && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>计划发布日期</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {currentNotification.publishDate ? (
                          format(currentNotification.publishDate, "yyyy-MM-dd")
                        ) : (
                          <span>选择日期</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={currentNotification.publishDate || undefined}
                        onSelect={(date) =>
                          setCurrentNotification({
                            ...currentNotification,
                            publishDate: date || null,
                          })
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>过期日期</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {currentNotification.expiryDate ? (
                          format(currentNotification.expiryDate, "yyyy-MM-dd")
                        ) : (
                          <span>选择日期</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={currentNotification.expiryDate || undefined}
                        onSelect={(date) =>
                          setCurrentNotification({
                            ...currentNotification,
                            expiryDate: date || null,
                          })
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <Label>通知内容</Label>
              <Textarea
                value={currentNotification.content}
                onChange={(e) => setCurrentNotification({ ...currentNotification, content: e.target.value })}
                placeholder="输入通知内容"
                className="min-h-[200px]"
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="sendToAll"
                  checked={currentNotification.sendToAll}
                  onCheckedChange={(checked) =>
                    setCurrentNotification({
                      ...currentNotification,
                      sendToAll: checked,
                      recipientGroups: checked ? ["all"] : [],
                    })
                  }
                />
                <Label htmlFor="sendToAll">发送给所有用户</Label>
              </div>

              {!currentNotification.sendToAll && (
                <div className="space-y-2">
                  <Label>选择接收用户组</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {userGroups
                      .filter((g) => g.id !== "all")
                      .map((group) => (
                        <div key={group.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`group-${group.id}`}
                            checked={currentNotification.recipientGroups.includes(group.id)}
                            onChange={(e) => {
                              const updatedGroups = e.target.checked
                                ? [...currentNotification.recipientGroups, group.id]
                                : currentNotification.recipientGroups.filter((g) => g !== group.id)

                              setCurrentNotification({
                                ...currentNotification,
                                recipientGroups: updatedGroups,
                              })
                            }}
                            className="rounded border-gray-300"
                          />
                          <Label htmlFor={`group-${group.id}`}>{group.name}</Label>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditing(false)}>
              取消
            </Button>
            <Button variant="outline" onClick={saveNotification}>
              <Save className="mr-2 h-4 w-4" />
              保存草稿
            </Button>
            <Button onClick={publishNotification}>
              <Send className="mr-2 h-4 w-4" />
              {currentNotification.status === "scheduled" ? "计划发布" : "立即发布"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 预览通知对话框 */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>通知预览</DialogTitle>
            <DialogDescription>以下是通知在用户端的显示效果</DialogDescription>
          </DialogHeader>

          <div className="border rounded-lg p-4 mt-4">
            <div className="flex items-start gap-4">
              <div
                className={`rounded-full p-3 ${
                  currentNotification.type === "system"
                    ? "bg-blue-100 text-blue-600"
                    : currentNotification.type === "task"
                      ? "bg-green-100 text-green-600"
                      : "bg-red-100 text-red-600"
                }`}
              >
                <AlertCircle className="h-5 w-5" />
              </div>

              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{currentNotification.title}</h4>
                  <span className="text-xs text-muted-foreground">
                    {format(currentNotification.createdAt, "yyyy-MM-dd HH:mm")}
                  </span>
                </div>

                <div className="mt-2 text-sm whitespace-pre-wrap">{currentNotification.content}</div>

                <div className="flex items-center mt-2">
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${
                      currentNotification.type === "system"
                        ? "bg-blue-100 text-blue-600"
                        : currentNotification.type === "task"
                          ? "bg-green-100 text-green-600"
                          : "bg-red-100 text-red-600"
                    }`}
                  >
                    {currentNotification.type === "system"
                      ? "系统通知"
                      : currentNotification.type === "task"
                        ? "任务通知"
                        : "安全通知"}
                  </div>

                  {currentNotification.priority === "high" && (
                    <div className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-600 ml-2">重要</div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreview(false)}>
              关闭
            </Button>
            <Button
              onClick={() => {
                setShowPreview(false)
                editNotification(currentNotification)
              }}
            >
              编辑此通知
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}

