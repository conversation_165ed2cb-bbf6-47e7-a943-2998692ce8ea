"use client"

import React, { useState, useMemo, use<PERSON>allback, useEffect } from 'react';
import {
  Edit,
  Trash2,
  Calendar,
  AlertCircle,
  Bell,
  Check,
  Loader,
  RotateCw,
  Trash,
  CheckCircle,
  AlertTriangle,
  AlertOctagon,
  Info,
  Plus,
  Settings,
  CheckSquare,
  BarChart2
} from "lucide-react";
import { NotificationItem, PriorityBadge } from '@/components/notifications/notification-item';
import { Button } from "@/components/ui/button";
import { format } from 'date-fns';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from "@/lib/auth-provider";
import withAuth from "@/lib/with-auth";
import { toast } from "@/components/ui/use-toast";
import Link from "next/link";
// 已在上方导入
import { useRouter } from 'next/navigation';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { cn } from "@/lib/utils";
import logger from '@/lib/utils/logger';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"

// 样式定义
const notificationContentStyle: React.CSSProperties = {
  maxHeight: '100px',
  overflow: 'hidden',
  padding: '10px',
  marginTop: '10px',
  borderRadius: '6px',
  backgroundColor: 'rgb(247, 249, 249)',
  border: '1px solid rgb(236, 238, 240)',
  whiteSpace: 'pre-wrap',
  wordBreak: 'break-word'
};

// 通知类型图标映射
const NotificationTypeIcon = ({ type }: { type: string }) => {
  const typeClass = type?.toLowerCase() || 'system';

  switch (typeClass) {
    case 'success':
      return <Badge variant="outline" className="bg-green-50 text-green-700 h-8 w-8 flex items-center justify-center rounded-full"><Check className="h-5 w-5" /></Badge>;
    case 'warning':
      return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 h-8 w-8 flex items-center justify-center rounded-full"><AlertCircle className="h-5 w-5" /></Badge>;
    case 'error':
      return <Badge variant="outline" className="bg-red-50 text-red-700 h-8 w-8 flex items-center justify-center rounded-full"><AlertCircle className="h-5 w-5" /></Badge>;
    case 'info':
    case 'system':
    default:
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 h-8 w-8 flex items-center justify-center rounded-full"><Bell className="h-5 w-5" /></Badge>;
  }
};

// 使用导入的 PriorityBadge 组件

// 通知项组件 - 已移动到单独文件
function LocalNotificationItem({
  notification,
  onMarkRead,
  onDelete,
  hasManagePermission = false,
}: {
  notification: any;
  onMarkRead?: (id: string) => void;
  onDelete?: (id: string) => void;
  hasManagePermission?: boolean;
}) {
  const [expanded, setExpanded] = useState(false);
  const [open, setOpen] = useState(false);

  const { id, title, content, type, priority, createdAt, read } = notification;

  // 安全处理日期格式
  const formattedDate = useMemo(() => {
    try {
      return new Date(createdAt).toLocaleString();
    } catch (e) {
      return 'Invalid date';
    }
  }, [createdAt]);

  // 限制标题长度
  const limitedTitle = useMemo(() => {
    if (!title) return 'No Title';
    return title.length > 50 ? title.substring(0, 50) + '...' : title;
  }, [title]);

  // 计算内容预览
  const contentPreview = useMemo(() => {
    if (!content) return null;
    return content.length > 200 ? content.substring(0, 200) + '...' : content;
  }, [content]);

  return (
    <div className={`notification-item p-4 mb-4 rounded-lg bg-white ${read ? 'opacity-70 border border-gray-200' : 'border-l-4 border-blue-500 shadow-sm'}`}>
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center">
          <NotificationTypeIcon type={type} />
          <div className="ml-2">
            <div className="flex items-center">
              <h3 className="text-md font-semibold mr-2">{limitedTitle}</h3>
              <PriorityBadge priorityClass={priority} />
              {read && <Badge variant="outline" className="ml-2 bg-gray-100 text-gray-600">已读</Badge>}
            </div>
            <p className="text-xs text-gray-500">{formattedDate}</p>
          </div>
        </div>
        <div className="flex items-center">
          {/* 管理按钮 */}
          {hasManagePermission && (
            <div className="flex space-x-2 z-50">
              <Button
                variant="outline"
                size="sm"
                asChild
              >
                <Link href={`/notifications/edit/${id}`}>
                  <Edit className="h-4 w-4 mr-1" />
                  编辑
                </Link>
              </Button>
              {onDelete && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => setOpen(true)}
                >
                  <Trash className="h-4 w-4 mr-1" />
                  删除
                </Button>
              )}
            </div>
          )}
          {!read && onMarkRead && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onMarkRead(id)}
              className="ml-2"
            >
              <CheckCircle className="h-4 w-4 mr-1" />
              标为已读
            </Button>
          )}
        </div>
      </div>

      {/* 通知内容渲染 */}
      {content && (
        <div
          className="mt-3 p-3 bg-gray-50 rounded-lg border border-gray-200 overflow-auto"
          style={{ position: 'relative', zIndex: 1, maxHeight: expanded ? 'none' : '200px' }}
        >
          {typeof content === 'string' && content.trim().startsWith('<') ? (
            <div
              className={expanded ? '' : 'max-h-[100px] overflow-hidden'}
              dangerouslySetInnerHTML={{ __html: content }}
            />
          ) : (
            <div className={expanded ? '' : 'max-h-[100px] overflow-hidden'}>
              <p style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>{content}</p>
            </div>
          )}

          {content.length > 200 && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-2"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? '收起' : '显示更多'}
            </Button>
          )}
        </div>
      )}

      {/* 删除确认对话框 */}
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除该通知，无法恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (onDelete) {
                  onDelete(id);
                }
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// 创建一个客户端组件包装器
function NotificationsContent() {
  const router = useRouter();
  const auth = useAuth();
  const { user } = auth;

  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasManagePermission, setHasManagePermission] = useState(false);

  // 添加分页状态
  const [page, setPage] = useState(1);
  const [pageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // 新增状态
  const [readFilter, setReadFilter] = useState<'all' | 'read' | 'unread'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  // 修改通知类型状态
  const [notificationTypes, setNotificationTypes] = useState<Array<{
    id: string;
    code: string;
    name: string;
    description?: string;
    icon?: string;
  }>>([]);

  // 检查管理权限
  const checkManagePermission = useCallback(async () => {
    try {
      // 检查所有相关权限
      const [editResponse, deleteResponse, createResponse] = await Promise.all([
        fetch("/api/auth/check-permission", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ resource: "notifications", action: "edit" }),
        }),
        fetch("/api/auth/check-permission", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ resource: "notifications", action: "delete" }),
        }),
        fetch("/api/auth/check-permission", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ resource: "notifications", action: "create" }),
        })
      ]);

      if (editResponse.ok && deleteResponse.ok && createResponse.ok) {
        const editData = await editResponse.json();
        const deleteData = await deleteResponse.json();
        const createData = await createResponse.json();

        // 检查用户是否为管理员
        const userResponse = await fetch('/api/auth/me');
        if (userResponse.ok) {
          const userData = await userResponse.json();
          if (userData.success && userData.data && userData.data.roleCode === 'ADMIN') {
            logger.log('用户是管理员，自动授予所有权限');
            setHasManagePermission(true);
            return;
          }
        }

        setHasManagePermission(
          editData.success && editData.hasPermission &&
          deleteData.success && deleteData.hasPermission &&
          createData.success && createData.hasPermission
        );
      } else {
        setHasManagePermission(false);
      }
    } catch (error) {
      logger.error('检查权限失败:', error);
      setHasManagePermission(false);
    }
  }, []);

  // 初始化时检查权限
  useEffect(() => {
    checkManagePermission();
    logger.log('检查管理权限执行完成');
  }, [checkManagePermission]);

  // 获取通知类型
  const fetchNotificationTypes = async () => {
    try {
      const response = await fetch('/api/notifications/types');
      const result = await response.json();

      if (result.success) {
        setNotificationTypes(result.data);
      } else {
        logger.error('获取通知类型失败:', result.message);
      }
    } catch (error) {
      logger.error('获取通知类型错误:', error);
    }
  };

  // 初始化时获取通知类型
  useEffect(() => {
    fetchNotificationTypes();
  }, []);

  // 获取通知列表
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
      });

      // 添加筛选条件
      if (readFilter !== 'all') {
        queryParams.append('read', readFilter === 'read' ? 'true' : 'false');
      }
      if (selectedType !== 'all') {
        queryParams.append('type', selectedType);
      }
      if (searchTerm) {
        queryParams.append('search', searchTerm);
      }

      const response = await fetch(`/api/notifications?${queryParams.toString()}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || '获取通知列表失败');
      }

      if (result.success) {
        // 检查用户是否关闭了应用内通知
        if (result.data.notifications) {
          setNotifications(result.data.notifications);
          setTotal(result.data.pagination?.total || 0);
          setTotalPages(result.data.pagination?.totalPages || 1);
        } else {
          // 用户关闭了应用内通知
          setNotifications([]);
          setTotal(0);
          setTotalPages(1);
          setError('您已关闭应用内通知，如需接收通知，请在个人中心开启应用内通知');
        }
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      logger.error('获取通知列表错误:', error);
      setError(error instanceof Error ? error.message : '未知错误');
    } finally {
      setLoading(false);
    }
  };

  // 当筛选条件改变时重置页码并重新获取数据
  useEffect(() => {
    setPage(1);
    fetchNotifications();
  }, [readFilter, selectedType, searchTerm]);

  // 监听通知事件
  useEffect(() => {
    fetchNotifications();

    // 监听通知更新事件
    const handleNotificationUpdate = () => {
      logger.log('监听到通知更新事件，刷新通知列表');
      fetchNotifications();
    };

    window.addEventListener('notification:read', handleNotificationUpdate);
    window.addEventListener('notification:deleted', handleNotificationUpdate);
    window.addEventListener('notification:created', handleNotificationUpdate);

    return () => {
      window.removeEventListener('notification:read', handleNotificationUpdate);
      window.removeEventListener('notification:deleted', handleNotificationUpdate);
      window.removeEventListener('notification:created', handleNotificationUpdate);
    };
  }, [page]);

  // 处理页码变化
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // 通知操作处理函数
  const handleEdit = (id: string) => {
    router.push(`/notifications/edit/${id}`);
  };

  const handleMarkRead = async (id: string) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('标记通知为已读失败');
      }

      await fetchNotifications();

      toast({
        title: "成功",
        description: "通知已标记为已读",
      });
    } catch (error) {
      logger.error("标记通知为已读错误:", error);
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 批量标记已读处理函数
  const handleBatchMarkRead = async (options: { all?: boolean, ids?: string[], type?: string }) => {
    try {
      setLoading(true);

      const { all, ids, type } = options;
      const payload: any = {};

      if (all) {
        payload.all = true;
      } else if (ids && ids.length > 0) {
        payload.notificationIds = ids;
      } else if (type) {
        payload.type = type;
      } else {
        throw new Error('请指定要标记的通知');
      }

      const response = await fetch('/api/notifications/batch-read', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '批量标记已读失败');
      }

      const data = await response.json();

      toast({
        title: '批量标记已读成功',
        description: data.message || `成功标记${data.data?.markedCount || 0}条通知为已读`,
        variant: 'success',
      });

      // 重新加载通知列表
      await fetchNotifications();
    } catch (error) {
      logger.error('批量标记已读错误:', error);
      toast({
        title: '批量标记已读失败',
        description: error instanceof Error ? error.message : '请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/notifications/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('删除通知失败');
      }

      await fetchNotifications();

      toast({
        title: "成功",
        description: "通知已删除",
      });
    } catch (error) {
      logger.error("删除通知错误:", error);
      toast({
        title: "错误",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 标记所有通知为已读
  const handleMarkAllRead = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/notifications/read-all', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('标记所有通知为已读失败');
      }

      const data = await response.json();

      if (data.success) {
        toast({
          title: "标记成功",
          description: `${data.data?.count || 0} 条通知已标记为已读`,
        });

        // 触发通知已读事件
        window.dispatchEvent(new CustomEvent('notification:read'));

        // 刷新通知列表
        await fetchNotifications();
      } else {
        throw new Error(data.message || '标记所有通知为已读失败');
      }
    } catch (error) {
      logger.error('标记所有通知为已读错误:', error);
      toast({
        title: "操作失败",
        description: error instanceof Error ? error.message : "操作失败，请重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 渲染主界面
  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">通知中心</h1>
        <div className="flex space-x-2">
          {hasManagePermission && (
            <>
              <Button asChild className="bg-blue-500 hover:bg-blue-600">
                <Link href="/notifications/create">
                  <Plus className="h-5 w-5 mr-1" />
                  发布通知
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/notification-center/types">
                  <Settings className="h-4 w-4 mr-1" />
                  通知类型管理
                </Link>
              </Button>
              <Button asChild variant="outline">
                <Link href="/notifications/stats">
                  <BarChart2 className="h-4 w-4 mr-1" />
                  通知统计
                </Link>
              </Button>
            </>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <CheckSquare className="h-4 w-4 mr-1" />
                批量标记已读
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>标记已读选项</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleBatchMarkRead({ all: true })}>
                <CheckCircle className="h-4 w-4 mr-2" />
                标记所有通知为已读
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleBatchMarkRead({ ids: notifications.filter(n => !n.read).map(n => n.id) })}>
                <CheckCircle className="h-4 w-4 mr-2" />
                标记当前页未读通知
              </DropdownMenuItem>
              {selectedType !== 'all' && (
                <DropdownMenuItem onClick={() => handleBatchMarkRead({ type: selectedType })}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  标记当前类型通知
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* 筛选和搜索区域 */}
      <div className="mb-6 flex flex-wrap gap-4">
        <div className="flex items-center space-x-4">
          <Select
            value={readFilter}
            onValueChange={(value) => setReadFilter(value as 'all' | 'read' | 'unread')}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部消息</SelectItem>
              <SelectItem value="unread">未读消息</SelectItem>
              <SelectItem value="read">已读消息</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={selectedType}
            onValueChange={setSelectedType}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部类型</SelectItem>
              {notificationTypes.map(type => (
                <SelectItem key={type.id} value={type.code}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex-1 max-w-sm relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="搜索标题或内容..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <RotateCw className="h-12 w-12 text-gray-400 animate-spin" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg text-center">
          <AlertCircle className="h-6 w-6 mx-auto mb-2" />
          <p>{error}</p>
        </div>
      ) : (
        <div className="space-y-4">
          {notifications.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无通知
            </div>
          ) : (
            <>
              {/* 通知列表 */}
              <div className="space-y-4">

                {notifications.map((notification: any) => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkRead={handleMarkRead}
                    onDelete={handleDelete}
                    onEdit={handleEdit}
                    hasManagePermission={hasManagePermission}
                    isAdmin={hasManagePermission}
                  />
                ))}
              </div>

              {/* 分页组件 */}
              <div className="mt-4 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(page - 1)}
                        className={cn(page === 1 && "pointer-events-none opacity-50")}
                      />
                    </PaginationItem>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                      <PaginationItem key={pageNum}>
                        <PaginationLink
                          onClick={() => handlePageChange(pageNum)}
                          isActive={pageNum === page}
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(page + 1)}
                        className={cn(page === totalPages && "pointer-events-none opacity-50")}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

// 使用 withAuth 高阶组件包装通知管理页面
const NotificationsPage = () => {
  return <NotificationsContent />;
};

// 导出包装后的页面组件
export default withAuth(NotificationsPage);