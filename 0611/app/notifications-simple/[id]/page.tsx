export default function NotificationDetailPage({ params }: { params: { id: string } }) {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-4">通知详情</h1>
      <p className="text-gray-500 mb-8">查看通知 ID: {params.id}</p>

      <div className="border rounded-lg p-6 bg-white shadow-sm">
        <h2 className="text-xl font-semibold mb-2">通知标题示例</h2>
        <div className="text-sm text-gray-500 mb-4">发布时间: 2023-12-15 10:00</div>

        <div className="prose">
          <p>这是通知的详细内容。这里可以包含格式化的文本、列表和其他内容。</p>
          <ul>
            <li>通知要点 1</li>
            <li>通知要点 2</li>
            <li>通知要点 3</li>
          </ul>
          <p>感谢您阅读此通知。</p>
        </div>
      </div>

      <div className="mt-6">
        <a href="/notifications-simple" className="text-blue-500 hover:underline">
          &larr; 返回通知列表
        </a>
      </div>
    </div>
  )
}

