@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 基础颜色变量 */
    --background: 0 0% 100%; /* 背景色 - 白色 */
    --foreground: 222.2 84% 4.9%; /* 前景色 - 深灰色 */

    /* 卡片相关颜色 */
    --card: 0 0% 100%; /* 卡片背景色 */
    --card-foreground: 222.2 84% 4.9%; /* 卡片文字颜色 */

    /* 弹出层相关颜色 */
    --popover: 0 0% 100%; /* 弹出层背景色 */
    --popover-foreground: 222.2 84% 4.9%; /* 弹出层文字颜色 */

    /* 主要颜色 */
    --primary: 222.2 47.4% 11.2%; /* 主要颜色 - 蓝色 */
    --primary-foreground: 210 40% 98%; /* 主要文字颜色 */

    /* 次要颜色 */
    --secondary: 210 40% 96.1%; /* 次要颜色 */
    --secondary-foreground: 222.2 47.4% 11.2%; /* 次要文字颜色 */

    /* 静音颜色 */
    --muted: 210 40% 96.1%; /* 静音背景色 */
    --muted-foreground: 215.4 16.3% 46.9%; /* 静音文字颜色 */

    /* 强调颜色 */
    --accent: 210 40% 96.1%; /* 强调背景色 */
    --accent-foreground: 222.2 47.4% 11.2%; /* 强调文字颜色 */

    /* 警告/错误颜色 */
    --destructive: 0 84.2% 60.2%; /* 警告/错误背景色 */
    --destructive-foreground: 210 40% 98%; /* 警告/错误文字颜色 */

    /* 边框和输入框相关 */
    --border: 214.3 31.8% 91.4%; /* 边框颜色 */
    --input: 214.3 31.8% 91.4%; /* 输入框边框颜色 */
    --ring: 222.2 84% 4.9%; /* 聚焦环颜色 */

    /* 圆角大小 */
    --radius: 0.5rem;

    /* 侧边栏变量 */
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-muted: 210 40% 96.1%;
    --sidebar-muted-foreground: 215.4 16.3% 46.9%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;

    /* 自定义主题颜色 */
    --theme-color: #0284c7;
  }

  .dark {
    /* 暗色模式颜色变量 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* 侧边栏变量 */
    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-muted: 217.2 32.6% 17.5%;
    --sidebar-muted-foreground: 215 20.2% 65.1%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 侧边栏折叠动画 */
.sidebar-transition {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 宽度过渡 */
.transition-width {
  transition-property: width, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 不透明度过渡 */
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 消除过渡期间内容溢出 */
.overflow-hidden-important {
  overflow: hidden !important;
}

/* 侧边栏背景和文本颜色 */
.bg-sidebar {
  background-color: hsl(var(--sidebar));
}
.text-sidebar-foreground {
  color: hsl(var(--sidebar-foreground));
}
.bg-sidebar-muted {
  background-color: hsl(var(--sidebar-muted));
}
.text-sidebar-muted-foreground {
  color: hsl(var(--sidebar-muted-foreground));
}
.bg-sidebar-accent {
  background-color: hsl(var(--sidebar-accent));
}
.text-sidebar-accent-foreground {
  color: hsl(var(--sidebar-accent-foreground));
}
.border-sidebar-border {
  border-color: hsl(var(--sidebar-border));
}
.bg-sidebar-primary {
  background-color: hsl(var(--sidebar-primary));
}
.text-sidebar-primary-foreground {
  color: hsl(var(--sidebar-primary-foreground));
}

/* 主题颜色应用 */
.theme-color {
  color: var(--theme-color);
}
.bg-theme-color {
  background-color: var(--theme-color);
}
.border-theme-color {
  border-color: var(--theme-color);
}

/* Add styles for the sticky table column */
.table-enhanced {
  border-collapse: separate;
  border-spacing: 0;
}

.table-enhanced th.sticky,
.table-enhanced td.sticky {
  position: sticky;
  right: 0;
  z-index: 2;
  background-color: hsl(var(--background));
}

.table-enhanced th.sticky {
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

.table-enhanced td.sticky {
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

.dark .table-enhanced th.sticky,
.dark .table-enhanced td.sticky {
  background-color: hsl(var(--background));
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
}

/* 登录页面特定动画 */
@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse-particle {
  0% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
  100% { opacity: 0.3; transform: scale(1); }
}

@keyframes data-flow {
  0% { transform: translateX(-100%); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%); opacity: 0; }
}

@keyframes glow {
  0% { opacity: 0.3; }
  50% { opacity: 0.5; }
  100% { opacity: 0.3; }
}

@keyframes scan {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

@keyframes title-glow {
  0% { text-shadow: 0 0 10px rgba(59, 130, 246, 0.5); }
  50% { text-shadow: 0 0 20px rgba(59, 130, 246, 0.7); }
  100% { text-shadow: 0 0 10px rgba(59, 130, 246, 0.5); }
}

@keyframes title-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-gradient {
  animation: gradient 15s ease infinite;
  background-size: 200% 200%;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-particle {
  animation: pulse-particle 3s ease-in-out infinite;
}

.animate-data-flow {
  animation: data-flow 8s linear infinite;
}

.animate-glow {
  animation: glow 4s ease-in-out infinite;
}

.animate-scan {
  animation: scan 4s linear infinite;
}

.animate-title-glow {
  animation: title-glow 3s ease-in-out infinite;
}

.animate-title-shine {
  animation: title-shine 3s linear infinite;
}

.bg-grid {
  background-image: linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.bg-grid-small {
  background-image: linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 10px 10px;
}

/* 登录表单容器 */
.login-form-container {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-lg;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 登录输入框 */
.login-input {
  @apply w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600
         focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400
         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100;
}

/* 登录按钮 */
.login-button {
  @apply w-full px-4 py-2 rounded-lg bg-blue-600 text-white font-semibold
         hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500
         focus:ring-offset-2 transition-colors duration-200;
}

/* 动画延迟工具类 */
.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* 动画持续时间工具类 */
.animation-duration-slow {
  animation-duration: 3s;
}

.animation-duration-slower {
  animation-duration: 5s;
}

/* 渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 特性滑入动画 */
@keyframes featureSlide {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-feature-slide {
  animation: featureSlide 0.5s ease-out forwards;
}

/* 慢速脉冲动画 */
@keyframes pulseSlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulseSlow 3s ease-in-out infinite;
}

/* 暗色主题适配 */
.dark .login-form-container {
  @apply bg-gray-800 border-gray-700;
}

.dark .login-input {
  @apply bg-gray-700 border-gray-600 text-gray-100;
}

.dark .login-button {
  @apply bg-blue-600 hover:bg-blue-700;
}

@layer components {
  /* 登录页面容器 */
  .login-container {
    @apply flex min-h-screen w-full;
  }

  /* 登录页面左侧区域 */
  .login-left {
    @apply relative hidden w-1/2 flex-col justify-between overflow-hidden bg-muted p-10 text-white dark:border-r lg:flex;
  }

  /* 登录页面右侧区域 */
  .login-right {
    @apply flex w-full items-center justify-center p-8 sm:w-auto sm:rounded-lg lg:w-1/2;
  }

  /* 登录表单容器 */
  .login-form-container {
    @apply mx-auto w-full max-w-md space-y-8;
  }

  /* 表单头部 */
  .form-header {
    @apply space-y-6 text-center;
  }

  /* 表单标题 */
  .form-title {
    @apply mt-6 text-center text-3xl font-extrabold tracking-tight text-gray-900;
  }

  /* 表单副标题 */
  .form-subtitle {
    @apply mt-2 text-center text-sm text-gray-600;
  }

  /* 表单组 */
  .form-group {
    @apply space-y-2;
  }

  /* 表单标签 */
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  /* 表单输入框 */
  .form-input {
    @apply block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm;
  }

  /* 表单按钮 */
  .form-button {
    @apply flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
  }

  /* 表单链接 */
  .form-link {
    @apply font-medium text-indigo-600 hover:text-indigo-500;
  }

  /* 表单错误提示 */
  .form-error {
    @apply mt-2 text-sm text-red-600;
  }

  /* 表单成功提示 */
  .form-success {
    @apply mt-2 text-sm text-green-600;
  }

  /* 复选框标签 */
  .form-checkbox-label {
    @apply ml-2 block text-sm text-gray-900;
  }

  /* 复选框 */
  .form-checkbox {
    @apply h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500;
  }

  /* 分隔线容器 */
  .form-divider {
    @apply relative my-6;
  }

  /* 分隔线 */
  .form-divider-line {
    @apply absolute inset-0 flex items-center;
  }

  /* 分隔线内部 */
  .form-divider-line-inner {
    @apply w-full border-t border-gray-300;
  }

  /* 分隔线文本容器 */
  .form-divider-text {
    @apply relative flex justify-center text-sm;
  }

  /* 分隔线文本 */
  .form-divider-text-inner {
    @apply bg-white px-2 text-gray-500;
  }

  /* 社交按钮容器 */
  .social-buttons {
    @apply mt-6 grid grid-cols-3 gap-3;
  }

  /* 社交按钮 */
  .social-button {
    @apply inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 shadow-sm hover:bg-gray-50;
  }

  /* 验证码按钮 */
  .verification-button {
    @apply ml-2 inline-flex items-center rounded-md border border-transparent bg-indigo-100 px-4 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
  }

  /* 密码强度提示 */
  .password-strength {
    @apply mt-1 text-sm;
  }

  /* 密码强度项 */
  .password-strength-item {
    @apply flex items-center space-x-2;
  }

  /* 密码强度图标 */
  .password-strength-icon {
    @apply h-4 w-4;
  }

  /* 密码强度文本 */
  .password-strength-text {
    @apply text-sm text-gray-600;
  }

  /* 对话框遮罩 */
  .dialog-overlay {
    @apply fixed inset-0 bg-black bg-opacity-25;
  }

  /* 对话框内容 */
  .dialog-content {
    @apply fixed left-[50%] top-[50%] max-h-[85vh] w-[90vw] max-w-[450px] translate-x-[-50%] translate-y-[-50%] rounded-[6px] bg-white p-[25px] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] focus:outline-none;
  }

  /* 对话框标题 */
  .dialog-title {
    @apply m-0 text-[17px] font-medium;
  }

  /* 对话框描述 */
  .dialog-description {
    @apply mb-5 mt-[10px] text-[15px] leading-normal;
  }

  /* 对话框关闭按钮 */
  .dialog-close {
    @apply absolute right-[10px] top-[10px] inline-flex h-[25px] w-[25px] appearance-none items-center justify-center rounded-full hover:bg-violet-400 focus:shadow-[0_0_0_2px] focus:outline-none;
  }
}

@layer utilities {
  .transition-width {
    transition-property: width;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* 引入 Quill 编辑器的样式 */
@import 'react-quill/dist/quill.snow.css';

/* 富文本编辑器暗色模式样式调整 */
.dark .quill-editor .ql-toolbar {
  background-color: hsl(var(--muted));
  border-color: hsl(var(--border));
}

.dark .quill-editor .ql-container {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
}

.dark .quill-editor .ql-picker-label {
  color: hsl(var(--foreground));
}

.dark .quill-editor .ql-stroke {
  stroke: hsl(var(--foreground));
}

.dark .quill-editor .ql-fill {
  fill: hsl(var(--foreground));
}

/* 修复富文本编辑器在暗色模式下的图标显示 */
.dark .quill-editor .ql-picker-item {
  color: #333;
}

/* 添加通知内容样式 */
.notification-content img {
  max-width: 100%;
  height: auto;
  margin: 8px 0;
  display: block;
}

.notification-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.notification-content table td,
.notification-content table th {
  border: 1px solid #ddd;
  padding: 8px;
}

.notification-content ul,
.notification-content ol {
  padding-left: 20px;
  margin: 8px 0;
}

.notification-content p {
  margin: 8px 0;
}

.notification-content h1,
.notification-content h2,
.notification-content h3,
.notification-content h4,
.notification-content h5,
.notification-content h6 {
  margin: 16px 0 8px 0;
}

/* 任务状态样式 */
.status-active {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #dcfce7;
  color: #16a34a;
}

.status-processing {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #dbeafe;
  color: #2563eb;
}

.status-pending {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-warning {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #fef3c7;
  color: #d97706;
}

/* 表格增强样式 */
.table-enhanced {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-enhanced th {
  font-weight: 500;
  text-align: left;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table-enhanced td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.table-enhanced tr:hover td {
  background-color: #f9fafb;
}

.table-enhanced .sticky {
  position: sticky;
  right: 0;
  background-color: white;
  z-index: 10;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

.dark .table-enhanced th {
  background-color: #1f2937;
  border-bottom: 1px solid #374151;
}

.dark .table-enhanced td {
  border-bottom: 1px solid #374151;
}

.dark .table-enhanced tr:hover td {
  background-color: #1f2937;
}

.dark .table-enhanced .sticky {
  background-color: #111827;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
}

