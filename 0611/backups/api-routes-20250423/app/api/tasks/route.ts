import { type NextRequest, NextResponse } from "next/server"
import { mockTasks } from "@/lib/api/mock"

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const type = searchParams.get("type") || ""
    const startDate = searchParams.get("startDate") || ""
    const endDate = searchParams.get("endDate") || ""

    // 过滤任务
    let filteredTasks = [...mockTasks]

    if (search) {
      filteredTasks = filteredTasks.filter((task) => task.name.includes(search) || task.content.includes(search))
    }

    if (status) {
      filteredTasks = filteredTasks.filter((task) => task.status === status)
    }

    if (type) {
      filteredTasks = filteredTasks.filter((task) => task.type === type)
    }

    if (startDate) {
      const startDateTime = new Date(startDate).getTime()
      filteredTasks = filteredTasks.filter((task) => new Date(task.startTime).getTime() >= startDateTime)
    }

    if (endDate) {
      const endDateTime = new Date(endDate).getTime()
      filteredTasks = filteredTasks.filter((task) => new Date(task.startTime).getTime() <= endDateTime)
    }

    // 计算分页
    const total = filteredTasks.length
    const totalPages = Math.ceil(total / pageSize)
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedTasks = filteredTasks.slice(start, end)

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取任务列表成功",
        data: {
          list: paginatedTasks,
          total,
          page,
          pageSize,
          totalPages,
        },
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("获取任务列表错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body = await request.json()
    const { name, content, callType, startTime, resource, phoneNumber, smsType, smsTemplate } = body

    // 验证请求参数
    if (!name || !content || !callType || !startTime || !resource) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 },
      )
    }

    // 创建新任务（实际应用中应保存到数据库）
    const newTask = {
      id: `task-${Date.now()}`,
      name,
      type: callType,
      content,
      importTime: new Date().toISOString().replace("T", " ").substring(0, 19),
      startTime,
      progress: 0,
      completionTime: null,
      creator: "当前用户",
      status: "未开始",
    }

    // 返回成功响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "创建任务成功",
        data: newTask,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("创建任务错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

