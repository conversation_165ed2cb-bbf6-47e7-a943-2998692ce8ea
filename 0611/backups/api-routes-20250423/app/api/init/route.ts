import { NextRequest, NextResponse } from "next/server"
import { initNotificationTypes } from "@/lib/services/init-notification-types"

/**
 * 系统初始化API
 * 用于在应用启动时执行必要的初始化操作
 * 
 * @route GET /api/init
 */
export async function GET(request: NextRequest) {
  try {
    // 初始化通知类型
    await initNotificationTypes()
    
    return NextResponse.json({
      success: true,
      message: "系统初始化成功"
    })
  } catch (error) {
    console.error("系统初始化失败:", error)
    return NextResponse.json({
      success: false,
      message: "系统初始化失败: " + (error instanceof Error ? error.message : "未知错误")
    }, { status: 500 })
  }
}
