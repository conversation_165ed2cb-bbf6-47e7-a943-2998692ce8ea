import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { options } from "../../auth/[...nextauth]/options"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 同步菜单权限到jCasbin
 */
export async function POST(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 同步菜单权限`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以同步菜单权限",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const roleCode = body.roleCode || 'ADMIN'

    // 获取角色
    const role = await prisma.role.findUnique({
      where: { code: roleCode },
      include: {
        menus: true
      }
    })

    if (!role) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany()

    // 获取角色当前的所有权限
    const currentPermissions = await CasbinService.getRolePermissions(roleCode)
    
    // 过滤出菜单相关的权限
    const menuPermissions = currentPermissions.filter(p => p[1].startsWith('menu:'))
    
    // 删除所有菜单权限
    for (const permission of menuPermissions) {
      await CasbinService.removePermissionForRole(roleCode, permission[1], permission[2])
    }

    // 添加新的菜单权限
    for (const menu of role.menus) {
      await CasbinService.addPermissionForRole(roleCode, `menu:${menu.code}`, 'view')
    }

    return NextResponse.json({
      success: true,
      message: '菜单权限同步成功',
      data: {
        roleCode,
        menuCount: role.menus.length
      },
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 同步菜单权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "同步菜单权限失败",
      requestId
    }, { status: 500 })
  }
}
