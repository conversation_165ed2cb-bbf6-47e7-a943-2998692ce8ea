import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "../../../../lib/unified-auth-service"
import { AdminNotificationService } from "@/lib/services/admin-notification"

// 验证资料提交请求验证 schema
const verificationSubmitSchema = z.object({
  type: z.enum(["personal", "enterprise"]),

  // 个人认证信息
  realName: z.string().optional(),
  idCardNumber: z.string().optional(),
  idCardFront: z.string().optional(),
  idCardBack: z.string().optional(),
  idCardHolding: z.string().optional(),

  // 企业认证信息
  companyName: z.string().optional(),
  legalPerson: z.string().optional(),
  legalPersonIdCard: z.string().optional(),
  socialCreditCode: z.string().optional(),
  businessLicense: z.string().optional(),
  otherDocuments: z.array(z.string()).optional(),
  remark: z.string().optional(),
})

/**
 * 获取用户认证资料
 * GET /api/user/verification
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 使用统一认证服务获取用户
    const user = await UnifiedAuthService.getCurrentUser(request, "认证资料API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 从数据库获取用户认证资料
    const verification = await prisma.userVerification.findUnique({
      where: { userId: user.id },
    })

    // 如果没有认证资料，返回空数据
    if (!verification) {
      return NextResponse.json(
        {
          success: true,
          message: '获取认证资料成功',
          data: {
            status: "none",
            type: "none",
            data: {}
          }
        },
        { status: 200, headers }
      )
    }

    // 获取认证类型变更记录
    const verificationChange = await prisma.verificationTypeChange.findFirst({
      where: {
        userId: user.id,
        completed: false
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 构建返回数据
    const responseData = {
      status: verification.status,
      type: verification.type,
      reviewedAt: verification.reviewedAt,
      remark: verification.remark,
      data: {
        // 个人认证信息
        realName: verification.realName,
        idCardNumber: verification.idCardNumber,
        idCardFront: verification.idCardFront,
        idCardBack: verification.idCardBack,
        idCardHolding: verification.idCardHolding,

        // 企业认证信息
        companyName: verification.companyName,
        legalPerson: verification.legalPerson,
        legalPersonIdCard: verification.legalPersonIdCard,
        socialCreditCode: verification.socialCreditCode,
        businessLicense: verification.businessLicense,
        otherDocuments: verification.otherDocuments,
      },
      // 如果有未完成的认证类型变更，返回变更记录
      verificationChange: verificationChange && !verificationChange.completed ? {
        deadline: verificationChange.deadline,
        reason: verificationChange.reason,
        originalType: verificationChange.originalType,
        newType: verificationChange.newType
      } : null
    }

    return NextResponse.json(
      { success: true, message: '获取认证资料成功', data: responseData },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("[API] GET /api/user/verification - 错误:", error)
    return NextResponse.json(
      { success: false, message: '服务器错误，请稍后再试', error: 'ServerError' },
      { status: 500 }
    )
  }
}

/**
 * 提交用户认证资料
 * POST /api/user/verification
 */
export async function POST(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 使用统一认证服务获取用户
    const user = await UnifiedAuthService.getCurrentUser(request, "提交认证资料API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 获取请求体
    const body = await request.json()

    // 验证请求数据
    const validationResult = verificationSubmitSchema.safeParse(body)
    if (!validationResult.success) {
      const errorMessage = validationResult.error.errors[0]?.message || '请求数据格式错误'
      return NextResponse.json(
        { success: false, message: errorMessage, error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    const data = validationResult.data

    // 验证必填字段
    if (data.type === "personal") {
      if (!data.realName || !data.idCardNumber || !data.idCardFront || !data.idCardBack || !data.idCardHolding) {
        return NextResponse.json(
          { success: false, message: '个人认证信息不完整，请填写所有必填项', error: 'BadRequest' },
          { status: 400, headers }
        )
      }
    } else if (data.type === "enterprise") {
      if (!data.companyName || !data.legalPerson || !data.legalPersonIdCard || !data.socialCreditCode || !data.businessLicense) {
        return NextResponse.json(
          { success: false, message: '企业认证信息不完整，请填写所有必填项', error: 'BadRequest' },
          { status: 400, headers }
        )
      }
    }

    // 检查用户是否已有认证资料
    const existingVerification = await prisma.userVerification.findUnique({
      where: { userId: user.id },
    })

    // 如果已有相同类型的认证资料且状态为审核中或已认证，则不允许重新提交
    if (existingVerification && existingVerification.type === data.type &&
        (existingVerification.status === "pending" || existingVerification.status === "approved")) {
      const statusText = existingVerification.status === "pending" ? "审核中" : "已认证"
      const typeText = data.type === "personal" ? "个人" : "企业"
      return NextResponse.json(
        { success: false, message: `您的${typeText}认证资料正在${statusText}，不能重新提交`, error: 'BadRequest' },
        { status: 400, headers }
      )
    }

    // 更新用户认证状态
    await prisma.user.update({
      where: { id: user.id },
      data: {
        verificationType: data.type,
        verificationStatus: "pending"
      }
    })

    // 如果已有不同类型的认证资料，则删除原有认证资料
    if (existingVerification && existingVerification.type !== data.type) {
      await prisma.userVerification.delete({
        where: { userId: user.id }
      })
    }

    // 创建或更新认证资料
    const verification = await prisma.userVerification.upsert({
      where: { userId: user.id },
      create: {
        userId: user.id,
        type: data.type,
        status: "pending",

        // 个人认证信息
        realName: data.type === "personal" ? data.realName : null,
        idCardNumber: data.type === "personal" ? data.idCardNumber : null,
        idCardFront: data.type === "personal" ? data.idCardFront : null,
        idCardBack: data.type === "personal" ? data.idCardBack : null,
        idCardHolding: data.type === "personal" ? data.idCardHolding : null,

        // 企业认证信息
        companyName: data.type === "enterprise" ? data.companyName : null,
        legalPerson: data.type === "enterprise" ? data.legalPerson : null,
        legalPersonIdCard: data.type === "enterprise" ? data.legalPersonIdCard : null,
        socialCreditCode: data.type === "enterprise" ? data.socialCreditCode : null,
        businessLicense: data.type === "enterprise" ? data.businessLicense : null,
        otherDocuments: data.type === "enterprise" && data.otherDocuments ? data.otherDocuments : [],
        remark: data.remark || null,
      },
      update: {
        type: data.type,
        status: "pending",

        // 个人认证信息
        realName: data.type === "personal" ? data.realName : null,
        idCardNumber: data.type === "personal" ? data.idCardNumber : null,
        idCardFront: data.type === "personal" ? data.idCardFront : null,
        idCardBack: data.type === "personal" ? data.idCardBack : null,
        idCardHolding: data.type === "personal" ? data.idCardHolding : null,

        // 企业认证信息
        companyName: data.type === "enterprise" ? data.companyName : null,
        legalPerson: data.type === "enterprise" ? data.legalPerson : null,
        legalPersonIdCard: data.type === "enterprise" ? data.legalPersonIdCard : null,
        socialCreditCode: data.type === "enterprise" ? data.socialCreditCode : null,
        businessLicense: data.type === "enterprise" ? data.businessLicense : null,
        otherDocuments: data.type === "enterprise" && data.otherDocuments ? data.otherDocuments : [],
        remark: data.remark || null,

        // 重置审核信息
        reviewerId: null,
        reviewedAt: null,
      },
    })

    // 更新用户认证状态
    await prisma.user.update({
      where: { id: user.id },
      data: {
        verificationStatus: "pending",
        verificationType: data.type
      }
    })

    // 检查是否有认证类型变更记录
    const verificationChange = await prisma.verificationTypeChange.findUnique({
      where: { userId: user.id }
    })

    // 如果有认证类型变更记录，并且用户已提交资料，则标记为已完成
    if (verificationChange && verificationChange.newType === data.type) {
      await prisma.verificationTypeChange.update({
        where: { userId: user.id },
        data: { completed: true }
      })
    }

    // 向管理员发送通知
    try {
      await AdminNotificationService.createVerificationSubmittedNotification(
        user.id,
        user.name || user.username,
        data.type as 'personal' | 'enterprise'
      )
      console.log('已向管理员发送新认证资料提交通知')
    } catch (notificationError) {
      console.error('向管理员发送通知失败:', notificationError)
      // 通知发送失败不影响主流程
    }

    return NextResponse.json(
      {
        success: true,
        message: '认证资料提交成功，请等待审核',
        data: {
          status: verification.status,
          type: verification.type
        }
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("[API] POST /api/user/verification - 错误:", error)
    return NextResponse.json(
      { success: false, message: '服务器错误，请稍后再试', error: 'ServerError' },
      { status: 500 }
    )
  }
}
