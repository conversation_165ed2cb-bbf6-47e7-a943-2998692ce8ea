import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

export async function PUT(req: Request) {
  try {
    // 使用 NextAuth.js 获取用户信息
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      console.log('预设头像API - 未找到有效会话');
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 }
      )
    }

    const userId = session.user.id;

    console.log('预设头像API - 会话信息:', {
      user: session.user,
      userId
    });

    const { avatarUrl } = await req.json()

    if (!avatarUrl) {
      return NextResponse.json(
        { success: false, message: "请选择头像" },
        { status: 400 }
      )
    }

    // 更新用户头像
    const user = await prisma.user.update({
      where: { id: userId },
      data: { image: avatarUrl },
    })

    return NextResponse.json({
      success: true,
      data: { image: user.image },
    })
  } catch (error) {
    console.error("设置头像失败:", error)
    return NextResponse.json(
      { success: false, message: "设置头像失败" },
      { status: 500 }
    )
  }
}