/**
 * 系统日志 API 路由
 * 提供系统日志的查询功能
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthService } from "@/lib/auth-service"
import { SystemLogService } from "@/lib/system-log-service"
import { checkPermission } from "@/lib/middleware/check-permission"

// 获取系统日志列表
export async function GET(request: NextRequest) {
  try {
    // 验证权限
    const user = await AuthService.validateToken(request)
    if (!user) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(user, "logs", "view")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限访问" }, { status: 403 })
    }

    // 记录日志
    await SystemLogService.log({
      userId: user.id,
      action: "view",
      module: "logs",
      details: { message: "查看系统日志" },
    })

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get("page") || "1")
    const pageSize = parseInt(searchParams.get("pageSize") || "10")
    const keyword = searchParams.get("keyword") || ""
    const startDate = searchParams.get("startDate") ? new Date(searchParams.get("startDate") as string) : undefined
    const endDate = searchParams.get("endDate") ? new Date(searchParams.get("endDate") as string) : undefined
    const module = searchParams.get("module") || ""
    const action = searchParams.get("action") || ""

    // 构建查询条件
    const where: any = {}

    if (keyword) {
      where.OR = [
        { userId: { contains: keyword, mode: "insensitive" } },
        { action: { contains: keyword, mode: "insensitive" } },
        { module: { contains: keyword, mode: "insensitive" } },
        { resourceId: { contains: keyword, mode: "insensitive" } },
        { resourceType: { contains: keyword, mode: "insensitive" } },
        { ipAddress: { contains: keyword, mode: "insensitive" } },
      ]
    }

    if (startDate || endDate) {
      where.createdAt = {
        ...(startDate ? { gte: startDate } : {}),
        ...(endDate ? { lte: endDate } : {})
      }
    }

    if (module) {
      where.module = module
    }

    if (action) {
      where.action = action
    }

    // 查询总数
    const total = await prisma.system_log.count({ where })

    // 查询日志记录
    const logs = await prisma.system_log.findMany({
      where,
      orderBy: {
        createdAt: "desc"
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true,
            image: true,
            roleCode: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      logs,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    })
  } catch (error) {
    console.error("获取系统日志失败:", error)
    return NextResponse.json({ success: false, message: "获取系统日志失败" }, { status: 500 })
  }
}
