import { NextResponse } from "next/server"
import { writeFile } from "fs/promises"
import { join } from "path"

/**
 * 处理文件上传
 */
export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get("logo") as File

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: "请选择要上传的文件"
        },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        {
          success: false,
          message: "只能上传图片文件"
        },
        { status: 400 }
      )
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        {
          success: false,
          message: "文件大小不能超过5MB"
        },
        { status: 400 }
      )
    }

    // 生成文件名
    const timestamp = Date.now()
    const extension = file.name.split(".").pop()
    const fileName = `logo-${timestamp}.${extension}`

    // 保存文件
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // 确保上传目录存在
    const uploadDir = join(process.cwd(), "public", "uploads")
    await writeFile(join(uploadDir, fileName), buffer)

    // 返回文件URL
    const fileUrl = `/uploads/${fileName}`

    return NextResponse.json({
      success: true,
      url: fileUrl
    })
  } catch (error) {
    console.error("文件上传失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: "文件上传失败"
      },
      { status: 500 }
    )
  }
} 