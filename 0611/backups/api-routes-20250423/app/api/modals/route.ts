import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { prisma } from "@/lib/prisma"
import { Prisma } from "@prisma/client"

// 定义模态框配置类型
type ModalConfig = {
  id: string
  title: string
  description?: string | null
  maxWidth: string
  customStyles?: Prisma.JsonValue
  createdAt: Date
  updatedAt: Date
  createdById?: string | null
  updatedById?: string | null
}

// 获取模态框配置
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const searchParams = request.nextUrl.searchParams
    const modalId = searchParams.get("modalId")

    if (!modalId) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      )
    }

    // 使用 $queryRaw 代替直接访问 modalConfig
    const modalConfig = await prisma.$queryRaw<ModalConfig[]>`
      SELECT * FROM "modalConfig" WHERE id = ${modalId}
    `

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取模态框配置成功",
        data: modalConfig[0] || null,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("获取模态框配置错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

// 更新模态框配置
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { modalId, config } = body

    if (!modalId || !config) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      )
    }

    // 检查模态框是否存在
    const existingConfig = await prisma.$queryRaw<ModalConfig[]>`
      SELECT * FROM "modalConfig" WHERE id = ${modalId}
    `

    let updatedConfig: ModalConfig

    if (existingConfig && existingConfig.length > 0) {
      // 更新现有配置
      const updateResult = await prisma.$executeRaw`
        UPDATE "modalConfig"
        SET 
          title = ${config.title || existingConfig[0].title},
          description = ${config.description || existingConfig[0].description},
          "maxWidth" = ${config.maxWidth || existingConfig[0].maxWidth},
          "customStyles" = ${config.customStyles ? JSON.stringify(config.customStyles) : existingConfig[0].customStyles},
          "updatedAt" = ${new Date()},
          "updatedById" = ${config.updatedById || null}
        WHERE id = ${modalId}
      `
      
      // 获取更新后的配置
      const updated = await prisma.$queryRaw<ModalConfig[]>`
        SELECT * FROM "modalConfig" WHERE id = ${modalId}
      `
      updatedConfig = updated[0]
    } else {
      // 创建新配置
      await prisma.$executeRaw`
        INSERT INTO "modalConfig" (
          id, title, description, "maxWidth", "customStyles", "createdAt", "updatedAt", "createdById", "updatedById"
        ) VALUES (
          ${modalId},
          ${config.title},
          ${config.description || null},
          ${config.maxWidth || 'lg'},
          ${config.customStyles ? JSON.stringify(config.customStyles) : null},
          ${new Date()},
          ${new Date()},
          ${config.createdById || null},
          ${config.updatedById || null}
        )
      `
      
      // 获取新创建的配置
      const created = await prisma.$queryRaw<ModalConfig[]>`
        SELECT * FROM "modalConfig" WHERE id = ${modalId}
      `
      updatedConfig = created[0]
    }

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "更新模态框配置成功",
        data: updatedConfig,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("更新模态框配置错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

// 删除模态框配置
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const searchParams = request.nextUrl.searchParams
    const modalId = searchParams.get("modalId")

    if (!modalId) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      )
    }

    // 使用 $executeRaw 代替直接访问 modalConfig
    await prisma.$executeRaw`
      DELETE FROM "modalConfig" WHERE id = ${modalId}
    `

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "删除模态框配置成功",
        data: null,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("删除模态框配置错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
} 