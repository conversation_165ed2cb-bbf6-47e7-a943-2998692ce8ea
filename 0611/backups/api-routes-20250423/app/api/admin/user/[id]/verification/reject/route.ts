import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { emailService } from "@/lib/services/email"
import { NotificationService } from "@/lib/services/notification"
import { SystemLogService } from "@/lib/system-log-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { checkPermission } from "@/lib/casbin/enforcer"

/**
 * 管理员驳回用户认证资料
 *
 * @route POST /api/admin/user/[id]/verification/reject
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    // 获取请求体
    const body = await request.json()
    const { reason } = body

    if (!reason || !reason.trim()) {
      return NextResponse.json({
        success: false,
        message: '驳回原因不能为空'
      }, { status: 400 })
    }

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "认证驳回API")
    if (response) {
      return response
    }

    // 使用jCasbin检查特定操作权限
    const hasPermission = await checkPermission(
      admin.id,
      "user_verification",
      "reject",
      { role: admin.roleCode }
    )

    // 如果没有特定操作权限，返回403
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '没有权限执行此操作'
      }, { status: 403 })
    }

    const adminId = admin.id

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { verification: true }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    if (!user.verification) {
      return NextResponse.json({
        success: false,
        message: '用户未提交认证资料'
      }, { status: 400 })
    }

    if (user.verification.status !== 'pending') {
      return NextResponse.json({
        success: false,
        message: '该认证资料已审核，无需重复操作'
      }, { status: 400 })
    }

    // 更新认证状态
    const updatedVerification = await prisma.userVerification.update({
      where: { userId },
      data: {
        status: 'rejected',
        reviewerId: adminId as string,
        reviewedAt: new Date(),
        remark: reason
      }
    })

    // 记录审核日志
    await SystemLogService.log({
      userId: adminId,
      action: "verification_reject",
      module: "verification",
      resourceId: userId,
      resourceType: "user_verification",
      details: {
        verificationType: user.verification.type,
        userName: user.name || user.username,
        userEmail: user.email,
        reviewTime: new Date().toISOString(),
        previousStatus: "pending",
        newStatus: "rejected",
        rejectReason: reason
      }
    })

    // 更新用户认证状态
    await prisma.user.update({
      where: { id: userId },
      data: {
        verificationStatus: 'rejected'
      }
    })

    // 创建系统通知
    try {
      await NotificationService.createVerificationRejectedNotification(
        userId,
        user.verification.type as 'personal' | 'enterprise',
        reason
      )
    } catch (notificationError) {
      console.error('创建认证驳回通知失败:', notificationError)
      // 通知创建失败不影响主流程
    }

    // 发送邮件通知
    try {
      if (user.email) {
        await emailService.sendVerificationRejectedEmail(
          user.email,
          user.name || user.username,
          user.verification.type,
          reason
        )
      }
    } catch (emailError) {
      console.error('发送认证驳回邮件失败:', emailError)
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '认证申请已驳回',
      data: {
        userId,
        status: 'rejected'
      }
    })
  } catch (error) {
    console.error("驳回认证资料错误:", error)
    return NextResponse.json({
      success: false,
      message: '驳回认证资料失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
