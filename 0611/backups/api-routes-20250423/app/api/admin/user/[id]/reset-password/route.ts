/**
 * 管理员重置用户密码
 * 
 * @route POST /api/admin/user/[id]/reset-password
 * @access 需要管理员权限
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { generateRandomPassword } from "@/lib/utils"
import { NotificationService } from "@/lib/notification-service"
import { emailService } from "@/lib/email-service"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { id: adminId as string },
      include: { role: true }
    })

    if (!admin || admin.role?.code !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: '无权执行此操作'
      }, { status: 403 })
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 生成随机密码
    const newPassword = generateRandomPassword()
    const hashedPassword = await hash(newPassword, 10)

    // 更新用户密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword
      }
    })

    // 创建系统通知
    try {
      await NotificationService.createPasswordResetNotification(
        userId,
        admin.id
      )
    } catch (notificationError) {
      console.error('创建密码重置通知失败:', notificationError)
      // 通知创建失败不影响主流程
    }

    // 发送邮件通知
    try {
      if (user.email) {
        await emailService.sendPasswordResetEmail(
          user.email,
          user.name || user.username,
          newPassword
        )
      }
    } catch (emailError) {
      console.error('发送密码重置邮件失败:', emailError)
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '密码重置成功，新密码已通过邮件发送给用户',
      data: {
        password: newPassword
      }
    })
  } catch (error) {
    console.error('重置密码失败:', error)
    return NextResponse.json({
      success: false,
      message: '重置密码失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
