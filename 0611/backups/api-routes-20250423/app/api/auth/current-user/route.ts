import { cookies } from "next/headers"
import { NextResponse } from "next/server"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { v4 as uuidv4 } from 'uuid'
import { getServerSession } from "next-auth/next"
import { options } from "../[...nextauth]/options"

// 使用与登录路由相同的密钥
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

export async function GET(request: Request) {
  const requestId = uuidv4().substring(0, 8)
  console.log(`[当前用户API:${requestId}] 获取当前用户信息`)

  try {
    // 先尝试使用NextAuth获取会话
    const session = await getServerSession(options)
    let userId = null

    if (session?.user?.id) {
      console.log(`[当前用户API:${requestId}] 使用NextAuth会话获取用户ID: ${session.user.id}`)
      userId = session.user.id
    } else {
      // 如果没有NextAuth会话，尝试使用旧的JWT令牌
      console.log(`[当前用户API:${requestId}] 未找到NextAuth会话，尝试使用JWT令牌`)

      const cookieStore = cookies()
      // 仅检查主要的认证 cookie
      const token = cookieStore.get("token")?.value

      if (!token) {
        console.log(`[当前用户API:${requestId}] 未找到认证token`)
        return NextResponse.json({
          success: false,
          message: "未授权访问",
          requestId
        }, { status: 401 })
      }

      // 验证 token
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        userId = payload.userId || payload.sub
      } catch (error) {
        console.error(`[当前用户API:${requestId}] Token验证失败:`, error)
        return NextResponse.json({
          success: false,
          message: "认证已过期",
          requestId
        }, { status: 401 })
      }
    }

    if (!userId) {
      console.log(`[当前用户API:${requestId}] 未找到用户ID`)
      return NextResponse.json({
        success: false,
        message: "无效的认证信息",
        requestId
      }, { status: 401 })
    }

    // 从数据库获取最新的用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId as string },
      include: {
        role: {
          include: {
            menus: true,
            permissions: true
          }
        }
      }
    })

    // 如果是管理员用户，获取所有菜单
    let allMenus = [];
    if (user && (user.roleCode === 'ADMIN' || user.roleCode === 'admin')) {
      console.log(`[当前用户API:${requestId}] 用户是管理员，获取所有菜单`);
      allMenus = await prisma.menu.findMany({
        where: { visible: true },
        orderBy: { order: 'asc' }
      });
    }

    if (!user) {
      console.log(`[当前用户API:${requestId}] 数据库中未找到用户`)
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 确保返回必要的用户信息
    const { password: _, ...userWithoutPassword } = user

    // 如果是管理员并且有菜单数据，将所有菜单添加到用户角色中
    let roleData = user.role;
    if (user.roleCode === 'ADMIN' || user.roleCode === 'admin') {
      if (allMenus && allMenus.length > 0) {
        console.log(`[当前用户API:${requestId}] 为管理员设置所有菜单，菜单数量: ${allMenus.length}`);
        roleData = {
          ...user.role,
          menus: allMenus
        };
      }

      // 确保管理员有所有权限
      if (!roleData.permissions || !roleData.permissions.includes('*')) {
        roleData.permissions = ['*', ...roleData.permissions || []];
      }
    }

    const responseData = {
      ...userWithoutPassword,
      role: roleData
    }

    // 更新用户最后活动时间
    await prisma.user.update({
      where: { id: user.id },
      data: { lastActiveAt: new Date() }
    })

    console.log(`[当前用户API:${requestId}] 成功获取用户信息:`, {
      id: user.id,
      username: user.username,
      roleCode: user.roleCode
    })

    return NextResponse.json({
      success: true,
      data: responseData,
      requestId
    }, {
      status: 200,
      headers: {
        "Cache-Control": "no-store, no-cache, must-revalidate"
      }
    })

  } catch (error) {
    console.error(`[当前用户API:${requestId}] 服务器错误:`, error)
    return NextResponse.json({
      success: false,
      message: "服务器内部错误",
      requestId
    }, { status: 500 })
  }
}