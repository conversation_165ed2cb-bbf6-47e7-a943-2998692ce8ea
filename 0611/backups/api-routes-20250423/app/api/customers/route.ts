/**
 * 客户管理API
 * 处理客户相关的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { ErrorService } from "@/lib/error-service"
import { ApiError } from "@/app/lib/middleware/error-handler"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { checkPermission } from "@/lib/casbin/enforcer"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 获取客户列表
 *
 * @route GET /api/customers
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 首先尝试获取NextAuth会话
    const session = await getServerSession(authOptions);
    console.log('客户管理API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID和角色
    let userId = session?.user?.id;
    let userRole = session?.user?.roleCode;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!userId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true, roleCode: true }
        });
        if (user) {
          userId = user.id;
          userRole = user.roleCode;
          console.log('从数据库中查询到用户ID:', userId, '角色:', userRole);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 如果从NextAuth会话中无法获取用户ID，尝试使用JWT token
    if (!userId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies()
      const token = cookieStore.get("token")?.value

      if (!token) {
        return NextResponse.json({
          success: false,
          message: '未授权访问'
        }, { status: 401 })
      }

      // 解析token获取用户ID
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        userId = payload.sub
        userRole = payload.roleCode as string
        console.log('从令牌中提取的用户ID:', userId, '角色:', userRole);
      } catch (error) {
        console.error("令牌验证失败:", error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息'
        }, { status: 401 })
      }
    } else {
      console.log('从NextAuth会话中提取的用户ID:', userId, '角色:', userRole);
    }

    // 检查用户是否有查看客户列表的权限
    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的用户ID'
      }, { status: 401 })
    }

    // 暂时跳过jCasbin权限验证，因为jCasbin实现有问题
    try {
      // 尝试使用jCasbin检查权限
      const hasPermission = await checkPermission(userId, 'customers', 'read', { role: userRole });

      // 如果不是管理员且没有权限，返回403
      if (userRole !== 'ADMIN' && !hasPermission) {
        return NextResponse.json({
          success: false,
          message: '没有权限访问客户列表'
        }, { status: 403 })
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      // 如果权限检查失败，则只检查是否为管理员
      if (userRole !== 'ADMIN') {
        return NextResponse.json({
          success: false,
          message: '没有权限访问客户列表'
        }, { status: 403 })
      }
    }

    // 查询客户列表（非管理员用户）
    const customers = await prisma.user.findMany({
      where: {
        roleCode: {
          not: 'ADMIN'
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        image: true,
        roleCode: true,
        status: true,
        balance: true,
        creditLimit: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 处理日期格式和转换为前端需要的格式
    const formattedCustomers = customers.map(customer => ({
      id: customer.id,
      username: customer.username,
      email: customer.email,
      name: customer.name,
      phone: customer.phone || "",
      image: customer.image,
      roleCode: customer.roleCode,
      accountType: customer.role.type === "system" ? "admin" : customer.role.type,
      personalVerification: customer.verificationType === "personal" ? customer.verificationStatus : null,
      enterpriseVerification: customer.verificationType === "enterprise" ? customer.verificationStatus : null,
      role: customer.roleCode,
      balance: customer.balance || 0,
      creditLimit: customer.creditLimit || 0,
      status: customer.status,
      createdBy: customer.createdBy ? {
        id: customer.createdBy.id,
        name: customer.createdBy.name || customer.createdBy.username
      } : null,
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString()
    }))

    return NextResponse.json({
      success: true,
      message: '获取客户列表成功',
      data: formattedCustomers
    })
  } catch (error) {
    // 使用错误处理服务记录和格式化错误
    ErrorService.logError(error, { context: '获取客户列表' })

    // 处理不同类型的错误
    if (error instanceof ApiError) {
      return NextResponse.json(
        ErrorService.handleApiError(error),
        { status: error.statusCode }
      )
    }

    // 处理其他错误
    return NextResponse.json(
      ErrorService.handleApiError(error),
      { status: 500 }
    )
  }
}

/**
 * 创建客户
 *
 * @route POST /api/customers
 * @access 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 首先尝试获取NextAuth会话
    const session = await getServerSession(authOptions);
    console.log('创建客户API - NextAuth会话:', session?.user);

    // 从会话中获取用户ID和角色
    let userId = session?.user?.id;
    let userRole = session?.user?.roleCode;

    // 如果会话中没有ID，但有电子邮件，尝试使用电子邮件查询用户
    if (!userId && session?.user?.email) {
      console.log('使用电子邮件查询用户:', session.user.email);
      try {
        const user = await prisma.user.findUnique({
          where: { email: session.user.email },
          select: { id: true, roleCode: true }
        });
        if (user) {
          userId = user.id;
          userRole = user.roleCode;
          console.log('从数据库中查询到用户ID:', userId, '角色:', userRole);
        }
      } catch (error) {
        console.error('查询用户失败:', error);
      }
    }

    // 如果从NextAuth会话中无法获取用户ID，尝试使用JWT token
    if (!userId) {
      console.log("未找到NextAuth会话中的用户ID，尝试使用JWT token");
      const cookieStore = cookies()
      const token = cookieStore.get("token")?.value

      if (!token) {
        return NextResponse.json({
          success: false,
          message: '未授权访问'
        }, { status: 401 })
      }

      // 解析token获取用户ID
      try {
        const { payload } = await jose.jwtVerify(token, JWT_SECRET)
        userId = payload.sub
        userRole = payload.roleCode as string
        console.log('从令牌中提取的用户ID:', userId, '角色:', userRole);
      } catch (error) {
        console.error("令牌验证失败:", error);
        return NextResponse.json({
          success: false,
          message: '无效的认证信息'
        }, { status: 401 })
      }
    } else {
      console.log('从NextAuth会话中提取的用户ID:', userId, '角色:', userRole);
    }

    // 检查用户是否有创建客户的权限
    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的用户ID'
      }, { status: 401 })
    }

    // 暂时跳过jCasbin权限验证，因为jCasbin实现有问题
    try {
      // 尝试使用jCasbin检查权限
      const hasPermission = await checkPermission(userId, 'customers', 'create', { role: userRole });

      // 如果不是管理员且没有权限，返回403
      if (userRole !== 'ADMIN' && !hasPermission) {
        return NextResponse.json({
          success: false,
          message: '没有权限创建客户'
        }, { status: 403 })
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      // 如果权限检查失败，则只检查是否为管理员
      if (userRole !== 'ADMIN') {
        return NextResponse.json({
          success: false,
          message: '没有权限创建客户'
        }, { status: 403 })
      }
    }

    // 解析请求体
    const body = await request.json()
    console.log('收到创建客户请求:', body)
    const { name, username, email, phone, password, accountType, creditLimit } = body

    // 验证必填字段
    if (!username || !email) {
      return NextResponse.json({
        success: false,
        message: '用户名和邮箱为必填项'
      }, { status: 400 })
    }

    // 检查用户名是否已存在
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUsername) {
      return NextResponse.json({
        success: false,
        message: '用户名已存在'
      }, { status: 400 })
    }

    // 检查邮箱是否已存在
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingEmail) {
      return NextResponse.json({
        success: false,
        message: '邮箱已被注册'
      }, { status: 400 })
    }

    // 确定角色代码
    let roleCode = 'USER'
    if (accountType === 'enterprise') {
      roleCode = 'ENTERPRISE'
    }

    // 生成随机密码（如果未提供）
    const userPassword = password || Math.random().toString(36).slice(-8) + 'A1'
    console.log('用户密码:', userPassword)

    // 加密密码
    const hashedPassword = await hash(userPassword, 10)

    // 创建用户
    console.log('开始创建客户:', { username, email, name: name || username, roleCode })
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        name: username, // 始终使用用户名作为昵称
        phone,
        roleCode,
        permissions: [],
        image: null,
        emailVerified: null,
        creditLimit: creditLimit || 0,
        createdById: userId, // 设置创建者ID
        status: 'active' // 确保新用户状态为激活
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        roleCode: true,
        creditLimit: true,
        status: true,
        balance: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      }
    })

    // 格式化返回数据
    const formattedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      phone: user.phone || "",
      roleCode: user.roleCode,
      accountType: user.role.type === "system" ? "admin" : user.role.type,
      role: user.roleCode,
      balance: user.balance || 0,
      creditLimit: user.creditLimit || 0,
      status: user.status,
      createdBy: user.createdBy ? {
        id: user.createdBy.id,
        name: user.createdBy.name || user.createdBy.username
      } : null,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString()
    }

    return NextResponse.json({
      success: true,
      message: '客户创建成功',
      data: formattedUser
    }, { status: 201 })
  } catch (error) {
    // 使用错误处理服务记录和格式化错误
    ErrorService.logError(error, {
      context: '创建客户',
      userData: { username, email }
    })

    // 处理不同类型的错误
    if (error instanceof ApiError) {
      return NextResponse.json(
        ErrorService.handleApiError(error),
        { status: error.statusCode }
      )
    }

    // 处理其他错误
    return NextResponse.json(
      ErrorService.handleApiError(error),
      { status: 500 }
    )
  }
}
