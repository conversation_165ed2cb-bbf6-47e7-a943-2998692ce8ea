/**
 * 客户详情API
 * 处理单个客户相关的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 获取单个客户详情
 *
 * @route GET /api/customers/[id]
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('API接收到的客户ID:', customerId)

    // 如果是ID包含特殊字符，尝试清理
    if (customerId.includes('%') || customerId.includes('+')) {
      try {
        customerId = decodeURIComponent(customerId)
        console.log('再次解码后的ID:', customerId)
      } catch (e) {
        console.error('解码ID时出错:', e)
      }
    }

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查用户是否存在
    console.log('尝试查询客户，ID:', customerId)

    // 先尝试直接查询
    let customer = null

    try {
      customer = await prisma.user.findUnique({
        where: { id: customerId },
        include: {
          role: {
            select: {
              name: true,
              type: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              username: true
            }
          },
          verification: true
        }
      })
    } catch (err) {
      console.error('查询用户时出错:', err)
    }

    // 如果直接查询失败，尝试使用username查询
    // 这里优先使用username查询，因为前端传递的可能就是用户名
    try {
      console.log('尝试使用username查询:', customerId)

      // 尝试模糊匹配用户名
      customer = await prisma.user.findFirst({
        where: {
          OR: [
            { username: customerId },
            { username: { contains: customerId } },
            { username: { startsWith: customerId } },
            { username: { endsWith: customerId } }
          ]
        },
        include: {
          role: {
            select: {
              name: true,
              type: true
            }
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              username: true
            }
          },
          verification: true
        }
      })

      if (customer) {
        console.log('通过username找到用户:', customer.id)
        return formatAndReturnCustomer(customer)
      }
    } catch (err) {
      console.error('使用username查询用户时出错:', err)
    }

    // 如果还是没找到用户，尝试使用邮箱查询
    if (!customer && customerId.includes('@')) {
      try {
        console.log('尝试使用邮箱查询:', customerId)
        customer = await prisma.user.findFirst({
          where: { email: customerId },
          include: {
            role: {
              select: {
                name: true,
                type: true
              }
            },
            createdBy: {
              select: {
                id: true,
                name: true,
                username: true
              }
            },
            verification: true
          }
        })

        if (customer) {
          console.log('通过邮箱找到用户:', customer.id)
          return formatAndReturnCustomer(customer)
        }
      } catch (err) {
        console.error('使用邮箱查询用户时出错:', err)
      }
    }



    // 如果所有方法都失败，尝试查询所有用户并返回调试信息
    if (!customer) {
      console.log('未找到客户，尝试查询所有用户')

      // 查询所有用户，限制数量以避免响应过大
      const allUsers = await prisma.user.findMany({
        select: { id: true, username: true, email: true },
        take: 20
      })
      console.log('数据库中的用户示例:', allUsers)

      // 尝试模糊查询
      console.log('尝试模糊查询，查找包含该ID的用户')
      let fuzzyUsers = []
      try {
        fuzzyUsers = await prisma.user.findMany({
          where: {
            id: {
              contains: customerId.replace(/[^a-zA-Z0-9]/g, '')
            }
          },
          select: { id: true, username: true }
        })
        console.log('模糊查询结果:', fuzzyUsers)

        // 如果没有找到，尝试使用邮箱查询
        if (fuzzyUsers.length === 0 && customerId.includes('@')) {
          console.log('尝试使用邮箱查询:', customerId)
          const emailUser = await prisma.user.findFirst({
            where: { email: customerId },
            select: { id: true, username: true }
          })
          if (emailUser) {
            fuzzyUsers = [emailUser]
            console.log('通过邮箱找到用户:', emailUser.id)
          }
        }
      } catch (err) {
        console.error('模糊查询时出错:', err)
      }

      // 如果模糊查询找到了用户，使用第一个结果
      if (fuzzyUsers.length > 0) {
        let foundUserId = fuzzyUsers[0].id
        console.log('找到可能匹配的用户:', foundUserId)

        // 如果是完全匹配，优先使用
        const exactMatch = fuzzyUsers.find(u => u.id === customerId || u.username === customerId)
        if (exactMatch) {
          console.log('找到完全匹配的用户:', exactMatch.id)
          // 更新foundUserId为完全匹配的ID
          foundUserId = exactMatch.id
        }

        // 重新查询完整用户信息
        try {
          const foundUser = await prisma.user.findUnique({
            where: { id: foundUserId },
            include: {
              role: {
                select: {
                  name: true,
                  type: true
                }
              },
              createdBy: {
                select: {
                  id: true,
                  name: true,
                  username: true
                }
              },
              verification: true
            }
          })

          if (foundUser) {
            console.log('成功找到用户:', foundUser.id)
            // 使用找到的用户替代原来的customer
            return formatAndReturnCustomer(foundUser)
          }
        } catch (err) {
          console.error('查询模糊匹配用户时出错:', err)
        }
      }

      // 返回更详细的调试信息
      return NextResponse.json({
        success: false,
        message: '客户不存在',
        debug: {
          requestedId: customerId,
          allUsers: allUsers.map(u => ({ id: u.id, username: u.username, email: u.email })),
          fuzzyResults: fuzzyUsers.map(u => ({ id: u.id, username: u.username })),
          searchMethod: 'username'
        }
      }, { status: 404 })
    }

    // 格式化并返回客户数据
    function formatAndReturnCustomer(user) {
      // 格式化客户数据
      const { password, ...userData } = user

      // 转换为前端需要的格式
      const formattedCustomer = {
        id: user.id,
        name: user.name || user.username,
        loginName: user.username,
        email: user.email,
        phone: user.phone || '',
        accountType: user.role?.type || 'personal',
        status: user.status,
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
        industry: user.industry || '',
        address: `${user.province || ''} ${user.city || ''} ${user.district || ''} ${user.address || ''}`.trim() || '',
        description: user.description || '',
        balance: user.balance || 0,
        creditLimit: user.creditLimit || 0,
        feeRate: '0%', // 默认费率，实际应从费率表中获取
        disabledAt: user.disabledAt ? user.disabledAt.toISOString() : undefined,
        disableReason: user.disableReason || undefined,
        // 认证状态
        personalVerification: user.verificationType === 'personal' ? user.verificationStatus : undefined,
        enterpriseVerification: user.verificationType === 'enterprise' ? user.verificationStatus : undefined,
      }

      return NextResponse.json({
        success: true,
        message: '获取客户详情成功',
        data: formattedCustomer
      })
    }

    // 使用原始客户数据
    return formatAndReturnCustomer(customer)
  } catch (error) {
    console.error("获取客户详情错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取客户详情失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
