import { redirect } from "next/navigation"
import { cookies } from "next/headers"

export default async function Home() {
  // 从cookie中获取设置
  const cookieStore = await cookies()
  const settings = cookieStore.get("systemSettings")
  
  let dashboardEnabled = true
  if (settings?.value) {
    try {
      const parsedSettings = JSON.parse(settings.value)
      dashboardEnabled = parsedSettings.dashboardEnabled !== false
    } catch (e) {
      console.error("解析设置失败", e)
    }
  }

  // 服务器端重定向
  redirect(dashboardEnabled ? "/dashboard" : "/tasks")
}

