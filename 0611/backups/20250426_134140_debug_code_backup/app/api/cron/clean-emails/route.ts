/**
 * 邮件队列清理定时任务
 * 用于定期清理旧的邮件队列记录
 */

import { NextRequest, NextResponse } from "next/server"
import CronService from "@/lib/services/cron-service"

/**
 * 清理旧邮件队列记录
 *
 * @route GET /api/cron/clean-emails
 * @access 公开访问，但需要验证密钥
 */
export async function GET(request: NextRequest) {
  try {
    // 验证请求密钥
    const apiKey = request.headers.get('x-api-key');
    const cronSecret = process.env.CRON_SECRET || 'default-cron-secret';

    if (apiKey !== cronSecret) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    // 获取保留天数参数
    const url = new URL(request.url);
    const days = parseInt(url.searchParams.get('days') || '7');

    // 使用定时任务服务清理旧邮件记录
    const result = await CronService.cleanOldEmails(days);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("清理旧邮件记录错误:", error);
    return NextResponse.json({
      success: false,
      message: '清理旧邮件记录失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
