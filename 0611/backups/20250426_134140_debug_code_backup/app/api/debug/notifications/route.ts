import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

/**
 * 调试API - 获取用户通知信息
 * 
 * @route GET /api/debug/notifications?userId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '缺少用户ID参数'
      }, { status: 400 });
    }

    // 查询用户的通知
    const notifications = await prisma.notification.findMany({
      where: {
        OR: [
          { sendToAll: true },
          {
            sendToAll: false,
            recipients: {
              array_contains: [userId]
            }
          }
        ]
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    // 查询用户通知关联
    const userNotifications = await prisma.userNotification.findMany({
      where: {
        userId: userId
      },
      include: {
        notification: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    // 查询通知类型
    const notificationTypes = await prisma.notificationType.findMany();

    // 查询用户通知设置
    const userSettings = await prisma.userNotificationSettings.findUnique({
      where: {
        userId: userId
      }
    });

    return NextResponse.json({
      success: true,
      message: '获取调试信息成功',
      data: {
        notifications,
        userNotifications,
        notificationTypes,
        userSettings
      }
    });
  } catch (error) {
    console.error('获取调试信息失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取调试信息失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
