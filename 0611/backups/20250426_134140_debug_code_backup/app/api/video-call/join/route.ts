import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

interface VideoCallParticipant {
  user: {
    id: string
    username: string
    image: string | null
  }
}

interface VideoCall {
  id: string
  participants: VideoCallParticipant[]
}

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "加入视频通话API");
    if (response) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未登录",
          data: null,
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { callId } = body

    // 验证必要参数
    if (!callId) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      )
    }

    // 模拟检查视频通话
    // 注意：由于没有找到videoCall模型，我们这里模拟返回结果
    const videoCall = {
      id: callId,
      status: 'active',
      creatorId: 'user1',
      participants: [
        {
          id: '1',
          userId: 'user1',
          user: {
            id: 'user1',
            username: 'user1',
            image: '/avatar1.png'
          }
        }
      ]
    } as any

    if (!videoCall) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "视频通话不存在",
          data: null,
        },
        { status: 404 }
      )
    }

    // 模拟检查用户是否已经是参与者
    const isParticipant = videoCall.participants.some(
      (p: any) => p.user.id === user.id
    ) || false

    if (isParticipant) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "已经是参与者",
          data: null,
        },
        { status: 400 }
      )
    }

    // 模拟加入视频通话
    // 注意：由于没有找到videoCall模型，我们这里模拟更新操作
    console.log('模拟用户加入视频通话:', user.id, '到通话:', callId)

    // 模拟更新后的视频通话数据
    const updatedVideoCall = {
      id: callId,
      status: 'active',
      creatorId: 'user1',
      participants: [
        {
          id: '1',
          userId: 'user1',
          user: {
            id: 'user1',
            username: 'user1',
            image: '/avatar1.png'
          }
        },
        {
          id: '2',
          userId: user.id,
          user: {
            id: user.id,
            username: user.name || 'guest',
            image: user.image || '/default-avatar.png'
          }
        }
      ]
    } as any

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "加入视频通话成功",
        data: updatedVideoCall,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("加入视频通话错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}