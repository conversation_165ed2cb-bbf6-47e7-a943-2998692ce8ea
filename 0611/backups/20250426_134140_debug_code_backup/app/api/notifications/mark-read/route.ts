import { NextRequest, NextResponse } from "next/server";
import { prisma, Prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 标记通知为已读
 *
 * @route POST /api/notifications/mark-read
 * @access 所有已登录用户可访问
 */
export async function POST(req: NextRequest) {
  console.log("标记通知为已读API被调用");

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "标记通知为已读API");
    if (response) {
      console.log("标记通知为已读 - 未授权访问");
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    const userId = user.id;

    // 解析请求体
    const data = await req.json();
    const { notificationId, all } = data;

    console.log(`标记通知为已读 - 用户ID: ${userId}, 通知ID: ${notificationId}, 全部标记: ${all}`);

    let updateCount = 0;

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: user.id },
      select: { roleCode: true }
    });

    const isAdmin = userRole?.roleCode === 'ADMIN';
    console.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    if (all) {
      // 获取所有未读通知
      console.log("获取所有未读通知...");

      let unreadNotificationsQuery;

      if (isAdmin) {
        // 管理员可以标记所有通知为已读
        console.log('管理员用户，获取所有未读通知');
        unreadNotificationsQuery = prisma.$queryRaw<{ id: string }[]>`
          SELECT n.id
          FROM notification n
          LEFT JOIN "user_notification" un
            ON n.id = un."notificationId"
            AND un."userId" = ${user.id}
          WHERE n.status = 'published'
            AND (un.id IS NULL OR un.read = FALSE)
        `;
      } else {
        // 普通用户只能标记发送给自己的通知为已读
        console.log('普通用户，只获取发送给自己的未读通知');
        unreadNotificationsQuery = prisma.$queryRaw<{ id: string }[]>`
          SELECT n.id
          FROM notification n
          LEFT JOIN "user_notification" un
            ON n.id = un."notificationId"
            AND un."userId" = ${user.id}
          WHERE n.status = 'published'
            AND (un.id IS NULL OR un.read = FALSE)
            AND (n."sendToAll" = true OR
                (n."sendToAll" = false AND
                 (n.recipients)::jsonb @> jsonb_build_array(${user.id})))
        `;
      }

      const unreadNotifications = await unreadNotificationsQuery;

      console.log(`找到 ${unreadNotifications.length} 条未读通知`);

      if (unreadNotifications.length > 0) {
        // 为不存在关联的通知创建关联记录
        const insertResult = await prisma.$executeRaw`
          INSERT INTO "user_notification" ("userId", "notificationId", read, "readAt", "createdAt", "updatedAt")
          SELECT
            ${user.id} as "userId",
            n.id as "notificationId",
            TRUE as read,
            NOW() as "readAt",
            NOW() as "createdAt",
            NOW() as "updatedAt"
          FROM notification n
          LEFT JOIN user_notification un
            ON n.id = un."notificationId"
            AND un."userId" = ${user.id}
          WHERE n.id IN (${unreadNotifications.map(n => n.id).join(',')})
            AND un.id IS NULL
            AND n.status = 'published'
            AND (n."sendToAll" = true OR
                (n."sendToAll" = false AND
                 (n.recipients)::jsonb @> jsonb_build_array(${user.id})))
          ON CONFLICT ("userId", "notificationId") DO NOTHING
        `;

        const updateResult = await prisma.$executeRaw`
          UPDATE "user_notification"
          SET
            read = TRUE,
            "readAt" = NOW(),
            "updatedAt" = NOW()
          WHERE "userId" = ${user.id}
            AND "notificationId" IN (${unreadNotifications.map(n => n.id).join(',')})
            AND (read = FALSE OR read IS NULL)
        `;

        updateCount = Number(updateResult);
        console.log(`更新了 ${updateCount} 条通知为已读`);
      }
    } else if (notificationId) {
      // 为单个通知创建或更新关联记录
      await prisma.$executeRaw`
        INSERT INTO "user_notification" (
          "userId",
          "notificationId",
          read,
          "readAt",
          "createdAt",
          "updatedAt"
        )
        VALUES (
          ${user.id},
          ${notificationId},
          TRUE,
          NOW(),
          NOW(),
          NOW()
        )
        ON CONFLICT ("userId", "notificationId")
        DO UPDATE SET
          read = TRUE,
          "readAt" = NOW(),
          "updatedAt" = NOW()
      `;

      updateCount = 1;
    } else {
      console.log("未提供通知ID或all参数");
      return NextResponse.json({
        success: false,
        message: '缺少必要参数'
      }, { status: 400 });
    }

    console.log(`总共标记 ${updateCount} 条通知为已读`);

    return NextResponse.json({
      success: true,
      message: `成功标记 ${updateCount} 条通知为已读`,
      data: {
        count: updateCount
      }
    });
  } catch (error) {
    console.error("标记通知为已读错误:", error);
    return NextResponse.json({
      success: false,
      message: '标记通知为已读失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}