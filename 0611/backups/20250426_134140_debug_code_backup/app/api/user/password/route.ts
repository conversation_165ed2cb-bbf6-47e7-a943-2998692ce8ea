import { NextRequest } from "next/server"
import { prisma } from "@/lib/prisma"
import * as bcrypt from "bcryptjs"
import { getPasswordRules, validatePassword } from "@/lib/password-rules"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function PUT(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "修改密码API");
    if (response) {
      return response;
    }

    // 处理密码修改
    return await handlePasswordChange(request, user);
  } catch (error) {
    console.error("更新密码失败:", error)
    return Response.json({ success: false, message: "更新密码失败" }, { status: 500 })
  }
}

/**
 * 处理密码修改逻辑
 */
async function handlePasswordChange(request: NextRequest, user: any) {
  try {
    const body = await request.json()
    const { currentPassword, newPassword } = body

    // 验证当前密码
    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
      select: { password: true }
    })

    if (!dbUser) {
      return Response.json({ success: false, message: "用户不存在" }, { status: 404 })
    }

    const isPasswordValid = await bcrypt.compare(currentPassword, dbUser.password)
    if (!isPasswordValid) {
      return Response.json({ success: false, message: "当前密码错误" }, { status: 400 })
    }

    // 获取系统密码规则
    const passwordRules = await getPasswordRules()

    // 验证新密码强度
    const validation = validatePassword(newPassword, passwordRules)

    if (!validation.valid) {
      return Response.json({
        success: false,
        message: validation.message
      }, { status: 400 })
    }

    // 更新密码
    const hashedPassword = await bcrypt.hash(newPassword, 10)
    await prisma.user.update({
      where: { id: user.id },
      data: { password: hashedPassword }
    })

    return Response.json({ success: true, message: "密码已成功更新" })
  } catch (error) {
    console.error("处理密码修改失败:", error)
    return Response.json({ success: false, message: "更新密码失败" }, { status: 500 })
  }
}