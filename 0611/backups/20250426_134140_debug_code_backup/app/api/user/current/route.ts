import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 获取当前登录用户信息
 *
 * @route GET /api/user/current
 * @access private - 需要认证
 */
export async function GET(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "获取当前用户信息API");
    if (response) {
      return NextResponse.json(
        { success: false, message: "未授权访问", code: 401, data: null },
        { status: 401 }
      );
    }

    const userId = user.id;

    // 查询用户详细信息
    const userDetails = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: true
      }
    });

    if (!userDetails) {
      return NextResponse.json(
        { success: false, message: "用户不存在", code: 404, data: null },
        { status: 404 }
      );
    }

    // 返回用户信息（排除敏感字段）
    const userData = {
      id: userDetails.id,
      username: userDetails.username,
      email: userDetails.email,
      roleCode: userDetails.roleCode,
      permissions: userDetails.permissions,
      role: {
        id: userDetails.role.id,
        code: userDetails.role.code,
        name: userDetails.role.name,
        type: userDetails.role.type,
        permissions: userDetails.role.permissions
      },
      createdAt: userDetails.createdAt,
      updatedAt: userDetails.updatedAt
    };

    console.log("查询到的用户信息:", userData);

    return NextResponse.json({
      success: true,
      message: "获取用户信息成功",
      code: 200,
      data: userData
    });
  } catch (error) {
    console.error("获取用户信息失败:", error);
    return NextResponse.json(
      { success: false, message: "获取用户信息失败", code: 500, data: null },
      { status: 500 }
    );
  }
}