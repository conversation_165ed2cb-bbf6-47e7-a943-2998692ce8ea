import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { nanoid } from "nanoid"
import { existsSync } from "fs"
import sharp from "sharp"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

// 允许的图片类型
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"]
// 最大文件大小 (2MB) - 减小文件大小限制以提高安全性
const MAX_FILE_SIZE = 2 * 1024 * 1024

/**
 * 处理头像上传请求
 * @param req 请求对象
 * @param userId 用户ID
 */
async function processUpload(req: Request, userId: string) {
  const formData = await req.formData()
  const file = formData.get("file") as File || formData.get("avatar") as File

  if (!file) {
    return NextResponse.json(
      { success: false, message: "请选择要上传的图片" },
      { status: 400 }
    )
  }

  // 验证文件类型
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return NextResponse.json(
      { success: false, message: "不支持的文件类型" },
      { status: 400 }
    )
  }

  // 验证文件大小
  if (file.size > MAX_FILE_SIZE) {
    return NextResponse.json(
      { success: false, message: "文件大小不能超过5MB" },
      { status: 400 }
    )
  }

  // 创建存储目录
  const uploadDir = join(process.cwd(), "public", "uploads", "avatars")
  if (!existsSync(uploadDir)) {
    await mkdir(uploadDir, { recursive: true })
  }

  // 生成安全的文件名
  const ext = file.name.split(".").pop()?.toLowerCase() || 'jpg'
  const fileName = `${nanoid()}.${ext}`
  const path = join(uploadDir, fileName)

  // 读取文件内容
  const bytes = await file.arrayBuffer()
  const buffer = Buffer.from(bytes)

  // 简单的文件头检查，确保文件类型与扩展名匹配
  const isValidImage = validateImageHeader(buffer, ext)
  if (!isValidImage) {
    return NextResponse.json(
      { success: false, message: "无效的图片文件" },
      { status: 400 }
    )
  }

  // 压缩图片
  let processedBuffer: Buffer;
  try {
    // 处理图片压缩
    processedBuffer = await compressImage(buffer, ext);
  } catch (error) {
    console.error('图片压缩失败:', error);
    // 如果压缩失败，使用原始图片
    processedBuffer = buffer;
  }

  // 保存文件
  await writeFile(path, processedBuffer)

  // 更新用户头像
  const user = await prisma.user.update({
    where: { id: userId },
    data: { image: `/uploads/avatars/${fileName}` },
  })

  return NextResponse.json({
    success: true,
    data: { image: user.image },
  })
}

export async function POST(req: NextRequest) {
  try {
    console.log('头像上传API - 开始处理请求');

    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "头像上传API");
    if (response) {
      return response;
    }

    const userId = user.id;

    console.log('头像上传API - 用户信息:', {
      user,
      userId
    });

    // 处理上传请求
    return await processUpload(req, userId);
  } catch (error) {
    console.error("上传头像失败:", error)
    return NextResponse.json(
      { success: false, message: "上传头像失败" },
      { status: 500 }
    )
  }
}

// 验证图片文件头
function validateImageHeader(buffer: Buffer, extension: string): boolean {
  // 检查文件头部特征
  const fileSignature = buffer.toString("hex", 0, 8).toUpperCase()

  switch (extension) {
    case "jpg":
    case "jpeg":
      // JPEG文件以FF D8开头
      return fileSignature.startsWith("FFD8")
    case "png":
      // PNG文件以89 50 4E 47开头
      return fileSignature.startsWith("89504E47")
    case "gif":
      // GIF文件以47 49 46 38开头
      return fileSignature.startsWith("47494638")
    case "webp":
      // WebP文件以52 49 46 46开头，并且在第8位后有"WEBP"标记
      return fileSignature.startsWith("52494646") &&
             buffer.toString("ascii", 8, 12) === "WEBP"
    default:
      return false
  }
}

// 压缩图片
async function compressImage(buffer: Buffer, extension: string): Promise<Buffer> {
  // 头像尺寸设置
  const MAX_WIDTH = 400;  // 最大宽度
  const MAX_HEIGHT = 400; // 最大高度
  const QUALITY = 80;     // JPEG和WebP的质量设置 (0-100)

  // 初始化sharp实例
  let image = sharp(buffer);

  // 获取图片元数据
  const metadata = await image.metadata();

  // 计算新尺寸，保持纵横比
  let width = metadata.width || MAX_WIDTH;
  let height = metadata.height || MAX_HEIGHT;

  if (width > MAX_WIDTH || height > MAX_HEIGHT) {
    const aspectRatio = width / height;

    if (width > height) {
      width = Math.min(width, MAX_WIDTH);
      height = Math.round(width / aspectRatio);
    } else {
      height = Math.min(height, MAX_HEIGHT);
      width = Math.round(height * aspectRatio);
    }
  }

  // 调整尺寸
  image = image.resize(width, height, {
    fit: 'inside',
    withoutEnlargement: true
  });

  // 根据文件类型进行不同的处理
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return await image.jpeg({ quality: QUALITY }).toBuffer();
    case 'png':
      return await image.png({ compressionLevel: 9 }).toBuffer();
    case 'webp':
      return await image.webp({ quality: QUALITY }).toBuffer();
    case 'gif':
      // GIF保持原样，只调整尺寸
      return await image.gif().toBuffer();
    default:
      // 默认转换为JPEG
      return await image.jpeg({ quality: QUALITY }).toBuffer();
  }
}