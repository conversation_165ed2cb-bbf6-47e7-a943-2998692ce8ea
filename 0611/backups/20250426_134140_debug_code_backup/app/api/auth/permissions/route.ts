import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { PolicyService } from '@/lib/abac/policy-service'
import { AuthMiddleware } from '@/lib/middleware/auth-middleware'

/**
 * 获取当前用户的权限
 * @route GET /api/auth/permissions
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取用户权限API");
    if (response) {
      return NextResponse.json(
        {
          success: false,
          message: '未登录'
        },
        { status: 401 }
      )
    }

    // 简化处理，返回用户角色的权限
    const permissions = user.role?.permissions || []

    return NextResponse.json({
      success: true,
      data: permissions
    })
  } catch (error) {
    console.error('获取用户权限失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取用户权限失败'
      },
      { status: 500 }
    )
  }
}