import { NextRequest, NextResponse } from "next/server"
import { CasbinService } from "@/lib/services/casbin-service"
import { v4 as uuidv4 } from 'uuid'
import { prisma } from "@/lib/prisma"
import { checkPermission } from "@/lib/casbin/enforcer"
import { hasResourcePermissionWithCasbin } from "@/lib/abac/casbin-adapter"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"



/**
 * 权限检查API
 * 支持NextAuth和传统JWT认证
 */
export async function POST(request: NextRequest) {
  const requestId = uuidv4().substring(0, 8)
  console.log(`[权限检查API:${requestId}] 收到权限检查请求`)

  try {
    // 获取请求体
    const body = await request.json()
    const { resource, action = 'access' } = body

    if (!resource) {
      console.log(`[权限检查API:${requestId}] 缺少资源参数`)
      return NextResponse.json(
        { success: false, message: "请提供资源参数" },
        { status: 400 }
      )
    }

    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, `权限检查API:${requestId}`);
    if (response) {
      console.log(`[权限检查API:${requestId}] 未授权访问`)
      return NextResponse.json(
        { success: false, message: "未登录", hasPermission: false },
        { status: 401 }
      )
    }

    let userId = user.id;
    let userRole = user.roleCode;
    let hasPermission = false;

    console.log(`[权限检查API:${requestId}] 用户已登录，用户ID: ${userId}, 角色: ${userRole}`)

    // 检查是否为管理员角色
    if (userRole && userRole.toUpperCase() === 'ADMIN') {
      console.log(`[权限检查API:${requestId}] 用户是管理员，自动授予所有权限`)
      hasPermission = true;
    } else {
      // 3. 使用jCasbin进行权限检查
      try {
        // 对用户进行权限检查
        hasPermission = await CasbinService.checkPermission(userId as string, resource, action)
        console.log(`[权限检查API:${requestId}] 用户级权限检查结果: ${hasPermission}`)

        // 如果用户没有权限，检查角色权限
        if (!hasPermission && userRole) {
          hasPermission = await CasbinService.checkPermission(userRole, resource, action)
          console.log(`[权限检查API:${requestId}] 角色级权限检查结果: ${hasPermission}`)
        }
      } catch (error) {
        console.error(`[权限检查API:${requestId}] 权限检查失败:`, error)
        hasPermission = false
      }
    }

    return NextResponse.json({
      success: true,
      hasPermission,
      userId,
      resource,
      action
    })
  } catch (error) {
    console.error(`[权限检查API:${requestId}] 处理错误:`, error)
    return NextResponse.json(
      {
        success: false,
        message: "处理权限检查请求时发生错误",
        hasPermission: false
      },
      { status: 500 }
    )
  }
}

// GET 方法保持不变，用于客户端权限检查
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const resource = url.searchParams.get("resource");
    const action = url.searchParams.get("action");

    if (!resource || !action) {
      return NextResponse.json(
        { success: false, message: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "权限检查API");
    if (response) {
      return NextResponse.json(
        { success: false, message: "未登录", hasPermission: false },
        { status: 401 }
      )
    }

    const userId = user.id;

    // 获取用户角色
    const userWithRole = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        role: {
          select: {
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!userWithRole) {
      return NextResponse.json(
        { success: false, message: "用户不存在", hasPermission: false },
        { status: 404 }
      )
    }

    // 检查是否是管理员或有 * 权限
    if (userWithRole.role.code.toUpperCase() === 'ADMIN' || userWithRole.role.permissions.includes('*')) {
      console.log('用户是管理员或有通配符权限，自动授予所有权限');
      return NextResponse.json({
        success: true,
        hasPermission: true
      })
    }

    // 使用 jCasbin 检查权限
    try {
      // 构建环境变量
      const env = {
        role: userWithRole.role.code,
        user: {
          id: userId,
          roleCode: userWithRole.role.code
        }
      };

      // 使用增强的 checkPermission 函数
      const casbinResult = await checkPermission(request, resource, action, env);
      if (casbinResult) {
        return NextResponse.json({
          success: true,
          hasPermission: true
        });
      }

      // 如果基本检查失败，尝试使用 ABAC 兼容模式
      const userObj = {
        id: userId,
        roleCode: userWithRole.role.code,
        role: userWithRole.role
      };

      const abacResult = await hasResourcePermissionWithCasbin(userObj, resource, action);
      if (abacResult) {
        return NextResponse.json({
          success: true,
          hasPermission: true
        });
      }
    } catch (error) {
      console.error("jCasbin 权限检查失败:", error);
      // 如果 jCasbin 检查失败，回退到使用原有的权限检查逻辑
    }

    // 回退到原有的权限检查逻辑
    const permissionString = `${resource}:${action}`;
    const hasPermission = userWithRole.role.permissions.includes(permissionString);

    return NextResponse.json({
      success: true,
      hasPermission
    });
  } catch (error) {
    console.error("权限检查失败:", error);
    return NextResponse.json(
      { success: false, message: "权限检查失败", hasPermission: false },
      { status: 500 }
    );
  }
}