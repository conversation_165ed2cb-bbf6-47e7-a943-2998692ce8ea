import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 检查用户是否有特定操作的权限
 * @route GET /api/auth/check-operation
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "检查操作权限API");
    if (response) {
      return NextResponse.json(
        { hasPermission: false, message: "未授权访问" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const operationCode = searchParams.get("code")

    if (!operationCode) {
      return NextResponse.json(
        { hasPermission: false, message: "缺少操作代码参数" },
        { status: 400 }
      )
    }

    // 管理员角色默认拥有所有权限
    if (user.roleCode === "ADMIN") {
      return NextResponse.json({ hasPermission: true })
    }

    // 查询用户角色
    const role = await prisma.role.findUnique({
      where: { code: user.roleCode },
      include: {
        operations: true
      }
    })

    if (!role) {
      return NextResponse.json({ hasPermission: false })
    }

    // 检查角色是否有该操作权限
    const hasPermission = role.operations.some(op => op.code === operationCode)

    return NextResponse.json({ hasPermission })
  } catch (error) {
    console.error("检查操作权限失败:", error)
    return NextResponse.json(
      { hasPermission: false, message: "检查操作权限失败" },
      { status: 500 }
    )
  }
}
