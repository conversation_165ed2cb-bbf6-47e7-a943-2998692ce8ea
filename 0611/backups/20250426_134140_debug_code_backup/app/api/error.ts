import { NextResponse } from 'next/server';

export class APIError extends <PERSON>rror {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code: string = 'INTERNAL_SERVER_ERROR'
  ) {
    super(message);
    this.name = 'APIError';
  }
}

export function handleAPIError(error: unknown) {
  console.error('API Error:', error);

  // 如果是已知的 API 错误
  if (error instanceof APIError) {
    return NextResponse.json(
      {
        success: false,
        message: process.env.NODE_ENV === 'production' 
          ? '请求处理失败' 
          : error.message,
        code: error.code,
      },
      { status: error.statusCode }
    );
  }

  // 未知错误
  return NextResponse.json(
    {
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? '服务器内部错误' 
        : (error instanceof Error ? error.message : '未知错误'),
      code: 'INTERNAL_SERVER_ERROR',
    },
    { status: 500 }
  );
} 