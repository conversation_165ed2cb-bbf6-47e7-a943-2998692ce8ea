import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { prisma } from "@/lib/prisma";
import { verifyVerificationCode } from "@/lib/services/verification";
import nodemailer from "nodemailer";

/**
 * 验证验证码并更新邮箱授权码
 * POST /api/settings/email/verify-and-update
 * 
 * 用于验证邮箱验证码并更新邮箱授权码
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "更新邮箱授权码");
    if (authResult.response) {
      return authResult.response;
    }
    
    const user = authResult.user;
    if (!user?.email) {
      return NextResponse.json({
        success: false,
        message: "无法获取管理员邮箱地址"
      }, { status: 400 });
    }

    // 获取请求数据
    const data = await request.json();
    
    // 验证必要字段
    if (!data.code || !data.authCode) {
      return NextResponse.json({
        success: false,
        message: "验证码和授权码不能为空"
      }, { status: 400 });
    }

    // 验证验证码
    const isValid = await verifyVerificationCode(user.email, data.code, "email_auth_code");
    if (!isValid) {
      return NextResponse.json({
        success: false,
        message: "验证码无效或已过期"
      }, { status: 400 });
    }

    // 获取当前邮件设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      return NextResponse.json({
        success: false,
        message: "系统设置不存在"
      }, { status: 404 });
    }

    // 合并现有邮件设置
    const currentEmailSettings = settings.emailSettings || {};
    
    // 验证新授权码是否有效
    try {
      // 创建临时传输器进行验证
      const transporter = nodemailer.createTransport({
        host: currentEmailSettings.host,
        port: parseInt(currentEmailSettings.port),
        secure: currentEmailSettings.secure,
        auth: {
          user: currentEmailSettings.auth?.user,
          pass: data.authCode
        },
        tls: {
          rejectUnauthorized: false
        }
      });

      // 验证连接
      await transporter.verify();
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: "邮箱授权码验证失败: " + (error instanceof Error ? error.message : "未知错误")
      }, { status: 400 });
    }

    // 创建新的邮件设置对象
    const emailSettings = {
      ...currentEmailSettings,
      auth: {
        ...currentEmailSettings.auth,
        pass: data.authCode
      }
    };

    // 更新系统设置
    await prisma.systemSettings.update({
      where: {
        id: 1
      },
      data: {
        emailSettings: emailSettings
      }
    });

    // 更新环境变量（仅在开发环境中有效）
    if (process.env.NODE_ENV === 'development') {
      process.env.EMAIL_PASS = data.authCode;
    }

    return NextResponse.json({
      success: true,
      message: "邮箱授权码已更新",
      data: {
        ...emailSettings,
        auth: {
          user: emailSettings.auth.user,
          pass: '******' // 不返回实际密码
        }
      }
    });
  } catch (error) {
    console.error("更新邮箱授权码失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "更新邮箱授权码失败"
    }, { status: 500 });
  }
}
