/**
 * 客户充值API
 * 处理管理员对客户账户的充值操作
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 为客户充值
 *
 * @route POST /api/customers/[id]/recharge
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('充值API接收到的客户ID:', customerId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "客户充值API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const adminId = admin.id;

    // 获取请求体
    const body = await request.json()
    const { amount, paymentMethod, remarks } = body

    // 验证必填字段
    if (amount === undefined) {
      return NextResponse.json({
        success: false,
        message: '金额为必填项'
      }, { status: 400 })
    }

    // 查找客户
    const customer = await prisma.user.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return NextResponse.json({
        success: false,
        message: '客户不存在'
      }, { status: 404 })
    }

    // 计算新余额
    const newBalance = customer.balance + parseFloat(amount.toString())

    // 更新客户余额
    const updatedCustomer = await prisma.user.update({
      where: { id: customerId },
      data: {
        balance: newBalance
      },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    // 确定交易类型
    let transactionType = 'recharge'
    if (parseFloat(amount.toString()) < 0) {
      // 如果金额为负，则是扣费或清零操作
      if (newBalance === 0 && customer.balance > 0) {
        // 如果新余额为0且原余额大于0，则是清零操作
        transactionType = 'balance_reset'
      } else {
        transactionType = 'deduct'
      }
    }

    // 记录余额变动日志
    await prisma.balanceTransaction.create({
      data: {
        userId: customerId,
        amount: parseFloat(amount.toString()),
        balanceAfter: newBalance,
        type: transactionType,
        paymentMethod: paymentMethod || 'system',
        remarks: remarks || '',
        adminId: adminId,
      }
    })

    return NextResponse.json({
      success: true,
      message: transactionType === 'balance_reset' ? '余额清零成功' : (transactionType === 'recharge' ? '充值成功' : '扣费成功'),
      data: {
        balance: updatedCustomer.balance,
        customer: {
          id: updatedCustomer.id,
          username: updatedCustomer.username,
          name: updatedCustomer.name
        }
      }
    })
  } catch (error) {
    console.error("客户账户操作错误:", error)
    return NextResponse.json({
      success: false,
      message: '操作失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
