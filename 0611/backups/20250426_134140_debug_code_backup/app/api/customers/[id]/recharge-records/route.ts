/**
 * 客户余额记录API
 * 处理获取客户余额变动记录的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取客户余额记录
 *
 * @route GET /api/customers/[id]/recharge-records
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('余额记录API接收到的客户ID:', customerId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "获取客户余额记录API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 })
    }

    const adminId = admin.id;

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') || undefined
    const startDate = searchParams.get('startDate') || undefined
    const endDate = searchParams.get('endDate') || undefined
    const search = searchParams.get('search') || undefined

    // 先尝试直接查询用户
    let user = null
    try {
      user = await prisma.user.findUnique({
        where: { id: customerId },
        select: { id: true }
      })
    } catch (err) {
      console.error('查询用户时出错:', err)
    }

    // 如果没找到用户，尝试使用username查询
    if (!user) {
      try {
        console.log('尝试使用username查询:', customerId)
        user = await prisma.user.findFirst({
          where: { username: customerId },
          select: { id: true }
        })

        if (user) {
          console.log('通过username找到用户:', user.id)
          customerId = user.id
        }
      } catch (err) {
        console.error('使用username查询用户时出错:', err)
      }
    }

    // 如果还是没找到用户，尝试使用邮箱查询
    if (!user && customerId.includes('@')) {
      try {
        console.log('尝试使用邮箱查询:', customerId)
        user = await prisma.user.findFirst({
          where: { email: customerId },
          select: { id: true }
        })

        if (user) {
          console.log('通过邮箱找到用户:', user.id)
          customerId = user.id
        }
      } catch (err) {
        console.error('使用邮箱查询用户时出错:', err)
      }
    }

    // 构建查询条件
    const where: any = {
      userId: customerId,
    }

    if (type && type !== 'all') {
      where.type = type
    }

    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    } else if (startDate) {
      where.createdAt = {
        gte: new Date(startDate),
      }
    } else if (endDate) {
      where.createdAt = {
        lte: new Date(endDate),
      }
    }

    // 添加搜索条件
    if (search) {
      where.OR = [
        { remarks: { contains: search, mode: 'insensitive' } },
        { paymentMethod: { contains: search, mode: 'insensitive' } }
      ]
    }

    // 查询总记录数
    const total = await prisma.balanceTransaction.count({
      where,
    })

    // 查询交易记录
    const transactions = await prisma.balanceTransaction.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        amount: true,
        balanceAfter: true,
        creditLimitChange: true,
        creditLimitAfter: true,
        type: true,
        paymentMethod: true,
        remarks: true,
        createdAt: true,
        admin: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    })

    // 返回交易记录
    return NextResponse.json({
      success: true,
      message: '获取余额记录成功',
      data: {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error("获取客户余额记录错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取余额记录失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
