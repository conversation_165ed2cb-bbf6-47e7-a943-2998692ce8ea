/**
 * 直接修复数据库中的角色-菜单关联
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function POST(request: NextRequest) {
  try {
    console.log("开始修复数据库关联...")

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "修复数据库关联API");
    if (response) {
      return NextResponse.json(
        { success: false, error: "需要管理员权限" },
        { status: 403 }
      )
    }

    // 1. 获取管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: "ADMIN" }
    })

    if (!adminRole) {
      console.error("管理员角色不存在")
      return NextResponse.json(
        { success: false, error: "管理员角色不存在" },
        { status: 404 }
      )
    }

    console.log("找到管理员角色:", adminRole.id)

    // 2. 获取所有菜单
    const allMenus = await prisma.menu.findMany({
      where: { visible: true }
    })

    console.log(`找到 ${allMenus.length} 个菜单`)

    // 3. 获取所有操作
    const allOperations = await prisma.operation.findMany({
      where: { enabled: true }
    })

    console.log(`找到 ${allOperations.length} 个操作`)

    // 4. 直接执行SQL修复角色-菜单关联
    await prisma.$executeRaw`
      DELETE FROM "_RoleMenus" WHERE "B" = ${adminRole.id}
    `

    console.log("已删除现有的角色-菜单关联")

    // 5. 直接插入正确的关联
    for (const menu of allMenus) {
      await prisma.$executeRaw`
        INSERT INTO "_RoleMenus" ("A", "B")
        VALUES (${menu.id}, ${adminRole.id})
        ON CONFLICT DO NOTHING
      `
    }

    console.log("已创建新的角色-菜单关联")

    // 6. 直接执行SQL修复角色-操作关联
    try {
      await prisma.$executeRaw`
        DELETE FROM "_OperationToRole" WHERE "B" = ${adminRole.id}
      `
      console.log("已删除现有的角色-操作关联")
    } catch (error) {
      console.log("删除角色-操作关联失败，可能表不存在:", error)
    }

    // 7. 直接插入正确的关联
    try {
      for (const operation of allOperations) {
        await prisma.$executeRaw`
          INSERT INTO "_OperationToRole" ("A", "B")
          VALUES (${operation.id}, ${adminRole.id})
          ON CONFLICT DO NOTHING
        `
      }
      console.log("已创建新的角色-操作关联")
    } catch (error) {
      console.log("创建角色-操作关联失败，可能表不存在:", error)
    }

    // 8. 检查是否有反向关联
    const reverseRoleMenus = await prisma.$queryRaw`
      SELECT * FROM "_RoleMenus" WHERE "A" = ${adminRole.id}
    `

    if ((reverseRoleMenus as any[]).length > 0) {
      console.log(`发现 ${(reverseRoleMenus as any[]).length} 个反向关联，开始修复...`)

      // 保存反向关联的菜单ID
      const menuIds = (reverseRoleMenus as any[]).map(rm => rm.B)

      // 删除反向关联
      await prisma.$executeRaw`
        DELETE FROM "_RoleMenus" WHERE "A" = ${adminRole.id}
      `

      // 创建正向关联
      for (const menuId of menuIds) {
        await prisma.$executeRaw`
          INSERT INTO "_RoleMenus" ("A", "B")
          VALUES (${menuId}, ${adminRole.id})
          ON CONFLICT DO NOTHING
        `
      }

      console.log("已修复反向关联")
    }

    return NextResponse.json({
      success: true,
      message: "数据库关联已修复",
      data: {
        role: adminRole.id,
        menusCount: allMenus.length,
        operationsCount: allOperations.length,
        reverseRelationsFixed: (reverseRoleMenus as any[]).length
      }
    })
  } catch (error) {
    console.error("修复数据库关联失败:", error)
    return NextResponse.json(
      { success: false, error: "修复数据库关联失败" },
      { status: 500 }
    )
  }
}
