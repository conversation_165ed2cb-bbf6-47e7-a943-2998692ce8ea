/**
 * 用户余额管理API
 * 处理管理员对用户余额的操作
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { checkPermission } from "@/lib/casbin/enforcer"

/**
 * 更新用户余额
 *
 * @route POST /api/admin/users/:id/balance
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "用户余额管理API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const adminId = admin.id;

    // 检查是否有管理余额的权限
    const hasPermission = await checkPermission(request, 'users', 'manage_balance');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限管理用户余额'
      }, { status: 403 })
    }

    // 解析请求体
    const body = await request.json()
    const { amount, paymentMethod, remarks } = body

    // 验证必填字段
    if (amount === undefined) {
      return NextResponse.json({
        success: false,
        message: '金额为必填项'
      }, { status: 400 })
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 计算新余额
    const newBalance = user.balance + parseFloat(amount.toString())

    // 确保余额不为负数
    if (newBalance < 0) {
      return NextResponse.json({
        success: false,
        message: '余额不足，无法扣除'
      }, { status: 400 })
    }

    // 更新用户余额
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        balance: newBalance
      },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    // 记录余额变动日志
    const transactionType = amount >= 0 ? 'recharge' : 'deduct'
    await prisma.balanceTransaction.create({
      data: {
        userId: userId,
        amount: parseFloat(amount.toString()),
        balanceAfter: newBalance,
        type: transactionType,
        paymentMethod: paymentMethod || 'other',
        remarks: remarks || '',
        adminId: adminId,
      }
    })

    return NextResponse.json({
      success: true,
      message: amount >= 0 ? '充值成功' : '扣费成功',
      data: {
        balance: updatedUser.balance,
        user: {
          id: updatedUser.id,
          username: updatedUser.username,
          name: updatedUser.name
        }
      }
    })
  } catch (error) {
    console.error("更新用户余额错误:", error)
    return NextResponse.json({
      success: false,
      message: '更新用户余额失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}

/**
 * 获取用户余额
 *
 * @route GET /api/admin/users/:id/balance
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "用户余额管理API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const adminId = admin.id;

    // 检查是否有查看余额的权限
    const hasPermission = await checkPermission(request, 'users', 'read');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限查看用户余额'
      }, { status: 403 })
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        name: true,
        balance: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: '获取用户余额成功',
      data: {
        balance: user.balance,
        user: {
          id: user.id,
          username: user.username,
          name: user.name
        }
      }
    })
  } catch (error) {
    console.error("获取用户余额错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户余额失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}


