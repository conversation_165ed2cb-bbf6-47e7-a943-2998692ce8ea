'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

/**
 * 全局错误处理组件
 *
 * 这个组件会捕获在渲染过程中发生的错误，并显示友好的错误页面
 * 它是Next.js的内置错误处理机制的一部分
 *
 * @see https://nextjs.org/docs/app/building-your-application/routing/error-handling
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // 在开发环境中记录错误到控制台
    console.error('全局错误:', error)
  }, [error])

  // 定义处理函数
  const handleReset = () => {
    reset()
  }

  const handleGoHome = () => {
    window.location.href = '/dashboard'
  }

  return (
    <html>
      <body>
        <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
          <div className="w-full max-w-md text-center">
            <div className="flex justify-center mb-6">
              <AlertCircle className="h-16 w-16 text-red-500" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              出错了
            </h1>
            <h2 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
              系统遇到了一个问题
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              我们已经记录了这个错误，并将尽快修复。请尝试刷新页面或返回首页。
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                onClick={handleReset}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                重试
              </Button>
              <Button
                onClick={handleGoHome}
                variant="outline"
              >
                返回首页
              </Button>
            </div>
            {process.env.NODE_ENV !== 'production' && (
              <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-md text-left overflow-auto">
                <p className="font-mono text-sm text-red-600 dark:text-red-400">
                  {error.message}
                </p>
                {error.stack && (
                  <pre className="mt-2 text-xs text-gray-700 dark:text-gray-300 overflow-auto">
                    {error.stack}
                  </pre>
                )}
              </div>
            )}
          </div>
        </div>
      </body>
    </html>
  )
}
