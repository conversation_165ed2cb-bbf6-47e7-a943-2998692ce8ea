"use client"

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Donut<PERSON><PERSON> } from '@tremor/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AlertCircle,
  BarChart2,
  Pie<PERSON>hart as PieChartIcon,
  LineChart as LineChartIcon,
  RotateCw,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

/**
 * 通知统计页面
 *
 * 此页面用于展示通知系统的统计数据，包括：
 * 1. 通知总数、已读数、未读数
 * 2. 通知阅读率趋势
 * 3. 通知类型分布
 * 4. 通知优先级分布
 */
export default function NotificationStatsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState('week');
  const [stats, setStats] = useState<any>(null);

  // 获取统计数据
  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/notifications/stats?period=${period}`);

      if (!response.ok) {
        throw new Error('获取通知统计数据失败');
      }

      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      } else {
        throw new Error(data.message || '获取通知统计数据失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取通知统计数据失败');
      console.error('获取通知统计数据错误:', err);
      toast({
        title: "错误",
        description: err instanceof Error ? err.message : "未知错误",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 监听时间段变化
  useEffect(() => {
    fetchStats();
  }, [period]);

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // 优先级颜色映射
  const priorityColorMap: Record<string, string> = {
    high: 'red',
    medium: 'yellow',
    low: 'green'
  };

  // 渲染统计卡片
  const renderOverviewCards = () => {
    if (!stats) return null;

    const { overview } = stats;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">总通知数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">已读通知</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{overview.read}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">未读通知</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{overview.unread}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">平均阅读率</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatPercentage(overview.readRate)}</div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // 渲染阅读率趋势图
  const renderReadRateTrend = () => {
    if (!stats || !stats.readRateTrend || stats.readRateTrend.length === 0) return null;

    // 格式化数据
    const chartData = stats.readRateTrend.map((item: any) => ({
      date: item.date,
      '阅读率': parseFloat((item.readRate * 100).toFixed(2)),
      '已读数': item.read,
      '未读数': item.unread
    }));

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <LineChartIcon className="h-5 w-5 mr-2 text-blue-500" />
            通知阅读率趋势
          </CardTitle>
          <CardDescription>
            展示不同时间段的通知阅读率变化
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LineChart
            className="h-80"
            data={chartData}
            index="date"
            categories={['阅读率']}
            colors={['blue']}
            valueFormatter={(value) => `${value}%`}
            yAxisWidth={40}
          />
        </CardContent>
      </Card>
    );
  };

  // 渲染通知类型分布图
  const renderTypeDistribution = () => {
    if (!stats) return null;

    // 即使没有类型分布数据，也显示图表
    const chartData = stats.typeDistribution && stats.typeDistribution.length > 0
      ? stats.typeDistribution.map((item: any) => ({
          name: item.typeName,
          value: item.count
        }))
      : [];

    // 如果没有数据，显示提示信息
    const noDataContent = chartData.length === 0 ? (
      <div className="flex flex-col items-center justify-center h-80 text-gray-500">
        <PieChartIcon className="h-16 w-16 mb-4 opacity-50" />
        <p>暂无通知类型分布数据</p>
      </div>
    ) : (
      <DonutChart
        className="h-80"
        data={chartData}
        index="name"
        variant="pie"
        valueFormatter={(value) => `${value}条`}
        colors={['blue', 'green', 'amber', 'rose', 'indigo', 'emerald']}
      />
    );

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <PieChartIcon className="h-5 w-5 mr-2 text-green-500" />
            通知类型分布
          </CardTitle>
          <CardDescription>
            展示不同类型通知的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          {noDataContent}
        </CardContent>
      </Card>
    );
  };

  // 渲染通知优先级分布图
  const renderPriorityDistribution = () => {
    if (!stats) return null;

    // 即使没有优先级分布数据，也显示图表
    const chartData = stats.priorityDistribution && stats.priorityDistribution.length > 0
      ? stats.priorityDistribution.map((item: any) => ({
          name: item.priority === 'high' ? '高优先级' :
                item.priority === 'medium' ? '中优先级' : '低优先级',
          value: item._count.id,
          color: priorityColorMap[item.priority] || 'blue'
        }))
      : [
          { name: '高优先级', value: 0, color: priorityColorMap['high'] },
          { name: '中优先级', value: 0, color: priorityColorMap['medium'] },
          { name: '低优先级', value: 0, color: priorityColorMap['low'] }
        ];

    // 如果没有数据，显示提示信息
    const noDataContent = chartData.every((item: any) => item.value === 0) ? (
      <div className="flex flex-col items-center justify-center h-80 text-gray-500">
        <BarChart2 className="h-16 w-16 mb-4 opacity-50" />
        <p>暂无通知优先级分布数据</p>
      </div>
    ) : (
      <BarChart
        className="h-80"
        data={chartData}
        index="name"
        categories={['value']}
        colors={['blue']}
        valueFormatter={(value) => `${value}条`}
        yAxisWidth={48}
      />
    );

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart2 className="h-5 w-5 mr-2 text-amber-500" />
            通知优先级分布
          </CardTitle>
          <CardDescription>
            展示不同优先级通知的数量分布
          </CardDescription>
        </CardHeader>
        <CardContent>
          {noDataContent}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto p-4 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">通知统计</h1>
        <div className="flex space-x-2">
          <Select
            value={period}
            onValueChange={setPeriod}
          >
            <SelectTrigger className="w-[180px]">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue placeholder="选择时间段" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">最近24小时</SelectItem>
              <SelectItem value="week">最近一周</SelectItem>
              <SelectItem value="month">最近一个月</SelectItem>
              <SelectItem value="year">最近一年</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            onClick={fetchStats}
            disabled={loading}
          >
            {loading ? (
              <RotateCw className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <RotateCw className="h-4 w-4 mr-1" />
            )}
            刷新数据
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <RotateCw className="h-12 w-12 text-gray-400 animate-spin" />
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg text-center">
          <AlertCircle className="h-6 w-6 mx-auto mb-2" />
          <p>{error}</p>
        </div>
      ) : (
        <div>
          {renderOverviewCards()}
          {renderReadRateTrend()}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {renderTypeDistribution()}
            {renderPriorityDistribution()}
          </div>
        </div>
      )}
    </div>
  );
}
