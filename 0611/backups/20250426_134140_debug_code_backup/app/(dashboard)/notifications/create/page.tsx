"use client"

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft, Send, Bell, Info, Tag, Zap, Shield } from 'lucide-react';
import Link from 'next/link';
import RichTextEditor from '@/components/shared/rich-text-editor';
import { UserSelector } from '@/components/user/user-selector';

export default function CreateNotificationPage() {
  const router = useRouter()
  const [title, setTitle] = useState('')
  const [content, setContent] = useState('')
  const [type, setType] = useState('1')
  const [priority, setPriority] = useState('medium')
  const [sendToAll, setSendToAll] = useState(true)
  const [recipients, setRecipients] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [notificationTypes, setNotificationTypes] = useState<Array<{ id: number, code: string, name: string }>>([])

  // 获取通知类型列表
  useEffect(() => {
    const fetchNotificationTypes = async () => {
      try {
        const response = await fetch('/api/notifications/types')
        const data = await response.json()
        if (data.success) {
          setNotificationTypes(data.data)
          if (data.data.length > 0) {
            setType(String(data.data[0].id)) // 设置默认类型
          }
        }
      } catch (error) {
        console.error('获取通知类型失败:', error)
      }
    }
    fetchNotificationTypes()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!title || !content) {
      toast({
        title: "表单不完整",
        description: "请填写标题和内容",
        variant: "destructive",
      })
      return
    }

    // 验证指定用户时必须选择用户
    if (!sendToAll && recipients.length === 0) {
      toast({
        title: "表单验证失败",
        description: "请至少选择一个接收用户",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      console.log("发送通知数据:", {
        title,
        content,
        type,
        priority,
        sendToAll,
        recipients: !sendToAll ? recipients : undefined,
      });

      // 确保有效的用户ID
      if (!sendToAll && (!recipients || recipients.length === 0)) {
        toast({
          title: "选择用户错误",
          description: "请至少选择一个接收用户",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      const response = await fetch("/api/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title,
          content,
          type,
          priority,
          sendToAll,
          recipients: !sendToAll ? recipients : undefined,
        }),
      })

      const data = await response.json()
      console.log("API响应:", data);

      if (response.ok && data.success) {
        // 触发全局通知事件
        window.dispatchEvent(new CustomEvent('notification:created'));

        toast({
          title: "发布成功",
          description: "通知已成功发布",
        })
        router.push("/notifications")
      } else {
        throw new Error(data.message || "发布失败")
      }
    } catch (error) {
      console.error("发布通知失败:", error)
      toast({
        title: "发布失败",
        description: error instanceof Error ? error.message : "创建通知时出错，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 获取图标
  const getTypeIcon = (notificationType: string) => {
    switch (notificationType) {
      case "system":
        return <Info className="h-4 w-4 text-blue-500" />
      case "task":
        return <Tag className="h-4 w-4 text-green-500" />
      case "security":
        return <Shield className="h-4 w-4 text-red-500" />
      case "account":
        return <Zap className="h-4 w-4 text-yellow-500" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  return (
    <div className="container py-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/notifications">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">发布通知</h1>
      </div>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>创建新通知</CardTitle>
            <CardDescription>填写通知信息，发布后将按接收范围推送通知</CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">通知标题</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="输入通知标题"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">通知类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type" className="w-full">
                    <SelectValue placeholder="选择通知类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map((type) => (
                      <SelectItem key={type.id} value={String(type.id)}>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(type.code)}
                          <span>{type.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority">优先级</Label>
              <RadioGroup defaultValue="medium" value={priority} onValueChange={setPriority} className="flex space-x-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="low" id="low" />
                  <Label htmlFor="low">低</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="medium" id="medium" />
                  <Label htmlFor="medium">中</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="high" id="high" />
                  <Label htmlFor="high">高</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">通知内容</Label>
              <RichTextEditor
                value={content}
                onChange={setContent}
                className="min-h-[300px]"
              />
            </div>

            <div className="space-y-2">
              <Label>接收范围</Label>
              <RadioGroup defaultValue="all" value={sendToAll ? "all" : "specific"}
                onValueChange={(value) => setSendToAll(value === "all")}
                className="flex space-x-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="all" id="all" />
                  <Label htmlFor="all">所有用户</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="specific" id="specific" />
                  <Label htmlFor="specific">指定用户</Label>
                </div>
              </RadioGroup>
            </div>

            {!sendToAll && (
              <div className="space-y-2">
                <Label>选择接收用户</Label>
                <UserSelector
                  selectedUsers={recipients}
                  onSelectionChange={setRecipients}
                  placeholder="选择接收通知的用户..."
                />
                {recipients.length === 0 && (
                  <p className="text-sm text-amber-500 mt-1">
                    请至少选择一个接收用户
                  </p>
                )}
              </div>
            )}
          </CardContent>

          <CardFooter className="justify-end space-x-2">
            <Button
              variant="outline"
              type="button"
              onClick={() => router.push("/notifications")}
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-1"
            >
              <Send className="h-4 w-4" />
              {isSubmitting ? '发布中...' : '发布通知'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}