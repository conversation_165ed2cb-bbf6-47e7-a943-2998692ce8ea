"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Bell, Check, Info, Save, Shield, Tag, Zap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/components/ui/use-toast"
import RichTextEditor from "@/components/shared/rich-text-editor"
import Link from "next/link"

type NotificationType = "system" | "task" | "security" | "account"

// 通知数据结构
interface Notification {
  id: string
  title: string
  content: string
  type: NotificationType
  priority: string
  createdAt: string
  read?: boolean
  readAt?: string
}

export default function EditNotificationPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [priority, setPriority] = useState("medium")
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [notificationTypes, setNotificationTypes] = useState<Array<{ id: number, code: string, name: string }>>([])

  // 获取通知类型列表
  useEffect(() => {
    const fetchNotificationTypes = async () => {
      try {
        const response = await fetch('/api/notifications/types')
        const data = await response.json()
        if (data.success) {
          setNotificationTypes(data.data)
        }
      } catch (error) {
        console.error('获取通知类型失败:', error)
      }
    }
    fetchNotificationTypes()
  }, [])

  // 获取通知详情
  useEffect(() => {
    const fetchNotification = async () => {
      try {
        setIsLoading(true)
        const response = await fetch(`/api/notifications/${params.id}`)
        
        if (!response.ok) {
          throw new Error("获取通知详情失败")
        }
        
        const data = await response.json()
        
        if (!data.success || !data.data) {
          throw new Error(data.message || "通知不存在")
        }
        
        const notification = data.data
        
        // 填充表单
        setTitle(notification.title)
        setContent(notification.content)
        setType(String(notification.typeId)) // 使用 typeId
        setPriority(notification.priority)
      } catch (error) {
        console.error("获取通知详情失败:", error)
        toast({
          title: "加载失败",
          description: error instanceof Error ? error.message : "获取通知详情失败，请稍后重试",
          variant: "destructive",
        })
        router.push("/notifications")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchNotification()
  }, [params.id, router])

  // 提交更新
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!title || !content || !type) {
      toast({
        title: "表单不完整",
        description: "请填写标题、内容和选择通知类型",
        variant: "destructive",
      })
      return
    }
    
    setIsSubmitting(true)
    
    try {
      console.log("提交数据:", {
        title,
        content,
        type,
        priority,
      });
      
      const response = await fetch(`/api/notifications/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title,
          content,
          type: parseInt(type), // 确保 type 是数字
          priority,
        }),
      })
      
      const data = await response.json()
      
      if (response.ok && data.success) {
        toast({
          title: "更新成功",
          description: "通知已成功更新",
        })
        router.push("/notifications")
      } else {
        throw new Error(data.message || "更新失败")
      }
    } catch (error) {
      console.error("更新通知失败:", error)
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "更新通知时出错，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // 获取图标
  const getTypeIcon = (notificationType: string) => {
    switch (notificationType) {
      case "system":
        return <Info className="h-4 w-4 text-blue-500" />
      case "task":
        return <Tag className="h-4 w-4 text-green-500" />
      case "security":
        return <Shield className="h-4 w-4 text-red-500" />
      case "account":
        return <Zap className="h-4 w-4 text-yellow-500" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return (
      <div className="container py-6">
        <div className="flex items-center justify-center h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container py-6">
      <div className="flex items-center gap-2 mb-6">
        <Button variant="ghost" size="icon" asChild>
          <Link href="/notifications">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-2xl font-bold">编辑通知</h1>
      </div>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>修改通知信息</CardTitle>
            <CardDescription>更新通知信息后保存</CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">通知标题</Label>
                <Input 
                  id="title" 
                  value={title} 
                  onChange={(e) => setTitle(e.target.value)} 
                  placeholder="输入通知标题" 
                  required 
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="type">通知类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type" className="w-full">
                    <SelectValue placeholder="选择通知类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {notificationTypes.map((type) => (
                      <SelectItem key={type.id} value={String(type.id)}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>优先级</Label>
              <RadioGroup 
                value={priority} 
                onValueChange={setPriority} 
                className="flex items-center space-x-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="low" id="low" />
                  <Label htmlFor="low">低</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="medium" id="medium" />
                  <Label htmlFor="medium">中</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="high" id="high" />
                  <Label htmlFor="high">高</Label>
                </div>
              </RadioGroup>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="content">通知内容</Label>
              <RichTextEditor 
                value={content} 
                onChange={(html) => setContent(html)} 
                className="min-h-[300px]"
              />
            </div>
          </CardContent>
          
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" asChild>
              <Link href="/notifications">取消</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex items-center gap-2">
              {isSubmitting ? (
                <>处理中...</>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存更改
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
} 