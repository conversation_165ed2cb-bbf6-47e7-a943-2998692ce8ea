"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"

export default function AdminInitPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  // 初始化菜单数据
  const initMenus = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/init-menus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success("菜单数据初始化成功")
        setResult(data)
      } else {
        toast.error(data.message || "菜单数据初始化失败")
      }
    } catch (error) {
      console.error("菜单数据初始化失败:", error)
      toast.error("菜单数据初始化失败")
    } finally {
      setLoading(false)
    }
  }

  // 修复角色菜单关联
  const fixRoleMenus = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/fix-role-menus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success("角色菜单关联修复成功")
        setResult(data)
      } else {
        toast.error(data.message || "角色菜单关联修复失败")
      }
    } catch (error) {
      console.error("角色菜单关联修复失败:", error)
      toast.error("角色菜单关联修复失败")
    } finally {
      setLoading(false)
    }
  }

  // 初始化管理员菜单权限
  const initAdminMenus = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/init-admin-menus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success("管理员菜单权限初始化成功")
        setResult(data)
      } else {
        toast.error(data.message || "管理员菜单权限初始化失败")
      }
    } catch (error) {
      console.error("管理员菜单权限初始化失败:", error)
      toast.error("管理员菜单权限初始化失败")
    } finally {
      setLoading(false)
    }
  }

  // 同步角色权限
  const syncRolePermissions = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/roles/sync-permissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({})
      })

      const data = await response.json()

      if (data.success) {
        toast.success("角色权限同步成功")
        setResult(data)
      } else {
        toast.error(data.message || "角色权限同步失败")
      }
    } catch (error) {
      console.error("角色权限同步失败:", error)
      toast.error("角色权限同步失败")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>管理员初始化工具</CardTitle>
          <CardDescription>
            用于初始化系统数据和修复系统问题
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>菜单初始化</CardTitle>
                <CardDescription>
                  初始化系统菜单数据
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button 
                  onClick={initMenus} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? "处理中..." : "初始化菜单"}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>角色菜单关联修复</CardTitle>
                <CardDescription>
                  修复角色和菜单的关联关系
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button 
                  onClick={fixRoleMenus} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? "处理中..." : "修复关联"}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>管理员菜单权限初始化</CardTitle>
                <CardDescription>
                  初始化管理员角色的菜单权限
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button 
                  onClick={initAdminMenus} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? "处理中..." : "初始化权限"}
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>角色权限同步</CardTitle>
                <CardDescription>
                  同步所有角色的权限到jCasbin
                </CardDescription>
              </CardHeader>
              <CardFooter>
                <Button 
                  onClick={syncRolePermissions} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? "处理中..." : "同步权限"}
                </Button>
              </CardFooter>
            </Card>
          </div>

          {result && (
            <Card>
              <CardHeader>
                <CardTitle>操作结果</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push("/dashboard")}>
            返回仪表盘
          </Button>
          <Button variant="outline" onClick={() => window.location.reload()}>
            刷新页面
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
