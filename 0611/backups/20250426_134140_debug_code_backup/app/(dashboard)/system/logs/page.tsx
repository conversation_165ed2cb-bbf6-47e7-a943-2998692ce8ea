/**
 * 系统日志页面
 */
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { DatePicker } from '@/components/ui/date-picker'
import { Pagination } from '@/components/ui/pagination'
import { toast } from 'sonner'
import { Loader2, RefreshCw, Download, Search, X } from 'lucide-react'

export default function LogsPage() {
  const [logs, setLogs] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [exporting, setExporting] = useState(false)
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  
  // 筛选条件
  const [filters, setFilters] = useState({
    userId: '',
    action: '',
    module: '',
    status: '',
    startDate: null as Date | null,
    endDate: null as Date | null
  })
  
  // 获取日志列表
  const fetchLogs = async () => {
    try {
      setLoading(true)
      
      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString()
      })
      
      if (filters.userId) queryParams.append('userId', filters.userId)
      if (filters.action) queryParams.append('action', filters.action)
      if (filters.module) queryParams.append('module', filters.module)
      if (filters.status) queryParams.append('status', filters.status)
      if (filters.startDate) queryParams.append('startDate', filters.startDate.toISOString())
      if (filters.endDate) queryParams.append('endDate', filters.endDate.toISOString())
      
      const response = await fetch(`/api/logs?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('获取日志列表失败')
      }
      
      const data = await response.json()
      if (data.success) {
        setLogs(data.logs || [])
        setTotal(data.total || 0)
      } else {
        toast.error(data.message || '获取日志列表失败')
      }
    } catch (error) {
      console.error('获取日志列表失败:', error)
      toast.error('获取日志列表失败: ' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setLoading(false)
    }
  }
  
  // 刷新数据
  const refreshData = () => {
    fetchLogs()
    toast.success('数据已刷新')
  }
  
  // 导出日志
  const exportLogs = async (format: 'csv' | 'excel') => {
    try {
      setExporting(true)
      
      // 构建查询参数
      const queryParams = new URLSearchParams({
        format
      })
      
      if (filters.userId) queryParams.append('userId', filters.userId)
      if (filters.action) queryParams.append('action', filters.action)
      if (filters.module) queryParams.append('module', filters.module)
      if (filters.status) queryParams.append('status', filters.status)
      if (filters.startDate) queryParams.append('startDate', filters.startDate.toISOString())
      if (filters.endDate) queryParams.append('endDate', filters.endDate.toISOString())
      
      const response = await fetch(`/api/logs/export?${queryParams.toString()}`, {
        method: 'GET'
      })
      
      if (!response.ok) {
        throw new Error('导出日志失败')
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      let filename = 'system-logs'
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/)
        if (filenameMatch) {
          filename = filenameMatch[1]
        }
      }
      
      // 下载文件
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = filename
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      
      toast.success(`日志已导出为 ${format.toUpperCase()} 格式`)
    } catch (error) {
      console.error('导出日志失败:', error)
      toast.error('导出日志失败: ' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setExporting(false)
    }
  }
  
  // 处理筛选条件变化
  const handleFilterChange = (name: string, value: any) => {
    setFilters(prev => ({ ...prev, [name]: value }))
  }
  
  // 重置筛选条件
  const resetFilters = () => {
    setFilters({
      userId: '',
      action: '',
      module: '',
      status: '',
      startDate: null,
      endDate: null
    })
    setPage(1)
  }
  
  // 应用筛选条件
  const applyFilters = () => {
    setPage(1)
    fetchLogs()
  }
  
  // 处理分页变化
  const handlePageChange = (newPage: number) => {
    setPage(newPage)
  }
  
  // 处理每页条数变化
  const handlePageSizeChange = (value: string) => {
    setPageSize(parseInt(value))
    setPage(1)
  }
  
  // 获取状态标签样式
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    }
  }
  
  // 初始加载
  useEffect(() => {
    fetchLogs()
  }, [page, pageSize])
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">系统日志</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={refreshData}
            disabled={loading}
          >
            {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCw className="mr-2 h-4 w-4" />}
            刷新
          </Button>
          <Button
            variant="outline"
            onClick={() => exportLogs('csv')}
            disabled={exporting}
          >
            {exporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
            导出 CSV
          </Button>
          <Button
            variant="outline"
            onClick={() => exportLogs('excel')}
            disabled={exporting}
          >
            {exporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
            导出 Excel
          </Button>
        </div>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>筛选条件</CardTitle>
          <CardDescription>
            根据条件筛选系统日志
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userId">用户ID</Label>
              <Input
                id="userId"
                value={filters.userId}
                onChange={(e) => handleFilterChange('userId', e.target.value)}
                placeholder="输入用户ID"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="action">操作类型</Label>
              <Input
                id="action"
                value={filters.action}
                onChange={(e) => handleFilterChange('action', e.target.value)}
                placeholder="如：login, update, delete"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="module">模块</Label>
              <Input
                id="module"
                value={filters.module}
                onChange={(e) => handleFilterChange('module', e.target.value)}
                placeholder="如：user, role, menu"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="status">状态</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="success">成功</SelectItem>
                  <SelectItem value="error">错误</SelectItem>
                  <SelectItem value="warning">警告</SelectItem>
                  <SelectItem value="info">信息</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>开始日期</Label>
              <DatePicker
                selected={filters.startDate}
                onSelect={(date) => handleFilterChange('startDate', date)}
                placeholder="选择开始日期"
              />
            </div>
            <div className="space-y-2">
              <Label>结束日期</Label>
              <DatePicker
                selected={filters.endDate}
                onSelect={(date) => handleFilterChange('endDate', date)}
                placeholder="选择结束日期"
              />
            </div>
          </div>
          
          <div className="flex justify-end mt-4 space-x-2">
            <Button variant="outline" onClick={resetFilters}>
              <X className="mr-2 h-4 w-4" />
              重置
            </Button>
            <Button onClick={applyFilters}>
              <Search className="mr-2 h-4 w-4" />
              查询
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>日志列表</CardTitle>
          <CardDescription>
            系统操作日志记录
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-40">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>用户</TableHead>
                    <TableHead>操作</TableHead>
                    <TableHead>模块</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>IP地址</TableHead>
                    <TableHead>时间</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-4">
                        暂无日志数据
                      </TableCell>
                    </TableRow>
                  ) : (
                    logs.map(log => (
                      <TableRow key={log.id}>
                        <TableCell className="font-medium">{log.id.substring(0, 8)}</TableCell>
                        <TableCell>{log.userId}</TableCell>
                        <TableCell>{log.action}</TableCell>
                        <TableCell>{log.module}</TableCell>
                        <TableCell>
                          <span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(log.status)}`}>
                            {log.status}
                          </span>
                        </TableCell>
                        <TableCell>{log.ipAddress}</TableCell>
                        <TableCell>{new Date(log.createdAt).toLocaleString()}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
              
              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="pageSize">每页显示</Label>
                  <Select
                    value={pageSize.toString()}
                    onValueChange={handlePageSizeChange}
                  >
                    <SelectTrigger id="pageSize" className="w-[70px]">
                      <SelectValue placeholder={pageSize.toString()} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span>条记录，共 {total} 条</span>
                </div>
                
                <Pagination
                  currentPage={page}
                  totalPages={Math.ceil(total / pageSize)}
                  onPageChange={handlePageChange}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
