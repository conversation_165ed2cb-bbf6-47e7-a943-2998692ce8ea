"use client"

import { useEffect, useState } from "react"
import { DashboardShell } from "@/components/dashboard-shell"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export default function TestLoginPage() {
  const [cookieInfo, setCookieInfo] = useState<string>("加载中...")
  const [userInfo, setUserInfo] = useState<string>("加载中...")

  useEffect(() => {
    // 获取所有cookie信息（仅用于测试）
    const cookies = document.cookie.split(';').map(cookie => cookie.trim())
    setCookieInfo(cookies.join('\n') || "没有找到任何cookie")

    // 获取当前用户信息
    const fetchUserInfo = async () => {
      try {
        const response = await fetch('/api/auth/current-user')
        const data = await response.json()
        setUserInfo(JSON.stringify(data, null, 2))
      } catch (error) {
        setUserInfo(`获取用户信息失败: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    fetchUserInfo()
  }, [])

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' })
      window.location.href = '/login'
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  return (
    <DashboardShell>
      <div className="flex flex-col space-y-4">
        <h1 className="text-2xl font-bold">登录测试页面</h1>
        <p>如果你能看到这个页面，说明登录成功并且重定向正常工作！</p>
        
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Cookie 信息（仅用于测试）</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-40">
              {cookieInfo}
            </pre>
          </CardContent>
        </Card>

        <Card className="mb-4">
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-40">
              {userInfo}
            </pre>
          </CardContent>
        </Card>

        <Button onClick={handleLogout} variant="destructive">
          登出测试
        </Button>
      </div>
    </DashboardShell>
  )
}
