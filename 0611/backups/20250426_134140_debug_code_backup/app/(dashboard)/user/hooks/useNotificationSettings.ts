"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"

export interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  appEnabled: boolean
  types: string[]
}

export function useNotificationSettings() {
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailEnabled: true,
    smsEnabled: false, // 默认关闭短信通知
    appEnabled: true,
    types: ["SYSTEM", "SECURITY", "TASK"]
  })
  
  const [savingNotificationSettings, setSavingNotificationSettings] = useState(false)

  // 获取通知设置
  const fetchNotificationSettings = async () => {
    try {
      const response = await fetch('/api/user/notifications/settings')
      const data = await response.json()

      if (data.success) {
        setNotificationSettings(data.data)
        return data.data
      } else {
        throw new Error(data.message || '获取通知设置失败')
      }
    } catch (error: any) {
      console.error('Error fetching notification settings:', error)
      toast({
        title: "获取通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return null
    }
  }

  // 保存通知设置
  const saveNotificationSettings = async () => {
    try {
      setSavingNotificationSettings(true)
      const response = await fetch('/api/user/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationSettings),
      })

      const data = await response.json()
      if (data.success) {
        setNotificationSettings(data.data)
        toast({
          title: "通知设置已更新",
          description: "您的通知偏好设置已成功保存",
          variant: "success",
        })
        return true
      } else {
        throw new Error(data.message)
      }
    } catch (error: any) {
      toast({
        title: "更新通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return false
    } finally {
      setSavingNotificationSettings(false)
    }
  }

  // 处理通知类型变更
  const handleNotificationTypeChange = (type: string, checked: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      types: checked
        ? [...prev.types, type]
        : prev.types.filter(t => t !== type)
    }))
  }

  // 处理通知渠道变更
  const handleChannelChange = async (channel: 'email' | 'sms' | 'app', checked: boolean) => {
    try {
      setSavingNotificationSettings(true)
      
      // 更新本地状态
      const updatedSettings = {
        ...notificationSettings,
        [channel === 'email' ? 'emailEnabled' : 
         channel === 'sms' ? 'smsEnabled' : 'appEnabled']: checked
      }
      
      // 调用API保存设置
      const response = await fetch('/api/user/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      })

      const data = await response.json()
      if (data.success) {
        setNotificationSettings(data.data)
        toast({
          title: "通知设置已更新",
          description: `${channel === 'email' ? '邮件' : 
                        channel === 'sms' ? '短信' : '应用内'}通知已${checked ? '开启' : '关闭'}`,
          variant: "success",
        })
        return true
      } else {
        throw new Error(data.message)
      }
    } catch (error: any) {
      toast({
        title: "更新通知设置失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      // 恢复原来的设置
      return false
    } finally {
      setSavingNotificationSettings(false)
    }
  }

  return {
    notificationSettings,
    setNotificationSettings,
    savingNotificationSettings,
    fetchNotificationSettings,
    saveNotificationSettings,
    handleNotificationTypeChange,
    handleChannelChange
  }
}
