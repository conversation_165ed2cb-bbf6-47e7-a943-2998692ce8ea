"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { DateRange } from "react-day-picker"

export interface LoginHistoryItem {
  id: string
  ipAddress: string
  location: string
  device: string
  status: string
  failReason?: string
  createdAt: string
}

export interface LoginHistoryData {
  items: LoginHistoryItem[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export function useLoginHistory() {
  const [loginHistory, setLoginHistory] = useState<LoginHistoryData>({
    items: [],
    total: 0,
    page: 1,
    pageSize: 10,
    totalPages: 0
  })
  
  const [loadingHistory, setLoadingHistory] = useState(false)
  const [historySearchTerm, setHistorySearchTerm] = useState("")
  const [historyDateRange, setHistoryDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  })
  const [historyStatusFilter, setHistoryStatusFilter] = useState<string>("all")

  // 获取登录历史
  const fetchLoginHistory = async (
    page = loginHistory.page,
    search = historySearchTerm,
    status = historyStatusFilter,
    startDate = historyDateRange.from?.toISOString(),
    endDate = historyDateRange.to?.toISOString()
  ) => {
    try {
      setLoadingHistory(true)
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: loginHistory.pageSize.toString(),
        ...(search ? { search } : {}),
        ...(status !== 'all' ? { status } : {}),
        ...(startDate ? { startDate } : {}),
        ...(endDate ? { endDate } : {})
      })

      const response = await fetch(`/api/user/login-history?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setLoginHistory(data.data)
        return data.data
      } else {
        throw new Error(data.message || '获取登录历史失败')
      }
    } catch (error: any) {
      console.error('Error fetching login history:', error)
      toast({
        title: "获取登录历史失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
      return null
    } finally {
      setLoadingHistory(false)
    }
  }

  // 导出登录历史
  const exportLoginHistory = async (format: 'csv' | 'excel') => {
    try {
      // 准备导出数据
      const headers = ['时间', 'IP地址', '位置', '设备', '状态', '失败原因']
      const data = loginHistory.items.map(item => [
        new Date(item.createdAt).toLocaleString('zh-CN'),
        item.ipAddress,
        item.location,
        item.device,
        item.status === 'success' ? '成功' : '失败',
        item.failReason || '-'
      ])

      if (format === 'csv') {
        // 导出为CSV
        const csvContent = [
          headers.join(','),
          ...data.map(row => row.map(cell => `"${cell}"`).join(','))
        ].join('\n')

        const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), csvContent], { type: 'text/csv;charset=utf-8;' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.setAttribute('href', url)
        link.setAttribute('download', `登录历史_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else if (format === 'excel') {
        // 导出为Excel (XML格式)
        let xlsContent = '<?xml version="1.0" encoding="UTF-8"?>\n'
        xlsContent += '<?mso-application progid="Excel.Sheet"?>\n'
        xlsContent += '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" '
        xlsContent += 'xmlns:o="urn:schemas-microsoft-com:office:office" '
        xlsContent += 'xmlns:x="urn:schemas-microsoft-com:office:excel" '
        xlsContent += 'xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" '
        xlsContent += 'xmlns:html="http://www.w3.org/TR/REC-html40">\n'
        xlsContent += '<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">\n'
        xlsContent += '<Author>系统导出</Author>\n'
        xlsContent += `<Created>${new Date().toISOString()}</Created>\n`
        xlsContent += '</DocumentProperties>\n'
        xlsContent += '<Styles>\n'
        xlsContent += '<Style ss:ID="s62">\n'
        xlsContent += '<Font ss:Bold="1"/>\n'
        xlsContent += '<Interior ss:Color="#DDDDDD" ss:Pattern="Solid"/>\n'
        xlsContent += '</Style>\n'
        xlsContent += '</Styles>\n'

        // 添加工作表
        xlsContent += `<Worksheet ss:Name="登录历史">\n`
        xlsContent += '<Table ss:ExpandedColumnCount="' + headers.length + '" ' +
                      'ss:ExpandedRowCount="' + (data.length + 1) + '" ' +
                      'x:FullColumns="1" x:FullRows="1" ' +
                      'ss:DefaultColumnWidth="100" ss:DefaultRowHeight="20">\n'

        // 添加列宽
        xlsContent += '<Column ss:Width="150"/>\n' // 时间
        xlsContent += '<Column ss:Width="100"/>\n' // IP地址
        xlsContent += '<Column ss:Width="100"/>\n' // 位置
        xlsContent += '<Column ss:Width="200"/>\n' // 设备
        xlsContent += '<Column ss:Width="80"/>\n'  // 状态
        xlsContent += '<Column ss:Width="120"/>\n' // 失败原因

        // 添加表头
        xlsContent += '<Row ss:Height="25">\n'
        headers.forEach(header => {
          xlsContent += `<Cell ss:StyleID="s62"><Data ss:Type="String">${escapeXML(header)}</Data></Cell>\n`
        })
        xlsContent += '</Row>\n'

        // 添加数据行
        data.forEach(rowData => {
          xlsContent += '<Row>\n'
          rowData.forEach(cellData => {
            xlsContent += `<Cell><Data ss:Type="String">${escapeXML(String(cellData))}</Data></Cell>\n`
          })
          xlsContent += '</Row>\n'
        })

        xlsContent += '</Table>\n'
        xlsContent += '<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">\n'
        xlsContent += '<PageSetup>\n'
        xlsContent += '<Header x:Margin="0.3"/>\n'
        xlsContent += '<Footer x:Margin="0.3"/>\n'
        xlsContent += '<PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>\n'
        xlsContent += '</PageSetup>\n'
        xlsContent += '<Selected/>\n'
        xlsContent += '<ProtectObjects>False</ProtectObjects>\n'
        xlsContent += '<ProtectScenarios>False</ProtectScenarios>\n'
        xlsContent += '</WorksheetOptions>\n'
        xlsContent += '</Worksheet>\n'
        xlsContent += '</Workbook>\n'

        // 创建Blob并下载
        const blob = new Blob([new Uint8Array([0xEF, 0xBB, 0xBF]), xlsContent], { type: 'application/vnd.ms-excel;charset=utf-8' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.setAttribute('href', url)
        link.setAttribute('download', `登录历史_${new Date().toISOString().slice(0, 10)}.xls`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      toast({
        title: "导出成功",
        description: `登录历史已导出为${format === 'csv' ? 'CSV' : 'Excel'}文件`,
        variant: "success",
      })
    } catch (error: any) {
      console.error('Error exporting login history:', error)
      toast({
        title: "导出失败",
        description: error.message || "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 辅助函数：转义XML特殊字符
  const escapeXML = (str: string) => {
    return str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  return {
    loginHistory,
    loadingHistory,
    historySearchTerm,
    setHistorySearchTerm,
    historyDateRange,
    setHistoryDateRange,
    historyStatusFilter,
    setHistoryStatusFilter,
    fetchLoginHistory,
    exportLoginHistory
  }
}
