"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon, Info, Trash2, Upload, X } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { VideoCallService } from "@/lib/api/video-call-service"
import { VIDEO_CALL_API_CONFIG } from "@/lib/api/config"
import type { BotInfo, BlacklistGroup } from "@/lib/api/interfaces"

export default function VideoTasksPage() {
  // 使用视频外呼服务单例
  const { videoCallService } = require("@/lib/api/video-call-service")

  // 用户角色状态
  const { user } = useAuth()
  const userType = user?.data?.role?.code === "super" ? "admin" : "customer"

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // 对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)

  // 新建任务表单状态
  const [newTaskName, setNewTaskName] = useState("")
  const [newTaskContent, setNewTaskContent] = useState("")
  const [newTaskType, setNewTaskType] = useState("")
  const [newTaskStartTime, setNewTaskStartTime] = useState<Date | undefined>(undefined)
  const [newTaskResource, setNewTaskResource] = useState("")
  const [newTaskPhoneNumber, setNewTaskPhoneNumber] = useState("")
  const [newTaskSmsType, setNewTaskSmsType] = useState("")
  const [newTaskSmsTemplate, setNewTaskSmsTemplate] = useState("")
  const [showDateTimePicker, setShowDateTimePicker] = useState(false)
  const [selectedHour, setSelectedHour] = useState("00")
  const [selectedMinute, setSelectedMinute] = useState("00")
  const [selectedSecond, setSelectedSecond] = useState("00")
  const [activeTab, setActiveTab] = useState("manual")

  // Bot和黑名单组状态
  const [botList, setBotList] = useState<BotInfo[]>([])
  const [blacklistGroups, setBlacklistGroups] = useState<BlacklistGroup[]>([])
  const [selectedBot, setSelectedBot] = useState("")
  const [selectedBlacklistGroups, setSelectedBlacklistGroups] = useState<string[]>([])

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [tasks, setTasks] = useState<
    Array<{
      taskId: string
      name: string
      status: string
      createdAt: string
      creator: string
      videoBotId: string
      importNumber?: number
      deliverNumber?: number
      calledCount?: number
    }>
  >([])
  const [totalTasks, setTotalTasks] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  /**
   * 页面加载时获取任务列表和Bot列表
   */
  useEffect(() => {
    fetchTasks()
    fetchBotList()
    fetchBlacklistGroups()
  }, [currentPage])

  /**
   * 获取任务列表
   * 调用API获取任务数据
   */
  const fetchTasks = async () => {
    setIsLoading(true)
    try {
      // 计算时间范围
      const endTime = Date.now()
      const startTime = endTime - 30 * 24 * 60 * 60 * 1000 // 30天前

      const response = await videoCallService.getTaskList(startTime, endTime, currentPage, itemsPerPage)

      setTasks(response.list)
      setTotalPages(Math.ceil(response.list.length / itemsPerPage))
      setTotalTasks(response.list.length)

      // 获取每个任务的详情
      const tasksWithDetails = await Promise.all(
        response.list.map(async (task: any) => {
          try {
            const details = await videoCallService.getTaskDetail(task.taskId)
            return { ...task, ...details }
          } catch (error) {
            console.error(`获取任务详情失败 [${task.taskId}]:`, error)
            return task
          }
        }),
      )

      setTasks(tasksWithDetails)
    } catch (error) {
      console.error("获取任务列表失败:", error)
      toast({
        title: "获取任务列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取Bot列表
   */
  const fetchBotList = async () => {
    try {
      const bots = await videoCallService.getBotList()
      setBotList(bots)
    } catch (error) {
      console.error("获取Bot列表失败:", error)
      toast({
        title: "获取Bot列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  /**
   * 获取黑名单分组列表
   */
  const fetchBlacklistGroups = async () => {
    try {
      const groups = await videoCallService.getBlacklistGroups()
      setBlacklistGroups(groups)
    } catch (error) {
      console.error("获取黑名单分组列表失败:", error)
      toast({
        title: "获取黑名单分组列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  /**
   * 处理任务删除
   * 目前API不支持删除任务，可以实现为停止任务
   */
  const handleDeleteTask = async () => {
    if (!taskToDelete) return

    setIsLoading(true)
    try {
      // 这里应该调用停止任务的API，但文档中没有提供
      toast({
        title: "操作提示",
        description: "当前API不支持删除任务，请在平台管理界面操作",
        variant: "default",
      })

      // 重新获取任务列表
      fetchTasks()
    } catch (error) {
      console.error("操作任务失败:", error)
      toast({
        title: "操作任务失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setTaskToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  /**
   * 处理新建任务
   * 调用API创建新任务
   */
  const handleCreateTask = async () => {
    // 表单验证
    if (!newTaskName) {
      toast({
        title: "表单错误",
        description: "请输入任务名称",
        variant: "destructive",
      })
      return
    }

    if (!selectedBot) {
      toast({
        title: "表单错误",
        description: "请选择外呼内容",
        variant: "destructive",
      })
      return
    }

    if (!newTaskType) {
      toast({
        title: "表单错误",
        description: "请选择外呼类型",
        variant: "destructive",
      })
      return
    }

    if (!newTaskStartTime) {
      toast({
        title: "表单错误",
        description: "请选择外呼开始时间",
        variant: "destructive",
      })
      return
    }

    if (!newTaskResource) {
      toast({
        title: "表单错误",
        description: "请选择外呼资源",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "manual" && !newTaskPhoneNumber) {
      toast({
        title: "表单错误",
        description: "请输入呼叫号码",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "import" && !importFile) {
      toast({
        title: "表单错误",
        description: "请选择导入文件",
        variant: "destructive",
      })
      return
    }

    // 格式化日期和时间
    let formattedDateTime = ""
    if (newTaskStartTime) {
      const date = format(newTaskStartTime, "yyyy-MM-dd")
      formattedDateTime = `${date} ${selectedHour}:${selectedMinute}:${selectedSecond}`
    }

    setIsLoading(true)
    try {
      // 准备手机号列表
      let phoneList: Array<{ name?: string; phone: string }> = []

      if (activeTab === "manual") {
        // 手动输入的手机号，按逗号分隔
        phoneList = newTaskPhoneNumber.split(/[,，]/).map((phone) => ({
          phone: phone.trim(),
        }))
      } else if (activeTab === "import" && importFile) {
        // 这里应该处理导入的文件，但需要在后端实现
        toast({
          title: "文件导入",
          description: "文件导入功能需要后端支持，请使用手动输入方式",
          variant: "default",
        })
        return
      }

      // 准备黑名单分组
      const blackListGroups =
        selectedBlacklistGroups.length > 0
          ? blacklistGroups
              .filter((group) => selectedBlacklistGroups.includes(group.groupId))
              .map((group) => ({ groupId: group.groupId, groupName: group.groupName }))
          : undefined

      // 创建视频外呼任务
      const response = await videoCallService.createVideoCallTask({
        name: newTaskName,
        mediaType: "videoBot",
        videoBotId: selectedBot,
        deliverList: phoneList,
        isRelateSendMessage: newTaskSmsType === "文本短信",
        msgTemplateId: newTaskSmsType === "文本短信" ? newTaskSmsTemplate : undefined,
        msgTitle: newTaskSmsType === "文本短信" ? "系统通知" : undefined,
        sendMessagePhase: newTaskSmsType === "文本短信" ? 1 : undefined,
        isRelateVideoMessage: newTaskSmsType === "视频短信",
        videoMessageTemplateId: newTaskSmsType === "视频短信" ? newTaskSmsTemplate : undefined,
        sendVideoMessagePhase: newTaskSmsType === "视频短信" ? 1 : undefined,
        blackListGroups,
      })

      toast({
        title: "创建成功",
        description: `任务ID: ${response.taskId}`,
        variant: "success",
      })

      // 重置表单并关闭对话框
      resetNewTaskForm()
      setShowImportDialog(false)

      // 重新获取任务列表
      fetchTasks()
    } catch (error) {
      console.error("创建任务失败:", error)
      toast({
        title: "创建任务失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 重置新建任务表单
   * 清空所有表单字段
   */
  const resetNewTaskForm = () => {
    setNewTaskName("")
    setNewTaskContent("")
    setNewTaskType("")
    setNewTaskStartTime(undefined)
    setNewTaskResource("")
    setNewTaskPhoneNumber("")
    setNewTaskSmsType("")
    setNewTaskSmsTemplate("")
    setSelectedHour("00")
    setSelectedMinute("00")
    setSelectedSecond("00")
    setActiveTab("manual")
    setImportFile(null)
    setSelectedBot("")
    setSelectedBlacklistGroups([])
  }

  /**
   * 处理文件选择
   * 当用户选择导入文件时触发
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0])
    }
  }

  /**
   * 处理搜索
   * 根据搜索条件获取任务列表
   */
  const handleSearch = () => {
    setCurrentPage(1) // 重置到第一页
    fetchTasks()
  }

  /**
   * 生成时间选择器选项
   * 生成小时、分钟、秒钟的选择器选项
   */
  const generateTimeOptions = (max: number) => {
    const options = []
    for (let i = 0; i < max; i++) {
      const value = i.toString().padStart(2, "0")
      options.push(
        <option key={value} value={value}>
          {value}
        </option>,
      )
    }
    return options
  }

  /**
   * 获取任务状态显示样式
   */
  const getStatusClass = (status: string) => {
    switch (status) {
      case "initialized":
        return "status-pending"
      case "started":
        return "status-processing"
      case "paused":
        return "status-warning"
      case "finished":
        return "status-active"
      case "stopped":
        return "status-inactive"
      default:
        return "status-default"
    }
  }

  /**
   * 获取任务状态中文名称
   */
  const getStatusName = (status: string) => {
    switch (status) {
      case "initialized":
        return "待启动"
      case "started":
        return "已启动"
      case "paused":
        return "已暂停"
      case "finished":
        return "已完成"
      case "stopped":
        return "已停止"
      default:
        return status
    }
  }

  // 根据用户角色调整页面标题和描述
  const pageTitle = userType === "admin" ? "5G视频外呼任务" : "我的5G视频外呼任务"
  const pageDescription = userType === "admin" ? "管理5G视频外呼任务" : "管理您的5G视频外呼任务"

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading={pageTitle} text={pageDescription} />
        <Card>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    {botList.map((bot) => (
                      <SelectItem key={bot._id} value={bot._id}>
                        {bot.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="1">视频通知</SelectItem>
                    <SelectItem value="2">视频互动</SelectItem>
                    <SelectItem value="3">语音互动</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => {
                        if (range) {
                          setStartDateRange({
                            from: range.from,
                            to: range.to || undefined
                          })
                        } else {
                          setStartDateRange({ from: undefined, to: undefined })
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => {
                        if (range) {
                          setCreateDateRange({
                            from: range.from,
                            to: range.to || undefined
                          })
                        } else {
                          setCreateDateRange({ from: undefined, to: undefined })
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch} disabled={isLoading}>
                {isLoading ? "查询中..." : "查询"}
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  resetNewTaskForm()
                  setShowImportDialog(true)
                }}
                disabled={isLoading}
              >
                <Upload className="mr-2 h-4 w-4" />
                创建任务
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 任务列表表格 */}
        <div className="rounded-md border mt-6">
          <Table className="table-enhanced">
            <TableHeader>
              <TableRow>
                <TableHead>外呼任务名称</TableHead>
                <TableHead>外呼类型</TableHead>
                <TableHead>任务状态</TableHead>
                <TableHead>导入数量</TableHead>
                <TableHead>有效数量</TableHead>
                <TableHead>已呼数量</TableHead>
                <TableHead>任务创建时间</TableHead>
                <TableHead>创建人</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    <div className="flex justify-center items-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                      加载中...
                    </div>
                  </TableCell>
                </TableRow>
              ) : tasks.length > 0 ? (
                tasks.map((task) => (
                  <TableRow key={task.taskId}>
                    <TableCell className="font-medium">{task.name}</TableCell>
                    <TableCell>
                      <span className={task.status === "finished" ? "status-active" : "status-pending"}>
                        {task.status === "1" ? "视频通知" : task.status === "2" ? "视频互动" : "语音互动"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={getStatusClass(task.status)}>{getStatusName(task.status)}</span>
                    </TableCell>
                    <TableCell>{task.importNumber || 0}</TableCell>
                    <TableCell>{task.deliverNumber || 0}</TableCell>
                    <TableCell>{task.calledCount || 0}</TableCell>
                    <TableCell>{task.createdAt}</TableCell>
                    <TableCell>{task.creator}</TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8 text-blue-600"
                          onClick={() => {
                            // 查看任务详情
                            window.open(`/video-tasks/${task.taskId}`, "_blank")
                          }}
                        >
                          <Info className="h-4 w-4" />
                          <span className="sr-only">详情</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8 text-red-600"
                          disabled={isLoading || task.status === "finished" || task.status === "stopped"}
                          onClick={() => {
                            setTaskToDelete(task.taskId)
                            setShowDeleteDialog(true)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">删除</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="h-24 text-center">
                    没有找到任务
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页控件 */}
        {totalTasks > 0 && (
          <div className="mt-4 flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => !isLoading && setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => !isLoading && setCurrentPage(page)}
                      isActive={currentPage === page}
                      className={isLoading ? "cursor-pointer opacity-50 pointer-events-none" : "cursor-pointer"}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => !isLoading && setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>

      {/* 删除任务确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认操作</DialogTitle>
            <DialogDescription>您确定要停止此任务吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTask} disabled={isLoading}>
              {isLoading ? "处理中..." : "确认"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 新建5G视频外呼任务对话框 */}
      <Dialog
        open={showImportDialog}
        onOpenChange={(open) => {
          if (!isLoading) {
            setShowImportDialog(open)
            if (!open) resetNewTaskForm()
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>新建5G视频外呼任务</DialogTitle>
          </DialogHeader>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogClose>

          <ScrollArea className="h-[60vh] pr-4">
            <div className="grid gap-3 py-2">
              <div className="space-y-1">
                <Label htmlFor="new-task-name">外呼任务名称</Label>
                <Input
                  id="new-task-name"
                  placeholder="请输入任务名称"
                  value={newTaskName}
                  onChange={(e) => setNewTaskName(e.target.value)}
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-content">视频外呼内容</Label>
                <Select value={selectedBot} onValueChange={setSelectedBot}>
                  <SelectTrigger id="new-task-content">
                    <SelectValue placeholder="请选择外呼内容" />
                  </SelectTrigger>
                  <SelectContent>
                    {botList.map((bot) => (
                      <SelectItem key={bot._id} value={bot._id}>
                        {bot.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-type">外呼类型</Label>
                <Select value={newTaskType} onValueChange={setNewTaskType}>
                  <SelectTrigger id="new-task-type">
                    <SelectValue placeholder="请选择外呼类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">视频通知</SelectItem>
                    <SelectItem value="2">视频互动</SelectItem>
                    <SelectItem value="3">语音互动</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-start-time">外呼开始时间</Label>
                <div className="flex space-x-2">
                  <Popover open={showDateTimePicker} onOpenChange={setShowDateTimePicker}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !newTaskStartTime && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {newTaskStartTime ? (
                          format(newTaskStartTime, "yyyy-MM-dd") +
                          ` ${selectedHour}:${selectedMinute}:${selectedSecond}`
                        ) : (
                          <span>选择日期和时间</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <div className="p-2">
                        <Calendar
                          mode="single"
                          selected={newTaskStartTime}
                          onSelect={(date) => setNewTaskStartTime(date)}
                          initialFocus
                        />
                        <div className="flex items-center justify-between px-3 py-2 border-t">
                          <Label>时间:</Label>
                          <div className="flex space-x-1">
                            <select
                              value={selectedHour}
                              onChange={(e) => setSelectedHour(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(24)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedMinute}
                              onChange={(e) => setSelectedMinute(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedSecond}
                              onChange={(e) => setSelectedSecond(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                          </div>
                        </div>
                        <div className="flex justify-end p-2">
                          <Button
                            size="sm"
                            onClick={() => setShowDateTimePicker(false)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            确定
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-resource">视频外呼资源</Label>
                <Select value={newTaskResource} onValueChange={setNewTaskResource}>
                  <SelectTrigger id="new-task-resource">
                    <SelectValue placeholder="请选择外呼资源" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0755本地中继">0755本地中继</SelectItem>
                    <SelectItem value="全国固话">全国固话</SelectItem>
                    <SelectItem value="全国虚商">全国虚商</SelectItem>
                    <SelectItem value="全国实商">全国实商</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="blacklist-groups">黑名单分组</Label>
                <Select
                  value={selectedBlacklistGroups.join(",")}
                  onValueChange={(value) => setSelectedBlacklistGroups(value ? value.split(",") : [])}
                >
                  <SelectTrigger id="blacklist-groups">
                    <SelectValue placeholder="请选择黑名单分组" />
                  </SelectTrigger>
                  <SelectContent>
                    {blacklistGroups.map((group) => (
                      <SelectItem key={group.groupId} value={group.groupId}>
                        {group.groupName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-phone">呼叫号码</Label>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="manual">手动输入</TabsTrigger>
                    <TabsTrigger value="import">导入表格</TabsTrigger>
                  </TabsList>
                  <TabsContent value="manual" className="mt-2">
                    <Input
                      id="new-task-phone"
                      placeholder="请输入呼叫号码"
                      value={newTaskPhoneNumber}
                      onChange={(e) => setNewTaskPhoneNumber(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">多个号码请用中文逗号（，）分隔</p>
                  </TabsContent>
                  <TabsContent value="import" className="mt-2">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Input
                          id="phone-file"
                          type="file"
                          accept=".csv,.xlsx,.xls"
                          onChange={handleFileChange}
                          className="flex-1"
                        />
                      </div>
                      {importFile && <p className="text-sm text-muted-foreground">已选择文件: {importFile.name}</p>}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-sms-type">短信方式</Label>
                <Select value={newTaskSmsType} onValueChange={setNewTaskSmsType}>
                  <SelectTrigger id="new-task-sms-type">
                    <SelectValue placeholder="请选择短信方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="文本短信">文本短信</SelectItem>
                    <SelectItem value="视频短信">视频短信</SelectItem>
                    <SelectItem value="无">无</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-sms-template">短信模板</Label>
                <Select
                  value={newTaskSmsTemplate}
                  onValueChange={setNewTaskSmsTemplate}
                  disabled={newTaskSmsType === "无"}
                >
                  <SelectTrigger id="new-task-sms-template">
                    <SelectValue placeholder="请选择短信模板" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="600184215">模板1：我们门店的地址是……</SelectItem>
                    <SelectItem value="600184216">模板2：我们门店的地址是……</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setShowImportDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button onClick={handleCreateTask} className="bg-blue-600 hover:bg-blue-700" disabled={isLoading}>
              {isLoading ? "创建中..." : "确定"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

