"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Download, Search } from "lucide-react"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"

// 示例数据 - 账户信息
const accountData = {
  id: "1",
  name: "张三企业",
  username: "z<PERSON><PERSON>",
  initialPassword: "******",
  email: "z<PERSON><PERSON>@example.com",
  accountType: "一级客户",
  phone: "***********",
  balance: 10000,
  creditLimit: 50000,
  rate: 0.05,
  billingUnit: "分钟",
  status: "启用",
  createdTime: "2023-01-01 09:00:00",
}

// 示例数据 - 充值记录
const paymentRecords = [
  {
    id: "1",
    date: "2023-05-01",
    type: "充值",
    amount: 5000,
    operator: "管理员",
    remark: "月度充值",
  },
  {
    id: "2",
    date: "2023-05-15",
    type: "扣费",
    amount: 1200,
    operator: "系统",
    remark: "任务费用",
  },
  {
    id: "3",
    date: "2023-06-01",
    type: "充值",
    amount: 10000,
    operator: "管理员",
    remark: "季度充值",
  },
  {
    id: "4",
    date: "2023-06-15",
    type: "扣费",
    amount: 3500,
    operator: "系统",
    remark: "服务费用",
  },
  {
    id: "5",
    date: "2023-07-01",
    type: "充值",
    amount: 8000,
    operator: "管理员",
    remark: "月度充值",
  },
  {
    id: "6",
    date: "2023-07-15",
    type: "扣费",
    amount: 2500,
    operator: "系统",
    remark: "外呼服务费",
  },
  {
    id: "7",
    date: "2023-08-01",
    type: "充值",
    amount: 6000,
    operator: "管理员",
    remark: "月度充值",
  },
  {
    id: "8",
    date: "2023-08-15",
    type: "扣费",
    amount: 1800,
    operator: "系统",
    remark: "任务费用",
  },
]

export default function AdminAccountPage({ params }: { params: { id: string } }) {
  // 对话框状态
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showRecordsDialog, setShowRecordsDialog] = useState(false)

  // 表单状态
  const [paymentType, setPaymentType] = useState("充值")
  const [paymentAmount, setPaymentAmount] = useState("")
  const [paymentRemark, setPaymentRemark] = useState("")

  // 编辑账户表单状态
  const [editForm, setEditForm] = useState({ ...accountData })

  // 搜索状态
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // 过滤充值记录
  const filteredRecords = paymentRecords.filter(
    (record) =>
      record.date.includes(searchTerm) ||
      record.type.includes(searchTerm) ||
      record.operator.includes(searchTerm) ||
      record.remark.includes(searchTerm),
  )

  // 计算总页数
  const totalPages = Math.ceil(filteredRecords.length / itemsPerPage)

  // 获取当前页的数据
  const currentRecords = filteredRecords.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // 处理充值/扣费提交
  const handlePaymentSubmit = () => {
    // 这里应该有实际的API调用
    console.log("提交充值/扣费", {
      type: paymentType,
      amount: paymentAmount,
      remark: paymentRemark,
    })

    // 重置表单并关闭对话框
    setPaymentType("充值")
    setPaymentAmount("")
    setPaymentRemark("")
    setShowPaymentDialog(false)
  }

  // 处理编辑账户提交
  const handleEditSubmit = () => {
    // 这里应该有实际的API调用
    console.log("提交账户编辑", editForm)

    // 关闭对话框
    setShowEditDialog(false)
  }

  // 导出充值记录为CSV
  const exportToCSV = () => {
    const headers = ["日期", "缴费类型", "金额", "操作人员", "备注"]
    const csvData = [
      headers.join(","),
      ...filteredRecords.map((record) =>
        [record.date, record.type, record.amount, record.operator, record.remark].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `充值记录_${accountData.name}_${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading="账户详情（管理员）" text={`管理 ${accountData.name} 的账户信息`} />
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>查看和管理账户的基本信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">账户名称</p>
                <p className="text-sm text-muted-foreground">{accountData.name}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">登录名称</p>
                <p className="text-sm text-muted-foreground">{accountData.username}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">初始密码</p>
                <p className="text-sm text-muted-foreground">{accountData.initialPassword}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">邮箱</p>
                <p className="text-sm text-muted-foreground">{accountData.email}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">账户类型</p>
                <p className="text-sm text-muted-foreground">{accountData.accountType}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">手机号</p>
                <p className="text-sm text-muted-foreground">{accountData.phone}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">余额</p>
                <p className="text-sm text-muted-foreground">¥{accountData.balance.toLocaleString()}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">授信额度</p>
                <p className="text-sm text-muted-foreground">¥{accountData.creditLimit.toLocaleString()}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">费率</p>
                <p className="text-sm text-muted-foreground">{(accountData.rate * 100).toFixed(2)}%</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">计费单位</p>
                <p className="text-sm text-muted-foreground">{accountData.billingUnit}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">状态</p>
                <p className="text-sm text-muted-foreground">{accountData.status}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">开户时间</p>
                <p className="text-sm text-muted-foreground">{accountData.createdTime}</p>
              </div>
            </div>
            <div className="mt-6 flex gap-2">
              <Button onClick={() => setShowPaymentDialog(true)} className="bg-green-600 hover:bg-green-700">
                充值
              </Button>
              <Button onClick={() => setShowEditDialog(true)} className="bg-blue-600 hover:bg-blue-700">
                编辑账号
              </Button>
              <Button onClick={() => setShowRecordsDialog(true)} className="bg-purple-600 hover:bg-purple-700">
                充值记录
              </Button>
            </div>
          </CardContent>
        </Card>
      </DashboardShell>

      {/* 充值/扣费对话框 */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>客户充值</DialogTitle>
            <DialogDescription>为客户账户充值或扣费</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="payment-type" className="text-right">
                缴费类型
              </Label>
              <Select value={paymentType} onValueChange={setPaymentType}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="充值">充值</SelectItem>
                  <SelectItem value="扣费">扣费</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                金额
              </Label>
              <Input
                id="amount"
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="remark" className="text-right">
                备注
              </Label>
              <Input
                id="remark"
                value={paymentRemark}
                onChange={(e) => setPaymentRemark(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>
              取消
            </Button>
            <Button onClick={handlePaymentSubmit} className="bg-green-600 hover:bg-green-700">
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑账号对话框 */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑账号</DialogTitle>
            <DialogDescription>修改账户的基本信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                账户名称
              </Label>
              <Input
                id="edit-name"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-username" className="text-right">
                登录名称
              </Label>
              <Input
                id="edit-username"
                value={editForm.username}
                onChange={(e) => setEditForm({ ...editForm, username: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-password" className="text-right">
                初始密码
              </Label>
              <Input
                id="edit-password"
                type="password"
                value={editForm.initialPassword}
                onChange={(e) => setEditForm({ ...editForm, initialPassword: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-email" className="text-right">
                邮箱
              </Label>
              <Input
                id="edit-email"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-phone" className="text-right">
                手机号
              </Label>
              <Input
                id="edit-phone"
                value={editForm.phone}
                onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-balance" className="text-right">
                余额
              </Label>
              <Input
                id="edit-balance"
                type="number"
                value={editForm.balance}
                onChange={(e) => setEditForm({ ...editForm, balance: Number(e.target.value) })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-credit" className="text-right">
                授信额度
              </Label>
              <Input
                id="edit-credit"
                type="number"
                value={editForm.creditLimit}
                onChange={(e) => setEditForm({ ...editForm, creditLimit: Number(e.target.value) })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-rate" className="text-right">
                费率
              </Label>
              <Input
                id="edit-rate"
                type="number"
                step="0.01"
                value={editForm.rate}
                onChange={(e) => setEditForm({ ...editForm, rate: Number(e.target.value) })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-billing-unit" className="text-right">
                计费单位
              </Label>
              <Select
                value={editForm.billingUnit}
                onValueChange={(value) => setEditForm({ ...editForm, billingUnit: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择计费单位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="分钟">分钟</SelectItem>
                  <SelectItem value="6秒">6秒</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-account-type" className="text-right">
                账户类型
              </Label>
              <Select
                value={editForm.accountType}
                onValueChange={(value) => setEditForm({ ...editForm, accountType: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择账户类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="一级客户">一级客户</SelectItem>
                  <SelectItem value="运营人员">运营人员</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              取消
            </Button>
            <Button onClick={handleEditSubmit} className="bg-blue-600 hover:bg-blue-700">
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 充值记录对话框 */}
      <Dialog open={showRecordsDialog} onOpenChange={setShowRecordsDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>充值记录</DialogTitle>
            <DialogDescription>查看账户的充值和扣费记录</DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-between mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索记录..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value)
                  setCurrentPage(1) // 重置到第一页
                }}
              />
            </div>
            <Button variant="outline" onClick={exportToCSV}>
              <Download className="mr-2 h-4 w-4" />
              导出
            </Button>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>日期</TableHead>
                  <TableHead>缴费类型</TableHead>
                  <TableHead className="text-right">金额</TableHead>
                  <TableHead>操作人员</TableHead>
                  <TableHead>备注</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>
                      <span
                        className={`px-2 py-1 rounded-full text-xs ${
                          record.type === "充值" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                        }`}
                      >
                        {record.type}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className={record.type === "充值" ? "text-green-600" : "text-red-600"}>
                        {record.type === "充值" ? "+" : "-"}¥{record.amount.toLocaleString()}
                      </span>
                    </TableCell>
                    <TableCell>{record.operator}</TableCell>
                    <TableCell>{record.remark}</TableCell>
                  </TableRow>
                ))}
                {currentRecords.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      没有找到记录
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页控件 */}
          {filteredRecords.length > 0 && (
            <div className="mt-4 flex justify-end">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <PaginationItem key={page}>
                      <PaginationLink
                        onClick={() => setCurrentPage(page)}
                        isActive={currentPage === page}
                        className="cursor-pointer"
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

