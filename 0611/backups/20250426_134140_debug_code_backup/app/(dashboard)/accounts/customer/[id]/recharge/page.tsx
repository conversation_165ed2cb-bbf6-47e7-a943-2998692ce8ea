"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Wallet } from "lucide-react"



export default function CustomerRechargePage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const customerId = params.id

  // 客户数据状态
  const [customer, setCustomer] = useState<{ id: string; name: string; balance: number } | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")

  // 充值表单状态
  const [amount, setAmount] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("bank")
  const [remark, setRemark] = useState("")
  const [formError, setFormError] = useState("")

  // 获取客户数据
  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/customers/${customerId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
          },
          credentials: 'include'
        })

        if (!response.ok) {
          throw new Error('获取客户数据失败')
        }

        const data = await response.json()
        if (data.success && data.data) {
          setCustomer(data.data)
        } else {
          setError(data.message || '客户数据不存在')
        }
      } catch (error) {
        console.error('获取客户数据失败:', error)
        setError(error instanceof Error ? error.message : '获取客户数据失败')
      } finally {
        setLoading(false)
      }
    }

    fetchCustomer()
  }, [customerId])

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <DashboardShell>
        <DashboardHeader heading="客户充值">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground mb-4">正在加载客户数据...</p>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  // 如果找不到客户，显示错误信息
  if (!customer) {
    return (
      <DashboardShell>
        <DashboardHeader heading="客户不存在">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
        </DashboardHeader>
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <p className="text-muted-foreground mb-4">{error || `未找到ID为 ${customerId} 的客户信息`}</p>
            <Button onClick={() => router.push("/accounts/customer")}>返回客户列表</Button>
          </CardContent>
        </Card>
      </DashboardShell>
    )
  }

  // 提交充值
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 验证金额
    if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
      setFormError("请输入有效的充值金额")
      return
    }

    // 清除错误
    setFormError("")

    try {
      console.log('开始充值操作:', {
        customerId,
        amount: Number(amount),
        paymentMethod,
        remarks: remark
      })

      // 调用API处理充值
      const response = await fetch(`/api/customers/${customerId}/recharge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include',
        body: JSON.stringify({
          amount: Number(amount),
          paymentMethod,
          remarks: remark
        })
      })

      const data = await response.json()
      console.log('充值响应数据:', data)

      if (!response.ok) {
        throw new Error(data.message || '充值操作失败')
      }

      if (data.success) {
        // 更新客户余额显示
        if (customer && data.data && data.data.balance !== undefined) {
          setCustomer({
            ...customer,
            balance: data.data.balance
          })
        }

        // 显示成功消息
        alert('充值成功！新余额: ¥' + data.data.balance.toFixed(2))

        // 重置表单
        setAmount('')
        setRemark('')

        // 跳转到客户详情页
        router.push(`/accounts/customer/${customerId}`)
      } else {
        throw new Error(data.message || '充值操作失败')
      }
    } catch (error) {
      console.error('充值操作失败:', error)
      setFormError(error instanceof Error ? error.message : '充值操作失败，请重试')
    }
  }

  return (
    <DashboardShell>
      <DashboardHeader heading={`为 ${customer.name} 充值`}>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
      </DashboardHeader>

      <Card>
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <CardTitle>账户充值</CardTitle>
            <CardDescription>为客户账户充值，增加账户余额</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <h4 className="text-sm font-medium text-green-800 mb-1 flex items-center">
                <Wallet className="h-4 w-4 mr-2" />
                当前账户余额
              </h4>
              <p className="text-2xl font-bold text-green-700">¥{customer.balance.toLocaleString()}</p>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="amount">
                  充值金额 <span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <span className="absolute left-3 top-2.5">¥</span>
                  <Input
                    id="amount"
                    type="number"
                    placeholder="请输入充值金额"
                    className="pl-8"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    required
                  />
                </div>
                {error && <p className="text-sm text-red-500">{error}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentMethod">
                  支付方式 <span className="text-red-500">*</span>
                </Label>
                <Select value={paymentMethod} onValueChange={setPaymentMethod} required>
                  <SelectTrigger id="paymentMethod">
                    <SelectValue placeholder="选择支付方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bank">银行转账</SelectItem>
                    <SelectItem value="alipay">支付宝</SelectItem>
                    <SelectItem value="wechat">微信支付</SelectItem>
                    <SelectItem value="cash">现金</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="remark">备注</Label>
                <Textarea
                  id="remark"
                  placeholder="请输入充值备注信息"
                  value={remark}
                  onChange={(e) => setRemark(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => router.back()}>
              取消
            </Button>
            <Button type="submit">确认充值</Button>
          </CardFooter>
        </form>
      </Card>
    </DashboardShell>
  )
}

