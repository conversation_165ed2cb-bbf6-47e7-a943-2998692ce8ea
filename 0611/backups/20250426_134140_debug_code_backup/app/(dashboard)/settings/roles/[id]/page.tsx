"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { ArrowLeft, Save, RefreshCw } from "lucide-react"
import { SyncPermissionsButton } from "@/components/sync-permissions-button"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MenuSelector } from "@/app/components/role/menu-selector"
import { OperationSelector } from "@/components/role/operation-selector"
import { RoleDuplicateDialog } from "@/app/components/role/role-duplicate-dialog"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import type { Role, Permission } from "@/types/role"
import { navConfig } from "@/config/nav"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, "名称至少2个字符").max(50, "名称最多50个字符"),
  code: z.string().min(2, "编码至少2个字符").max(50, "编码最多50个字符"),
  type: z.string(),
  description: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  menuItems: z.array(z.string()).default([]),
  operationIds: z.array(z.string()).default([]),
  autoGenerateCode: z.boolean().default(false),
})

// 获取所有菜单项
const getAllMenuItems = (items: readonly any[]): string[] => {
  return items.reduce((acc: string[], item) => {
    acc.push(item.href)
    if (item.children) {
      acc.push(...getAllMenuItems(item.children))
    }
    return acc
  }, [])
}

const allMenuItems = getAllMenuItems(navConfig.main)

export default function RoleDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [role, setRole] = useState<Role | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      code: "",
      type: "custom",
      description: "",
      permissions: [],
      menuItems: [],
      operationIds: [],
      autoGenerateCode: false,
    }
  })

  // 生成角色编码
  const generateRoleCode = () => {
    const name = form.getValues("name")
    const type = form.getValues("type")

    if (!name) {
      toast.error("请先输入角色名称")
      return
    }

    // 将名称转换为拼音首字母并大写
    const pinyin = name
      .split("")
      .map(char => {
        // 简单处理，实际项目中可以使用拼音库
        // 这里只是演示，直接使用英文字母和数字
        return char.replace(/[^a-zA-Z0-9]/g, "")
      })
      .join("")
      .toUpperCase()

    // 如果没有有效字符，使用类型前缀 + 随机数字
    const prefix = type === "system" ? "SYS_" : "CUST_"
    const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, "0")

    const code = pinyin ? `${prefix}${pinyin}` : `${prefix}ROLE_${randomSuffix}`

    // 更新表单中的编码字段
    form.setValue("code", code)
  }

  // 加载角色详情
  const loadRole = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/roles/${params.id}`)
      const data = await response.json()

      if (data.success) {
        setRole(data.data)

        // 设置表单默认值
        form.reset({
          name: data.data.name,
          code: data.data.code,
          type: data.data.type,
          description: data.data.description || "",
          permissions: data.data.permissions || [],
          menuItems: data.data.menuItems || [],
          operationIds: data.data.operationIds || [],
          autoGenerateCode: false,
        })
      } else {
        toast.error("加载角色详情失败")
        router.push("/settings/roles")
      }
    } catch (error) {
      console.error("加载角色详情失败:", error)
      toast.error("加载角色详情失败")
      router.push("/settings/roles")
    } finally {
      setLoading(false)
    }
  }

  // 更新角色
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setSaving(true)

      // 系统角色不允许修改代码
      if (role?.type === 'system' && values.code !== role.code) {
        toast.error("不允许修改系统角色的代码")
        form.setValue("code", role.code)
        setSaving(false)
        return
      }

      const response = await fetch(`/api/roles/${params.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          menuIds: values.menuItems,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("更新角色成功")
        setRole(data.data)
      } else {
        toast.error(data.message || "更新角色失败")
      }
    } catch (error) {
      console.error("更新角色失败:", error)
      toast.error("更新角色失败")
    } finally {
      setSaving(false)
    }
  }

  // 同步角色权限
  const syncRolePermissions = async () => {
    try {
      setSaving(true)
      const response = await fetch(`/api/roles/sync-permissions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roleCode: role?.code,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("同步角色权限成功")
      } else {
        toast.error(data.message || "同步角色权限失败")
      }
    } catch (error) {
      console.error("同步角色权限失败:", error)
      toast.error("同步角色权限失败")
    } finally {
      setSaving(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadRole()
  }, [params.id])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push("/settings/roles")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <h2 className="text-2xl font-bold">加载中...</h2>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.push("/settings/roles")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回
          </Button>
          <h2 className="text-2xl font-bold">编辑角色: {role?.name}</h2>
        </div>
        <SyncPermissionsButton
          variant="outline"
          roleCode={role?.code}
          disabled={saving}
          onSuccess={() => toast.success("权限同步成功")}
        />
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色名称</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入角色名称" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色编码</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="请输入角色编码"
                        {...field}
                        disabled={role.isPreset}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>角色类型</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={role.isPreset}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择角色类型" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="system">系统角色</SelectItem>
                        <SelectItem value="admin">管理角色</SelectItem>
                        <SelectItem value="business">业务角色</SelectItem>
                        <SelectItem value="custom">自定义角色</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入角色描述" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Tabs defaultValue="basic" className="mt-6">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="basic">基本信息</TabsTrigger>
                  <TabsTrigger value="menus">菜单权限</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="py-4">
                  <div className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色名称</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入角色名称" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色编码</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="请输入角色编码"
                              {...field}
                              disabled={role.isPreset}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>角色类型</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            disabled={role.isPreset}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="请选择角色类型" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="system">系统角色</SelectItem>
                              <SelectItem value="admin">管理角色</SelectItem>
                              <SelectItem value="business">业务角色</SelectItem>
                              <SelectItem value="custom">自定义角色</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>描述</FormLabel>
                          <FormControl>
                            <Input placeholder="请输入角色描述" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>



                <TabsContent value="menus" className="py-4">
                  <FormField
                    control={form.control}
                    name="menuItems"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>菜单权限</FormLabel>
                        <FormDescription>
                          选择此角色可以访问的菜单项
                        </FormDescription>
                        <FormControl>
                          <MenuSelector
                            selectedMenus={field.value}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>


              </Tabs>
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={saving}
              >
                {saving ? "保存中..." : "保存"}
              </Button>
            </div>
          </form>
        </Form>
    </div>
  )
}