"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Plus, RefreshCw, Copy, Database, Wrench, Shield } from "lucide-react"
import { SyncPermissionsButton } from "@/components/sync-permissions-button"
import { RoleDuplicateDialog } from "@/app/components/role/role-duplicate-dialog"
import { MenuSelector } from "@/app/components/role/menu-selector"
// 移除不需要的操作选择器
// import { OperationSelector } from "@/app/components/role/operation-selector"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
// 移除未使用的DataTable导入
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormI<PERSON>,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "sonner"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import type { Role } from "@/types/role"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, "名称至少2个字符").max(50, "名称最多50个字符"),
  code: z.string().min(2, "编码至少2个字符").max(50, "编码最多50个字符"),
  type: z.string(),
  description: z.string().optional(),
  permissions: z.array(z.string()).default([]),
  menuItems: z.array(z.string()).default([]),
  // 移除不需要的操作权限字段
  // operationIds: z.array(z.string()).default([]),
  autoGenerateCode: z.boolean().default(false),
})

export default function RolesPage() {
  const router = useRouter()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      code: "",
      type: "custom",
      description: "",
      permissions: [],
      menuItems: [],
      // 移除不需要的操作权限字段
      // operationIds: [],
      autoGenerateCode: true,
    }
  })

  // 生成角色编码
  const generateRoleCode = () => {
    const name = form.getValues("name")
    const type = form.getValues("type")

    if (!name) {
      toast.error("请先输入角色名称")
      return
    }

    // 将名称转换为拼音首字母并大写
    const pinyin = name
      .split("")
      .map(char => {
        // 简单处理，实际项目中可以使用拼音库
        // 这里只是演示，直接使用英文字母和数字
        return char.replace(/[^a-zA-Z0-9]/g, "")
      })
      .join("")
      .toUpperCase()

    // 如果没有有效字符，使用类型前缀 + 随机数字
    const prefix = type === "system" ? "SYS_" : "CUST_"
    const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, "0")

    const code = pinyin ? `${prefix}${pinyin}` : `${prefix}ROLE_${randomSuffix}`

    // 更新表单中的编码字段
    form.setValue("code", code)
  }

  // 监听名称和类型变化，自动生成编码
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if ((name === "name" || name === "type") && form.getValues("autoGenerateCode")) {
        generateRoleCode()
      }
    })

    return () => subscription.unsubscribe()
  }, [form])

  // 加载角色列表
  const loadRoles = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/roles")
      const data = await response.json()

      if (data.success) {
        setRoles(data.data)
      } else {
        toast.error("加载角色列表失败")
      }
    } catch (error) {
      console.error("加载角色列表失败:", error)
      toast.error("加载角色列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 初始化菜单和操作数据
  const initMenusAndOperations = async () => {
    try {
      const confirmed = confirm("确定要初始化菜单和操作数据吗？这将创建默认的菜单和操作权限数据。")
      if (!confirmed) return

      setLoading(true)
      const response = await fetch("/api/admin/init-menus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message || "初始化菜单和操作数据成功")
        // 重新加载页面以刷新数据
        window.location.reload()
      } else {
        toast.error(data.error || "初始化菜单和操作数据失败")
      }
    } catch (error) {
      console.error("初始化菜单和操作数据失败:", error)
      toast.error("初始化菜单和操作数据失败")
    } finally {
      setLoading(false)
    }
  }

  // 修复角色和菜单的关联关系
  const fixRoleMenus = async () => {
    try {
      const confirmed = confirm("确定要修复角色和菜单的关联关系吗？这将修正数据库中的关联表。")
      if (!confirmed) return

      setLoading(true)
      const response = await fetch("/api/admin/fix-role-menus", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        }
      })

      const data = await response.json()

      if (data.success) {
        toast.success(data.message || "修复角色和菜单的关联关系成功")
        // 重新加载页面以刷新数据
        window.location.reload()
      } else {
        toast.error(data.error || "修复角色和菜单的关联关系失败")
      }
    } catch (error) {
      console.error("修复角色和菜单的关联关系失败:", error)
      toast.error("修复角色和菜单的关联关系失败")
    } finally {
      setLoading(false)
    }
  }

  // 创建角色
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      console.log('提交的表单数据:', values)

      // 如果启用了自动生成编码但编码为空，则生成编码
      if (values.autoGenerateCode && !values.code) {
        generateRoleCode()
        values.code = form.getValues("code")
      }

      // 确保表单数据完整
      if (!values.name || !values.code || !values.type) {
        console.error('表单数据不完整:', values)
        toast.error("请填写完整的角色信息")
        return
      }

      // 确保菜单和操作权限的关联性
      // 如果选择了菜单，但没有选择该菜单下的任何操作，则自动添加该菜单下的所有操作
      if (values.menuItems.length > 0) {
        // 获取所有操作
        const operationsResponse = await fetch("/api/operations")
        const operationsData = await operationsResponse.json()

        if (operationsData.success) {
          const operations = operationsData.data
          const menuOperations = operations.filter((op: any) =>
            op.menuId && values.menuItems.includes(op.menuId)
          )

          // 合并已选择的操作和菜单关联的操作
          const selectedOperationIds = new Set(values.operationIds)
          menuOperations.forEach((op: any) => {
            selectedOperationIds.add(op.id)
          })

          values.operationIds = Array.from(selectedOperationIds)
        }
      }

      // 打印详细的请求信息
      console.log('发送请求到 /api/roles')
      console.log('请求方法: POST')
      console.log('请求头: ', { "Content-Type": "application/json" })
      console.log('请求体: ', JSON.stringify(values, null, 2))

      // 使用更详细的错误处理
      const response = await fetch("/api/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          menuIds: values.menuItems,  // 将menuItems转换为API需要的menuIds格式
        }),
      })

      console.log('响应状态码: ', response.status)
      console.log('响应状态文本: ', response.statusText)
      console.log('响应头: ', Object.fromEntries([...response.headers.entries()]))

      console.log('API响应状态:', response.status)
      const data = await response.json()
      console.log('API响应数据:', data)

      if (data.success) {
        toast.success("创建角色成功")
        setShowDialog(false)
        form.reset()
        loadRoles()
      } else {
        console.error('创建角色失败:', data)
        // 显示更详细的错误信息
        const errorMsg = data.error || data.message || "创建角色失败"
        console.log('错误信息详情:', errorMsg)
        toast.error(errorMsg)

        // 尝试直接在页面上显示错误信息
        alert('创建角色失败: ' + JSON.stringify(data))
      }
    } catch (error) {
      console.error("创建角色失败:", error)

      // 显示更详细的错误信息
      const errorMsg = error instanceof Error ? error.message : String(error)
      console.log('异常错误详情:', errorMsg)
      toast.error("创建角色失败: " + errorMsg)

      // 尝试直接在页面上显示错误信息
      alert('创建角色异常: ' + errorMsg)
    }
  }

  // 删除角色
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/roles/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast.success("删除角色成功")
        loadRoles()
      } else {
        toast.error(data.message || "删除角色失败")
      }
    } catch (error) {
      console.error("删除角色失败:", error)
      toast.error("删除角色失败")
    }
  }

  // 初始加载
  useEffect(() => {
    loadRoles()
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">角色管理</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={async () => {
              try {
                const response = await fetch("/api/admin/refresh-permissions", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" }
                });
                const data = await response.json();
                if (data.success) {
                  toast.success("权限系统已刷新");
                  loadRoles();
                } else {
                  toast.error(data.message || "刷新权限系统失败");
                }
              } catch (error) {
                console.error("刷新权限系统失败:", error);
                toast.error("刷新权限系统失败");
              }
            }}
          >
            <Shield className="mr-2 h-4 w-4" />
            刷新权限系统
          </Button>
        </div>
        <Dialog open={showDialog} onOpenChange={setShowDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              form.reset({
                name: "",
                code: "",
                type: "system",
                description: "",
                permissions: [],
                menuItems: [],
                operationIds: [],
                autoGenerateCode: true
              })
            }}>
              <Plus className="mr-2 h-4 w-4" />
              新建角色
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>新建角色</DialogTitle>
              <DialogDescription>
                创建新的角色，设置角色名称、编码和类型。
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                {/* 添加调试信息 */}
                <div className="hidden">
                  当前表单状态: {JSON.stringify(form.getValues())}
                </div>
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入角色名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="autoGenerateCode"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={field.onChange}
                          className="h-4 w-4 mt-1"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>自动生成角色编码</FormLabel>
                        <FormDescription>
                          根据角色名称和类型自动生成编码
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色编码</FormLabel>
                      <div className="flex space-x-2">
                        <FormControl>
                          <Input
                            placeholder="请输入角色编码"
                            {...field}
                            disabled={form.getValues("autoGenerateCode")}
                          />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={generateRoleCode}
                          disabled={!form.getValues("name")}
                          title="重新生成编码"
                        >
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>角色类型</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="请选择角色类型" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="system">系统角色</SelectItem>
                          <SelectItem value="admin">管理角色</SelectItem>
                          <SelectItem value="business">业务角色</SelectItem>
                          <SelectItem value="custom">自定义角色</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>描述</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入角色描述" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Tabs defaultValue="basic" className="mt-6">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic">基本信息</TabsTrigger>
                    <TabsTrigger value="menus">菜单权限</TabsTrigger>
                  </TabsList>

                  <TabsContent value="basic" className="py-4">
                    <div className="text-sm text-muted-foreground">
                      基本信息已经设置完成，可以切换到菜单权限选项卡设置此角色可以访问的菜单。
                    </div>
                  </TabsContent>

                  <TabsContent value="menus" className="py-4">
                    <FormField
                      control={form.control}
                      name="menuItems"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>菜单权限</FormLabel>
                          <FormDescription>
                            选择此角色可以访问的菜单项
                          </FormDescription>
                          <FormControl>
                            <MenuSelector
                              selectedMenus={field.value}
                              onChange={field.onChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                </Tabs>

                <DialogFooter>
                  <Button
                    type="submit"
                  >
                    创建
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr>
              <th className="p-4 text-left font-medium">角色名称</th>
              <th className="p-4 text-left font-medium">角色编码</th>
              <th className="p-4 text-left font-medium">角色类型</th>
              <th className="p-4 text-left font-medium">描述</th>
              <th className="p-4 text-left font-medium">权限数量</th>
              <th className="p-4 text-left font-medium">菜单数量</th>
              <th className="p-4 text-left font-medium">创建时间</th>
              <th className="p-4 text-left font-medium">更新时间</th>
              <th className="p-4 text-left font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={9} className="p-4 text-center">
                  加载中...
                </td>
              </tr>
            ) : roles.length === 0 ? (
              <tr>
                <td colSpan={9} className="p-4 text-center">
                  暂无数据
                </td>
              </tr>
            ) : (
              roles.map((role) => (
                <tr key={role.id}>
                  <td className="p-4">{role.name}</td>
                  <td className="p-4">{role.code}</td>
                  <td className="p-4">
                    {role.type === "system" ? "系统角色" :
                     role.type === "admin" ? "管理角色" :
                     role.type === "business" ? "业务角色" : "自定义角色"}
                  </td>
                  <td className="p-4">{role.description}</td>
                  <td className="p-4">{role.permissions?.length || 0}</td>
                  <td className="p-4">{role.menuItems?.length || 0}</td>
                  <td className="p-4">{role.createdAt ? new Date(role.createdAt).toLocaleString('zh-CN') : '-'}</td>
                  <td className="p-4">{role.updatedAt ? new Date(role.updatedAt).toLocaleString('zh-CN') : '-'}</td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/settings/roles/${role.id}`)}
                      >
                        编辑
                      </Button>
                      <RoleDuplicateDialog role={role} />
                      {role.type !== "system" && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(role.id)}
                        >
                          删除
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}