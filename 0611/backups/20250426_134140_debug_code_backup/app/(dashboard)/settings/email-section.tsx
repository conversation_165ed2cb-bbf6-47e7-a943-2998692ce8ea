import React from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Edit, Mail } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Icons } from "@/components/icons"

interface EmailSectionProps {
  settings: any
  setSettings: (settings: any) => void
  setIsSaving: (value: boolean) => void
}

export function EmailSection({ settings, setSettings, setIsSaving }: EmailSectionProps) {
  // 处理安全修改授权码
  const handleSecureUpdate = async () => {
    try {
      // 验证邮箱设置
      if (!settings.emailSettings?.host || !settings.emailSettings?.port || !settings.emailSettings?.auth?.user) {
        toast({
          title: "验证失败",
          description: "请先完成邮件服务器设置",
          variant: "destructive",
        })
        return
      }

      // 发送验证码
      const sendResponse = await fetch("/api/settings/email/send-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const sendData = await sendResponse.json()

      if (!sendData.success) {
        throw new Error(sendData.message || "发送验证码失败")
      }

      toast({
        title: "发送成功",
        description: "验证码已发送到您的邮箱",
      })

      // 输入验证码
      const code = prompt("请输入验证码")
      if (!code) return

      // 输入新的授权码
      const authCode = prompt("请输入新的邮箱授权码")
      if (!authCode) return

      // 验证验证码并更新授权码
      setIsSaving(true)
      const verifyResponse = await fetch("/api/settings/email/verify-and-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code,
          authCode,
        }),
      })

      const verifyData = await verifyResponse.json()

      if (verifyData.success) {
        toast({
          title: "更新成功",
          description: "邮箱授权码已安全更新",
        })

        // 更新邮件设置
        setSettings(prev => ({
          ...prev,
          emailSettings: verifyData.data
        }))
      } else {
        throw new Error(verifyData.message || "验证失败")
      }
    } catch (error) {
      console.error("更新邮箱授权码失败:", error)
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "无法更新邮箱授权码",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 保存邮件设置
  const handleSaveEmailSettings = async () => {
    try {
      // 验证邮箱设置
      if (!settings.emailSettings?.host || !settings.emailSettings?.port || !settings.emailSettings?.auth?.user) {
        toast({
          title: "验证失败",
          description: "请先完成邮件服务器设置",
          variant: "destructive",
        })
        return
      }

      // 保存邮件设置
      setIsSaving(true)

      // 创建要发送的邮件设置对象
      const emailSettingsToSave = { ...settings.emailSettings };

      // 如果授权码是占位符，则不发送授权码
      if (emailSettingsToSave.auth?.pass === '******') {
        // 创建一个新的auth对象，包含空密码字段
        // 这样后端会知道保留原来的授权码
        emailSettingsToSave.auth = {
          user: emailSettingsToSave.auth.user,
          pass: '' // 空字符串表示保留原来的授权码
        };

        // 在控制台记录安全日志（不包含敏感信息）
        console.log('保留现有授权码，不发送到服务器');
      } else if (emailSettingsToSave.auth?.pass) {
        // 在控制台记录安全日志（不包含敏感信息）
        console.log('发送新的授权码到服务器');
      }

      const response = await fetch('/api/settings/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailSettingsToSave),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "保存成功",
          description: "邮件设置已更新",
        })

        // 更新邮件设置
        setSettings(prev => ({
          ...prev,
          emailSettings: data.data
        }))
      } else {
        throw new Error(data.message || "保存失败")
      }
    } catch (error) {
      console.error("保存邮件设置失败:", error)
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "无法保存邮件设置",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 发送测试邮件
  const handleSendTestEmail = async () => {
    try {
      // 验证邮箱设置
      if (!settings.emailSettings?.host || !settings.emailSettings?.port || !settings.emailSettings?.auth?.user) {
        toast({
          title: "验证失败",
          description: "请先完成邮件设置",
          variant: "destructive",
        })
        return
      }

      // 检查授权码是否存在
      if (!settings.emailSettings?.auth?.pass || settings.emailSettings.auth.pass === '') {
        toast({
          title: "验证失败",
          description: "请输入邮箱授权码",
          variant: "destructive",
        })
        return
      }

      // 弹出输入测试邮箱对话框
      const testEmail = prompt("请输入测试邮箱地址")
      if (!testEmail) return

      // 发送测试邮件
      setIsSaving(true)
      const response = await fetch('/api/settings/email', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testEmail
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "发送成功",
          description: `测试邮件已发送到 ${testEmail}，请查收`,
        })
      } else {
        throw new Error(data.message || "发送失败")
      }
    } catch (error) {
      console.error("发送测试邮件失败:", error)
      toast({
        title: "发送失败",
        description: error instanceof Error ? error.message : "无法发送测试邮件",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  // 处理邮件队列
  const handleProcessEmailQueue = async () => {
    try {
      setIsSaving(true)
      const response = await fetch('/api/settings/email-queue', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "处理成功",
          description: data.message || "邮件队列已手动处理",
        })
      } else {
        throw new Error(data.message || "处理失败")
      }
    } catch (error) {
      console.error("处理邮件队列失败:", error)
      toast({
        title: "处理失败",
        description: error instanceof Error ? error.message : "无法处理邮件队列",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div>
      <div className="grid gap-6">
        {/* 服务器地址 */}
        <div className="space-y-2">
          <Label htmlFor="emailHost">服务器地址</Label>
          <Input
            id="emailHost"
            value={settings.emailSettings?.host || ''}
            onChange={(e) => {
              setSettings(prev => ({
                ...prev,
                emailSettings: {
                  ...prev.emailSettings,
                  host: e.target.value
                }
              }))
            }}
            placeholder="例如：smtp.qq.com"
          />
          <p className="text-sm text-gray-500">
            SMTP服务器地址，如 smtp.qq.com、smtp.163.com 等
          </p>
        </div>

        {/* 服务器端口 */}
        <div className="space-y-2">
          <Label htmlFor="emailPort">服务器端口</Label>
          <Input
            id="emailPort"
            type="number"
            value={settings.emailSettings?.port || ''}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              if (!isNaN(value) && value > 0) {
                setSettings(prev => ({
                  ...prev,
                  emailSettings: {
                    ...prev.emailSettings,
                    port: value
                  }
                }))
              }
            }}
            placeholder="例如：465"
          />
          <p className="text-sm text-gray-500">
            常用端口: 465 (SSL), 587 (TLS), 25 (非加密)
          </p>
        </div>

        {/* 安全连接 */}
        <div className="space-y-2">
          <Label htmlFor="emailSecure">安全连接</Label>
          <div className="flex items-center space-x-2">
            <input
              id="emailSecure"
              type="checkbox"
              checked={settings.emailSettings?.secure || false}
              onChange={(e) => {
                setSettings(prev => ({
                  ...prev,
                  emailSettings: {
                    ...prev.emailSettings,
                    secure: e.target.checked
                  }
                }))
              }}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <Label htmlFor="emailSecure" className="text-sm font-normal">
              是否使用SSL/TLS加密连接
            </Label>
          </div>
        </div>

        {/* 自动重试 */}
        <div className="space-y-2">
          <Label htmlFor="emailAutoRetry">自动重试</Label>
          <div className="flex items-center space-x-2">
            <input
              id="emailAutoRetry"
              type="checkbox"
              checked={settings.emailSettings?.enableAutoRetry !== false}
              onChange={(e) => {
                setSettings(prev => ({
                  ...prev,
                  emailSettings: {
                    ...prev.emailSettings,
                    enableAutoRetry: e.target.checked
                  }
                }))
              }}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <Label htmlFor="emailAutoRetry" className="text-sm font-normal">
              发送失败后自动重试
            </Label>
          </div>
        </div>

        {/* 发件人邮箱 */}
        <div className="space-y-2">
          <Label htmlFor="emailUser">发件人邮箱</Label>
          <Input
            id="emailUser"
            value={settings.emailSettings?.auth?.user || ''}
            onChange={(e) => {
              setSettings(prev => ({
                ...prev,
                emailSettings: {
                  ...prev.emailSettings,
                  auth: {
                    ...prev.emailSettings?.auth,
                    user: e.target.value
                  }
                }
              }))
            }}
            placeholder="请输入发件人邮箱"
          />
          <p className="text-sm text-gray-500">
            用于发送邮件的邮箱地址
          </p>
        </div>

        {/* 邮箱授权码 */}
        <div className="space-y-2">
          <Label htmlFor="emailPass">邮箱授权码</Label>
          <div className="flex gap-2">
            <Input
              id="emailPass"
              type="password"
              value={settings.emailSettings?.auth?.pass || ''}
              onChange={(e) => {
                // 如果输入框中有占位符但用户没有修改，不更新值
                if (settings.emailSettings?.auth?.pass === '******' && e.target.value === '******') {
                  return;
                }

                setSettings(prev => ({
                  ...prev,
                  emailSettings: {
                    ...prev.emailSettings,
                    auth: {
                      ...prev.emailSettings?.auth,
                      pass: e.target.value
                    }
                  }
                }))
              }}
              placeholder="请输入邮箱授权码"
              className="flex-1"
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleSecureUpdate}
              className="whitespace-nowrap"
            >
              <Edit className="mr-2 h-4 w-4" />
              安全修改
            </Button>
          </div>
          <p className="text-sm text-gray-500">
            邮箱的授权码，非邮箱登录密码，需要在邮箱设置中获取
          </p>
          {settings.emailSettings?.auth?.pass === '******' && (
            <p className="text-sm text-blue-500 mt-1">
              您已经设置过授权码，如果需要修改，请点击"安全修改"按钮
            </p>
          )}
          <p className="text-sm text-amber-500 mt-1">
            <span className="font-semibold">安全提示：</span>授权码将安全存储，不会在前端显示实际内容。修改授权码需要邮箱验证。
          </p>
        </div>

        {/* 发件人名称 */}
        <div className="space-y-2">
          <Label htmlFor="emailFrom">发件人名称</Label>
          <Input
            id="emailFrom"
            value={settings.emailSettings?.from || `"系统通知" <${settings.emailSettings?.auth?.user || ''}>`}
            onChange={(e) => {
              setSettings(prev => ({
                ...prev,
                emailSettings: {
                  ...prev.emailSettings,
                  from: e.target.value
                }
              }))
            }}
            placeholder="例如：系统通知 &lt;<EMAIL>&gt;"
          />
          <p className="text-sm text-gray-500">
            收件人看到的发件人名称，建议格式：系统通知 &lt;<EMAIL>&gt;
          </p>
        </div>

        {/* 重试间隔 */}
        <div className="space-y-2">
          <Label htmlFor="retryInterval">重试间隔（分钟）</Label>
          <Input
            id="retryInterval"
            type="number"
            value={settings.emailSettings?.retryInterval || 30}
            onChange={(e) => {
              const value = parseInt(e.target.value)
              if (!isNaN(value) && value > 0) {
                setSettings(prev => ({
                  ...prev,
                  emailSettings: {
                    ...prev.emailSettings,
                    retryInterval: value
                  }
                }))
              }
            }}
            placeholder="30"
          />
          <p className="text-sm text-gray-500">
            发送失败后的重试间隔时间，建议 30 分钟
          </p>
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <div className="flex gap-2">
          <Button
            variant="default"
            onClick={handleSaveEmailSettings}
            className="flex items-center gap-2"
          >
            <Icons.save className="h-4 w-4" />
            保存设置
          </Button>

          <Button
            variant="outline"
            onClick={handleSendTestEmail}
            className="flex items-center gap-2"
          >
            <Mail className="h-4 w-4" />
            发送测试邮件
          </Button>

          <Button
            variant="outline"
            onClick={handleProcessEmailQueue}
            className="flex items-center gap-2"
          >
            <Icons.refresh className="h-4 w-4" />
            手动处理邮件队列
          </Button>
        </div>
      </div>
    </div>
  )
}
