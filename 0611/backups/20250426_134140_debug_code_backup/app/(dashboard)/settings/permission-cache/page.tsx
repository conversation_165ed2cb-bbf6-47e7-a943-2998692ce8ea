"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "sonner"
import { RefreshCw, Trash } from "lucide-react"

interface CacheStats {
  size: number
  items: {
    key: string
    expireAt: number | null
  }[]
}

export default function PermissionCachePage() {
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [loading, setLoading] = useState(true)

  // 加载缓存统计信息
  const loadStats = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/permission-cache")
      const data = await response.json()

      if (data.success) {
        setStats(data.data)
      } else {
        toast.error(data.message || "加载缓存统计信息失败")
      }
    } catch (error) {
      console.error("加载缓存统计信息失败:", error)
      toast.error("加载缓存统计信息失败")
    } finally {
      setLoading(false)
    }
  }

  // 清除缓存
  const clearCache = async () => {
    try {
      const response = await fetch("/api/admin/permission-cache", {
        method: "DELETE"
      })
      const data = await response.json()

      if (data.success) {
        toast.success("缓存已清除")
        loadStats()
      } else {
        toast.error(data.message || "清除缓存失败")
      }
    } catch (error) {
      console.error("清除缓存失败:", error)
      toast.error("清除缓存失败")
    }
  }

  // 初始加载
  useEffect(() => {
    loadStats()
  }, [])

  // 格式化过期时间
  const formatExpireTime = (timestamp: number | null) => {
    if (timestamp === null) {
      return "永不过期"
    }
    return new Date(timestamp).toLocaleString("zh-CN")
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>权限缓存管理</CardTitle>
              <CardDescription>
                管理系统权限缓存，提高权限验证性能
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={loadStats}>
                <RefreshCw className="mr-2 h-4 w-4" />
                刷新
              </Button>
              <Button variant="destructive" onClick={clearCache}>
                <Trash className="mr-2 h-4 w-4" />
                清除缓存
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="text-lg font-medium">缓存统计</div>
            <div className="text-sm text-muted-foreground">
              当前缓存项数量: {stats?.size || 0}
            </div>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>缓存键</TableHead>
                  <TableHead>过期时间</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={2} className="text-center py-4">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : stats?.items.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={2} className="text-center py-4">
                      缓存为空
                    </TableCell>
                  </TableRow>
                ) : (
                  stats?.items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono text-xs">{item.key}</TableCell>
                      <TableCell>{formatExpireTime(item.expireAt)}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter>
          <div className="text-sm text-muted-foreground">
            权限缓存可以提高系统性能，减少数据库查询次数。缓存项默认5分钟后过期。
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
