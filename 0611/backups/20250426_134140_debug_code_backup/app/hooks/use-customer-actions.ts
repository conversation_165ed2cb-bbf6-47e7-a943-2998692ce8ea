"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { CustomerType } from "@/app/types/customer"

export function useCustomerActions(
  customers: CustomerType[],
  setCustomers: (customers: CustomerType[]) => void,
  refreshCustomers?: () => void
) {
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerType | null>(null)
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [disableUserDialogOpen, setDisableUserDialogOpen] = useState(false)
  const [enableUserDialogOpen, setEnableUserDialogOpen] = useState(false)
  const [rechargeDialogOpen, setRechargeDialogOpen] = useState(false)
  const [creditDialogOpen, setCreditDialogOpen] = useState(false)

  // 处理审核通过
  const handleApprove = async (customerId: string) => {
    try {
      const response = await fetch(`/api/admin/users/${customerId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'active' }),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 更新本地状态
        setCustomers(
          customers.map((customer) => (customer.id === customerId ? { ...customer, status: "active" } : customer)),
        )

        toast({
          title: "操作成功",
          description: "用户已成功启用",
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })
      } else {
        toast({
          title: "操作失败",
          description: data.message || "启用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('启用用户错误:', error)
      toast({
        title: "操作失败",
        description: "启用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理审核拒绝
  const handleReject = (customerId: string) => {
    const customer = customers.find((c) => c.id === customerId)
    if (customer) {
      setSelectedCustomer(customer)
      setRejectDialogOpen(true)
    }
  }

  // 确认拒绝并提交原因
  const confirmReject = async (reason: string) => {
    if (!selectedCustomer) return

    try {
      const response = await fetch(`/api/admin/users/${selectedCustomer.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'inactive', reason }),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 更新本地状态
        setCustomers(
          customers.map((customer) =>
            customer.id === selectedCustomer.id ? { ...customer, status: "inactive" } : customer,
          ),
        )

        toast({
          title: "操作成功",
          description: "用户已成功禁用",
        })
      } else {
        toast({
          title: "操作失败",
          description: data.message || "禁用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('禁用用户错误:', error)
      toast({
        title: "操作失败",
        description: "禁用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理启用用户
  const handleEnableUser = (customerId: string) => {
    const customer = customers.find((c) => c.id === customerId)
    if (customer) {
      setSelectedCustomer(customer)
      setEnableUserDialogOpen(true)
    }
  }

  // 确认启用用户
  const confirmEnableUser = async () => {
    if (!selectedCustomer) return

    try {
      const response = await fetch(`/api/admin/users/${selectedCustomer.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'active' }),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 更新本地状态
        setCustomers(
          customers.map((customer) =>
            customer.id === selectedCustomer.id ? { ...customer, status: "active" } : customer,
          ),
        )

        toast({
          title: "操作成功",
          description: "用户已成功启用",
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })
      } else {
        toast({
          title: "操作失败",
          description: data.message || "启用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('启用用户错误:', error)
      toast({
        title: "操作失败",
        description: "启用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理禁用用户
  const handleDisableUser = (customerId: string) => {
    const customer = customers.find((c) => c.id === customerId)
    if (customer) {
      setSelectedCustomer(customer)
      setDisableUserDialogOpen(true)
    }
  }

  // 确认禁用用户
  const confirmDisableUser = async (reason: string) => {
    if (!selectedCustomer) return

    try {
      const response = await fetch(`/api/admin/users/${selectedCustomer.id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'inactive', reason }),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 更新本地状态
        setCustomers(
          customers.map((customer) =>
            customer.id === selectedCustomer.id ? { ...customer, status: "inactive" } : customer,
          ),
        )

        toast({
          title: "操作成功",
          description: "用户已成功禁用",
        })
      } else {
        toast({
          title: "操作失败",
          description: data.message || "禁用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('禁用用户错误:', error)
      toast({
        title: "操作失败",
        description: "禁用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 处理充值
  const handleRecharge = (customer: CustomerType) => {
    // 确保使用最新的客户数据
    const latestCustomer = customers.find(c => c.id === customer.id) || customer
    setSelectedCustomer(latestCustomer)
    setRechargeDialogOpen(true)
  }

  // 处理充值成功
  const handleRechargeSuccess = (newBalance: number) => {
    if (!selectedCustomer) return

    console.log('充值成功，更新余额:', {
      customerId: selectedCustomer.id,
      oldBalance: selectedCustomer.balance,
      newBalance
    })

    // 更新本地状态
    setCustomers(
      customers.map((customer) =>
        customer.id === selectedCustomer.id ? { ...customer, balance: newBalance } : customer,
      ),
    )

    // 如果有刷新功能，调用刷新
    if (refreshCustomers) {
      setTimeout(() => {
        refreshCustomers()
      }, 500)
    }
  }

  // 处理授信额度
  const handleCredit = (customer: CustomerType) => {
    // 确保使用最新的客户数据
    const latestCustomer = customers.find(c => c.id === customer.id) || customer
    setSelectedCustomer(latestCustomer)
    setCreditDialogOpen(true)
  }

  // 处理授信额度成功
  const handleCreditSuccess = (newCreditLimit: number) => {
    if (!selectedCustomer) return

    console.log('授信额度调整成功，更新额度:', {
      customerId: selectedCustomer.id,
      oldCreditLimit: selectedCustomer.creditLimit,
      newCreditLimit
    })

    // 更新本地状态
    setCustomers(
      customers.map((customer) =>
        customer.id === selectedCustomer.id ? { ...customer, creditLimit: newCreditLimit } : customer,
      ),
    )

    // 如果有刷新功能，调用刷新
    if (refreshCustomers) {
      setTimeout(() => {
        refreshCustomers()
      }, 500)
    }
  }

  // 处理用户添加成功
  const handleUserAdded = (newUser: CustomerType) => {
    // 始终刷新列表，无论是否有效的新用户数据
    if (refreshCustomers) {
      refreshCustomers()
      return // 刷新后直接返回，不需要手动添加
    }

    // 如果没有刷新函数，才手动添加
    if (newUser && newUser.id) {
      setCustomers([...customers, newUser])
    }
  }

  return {
    selectedCustomer,
    rejectDialogOpen,
    setRejectDialogOpen,
    disableUserDialogOpen,
    setDisableUserDialogOpen,
    enableUserDialogOpen,
    setEnableUserDialogOpen,
    rechargeDialogOpen,
    setRechargeDialogOpen,
    creditDialogOpen,
    setCreditDialogOpen,
    handleApprove,
    handleReject,
    confirmReject,
    handleEnableUser,
    confirmEnableUser,
    handleDisableUser,
    confirmDisableUser,
    handleRecharge,
    handleRechargeSuccess,
    handleCredit,
    handleCreditSuccess,
    handleUserAdded,
  }
}
