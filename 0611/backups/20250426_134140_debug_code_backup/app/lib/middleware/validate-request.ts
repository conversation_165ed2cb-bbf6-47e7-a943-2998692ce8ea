import { type NextRequest } from 'next/server'
import { type ObjectSchema } from 'yup'
import { ApiError } from './error-handler'

/**
 * 验证请求参数
 * @param request - Next.js 请求对象
 * @param schema - Yup 验证模式
 * @returns Promise<T> 验证通过的请求数据
 * @throws ApiError 验证失败时抛出错误
 */
export async function validateRequest<T>(
  request: NextRequest,
  schema: ObjectSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    console.log('验证请求体:', body)
    
    const validatedData = await schema.validate(body, {
      abortEarly: false,
      stripUnknown: true
    })
    
    console.log('验证通过:', validatedData)
    return validatedData
  } catch (error) {
    console.error('请求验证失败:', error)
    
    if (error instanceof Error) {
      throw new ApiError(400, error.message)
    }
    
    throw new ApiError(400, '请求参数验证失败')
  }
} 