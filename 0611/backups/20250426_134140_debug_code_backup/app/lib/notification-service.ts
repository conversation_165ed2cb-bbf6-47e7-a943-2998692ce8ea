/**
 * 通知服务
 * 提供系统内通知功能
 */

import { prisma } from "@/lib/prisma"

interface NotificationOptions {
  title: string
  content: string
  type: string
  priority?: string
  recipients?: string[]
  senderId?: string
  senderName?: string
  status?: string
  sendToAll?: boolean
}

/**
 * 创建通知
 *
 * @param options 通知选项
 * @returns 创建的通知
 */
export async function createNotification(options: NotificationOptions) {
  try {
    // 设置默认值
    const priority = options.priority || 'normal';
    const recipients = options.recipients || [];
    const senderId = options.senderId || 'system';
    const senderName = options.senderName || '系统管理员';
    const status = options.status || 'published';
    const sendToAll = options.sendToAll !== undefined ? options.sendToAll : recipients.length === 0;

    // 检查通知类型是否存在
    let typeId = 1; // 默认使用 ID 1

    const notificationType = await prisma.notificationType.findFirst({
      where: {
        OR: [
          { code: options.type.toUpperCase() },
          { code: 'SYSTEM' }
        ]
      }
    });

    if (notificationType) {
      typeId = notificationType.id;
    } else {
      // 创建默认类型
      const newType = await prisma.notificationType.create({
        data: {
          code: options.type.toUpperCase(),
          name: options.type.charAt(0).toUpperCase() + options.type.slice(1) + ' 通知',
          description: '系统自动创建的通知类型'
        }
      });
      typeId = newType.id;
    }

    // 创建通知
    const notification = await prisma.notification.create({
      data: {
        title: options.title,
        content: options.content,
        type: options.type.toUpperCase(),
        priority: priority,
        sendToAll: sendToAll,
        recipients: recipients,
        senderId: senderId,
        senderName: senderName,
        status: status,
        typeId: typeId,
        publishedAt: new Date(),
        allowBatchMarkRead: true,
        readCount: 0,
        readRate: 0,
        totalRecipients: recipients.length || 0
      }
    });

    // 如果有指定接收者，创建用户通知关联
    if (recipients && recipients.length > 0) {
      for (const userId of recipients) {
        try {
          await prisma.userNotification.create({
            data: {
              userId: userId,
              notificationId: notification.id,
              read: false
            }
          });
        } catch (err) {
          console.error(`为用户 ${userId} 创建通知关联失败:`, err);
          // 继续处理其他用户
        }
      }
    }

    return notification;
  } catch (error) {
    console.error('创建通知失败:', error);
    throw error;
  }
}
