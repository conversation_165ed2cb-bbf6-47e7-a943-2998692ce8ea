import { JWT } from "next-auth/jwt";
import { prisma } from "@/lib/prisma";
import { Policy, PolicyCondition } from "./types";

/**
 * 检查用户是否有资源权限
 * @param token 用户JWT token
 * @param resourceCode 资源代码
 * @param operationCode 操作代码
 * @returns 是否有权限
 */
export async function hasResourcePermission(
  token: JWT,
  resourceCode: string,
  operationCode: string
): Promise<boolean> {
  try {
    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: token.sub },
      select: {
        id: true,
        roleCode: true,
        role: {
          select: {
            code: true,
            permissions: true,
            resources: {
              select: {
                code: true,
                operations: {
                  select: {
                    code: true
                  }
                }
              }
            }
          }
        }
      }
    });

    if (!user || !user.role) {
      console.log("用户或角色不存在");
      return false;
    }

    // 如果用户角色有 * 权限，允许所有操作
    if (user.role.permissions.includes('*')) {
      console.log("用户有超级管理员权限");
      return true;
    }

    // 检查资源权限
    const hasResourceAccess = user.role.resources.some(resource => {
      if (resource.code === resourceCode) {
        // 检查操作权限
        return resource.operations.some(operation => 
          operation.code === operationCode
        );
      }
      return false;
    });

    console.log(`资源权限检查结果: ${hasResourceAccess}`);
    return hasResourceAccess;

  } catch (error) {
    console.error("权限检查错误:", error);
    return false;
  }
}

/**
 * 评估策略条件
 * @param policy 策略
 * @param user 用户信息
 * @returns 是否满足策略条件
 */
async function evaluatePolicy(
  policy: Policy & { conditions: PolicyCondition[] },
  user: any
): Promise<boolean> {
  // 如果没有条件，则默认满足
  if (!policy.conditions || policy.conditions.length === 0) {
    return true;
  }

  // 检查所有条件
  for (const condition of policy.conditions) {
    const value = getAttributeValue(user, condition.attribute);
    if (!evaluateCondition(condition, value)) {
      return false;
    }
  }

  return true;
}

/**
 * 获取属性值
 * @param user 用户信息
 * @param attribute 属性名
 * @returns 属性值
 */
function getAttributeValue(user: any, attribute: string): any {
  const parts = attribute.split(".");
  let value = user;

  for (const part of parts) {
    if (value === undefined || value === null) {
      return undefined;
    }
    value = value[part];
  }

  return value;
}

/**
 * 评估条件
 * @param condition 条件
 * @param value 实际值
 * @returns 是否满足条件
 */
function evaluateCondition(
  condition: PolicyCondition,
  value: any
): boolean {
  switch (condition.operator) {
    case "equals":
      return value === condition.value;
    case "notEquals":
      return value !== condition.value;
    case "contains":
      return String(value).includes(String(condition.value));
    case "notContains":
      return !String(value).includes(String(condition.value));
    case "startsWith":
      return String(value).startsWith(String(condition.value));
    case "endsWith":
      return String(value).endsWith(String(condition.value));
    case "greaterThan":
      return Number(value) > Number(condition.value);
    case "lessThan":
      return Number(value) < Number(condition.value);
    case "greaterThanOrEquals":
      return Number(value) >= Number(condition.value);
    case "lessThanOrEquals":
      return Number(value) <= Number(condition.value);
    default:
      return false;
  }
}

/**
 * 检查用户是否有菜单权限
 * @param token 用户JWT token
 * @param menuCode 菜单代码
 * @returns 是否有权限
 */
export async function hasMenuPermission(
  token: JWT,
  menuCode: string
): Promise<boolean> {
  return hasResourcePermission(token, "menus", "view");
}

/**
 * 检查用户是否有操作权限
 * @param token 用户JWT token
 * @param operationCode 操作代码
 * @returns 是否有权限
 */
export async function hasOperationPermission(
  token: JWT,
  operationCode: string
): Promise<boolean> {
  return hasResourcePermission(token, "operations", operationCode);
}

/**
 * 检查用户是否有策略权限
 * @param token 用户JWT token
 * @param policyCode 策略代码
 * @returns 是否有权限
 */
export async function hasPolicyPermission(
  token: JWT,
  policyCode: string
): Promise<boolean> {
  return hasResourcePermission(token, "policies", "manage");
}