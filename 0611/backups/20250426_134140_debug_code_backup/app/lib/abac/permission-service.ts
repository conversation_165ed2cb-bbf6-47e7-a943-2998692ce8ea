/**
 * 权限服务 - 整合 RBAC 和 ABAC 功能
 * 作为系统权限控制的统一入口
 */

import { AbacService } from "./abac-service";
import { PermissionContext } from "./types";

/**
 * 增强型权限服务类
 * 结合 RBAC 基础权限和 ABAC 条件权限
 */
export class PermissionService {
  /**
   * 检查用户是否有指定权限
   * @param userId 用户ID
   * @param permissionKey 权限键 (如: "role:create")
   * @param context 权限上下文 (可选，用于 ABAC 条件评估)
   * @returns 是否有权限
   */
  static async hasPermission(
    userId: string,
    permissionKey: string,
    context?: PermissionContext
  ): Promise<boolean> {
    try {
      // 1. 基础 RBAC 权限检查
      const hasBasePermission = await this.checkBaseRbacPermission(userId, permissionKey);
      
      // 如果基础权限检查不通过，直接返回 false
      if (!hasBasePermission) {
        return false;
      }
      
      // 2. 如果没有提供上下文或 ABAC 未启用，仅使用 RBAC 结果
      if (!context) {
        return true;
      }
      
      // 3. 获取用户角色代码
      const roleCode = await this.getUserRoleCode(userId);
      if (!roleCode) {
        return false;
      }
      
      // 4. ABAC 条件权限检查
      const abacResult = await AbacService.evaluateConditions(
        permissionKey,
        roleCode,
        context
      );
      
      return abacResult.permitted;
    } catch (error) {
      console.error("权限检查错误:", error);
      return false;
    }
  }
  
  /**
   * 检查基础 RBAC 权限
   * @param userId 用户ID
   * @param permissionKey 权限键
   * @returns 是否有权限
   */
  private static async checkBaseRbacPermission(
    userId: string,
    permissionKey: string
  ): Promise<boolean> {
    try {
      // 利用现有 RBAC 系统检查权限
      // 注意：这里需要集成您现有的权限检查逻辑
      
      // 示例实现 (假设从用户对象中检查权限)
      const user = await this.getUser(userId);
      
      // 检查用户是否有 ADMIN 角色
      if (user?.roleCode === 'ADMIN') {
        return true;
      }
      
      // 检查用户权限列表是否包含所需权限
      return user?.permissions?.includes(permissionKey) || false;
    } catch (error) {
      console.error("RBAC 权限检查错误:", error);
      return false;
    }
  }
  
  /**
   * 获取用户信息
   * @param userId 用户ID
   * @returns 用户对象
   */
  private static async getUser(userId: string): Promise<any> {
    // 从数据库或缓存中获取用户信息
    // 注意：集成现有用户获取逻辑
    
    // 模拟实现，实际应使用您的数据访问层
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          roleCode: true,
          permissions: true
        }
      });
      
      return user;
    } catch (error) {
      console.error("获取用户信息错误:", error);
      return null;
    }
  }
  
  /**
   * 获取用户角色代码
   * @param userId 用户ID
   * @returns 角色代码
   */
  private static async getUserRoleCode(userId: string): Promise<string | null> {
    try {
      const user = await this.getUser(userId);
      return user?.roleCode || null;
    } catch (error) {
      console.error("获取用户角色错误:", error);
      return null;
    }
  }
}

// 确保导入 prisma 客户端
import prisma from "@/lib/prisma";

// 导出默认实例
export default PermissionService; 