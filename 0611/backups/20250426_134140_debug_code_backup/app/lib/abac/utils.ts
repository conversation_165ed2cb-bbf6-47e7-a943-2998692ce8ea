import { JWT } from "next-auth/jwt";
import { prisma } from "@/lib/prisma";
import { Policy, PolicyCondition } from "./types";

/**
 * 检查用户是否有资源权限
 * @param token 用户JWT token
 * @param resourceCode 资源代码
 * @param operationCode 操作代码
 * @returns 是否有权限
 */
export async function hasResourcePermission(
  token: JWT,
  resourceCode: string,
  operationCode: string
): Promise<boolean> {
  try {
    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: token.sub },
      include: {
        role: {
          include: {
            operations: true,
            resources: true,
          },
        },
      },
    });

    if (!user || !user.role) {
      return false;
    }

    // 检查资源是否存在
    const resource = await prisma.resource.findUnique({
      where: { code: resourceCode },
    });

    if (!resource) {
      return false;
    }

    // 检查操作是否存在
    const operation = await prisma.operation.findUnique({
      where: { code: operationCode },
    });

    if (!operation) {
      return false;
    }

    // 检查角色是否有资源权限
    const hasResource = user.role.resources.some(
      (r) => r.code === resourceCode
    );

    if (!hasResource) {
      return false;
    }

    // 检查角色是否有操作权限
    const hasOperation = user.role.operations.some(
      (o) => o.code === operationCode
    );

    if (!hasOperation) {
      return false;
    }

    // 获取策略
    const policies = await prisma.policy.findMany({
      where: {
        resourceCode,
        operationCode,
      },
      include: {
        conditions: true,
      },
    });

    // 如果没有策略，则默认允许
    if (policies.length === 0) {
      return true;
    }

    // 检查所有策略
    for (const policy of policies) {
      if (await evaluatePolicy(policy, user)) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("权限检查错误:", error);
    return false;
  }
}

/**
 * 评估策略条件
 * @param policy 策略
 * @param user 用户信息
 * @returns 是否满足策略条件
 */
async function evaluatePolicy(
  policy: Policy & { conditions: PolicyCondition[] },
  user: any
): Promise<boolean> {
  // 如果没有条件，则默认满足
  if (!policy.conditions || policy.conditions.length === 0) {
    return true;
  }

  // 检查所有条件
  for (const condition of policy.conditions) {
    const value = getAttributeValue(user, condition.attribute);
    if (!evaluateCondition(condition, value)) {
      return false;
    }
  }

  return true;
}

/**
 * 获取属性值
 * @param user 用户信息
 * @param attribute 属性名
 * @returns 属性值
 */
function getAttributeValue(user: any, attribute: string): any {
  const parts = attribute.split(".");
  let value = user;

  for (const part of parts) {
    if (value === undefined || value === null) {
      return undefined;
    }
    value = value[part];
  }

  return value;
}

/**
 * 评估条件
 * @param condition 条件
 * @param value 实际值
 * @returns 是否满足条件
 */
function evaluateCondition(
  condition: PolicyCondition,
  value: any
): boolean {
  switch (condition.operator) {
    case "equals":
      return value === condition.value;
    case "notEquals":
      return value !== condition.value;
    case "contains":
      return String(value).includes(String(condition.value));
    case "notContains":
      return !String(value).includes(String(condition.value));
    case "startsWith":
      return String(value).startsWith(String(condition.value));
    case "endsWith":
      return String(value).endsWith(String(condition.value));
    case "greaterThan":
      return Number(value) > Number(condition.value);
    case "lessThan":
      return Number(value) < Number(condition.value);
    case "greaterThanOrEquals":
      return Number(value) >= Number(condition.value);
    case "lessThanOrEquals":
      return Number(value) <= Number(condition.value);
    default:
      return false;
  }
}