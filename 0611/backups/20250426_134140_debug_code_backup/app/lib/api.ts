interface SystemSettings {
  backgroundImage: string;
  footerText: string;
}

// 模拟获取系统设置的API
export async function getSystemSettings(): Promise<SystemSettings> {
  // 在实际应用中，这里应该是一个API请求
  // 这里使用模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        backgroundImage: "/placeholder.svg?height=1080&width=1920",
        footerText:
          '© 2023 外呼管理系统 版权所有 | <a href="#" class="underline">隐私政策</a> | <a href="#" class="underline">使用条款</a>',
      })
    }, 300)
  })
}

// 用户认证相关API
interface LoginRequest {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterData {
  username: string
  password: string
  email: string
  verificationCode: string
}

interface ApiResponse<T = any> {
  code: number
  success: boolean
  message: string
  data: T | null
}

interface LoginResponse {
  token: string
  user: {
    id: string
    username: string
    name?: string
    role: string
    permissions: string[]
  }
}

/**
 * 用户登录
 * @param data - 登录请求数据
 * @returns Promise<ApiResponse<LoginResponse>> 登录响应
 */
export async function login(data: { username: string; password: string; remember?: boolean }) {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include', // 包含 cookies
    })

    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        code: result.code || 'LOGIN_FAILED',
        message: result.message || '登录失败，请稍后重试',
      }
    }

    // 不再将token存储到localStorage中，而是依赖HttpOnly cookie

    return {
      success: true,
      data: {
        user: result.data.user,
        token: result.data.token
      }
    }
  } catch (error) {
    console.error('登录错误:', error)
    return {
      success: false,
      code: 'NETWORK_ERROR',
      message: '网络错误，请检查网络连接',
    }
  }
}

/**
 * 发送验证码
 * @param email - 邮箱地址
 * @returns Promise<ApiResponse> 发送验证码响应
 */
export async function sendVerificationCode(email: string): Promise<ApiResponse> {
  const response = await fetch("/api/auth/send-verification-code", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ email }),
  })
  return response.json()
}

/**
 * 用户注册
 * @param data - 注册数据
 * @returns Promise<ApiResponse> 注册响应
 */
export async function register(data: RegisterData): Promise<ApiResponse> {
  const response = await fetch("/api/auth/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
  return response.json()
}

/**
 * 退出登录
 */
export async function logout() {
  const response = await fetch("/api/auth/logout", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
  })

  if (!response.ok) {
    throw new Error("退出登录失败")
  }

  return response.json()
}

// 其他API函数...

