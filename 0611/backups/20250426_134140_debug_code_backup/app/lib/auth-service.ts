/**
 * 认证服务
 * 提供用户认证、会话管理和权限验证功能
 *
 * 功能：
 * - JWT 令牌生成和验证
 * - Cookie 管理
 * - 用户登录和登出
 * - 会话状态管理
 *
 * @module AuthService
 */

import { prismaClient } from "@/lib/prisma"
import { compare } from "bcryptjs"
import { sign, verify } from "jsonwebtoken"
import { cookies } from "next/headers"
import { Prisma } from "@prisma/client"
import { TokenExpiredError } from "jsonwebtoken"

// JWT 配置
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"
const TOKEN_NAME = "token"

interface Menu {
  id: string
  code: string
  name: string
  path: string
  icon: string | null
  parentId: string | null
  order: number
  visible: boolean
  createdAt: Date
  updatedAt: Date
}

interface Role {
  id: string
  code: string
  name: string
  type: string
  description: string | null
  permissions: string[]
  createdAt: Date
  updatedAt: Date
  menus?: Menu[]
}

interface User {
  id: string
  username: string
  email: string
  password: string
  roleCode: string
  name: string | null
  image: string | null
  permissions: string[]
  role?: Role
  lastLoginAt?: Date
}

/**
 * 认证用户接口
 * 定义了认证系统中用户对象的数据结构
 */
export interface AuthUser {
  id: string
  email: string
  username: string
  roleCode: string
  role?: Role
  permissions?: string[]
}

/**
 * 认证服务类
 * 提供用户认证和会话管理的核心功能
 */
export class AuthService {
  /**
   * 用户登录
   * 验证用户凭据并创建新的会话
   *
   * @param identifier - 用户名或邮箱
   * @param password - 用户密码
   * @returns 认证成功的用户信息
   * @throws Error 当认证失败时
   */
  static async login(identifier: string, password: string): Promise<AuthUser> {
    try {
      if (!identifier || !password) {
        throw new Error("请输入用户名/邮箱和密码")
      }

      console.log('尝试登录:', { identifier })

      // 查找用户
      const user = await prismaClient.user.findFirst({
        where: {
          OR: [
            { username: identifier },
            { email: identifier }
          ]
        },
        include: {
          role: {
            include: {
              menus: true
            }
          }
        }
      })

      if (!user) {
        console.log('用户不存在:', { identifier })
        throw new Error("用户不存在")
      }

      console.log('找到用户:', {
        id: user.id,
        username: user.username,
        roleCode: user.roleCode
      })

      // 验证密码
      const isValid = await compare(password, user.password)
      if (!isValid) {
        console.log('密码错误:', { identifier })
        throw new Error("密码错误")
      }

      // 更新最后登录时间
      await prismaClient.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      })

      console.log('用户登录成功:', {
        id: user.id,
        username: user.username,
        roleCode: user.roleCode,
        role: user.role
      })

      // 生成 JWT 令牌
      const token = sign(
        {
          sub: user.id,
          userId: user.id,
          username: user.username,
          email: user.email,
          roleCode: user.roleCode,
          role: user.role
        },
        JWT_SECRET,
        { expiresIn: "30d" }
      )

      // 设置 cookie
      cookies().set(TOKEN_NAME, token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60,
        path: "/"
      })

      return {
        id: user.id,
        email: user.email,
        username: user.username,
        roleCode: user.roleCode,
        role: user.role
      }
    } catch (error) {
      console.error("Login error:", error)
      throw error
    }
  }

  /**
   * 用户登出
   * 清除用户会话
   */
  static async logout() {
    cookies().delete(TOKEN_NAME)
  }

  /**
   * 获取当前用户
   * 从 JWT 令牌中解析用户信息
   *
   * @returns 当前认证的用户信息，如果未认证则返回 null
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // 获取所有cookie
      const cookieStore = cookies()
      const allCookies = cookieStore.getAll();
      console.log("All cookies:", allCookies.map(c => c.name));

      // 尝试获取认证token
      // 尝试多个Cookie名称
      const cookieNames = [TOKEN_NAME, "auth_token", "token"];
      let token = null;

      for (const name of cookieNames) {
        const cookieValue = cookieStore.get(name)?.value;
        if (cookieValue) {
          console.log(`找到认证cookie: ${name}`);
          token = cookieValue;
          break;
        }
      }

      if (!token) {
        console.log("未找到认证cookie");
        return null;
      }

      console.log("开始验证令牌");

      // 验证令牌
      let decoded;
      try {
        decoded = verify(token, JWT_SECRET) as any;
        console.log("Token验证成功:", {
          userId: decoded.userId || decoded.id,
          username: decoded.username,
          roleCode: decoded.roleCode
        });
      } catch (tokenError) {
        console.error("Token验证失败:", tokenError);
        // 只在明确的令牌过期情况下清除 cookie
        if (tokenError instanceof TokenExpiredError) {
          console.log("Token已过期");
          cookieStore.delete(TOKEN_NAME);
        }
        return null;
      }

      // 获取用户ID
      const userId = decoded.userId || decoded.id || decoded.sub;

      if (!userId) {
        console.log("Token不包含用户ID");
        return null;
      }

      // 从数据库获取最新的用户信息，确保有正确的角色和权限
      console.log('从数据库获取用户信息:', { userId })
      const user = await prismaClient.user.findUnique({
        where: { id: userId },
        include: {
          role: true
        }
      })

      if (user) {
        console.log('成功获取用户信息:', {
          id: user.id,
          username: user.username,
          email: user.email,
          roleCode: user.roleCode
        })
      } else {
        console.log('未找到用户:', { userId })
      }

      if (!user) {
        console.log("数据库中未找到用户:", userId);
        return null;
      }

      // 设置返回的用户信息
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        username: user.username,
        roleCode: user.roleCode,
        role: user.role,
        permissions: [...(user.permissions || [])]
      };

      return authUser;
    } catch (error) {
      console.error("获取当前用户时出错:", error);
      return null;
    }
  }

  /**
   * 检查用户是否已认证
   *
   * @returns 如果用户已认证则返回 true，否则返回 false
   */
  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser()
    return !!user
  }

  /**
   * 检查用户是否有指定的权限
   *
   * @param permission - 要检查的权限代码
   * @returns 如果用户有指定权限则返回 true，否则返回 false
   */
  static async hasPermission(permission: string): Promise<boolean> {
    try {
      console.log('开始检查权限:', { permission })

      const user = await this.getCurrentUser()
      if (!user) {
        console.log('用户未登录')
        return false
      }

      console.log('获取到当前用户:', {
        id: user.id,
        username: user.username,
        roleCode: user.roleCode
      })

      // 从数据库获取最新的角色信息
      const role = await prismaClient.role.findUnique({
        where: { code: user.roleCode }
      })

      if (!role) {
        console.log('未找到用户角色:', { roleCode: user.roleCode })
        return false
      }

      console.log('获取到角色信息:', {
        roleCode: role.code,
        roleName: role.name,
        rolePermissions: role.permissions
      })

      // 管理员拥有所有权限
      if (role.code === 'ADMIN' || role.permissions.includes('*')) {
        console.log('用户具有管理员权限')
        return true
      }

      // 检查具体权限
      const hasPermission = role.permissions.includes(permission)
      console.log('权限检查结果:', {
        permission,
        hasPermission,
        rolePermissions: role.permissions
      })

      return hasPermission
    } catch (error) {
      console.error('检查权限失败:', error)
      return false
    }
  }

  /**
   * 检查用户是否有指定的角色
   *
   * @param roleCode - 要检查的角色代码
   * @returns 如果用户有指定角色则返回 true，否则返回 false
   */
  static async hasRole(roleCode: string): Promise<boolean> {
    const user = await this.getCurrentUser()
    if (!user) return false

    return user.roleCode === roleCode
  }
}