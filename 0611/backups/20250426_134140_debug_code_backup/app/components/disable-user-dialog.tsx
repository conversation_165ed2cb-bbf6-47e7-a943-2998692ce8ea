"use client"

import { useState } from "react"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"

interface DisableUserDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (reason: string) => void
  userName: string
}

export function DisableUserDialog({ isOpen, onClose, onConfirm, userName }: DisableUserDialogProps) {
  const [reason, setReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleConfirm = async () => {
    if (!reason.trim()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onConfirm(reason)
      setReason("")
      onClose()
    } catch (error) {
      console.error("禁用用户失败:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>禁用用户账户</DialogTitle>
          <DialogDescription>
            您正在禁用用户 <span className="font-semibold">{userName}</span> 的账户。请提供禁用原因，该原因将通过邮件和系统通知发送给用户。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Textarea
            placeholder="请输入禁用原因..."
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="min-h-[100px]"
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            取消
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!reason.trim() || isSubmitting}
            className="bg-red-500 hover:bg-red-600 text-white"
          >
            {isSubmitting ? "提交中..." : "确认禁用"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
