"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, AlertCircle, Check, X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"

interface VerificationData {
  status: string
  type: string
  reviewedAt?: string
  remark?: string
  data: {
    // 个人认证信息
    realName?: string
    idCardNumber?: string
    idCardFront?: string
    idCardBack?: string
    idCardHolding?: string

    // 企业认证信息
    companyName?: string
    legalPerson?: string
    legalPersonIdCard?: string
    socialCreditCode?: string
    businessLicense?: string
    otherDocuments?: string[]
  }
}

interface VerificationViewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  onApprove: (userId: string) => void
  onReject: (userId: string) => void
}

export function VerificationViewDialog({
  open,
  onOpenChange,
  userId,
  userName,
  onApprove,
  onReject
}: VerificationViewDialogProps) {
  const [verificationData, setVerificationData] = useState<VerificationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (open && userId) {
      fetchVerificationData()
    }
  }, [open, userId])

  const fetchVerificationData = async () => {
    setLoading(true)
    setError(null)

    try {
      console.log('开始获取用户认证资料:', userId)

      // 使用新的API路径
      console.log(`开始请求认证资料API: /api/admin/verification/${userId}`)
      const response = await fetch(`/api/admin/verification/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        credentials: 'include' // 确保包含认证信息
      })

      console.log('认证资料API响应状态:', response.status)

      if (!response.ok) {
        // 获取响应文本进行调试
        const responseText = await response.text();
        console.log('响应文本:', responseText);

        let errorMessage = '';
        try {
          // 尝试解析JSON
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.message || '未知错误';
          console.log('解析的错误数据:', errorData);
        } catch (parseError) {
          console.error('解析响应JSON失败:', parseError);
          errorMessage = `服务器响应不是有效的JSON格式: ${responseText.substring(0, 100)}...`;
        }

        // 根据状态码提供更具体的错误信息
        if (response.status === 401) {
          throw new Error(`认证失败，请确保您已登录并具有管理员权限: ${errorMessage}`)
        } else if (response.status === 403) {
          throw new Error(`您没有权限查看此用户的认证资料: ${errorMessage}`)
        } else if (response.status === 404) {
          throw new Error(`未找到该用户的认证资料，可能用户尚未提交: ${errorMessage}`)
        } else {
          throw new Error(`获取认证资料失败 (${response.status}): ${errorMessage}`)
        }
      }

      const data = await response.json()

      if (data.success) {
        setVerificationData(data.data)
      } else {
        throw new Error(data.message || '获取认证资料失败')
      }
    } catch (err) {
      console.error('获取认证资料错误:', err)
      setError(err instanceof Error ? err.message : '获取认证资料时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = () => {
    onApprove(userId)
    onOpenChange(false)
  }

  const handleReject = () => {
    onReject(userId)
    onOpenChange(false)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">待审核</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">已通过</Badge>
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">已拒绝</Badge>
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">未知</Badge>
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto w-[95vw] md:w-auto">
        <DialogHeader>
          <DialogTitle>用户认证资料</DialogTitle>
          <DialogDescription>
            查看用户 <span className="font-semibold">{userName}</span> 的认证资料
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-lg">加载认证资料中...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 my-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <div>
                <h3 className="text-red-800 font-medium">加载失败</h3>
                <p className="text-red-700 text-sm">{error}</p>
                <div className="mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchVerificationData}
                    className="bg-white hover:bg-gray-100"
                  >
                    <Loader2 className="mr-2 h-3 w-3" />
                    重试加载
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : !verificationData ? (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-4 my-4">
            <p className="text-gray-600 text-center">该用户尚未提交认证资料</p>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <span className="text-sm text-gray-500 mr-2">认证类型:</span>
                <span className="font-medium">
                  {verificationData.type === 'personal' ? '个人认证' : '企业认证'}
                </span>
              </div>
              <div>
                <span className="text-sm text-gray-500 mr-2">状态:</span>
                {getStatusBadge(verificationData.status)}
              </div>
            </div>

            {/* 个人认证信息 */}
            {verificationData.type === "personal" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-x-hidden">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">真实姓名</h3>
                    <p className="text-gray-800">{verificationData.data.realName || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">身份证号码</h3>
                    <p className="text-gray-800">{verificationData.data.idCardNumber || "-"}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">身份证正面照片</h3>
                  {verificationData.data.idCardFront ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img
                        src={verificationData.data.idCardFront}
                        alt="身份证正面"
                        className="max-h-60 w-full object-contain mx-auto"
                        loading="lazy"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">身份证反面照片</h3>
                  {verificationData.data.idCardBack ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img
                        src={verificationData.data.idCardBack}
                        alt="身份证反面"
                        className="max-h-60 w-full object-contain mx-auto"
                        loading="lazy"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">手持身份证照片</h3>
                  {verificationData.data.idCardHolding ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img
                        src={verificationData.data.idCardHolding}
                        alt="手持身份证"
                        className="max-h-60 w-full object-contain mx-auto"
                        loading="lazy"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>
              </div>
            )}

            {/* 企业认证信息 */}
            {verificationData.type === "enterprise" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-x-hidden">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">企业名称</h3>
                    <p className="text-gray-800">{verificationData.data.companyName || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">法人代表</h3>
                    <p className="text-gray-800">{verificationData.data.legalPerson || "-"}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-x-hidden">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">法人身份证号</h3>
                    <p className="text-gray-800">{verificationData.data.legalPersonIdCard || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">统一社会信用代码</h3>
                    <p className="text-gray-800">{verificationData.data.socialCreditCode || "-"}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">营业执照</h3>
                  {verificationData.data.businessLicense ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img
                        src={verificationData.data.businessLicense}
                        alt="营业执照"
                        className="max-h-60 w-full object-contain mx-auto"
                        loading="lazy"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                {verificationData.data.otherDocuments && verificationData.data.otherDocuments.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">其他证件</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 overflow-x-hidden">
                      {verificationData.data.otherDocuments.map((doc, index) => (
                        <div key={index} className="border rounded-md p-2">
                          <img
                            src={doc}
                            alt={`其他证件 ${index + 1}`}
                            className="max-h-60 w-full object-contain mx-auto"
                            loading="lazy"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 审核备注 */}
            {verificationData.status === 'rejected' && verificationData.remark && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="font-medium text-red-800 mb-1">驳回原因</h3>
                <p className="text-red-700 whitespace-pre-line">{verificationData.remark}</p>
              </div>
            )}
          </div>
        )}

        <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0 mt-4">
          {!loading && !error && verificationData && verificationData.status === 'pending' && (
            <>
              <Button
                variant="outline"
                onClick={handleReject}
                className="bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:text-red-800"
              >
                <X className="mr-2 h-4 w-4" />
                拒绝
              </Button>
              <Button
                onClick={handleApprove}
                className="bg-green-600 text-white hover:bg-green-700"
              >
                <Check className="mr-2 h-4 w-4" />
                通过
              </Button>
            </>
          )}
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
