"use client"

import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function TermsPage() {
  const [termsContent, setTermsContent] = useState<string>("")

  useEffect(() => {
    // 从localStorage获取服务条款内容
    try {
      const settings = localStorage.getItem("systemSettings")
      if (settings) {
        const parsedSettings = JSON.parse(settings)
        if (parsedSettings.termsContent) {
          setTermsContent(parsedSettings.termsContent)
        } else {
          // 如果没有保存的内容，使用默认内容
          setTermsContent(defaultTermsContent)
        }
      } else {
        setTermsContent(defaultTermsContent)
      }
    } catch (e) {
      console.error("加载服务条款失败", e)
      setTermsContent(defaultTermsContent)
    }
  }, [])

  // 默认服务条款内容
  const defaultTermsContent = `
# 5G外呼系统服务条款

## 1. 服务介绍

欢迎使用5G外呼系统（以下简称"本系统"）。本系统是一款专为企业设计的智能外呼管理平台，旨在提高通信效率和客户服务质量。

## 2. 用户注册与账户安全

2.1 用户在注册时需提供真实、准确、完整的个人资料，并及时更新。

2.2 用户应妥善保管账号和密码，因账号密码保管不善造成的损失由用户自行承担。

2.3 用户不得将账号转让、出借给他人使用，否则用户应对由此产生的后果负全部责任。

## 3. 系统使用规范

3.1 用户应遵守中华人民共和国相关法律法规。

3.2 用户不得利用本系统从事违法活动，包括但不限于：
   - 发送垃圾信息、诈骗信息
   - 侵犯他人知识产权
   - 散布违法或不良信息
   - 其他违反法律法规的行为

3.3 用户应尊重他人隐私，不得未经授权收集、使用他人个人信息。

## 4. 数据安全与隐私保护

4.1 我们重视用户数据安全和隐私保护，采取合理措施保护用户信息。

4.2 我们仅在提供服务所必需的范围内收集和使用用户信息。

4.3 未经用户同意，我们不会向第三方披露用户个人信息，法律法规另有规定的除外。

## 5. 服务变更、中断与终止

5.1 我们保留随时修改或中断服务的权利，无需事先通知用户。

5.2 对于用户违反本条款的行为，我们有权终止提供服务。

## 6. 知识产权

6.1 本系统的所有权、运营权和知识产权归我们所有。

6.2 用户仅获得使用本系统的权利，未经授权不得复制、修改、传播或销售本系统的任何部分。

## 7. 免责声明

7.1 我们不对因网络故障、系统维护等不可抗力因素导致的服务中断或数据丢失承担责任。

7.2 用户因使用本系统而产生的任何直接或间接损失，我们不承担责任。

## 8. 条款修改

8.1 我们保留随时修改本条款的权利，修改后的条款将在本页面公布。

8.2 用户继续使用本系统，即表示接受修改后的条款。

## 9. 适用法律与争议解决

9.1 本条款的解释、效力及纠纷的解决均适用中华人民共和国法律。

9.2 因使用本系统所引起的或与本系统有关的任何争议，应通过友好协商解决；协商不成的，提交本公司所在地有管辖权的人民法院诉讼解决。

## 10. 联系我们

如您对本条款或本系统有任何问题，请联系我们的客服团队。

最后更新日期：2025年3月20日
  `

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>服务条款</CardTitle>
          <Link href="/login">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
        </CardHeader>
        <CardContent>
          <div className="prose dark:prose-invert max-w-none">
            <div dangerouslySetInnerHTML={{ __html: termsContent.replace(/\n/g, "<br>") }} />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

