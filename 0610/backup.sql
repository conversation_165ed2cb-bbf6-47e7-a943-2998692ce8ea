--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Homebrew)
-- Dumped by pg_dump version 14.17 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: UserRole; Type: TYPE; Schema: public; Owner: postgres
--

CREATE TYPE public."UserRole" AS ENUM (
    'ADMIN',
    'USER'
);


ALTER TYPE public."UserRole" OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: LoginAttempt; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."LoginAttempt" (
    id text NOT NULL,
    "userId" text NOT NULL,
    attempts integer DEFAULT 1 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    identifier text NOT NULL,
    "lockedUntil" timestamp(3) without time zone
);


ALTER TABLE public."LoginAttempt" OWNER TO postgres;

--
-- Name: RegistrationAttempt; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."RegistrationAttempt" (
    id text NOT NULL,
    ip text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."RegistrationAttempt" OWNER TO postgres;

--
-- Name: Task; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Task" (
    id text NOT NULL,
    name text NOT NULL,
    content text NOT NULL,
    type text NOT NULL,
    status text DEFAULT '未开始'::text NOT NULL,
    progress integer DEFAULT 0 NOT NULL,
    priority integer DEFAULT 3 NOT NULL,
    "startTime" timestamp(3) without time zone NOT NULL,
    "endTime" timestamp(3) without time zone,
    "creatorId" text NOT NULL,
    "assigneeId" text,
    tags text[],
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Task" OWNER TO postgres;

--
-- Name: User; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."User" (
    id text NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    email text NOT NULL,
    name text,
    avatar text,
    permissions text[],
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    role public."UserRole" DEFAULT 'USER'::public."UserRole" NOT NULL
);


ALTER TABLE public."User" OWNER TO postgres;

--
-- Name: VerificationCode; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."VerificationCode" (
    id text NOT NULL,
    code text NOT NULL,
    email text NOT NULL,
    type text NOT NULL,
    "expiresAt" timestamp(3) without time zone NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public."VerificationCode" OWNER TO postgres;

--
-- Name: VideoCall; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."VideoCall" (
    id text NOT NULL,
    title text NOT NULL,
    description text,
    status text DEFAULT 'pending'::text NOT NULL,
    "startTime" timestamp(3) without time zone NOT NULL,
    duration integer DEFAULT 30 NOT NULL,
    recording boolean DEFAULT false NOT NULL,
    "creatorId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."VideoCall" OWNER TO postgres;

--
-- Name: VideoCallParticipant; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."VideoCallParticipant" (
    id text NOT NULL,
    "videoCallId" text NOT NULL,
    "userId" text NOT NULL,
    "joinedAt" timestamp(3) without time zone,
    "leftAt" timestamp(3) without time zone
);


ALTER TABLE public."VideoCallParticipant" OWNER TO postgres;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO postgres;

--
-- Name: abac_policies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.abac_policies (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    effect text NOT NULL,
    subject jsonb,
    resource jsonb,
    action text[],
    environment jsonb,
    priority integer NOT NULL,
    enabled boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.abac_policies OWNER TO postgres;

--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.system_settings (
    id integer DEFAULT 1 NOT NULL,
    "siteName" text NOT NULL,
    logo text NOT NULL,
    theme jsonb NOT NULL,
    features jsonb NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.system_settings OWNER TO postgres;

--
-- Data for Name: LoginAttempt; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."LoginAttempt" (id, "userId", attempts, "createdAt", identifier, "lockedUntil") FROM stdin;
\.


--
-- Data for Name: RegistrationAttempt; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."RegistrationAttempt" (id, ip, "createdAt") FROM stdin;
cm918vpqo0004nrfbdyo8sa93	::1	2025-04-03 11:00:33.936
\.


--
-- Data for Name: Task; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Task" (id, name, content, type, status, progress, priority, "startTime", "endTime", "creatorId", "assigneeId", tags, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: User; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."User" (id, username, password, email, name, avatar, permissions, "createdAt", "updatedAt", role) FROM stdin;
cm918gxge0000nr34fv6srnsr	admin	$2b$12$XlxuUA/SInI6DQo0nVQfKOpFYW3S5ZZovnXcXEgBiQGaG9nXsLHPm	<EMAIL>	系统管理员	\N	{*}	2025-04-03 10:49:04.094	2025-04-03 10:49:04.094	ADMIN
cm918vpt90005nrfb5ynu6n2s	xm2025	$2b$10$jSlcL55EWpCBpZ1LnjXyx.Cl9druuKeU/dTbnlTlixxwt3jh00Qxa	<EMAIL>	\N	\N	{TASK_READ,TASK_CREATE}	2025-04-03 11:00:34.029	2025-04-03 11:00:34.029	USER
\.


--
-- Data for Name: VerificationCode; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."VerificationCode" (id, code, email, type, "expiresAt", "createdAt") FROM stdin;
\.


--
-- Data for Name: VideoCall; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."VideoCall" (id, title, description, status, "startTime", duration, recording, "creatorId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: VideoCallParticipant; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."VideoCallParticipant" (id, "videoCallId", "userId", "joinedAt", "leftAt") FROM stdin;
\.


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
2d19fe54-6374-4101-b433-a10c8e26c9b3	a08bd2d611bc95ab638446b38560c577792c9165e91609a917565de19aad6568	2025-04-03 18:42:58.720874+08	20250403021908_add_login_attempts_and_permissions	\N	\N	2025-04-03 18:42:58.70905+08	1
207c48d9-2dfc-4cfc-b0bf-9905657eb6e1	c8b52c0f12b858d9fae9b0d1ce77074dfa3f5178296a4787509e3f63c3f8a2f5	2025-04-03 18:42:58.725544+08	20250403090800_add_registration_attempt	\N	\N	2025-04-03 18:42:58.72147+08	1
479bfef6-526d-44f8-8bc1-fc1b024f9172	ed23513fbc01bd3da45ada1e26e0fcbae8d9d5b61a397c7437ea3fd22c0b611b	2025-04-03 18:42:58.729558+08	20250403091151_add_verification_code	\N	\N	2025-04-03 18:42:58.726115+08	1
a3672ce7-14ee-4e0a-9af6-92aece4b07c8	f3be0bca1a8f35c6d902bb8d9eb44657f1c257c25a3284ecb4c1725392cfa203	2025-04-03 18:42:58.732322+08	20250403093537_add_login_attempt_fields	\N	\N	2025-04-03 18:42:58.730262+08	1
94b0d81e-3724-4cbd-9be4-9639847180a4	fba56a3bbe952fb0bd8082de5b080b3efe58ece5fd1bc5bb7cab8948f01b4aaf	2025-04-03 18:42:58.739759+08	20250403103625_add_system_settings	\N	\N	2025-04-03 18:42:58.736494+08	1
77d8dae7-a688-4cd9-af05-04d0e27b1144	29f1d193536b37c7a1a88df8774cc53ad81b8de294d5697a8399586a71377f69	2025-04-03 18:48:11.703655+08	20250403104811_add_abac_policies	\N	\N	2025-04-03 18:48:11.691366+08	1
\.


--
-- Data for Name: abac_policies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.abac_policies (id, name, description, effect, subject, resource, action, environment, priority, enabled, "createdAt", "updatedAt") FROM stdin;
cm918gxgq0001nr34ttwausuy	admin_full_access	管理员拥有所有资源的完全访问权限	allow	{"roles": ["ADMIN"]}	\N	{*}	\N	100	t	2025-04-03 10:49:04.106	2025-04-03 11:08:32.551
cm918gxgu0002nr34b3qmqmh9	user_own_tasks	用户只能访问自己创建的任务	allow	{"roles": ["USER"]}	{"types": ["task"], "attributes": {"ownerId": {"value": "${subject.id}", "operator": "equals"}}}	{read,update,delete}	\N	90	t	2025-04-03 10:49:04.11	2025-04-03 11:08:32.555
cm918gxgw0003nr340jfzaugo	user_create_task	用户可以创建新任务	allow	{"roles": ["USER"]}	{"types": ["task"]}	{create}	\N	80	t	2025-04-03 10:49:04.112	2025-04-03 11:08:32.568
cm918gxgx0004nr34sylawuth	working_hours_only	仅在工作时间允许访问	allow	\N	{"types": ["task", "report"]}	{create,update,delete}	{"attributes": {"dayOfWeek": {"value": 0, "operator": "notEquals"}}, "timeRanges": [{"end": "18:00", "start": "09:00"}]}	70	t	2025-04-03 10:49:04.114	2025-04-03 11:08:32.581
cm918gxgy0005nr34bqhou40l	default_deny	默认拒绝所有未明确允许的访问	deny	\N	\N	{*}	\N	0	t	2025-04-03 10:49:04.115	2025-04-03 11:08:32.596
\.


--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.system_settings (id, "siteName", logo, theme, features, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Name: LoginAttempt LoginAttempt_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."LoginAttempt"
    ADD CONSTRAINT "LoginAttempt_pkey" PRIMARY KEY (id);


--
-- Name: RegistrationAttempt RegistrationAttempt_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."RegistrationAttempt"
    ADD CONSTRAINT "RegistrationAttempt_pkey" PRIMARY KEY (id);


--
-- Name: Task Task_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Task"
    ADD CONSTRAINT "Task_pkey" PRIMARY KEY (id);


--
-- Name: User User_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."User"
    ADD CONSTRAINT "User_pkey" PRIMARY KEY (id);


--
-- Name: VerificationCode VerificationCode_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VerificationCode"
    ADD CONSTRAINT "VerificationCode_pkey" PRIMARY KEY (id);


--
-- Name: VideoCallParticipant VideoCallParticipant_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VideoCallParticipant"
    ADD CONSTRAINT "VideoCallParticipant_pkey" PRIMARY KEY (id);


--
-- Name: VideoCall VideoCall_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VideoCall"
    ADD CONSTRAINT "VideoCall_pkey" PRIMARY KEY (id);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: abac_policies abac_policies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.abac_policies
    ADD CONSTRAINT abac_policies_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (id);


--
-- Name: LoginAttempt_identifier_attempts_lockedUntil_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "LoginAttempt_identifier_attempts_lockedUntil_idx" ON public."LoginAttempt" USING btree (identifier, attempts, "lockedUntil");


--
-- Name: LoginAttempt_userId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "LoginAttempt_userId_key" ON public."LoginAttempt" USING btree ("userId");


--
-- Name: RegistrationAttempt_ip_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "RegistrationAttempt_ip_createdAt_idx" ON public."RegistrationAttempt" USING btree (ip, "createdAt");


--
-- Name: User_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "User_email_key" ON public."User" USING btree (email);


--
-- Name: User_username_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "User_username_key" ON public."User" USING btree (username);


--
-- Name: VerificationCode_email_type_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "VerificationCode_email_type_createdAt_idx" ON public."VerificationCode" USING btree (email, type, "createdAt");


--
-- Name: VideoCallParticipant_videoCallId_userId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "VideoCallParticipant_videoCallId_userId_key" ON public."VideoCallParticipant" USING btree ("videoCallId", "userId");


--
-- Name: abac_policies_name_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX abac_policies_name_key ON public.abac_policies USING btree (name);


--
-- Name: LoginAttempt LoginAttempt_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."LoginAttempt"
    ADD CONSTRAINT "LoginAttempt_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Task Task_assigneeId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Task"
    ADD CONSTRAINT "Task_assigneeId_fkey" FOREIGN KEY ("assigneeId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: Task Task_creatorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Task"
    ADD CONSTRAINT "Task_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: VideoCallParticipant VideoCallParticipant_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VideoCallParticipant"
    ADD CONSTRAINT "VideoCallParticipant_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VideoCallParticipant VideoCallParticipant_videoCallId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VideoCallParticipant"
    ADD CONSTRAINT "VideoCallParticipant_videoCallId_fkey" FOREIGN KEY ("videoCallId") REFERENCES public."VideoCall"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: VideoCall VideoCall_creatorId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VideoCall"
    ADD CONSTRAINT "VideoCall_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES public."User"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

