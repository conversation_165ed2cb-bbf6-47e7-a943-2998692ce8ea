"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

interface EmailChangeFormProps {
  currentEmail: string
  onSuccess: (newEmail: string) => void
}

export function EmailChangeForm({ currentEmail, onSuccess }: EmailChangeFormProps) {
  const [open, setOpen] = useState(false)
  const [newEmail, setNewEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [step, setStep] = useState<"input" | "verify">("input")

  // 发送验证码
  const handleSendCode = async () => {
    if (!newEmail) {
      toast({
        title: "请输入新邮箱",
        variant: "destructive",
      })
      return
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)) {
      toast({
        title: "邮箱格式不正确",
        variant: "destructive",
      })
      return
    }

    if (newEmail === currentEmail) {
      toast({
        title: "新邮箱不能与当前邮箱相同",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSendingCode(true)
      const response = await fetch("/api/user/send-email-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email: newEmail }),
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: "验证码已发送",
          description: "请查收您的邮箱",
          variant: "success",
        })
        setStep("verify")
        // 开始倒计时
        setCountdown(60)
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)
      } else {
        toast({
          title: "发送验证码失败",
          description: data.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("发送验证码错误:", error)
      toast({
        title: "发送验证码失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSendingCode(false)
    }
  }

  // 验证并修改邮箱
  const handleVerifyAndChange = async () => {
    if (!verificationCode) {
      toast({
        title: "请输入验证码",
        variant: "destructive",
      })
      return
    }

    try {
      setIsVerifying(true)
      const response = await fetch("/api/user/verify-email-change", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: newEmail,
          code: verificationCode,
        }),
      })

      const data = await response.json()
      if (data.success) {
        toast({
          title: "邮箱修改成功",
          variant: "success",
        })
        setOpen(false)
        // 重置表单
        setNewEmail("")
        setVerificationCode("")
        setStep("input")
        // 回调通知父组件
        onSuccess(newEmail)
      } else {
        toast({
          title: "验证失败",
          description: data.message || "请检查验证码是否正确",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("验证邮箱错误:", error)
      toast({
        title: "验证失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsVerifying(false)
    }
  }

  // 关闭对话框时重置状态
  const handleOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      setNewEmail("")
      setVerificationCode("")
      setStep("input")
    }
  }

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setOpen(true)}
        className="ml-2 h-8 px-2 text-blue-600 border-blue-200 hover:bg-blue-50"
      >
        修改
      </Button>

      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>修改邮箱</DialogTitle>
            <DialogDescription>
              {step === "input"
                ? "请输入新的邮箱地址，我们将向该邮箱发送验证码"
                : "请输入您收到的验证码以完成邮箱修改"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {step === "input" ? (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="current-email">当前邮箱</Label>
                  <Input
                    id="current-email"
                    value={currentEmail}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="new-email">新邮箱</Label>
                  <Input
                    id="new-email"
                    type="email"
                    placeholder="请输入新邮箱"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                    disabled={isSendingCode}
                  />
                </div>
              </>
            ) : (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="new-email">新邮箱</Label>
                  <Input
                    id="new-email"
                    type="email"
                    value={newEmail}
                    disabled
                    className="bg-gray-50"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="verification-code">验证码</Label>
                  <div className="flex gap-2">
                    <Input
                      id="verification-code"
                      placeholder="请输入验证码"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value)}
                      disabled={isVerifying}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleSendCode}
                      disabled={isSendingCode || countdown > 0}
                      className="whitespace-nowrap"
                    >
                      {isSendingCode ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : countdown > 0 ? (
                        `${countdown}秒后重发`
                      ) : (
                        "重新发送"
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                if (step === "verify") {
                  setStep("input")
                } else {
                  setOpen(false)
                }
              }}
            >
              {step === "verify" ? "返回" : "取消"}
            </Button>
            {step === "input" ? (
              <Button onClick={handleSendCode} disabled={isSendingCode}>
                {isSendingCode ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    发送中...
                  </>
                ) : (
                  "发送验证码"
                )}
              </Button>
            ) : (
              <Button onClick={handleVerifyAndChange} disabled={isVerifying}>
                {isVerifying ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    验证中...
                  </>
                ) : (
                  "确认修改"
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
