"use client"

import { useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Loader2 } from "lucide-react"
import { useNotificationSettings } from "../hooks/useNotificationSettings"

export function NotificationSettings() {
  const {
    notificationSettings,
    setNotificationSettings,
    savingNotificationSettings,
    fetchNotificationSettings,
    handleChannelChange
  } = useNotificationSettings()

  useEffect(() => {
    fetchNotificationSettings()
  }, [])

  return (
    <Card className="border-none shadow-md max-w-4xl mx-auto">
      <CardHeader className="bg-white border-b">
        <CardTitle className="text-xl font-bold text-blue-800">通知设置</CardTitle>
        <CardDescription>管理您接收通知的方式和偏好</CardDescription>
      </CardHeader>
      <CardContent className="pt-8 pb-6 bg-white">
        <div className="max-w-2xl mx-auto space-y-8">
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-100 flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-lg font-medium text-blue-800">邮件通知</Label>
              <p className="text-sm text-blue-700">
                通过邮件接收系统通知，及时了解重要信息
              </p>
            </div>
            <Switch
              checked={notificationSettings.emailEnabled}
              onCheckedChange={(checked) => {
                const saveSettings = async () => {
                  try {
                    const success = await handleChannelChange('email', checked)
                    if (!success) {
                      // 恢复原来的设置
                      setNotificationSettings(prev => ({
                        ...prev,
                        emailEnabled: !checked
                      }));
                    }
                  } catch (error: any) {
                    console.error('保存通知设置失败:', error);
                  }
                };
                saveSettings();
              }}
              disabled={savingNotificationSettings}
              className="data-[state=checked]:bg-blue-600"
            />
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-100 flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Label className="text-lg font-medium text-yellow-800">短信通知</Label>
                <span className="text-xs px-2 py-1 rounded-full bg-yellow-200 text-yellow-800 font-medium">
                  即将上线
                </span>
              </div>
              <p className="text-sm text-yellow-700">
                通过短信接收重要通知，确保重要信息不遗漏
              </p>
            </div>
            <Switch
              checked={false}
              disabled={true}
              className="opacity-50"
            />
          </div>

          <div className="bg-green-50 p-6 rounded-lg border border-green-100 flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-lg font-medium text-green-800">应用内通知</Label>
              <p className="text-sm text-green-700">
                在系统内接收通知，便于实时查看和处理
              </p>
            </div>
            <Switch
              checked={notificationSettings.appEnabled}
              onCheckedChange={(checked) => {
                const saveSettings = async () => {
                  try {
                    const success = await handleChannelChange('app', checked)
                    if (!success) {
                      // 恢复原来的设置
                      setNotificationSettings(prev => ({
                        ...prev,
                        appEnabled: !checked
                      }));
                    }
                  } catch (error: any) {
                    console.error('保存通知设置失败:', error);
                  }
                };
                saveSettings();
              }}
              disabled={savingNotificationSettings}
              className="data-[state=checked]:bg-green-600"
            />
          </div>

          {savingNotificationSettings && (
            <div className="flex items-center justify-center py-4 bg-gray-50 rounded-lg border border-gray-100">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="ml-3 text-base font-medium text-blue-600">正在保存设置...</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
