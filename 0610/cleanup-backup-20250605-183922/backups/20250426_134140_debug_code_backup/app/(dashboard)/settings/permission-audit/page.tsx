"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "sonner"
import { RefreshCw, Download, Search, Calendar } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { zhCN } from "date-fns/locale"

interface PermissionAuditLog {
  id: string
  userId: string
  action: string
  module: string
  resourceId: string
  resourceType: string
  details: any
  ipAddress: string
  userAgent: string
  createdAt: string
  user: {
    id: string
    username: string
    name: string
    email: string
    roleCode: string
  }
}

interface QueryResult {
  logs: PermissionAuditLog[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export default function PermissionAuditPage() {
  const [logs, setLogs] = useState<PermissionAuditLog[]>([])
  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [loading, setLoading] = useState(true)

  // 筛选条件
  const [userId, setUserId] = useState("")
  const [action, setAction] = useState("")
  const [targetType, setTargetType] = useState("")
  const [startDate, setStartDate] = useState<Date | undefined>()
  const [endDate, setEndDate] = useState<Date | undefined>()

  // 加载审计日志
  const loadLogs = async (pageNum = page) => {
    try {
      setLoading(true)

      // 构建查询参数
      const params = new URLSearchParams({
        page: pageNum.toString(),
        pageSize: pageSize.toString(),
      })

      if (userId) params.append("userId", userId)
      if (action) params.append("action", action)
      if (targetType) params.append("targetType", targetType)
      if (startDate) params.append("startDate", startDate.toISOString())
      if (endDate) params.append("endDate", endDate.toISOString())

      const response = await fetch(`/api/admin/permission-audit?${params.toString()}`)
      const data = await response.json()

      if (data.success) {
        setLogs(data.data.logs)
        setTotal(data.data.total)
        setPage(data.data.page)
        setTotalPages(data.data.totalPages)
      } else {
        toast.error(data.message || "加载权限审计日志失败")
      }
    } catch (error) {
      console.error("加载权限审计日志失败:", error)
      toast.error("加载权限审计日志失败")
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadLogs()
  }, [])

  // 处理分页
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage)
      loadLogs(newPage)
    }
  }

  // 处理搜索
  const handleSearch = () => {
    setPage(1)
    loadLogs(1)
  }

  // 处理重置
  const handleReset = () => {
    setUserId("")
    setAction("")
    setTargetType("")
    setStartDate(undefined)
    setEndDate(undefined)
    setPage(1)
    loadLogs(1)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "yyyy-MM-dd HH:mm:ss", { locale: zhCN })
  }

  // 格式化操作类型
  const formatAction = (action: string) => {
    if (action === "permission_add") return "添加权限"
    if (action === "permission_remove") return "移除权限"
    if (action === "permission_update") return "更新权限"
    if (action === "permission_view") return "查看权限"
    return action
  }

  // 格式化目标类型
  const formatTargetType = (type: string) => {
    if (type === "role") return "角色"
    if (type === "user") return "用户"
    if (type === "menu") return "菜单"
    if (type === "resource") return "资源"
    if (type === "operation") return "操作"
    return type
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>权限审计日志</CardTitle>
              <CardDescription>
                查看系统权限变更历史记录
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={() => loadLogs()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                刷新
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 筛选条件 */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div>
              <Input
                placeholder="用户ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>
            <div>
              <Select value={action} onValueChange={setAction}>
                <SelectTrigger>
                  <SelectValue placeholder="操作类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="permission_add">添加权限</SelectItem>
                  <SelectItem value="permission_remove">移除权限</SelectItem>
                  <SelectItem value="permission_update">更新权限</SelectItem>
                  <SelectItem value="permission_view">查看权限</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={targetType} onValueChange={setTargetType}>
                <SelectTrigger>
                  <SelectValue placeholder="目标类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="role">角色</SelectItem>
                  <SelectItem value="user">用户</SelectItem>
                  <SelectItem value="menu">菜单</SelectItem>
                  <SelectItem value="resource">资源</SelectItem>
                  <SelectItem value="operation">操作</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <div className="grid grid-cols-2 gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "yyyy-MM-dd") : "开始日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "yyyy-MM-dd") : "结束日期"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                搜索
              </Button>
              <Button variant="outline" onClick={handleReset}>
                重置
              </Button>
            </div>
          </div>

          {/* 日志列表 */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>操作时间</TableHead>
                  <TableHead>操作用户</TableHead>
                  <TableHead>操作类型</TableHead>
                  <TableHead>目标类型</TableHead>
                  <TableHead>目标ID</TableHead>
                  <TableHead>详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : logs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      暂无数据
                    </TableCell>
                  </TableRow>
                ) : (
                  logs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="font-mono text-xs">
                        {formatDate(log.createdAt)}
                      </TableCell>
                      <TableCell>
                        {log.user?.name || log.user?.username || "未知用户"}
                      </TableCell>
                      <TableCell>
                        <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                          {formatAction(log.action)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                          {formatTargetType(log.resourceType)}
                        </span>
                      </TableCell>
                      <TableCell className="font-mono text-xs">
                        {log.resourceId || "-"}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            toast.info(
                              <pre className="text-xs overflow-auto max-h-80">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>,
                              {
                                duration: 10000,
                              }
                            )
                          }}
                        >
                          查看
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                共 {total} 条记录，第 {page}/{totalPages} 页
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(1)}
                  disabled={page === 1}
                >
                  首页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page - 1)}
                  disabled={page === 1}
                >
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(page + 1)}
                  disabled={page === totalPages}
                >
                  下一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(totalPages)}
                  disabled={page === totalPages}
                >
                  末页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
