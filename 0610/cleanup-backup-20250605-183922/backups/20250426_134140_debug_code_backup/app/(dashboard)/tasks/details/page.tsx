"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon, Download, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { toast } from "@/components/ui/use-toast"

// 示例数据 - 外呼详情列表
const callDetails = [
  {
    id: "1",
    taskName: "汽车清洗促销活动",
    type: "5G视频通知",
    content: "汽车清洗",
    customerName: "张三",
    phoneNumber: "138****1234",
    connectionType: "视频接通",
    startTime: "2023-05-02 09:15:23",
    endTime: "2023-05-02 09:18:45",
    duration: "3分22秒",
    ringTime: "12秒",
    intention: "A",
  },
  {
    id: "2",
    taskName: "汽车清洗促销活动",
    type: "5G视频通知",
    content: "汽车清洗",
    customerName: "李四",
    phoneNumber: "139****5678",
    connectionType: "语音接通",
    startTime: "2023-05-02 09:25:10",
    endTime: "2023-05-02 09:27:35",
    duration: "2分25秒",
    ringTime: "8秒",
    intention: "B",
  },
  {
    id: "3",
    taskName: "维修大促活动",
    type: "5G视频互动",
    content: "维修大促",
    customerName: "王五",
    phoneNumber: "137****9012",
    connectionType: "视频接通",
    startTime: "2023-05-02 10:05:33",
    endTime: "2023-05-02 10:12:18",
    duration: "6分45秒",
    ringTime: "5秒",
    intention: "A",
  },
  {
    id: "4",
    taskName: "维修大促活动",
    type: "5G视频互动",
    content: "维修大促",
    customerName: "赵六",
    phoneNumber: "136****3456",
    connectionType: "语音接通",
    startTime: "2023-05-02 10:30:45",
    endTime: "2023-05-02 10:32:15",
    duration: "1分30秒",
    ringTime: "15秒",
    intention: "C",
  },
  {
    id: "5",
    taskName: "贴膜大促活动",
    type: "5G语音通话",
    content: "贴膜大促",
    customerName: "钱七",
    phoneNumber: "135****7890",
    connectionType: "语音接通",
    startTime: "2023-05-02 11:05:22",
    endTime: "2023-05-02 11:08:45",
    duration: "3分23秒",
    ringTime: "10秒",
    intention: "B",
  },
  {
    id: "6",
    taskName: "汽车保养提醒",
    type: "5G视频通知",
    content: "汽车保养",
    customerName: "孙八",
    phoneNumber: "134****2345",
    connectionType: "视频接通",
    startTime: "2023-05-03 09:10:15",
    endTime: "2023-05-03 09:14:30",
    duration: "4分15秒",
    ringTime: "7秒",
    intention: "A",
  },
  {
    id: "7",
    taskName: "轮胎更换活动",
    type: "5G视频互动",
    content: "轮胎更换",
    customerName: "周九",
    phoneNumber: "133****6789",
    connectionType: "视频接通",
    startTime: "2023-05-03 10:20:05",
    endTime: "2023-05-03 10:25:45",
    duration: "5分40秒",
    ringTime: "9秒",
    intention: "B",
  },
  {
    id: "8",
    taskName: "车险续保提醒",
    type: "5G语音通话",
    content: "车险续保",
    customerName: "吴十",
    phoneNumber: "132****1234",
    connectionType: "语音接通",
    startTime: "2023-05-03 11:30:10",
    endTime: "2023-05-03 11:32:40",
    duration: "2分30秒",
    ringTime: "11秒",
    intention: "C",
  },
  {
    id: "9",
    taskName: "汽车美容活动",
    type: "5G视频通知",
    content: "汽车美容",
    customerName: "郑十一",
    phoneNumber: "131****5678",
    connectionType: "视频接通",
    startTime: "2023-05-04 09:05:25",
    endTime: "2023-05-04 09:10:15",
    duration: "4分50秒",
    ringTime: "6秒",
    intention: "A",
  },
  {
    id: "10",
    taskName: "车辆年检提醒",
    type: "5G语音通话",
    content: "车辆年检",
    customerName: "王十二",
    phoneNumber: "130****9012",
    connectionType: "语音接通",
    startTime: "2023-05-04 10:15:30",
    endTime: "2023-05-04 10:18:45",
    duration: "3分15秒",
    ringTime: "8秒",
    intention: "B",
  },
  {
    id: "11",
    taskName: "新车上市通知",
    type: "5G视频通知",
    content: "新车上市",
    customerName: "刘十三",
    phoneNumber: "186****4567",
    connectionType: "视频接通",
    startTime: "2023-05-05 08:30:10",
    endTime: "2023-05-05 08:35:45",
    duration: "5分35秒",
    ringTime: "6秒",
    intention: "A",
  },
  {
    id: "12",
    taskName: "新车上市通知",
    type: "5G视频通知",
    content: "新车上市",
    customerName: "陈十四",
    phoneNumber: "187****5678",
    connectionType: "语音接通",
    startTime: "2023-05-05 09:15:20",
    endTime: "2023-05-05 09:17:10",
    duration: "1分50秒",
    ringTime: "10秒",
    intention: "C",
  },
  {
    id: "13",
    taskName: "新车上市通知",
    type: "5G视频通知",
    content: "新车上市",
    customerName: "杨十五",
    phoneNumber: "188****6789",
    connectionType: "视频接通",
    startTime: "2023-05-05 10:05:30",
    endTime: "2023-05-05 10:12:15",
    duration: "6分45秒",
    ringTime: "5秒",
    intention: "A",
  },
  {
    id: "14",
    taskName: "夏季空调检查",
    type: "5G视频互动",
    content: "空调检查",
    customerName: "赵十六",
    phoneNumber: "189****7890",
    connectionType: "视频接通",
    startTime: "2023-05-06 09:10:15",
    endTime: "2023-05-06 09:15:45",
    duration: "5分30秒",
    ringTime: "8秒",
    intention: "A",
  },
  {
    id: "15",
    taskName: "夏季空调检查",
    type: "5G视频互动",
    content: "空调检查",
    customerName: "钱十七",
    phoneNumber: "135****8901",
    connectionType: "语音接通",
    startTime: "2023-05-06 09:30:20",
    endTime: "2023-05-06 09:32:50",
    duration: "2分30秒",
    ringTime: "12秒",
    intention: "B",
  },
  {
    id: "16",
    taskName: "夏季空调检查",
    type: "5G视频互动",
    content: "空调检查",
    customerName: "孙十八",
    phoneNumber: "136****9012",
    connectionType: "视频接通",
    startTime: "2023-05-06 10:15:30",
    endTime: "2023-05-06 10:21:15",
    duration: "5分45秒",
    ringTime: "7秒",
    intention: "A",
  },
  {
    id: "17",
    taskName: "雨季防水检测",
    type: "5G语音通话",
    content: "防水检测",
    customerName: "李十九",
    phoneNumber: "137****0123",
    connectionType: "语音接通",
    startTime: "2023-05-07 09:05:10",
    endTime: "2023-05-07 09:08:40",
    duration: "3分30秒",
    ringTime: "9秒",
    intention: "B",
  },
  {
    id: "18",
    taskName: "雨季防水检测",
    type: "5G语音通话",
    content: "防水检测",
    customerName: "周二十",
    phoneNumber: "138****1234",
    connectionType: "语音接通",
    startTime: "2023-05-07 09:45:15",
    endTime: "2023-05-07 09:47:45",
    duration: "2分30秒",
    ringTime: "10秒",
    intention: "C",
  },
  {
    id: "19",
    taskName: "雨季防水检测",
    type: "5G语音通话",
    content: "防水检测",
    customerName: "吴二一",
    phoneNumber: "139****2345",
    connectionType: "语音接通",
    startTime: "2023-05-07 10:30:20",
    endTime: "2023-05-07 10:33:50",
    duration: "3分30秒",
    ringTime: "8秒",
    intention: "B",
  },
  {
    id: "20",
    taskName: "汽车电瓶检查",
    type: "5G视频通知",
    content: "电瓶检查",
    customerName: "郑二二",
    phoneNumber: "130****3456",
    connectionType: "视频接通",
    startTime: "2023-05-08 09:10:15",
    endTime: "2023-05-08 09:15:45",
    duration: "5分30秒",
    ringTime: "6秒",
    intention: "A",
  },
  {
    id: "21",
    taskName: "汽车电瓶检查",
    type: "5G视频通知",
    content: "电瓶检查",
    customerName: "王二三",
    phoneNumber: "131****4567",
    connectionType: "语音接通",
    startTime: "2023-05-08 09:45:20",
    endTime: "2023-05-08 09:48:50",
    duration: "3分30秒",
    ringTime: "11秒",
    intention: "B",
  },
  {
    id: "22",
    taskName: "汽车电瓶检查",
    type: "5G视频通知",
    content: "电瓶检查",
    customerName: "冯二四",
    phoneNumber: "132****5678",
    connectionType: "视频接通",
    startTime: "2023-05-08 10:30:25",
    endTime: "2023-05-08 10:36:55",
    duration: "6分30秒",
    ringTime: "5秒",
    intention: "A",
  },
  {
    id: "23",
    taskName: "轮胎平衡检测",
    type: "5G视频互动",
    content: "轮胎平衡",
    customerName: "陈二五",
    phoneNumber: "133****6789",
    connectionType: "视频接通",
    startTime: "2023-05-09 09:05:10",
    endTime: "2023-05-09 09:11:40",
    duration: "6分30秒",
    ringTime: "7秒",
    intention: "A",
  },
  {
    id: "24",
    taskName: "轮胎平衡检测",
    type: "5G视频互动",
    content: "轮胎平衡",
    customerName: "楚二六",
    phoneNumber: "134****7890",
    connectionType: "语音接通",
    startTime: "2023-05-09 09:45:15",
    endTime: "2023-05-09 09:48:45",
    duration: "3分30秒",
    ringTime: "10秒",
    intention: "B",
  },
  {
    id: "25",
    taskName: "轮胎平衡检测",
    type: "5G视频互动",
    content: "轮胎平衡",
    customerName: "魏二七",
    phoneNumber: "135****8901",
    connectionType: "视频接通",
    startTime: "2023-05-09 10:30:20",
    endTime: "2023-05-09 10:36:50",
    duration: "6分30秒",
    ringTime: "6秒",
    intention: "A",
  },
  {
    id: "26",
    taskName: "刹车系统检查",
    type: "5G语音通话",
    content: "刹车检查",
    customerName: "蒋二八",
    phoneNumber: "136****9012",
    connectionType: "语音接通",
    startTime: "2023-05-10 09:10:15",
    endTime: "2023-05-10 09:13:45",
    duration: "3分30秒",
    ringTime: "9秒",
    intention: "B",
  },
  {
    id: "27",
    taskName: "刹车系统检查",
    type: "5G语音通话",
    content: "刹车检查",
    customerName: "沈二九",
    phoneNumber: "137****0123",
    connectionType: "语音接通",
    startTime: "2023-05-10 09:45:20",
    endTime: "2023-05-10 09:48:50",
    duration: "3分30秒",
    ringTime: "8秒",
    intention: "C",
  },
  {
    id: "28",
    taskName: "刹车系统检查",
    type: "5G语音通话",
    content: "刹车检查",
    customerName: "韩三十",
    phoneNumber: "138****1234",
    connectionType: "语音接通",
    startTime: "2023-05-10 10:30:25",
    endTime: "2023-05-10 10:33:55",
    duration: "3分30秒",
    ringTime: "10秒",
    intention: "B",
  },
  {
    id: "29",
    taskName: "发动机保养提醒",
    type: "5G视频通知",
    content: "发动机保养",
    customerName: "杨三一",
    phoneNumber: "139****2345",
    connectionType: "视频接通",
    startTime: "2023-05-11 09:05:10",
    endTime: "2023-05-11 09:11:40",
    duration: "6分30秒",
    ringTime: "5秒",
    intention: "A",
  },
  {
    id: "30",
    taskName: "发动机保养提醒",
    type: "5G视频通知",
    content: "发动机保养",
    customerName: "朱三二",
    phoneNumber: "130****3456",
    connectionType: "语音接通",
    startTime: "2023-05-11 09:45:15",
    endTime: "2023-05-11 09:48:45",
    duration: "3分30秒",
    ringTime: "11秒",
    intention: "B",
  },
]

export default function TaskDetailsPage() {
  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [phoneNumber, setPhoneNumber] = useState("")
  const [connectionType, setConnectionType] = useState("")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // 过滤外呼详情
  const [filteredDetails, setFilteredDetails] = useState([])
  const [totalPages, setTotalPages] = useState(1)
  const [isLoading, setIsLoading] = useState(false)

  // Update the useEffect hook to initialize with demo data
  useEffect(() => {
    // 初始化时设置演示数据
    setFilteredDetails(callDetails)
    setTotalPages(Math.ceil(callDetails.length / itemsPerPage))
  }, [])

  // Add a function to fetch call details
  const fetchCallDetails = async () => {
    setIsLoading(true)
    try {
      // 构建查询参数
      const params: any = {
        page: currentPage,
        pageSize: itemsPerPage,
      }

      // 添加筛选条件
      if (taskName) params.taskName = taskName
      if (content && content !== "all") params.content = content
      if (type && type !== "all") params.type = type
      if (startDateRange.from) params.startDate = format(startDateRange.from, "yyyy-MM-dd")
      if (startDateRange.to) params.endDate = format(startDateRange.to, "yyyy-MM-dd")
      if (phoneNumber) params.phone = phoneNumber
      if (connectionType && connectionType !== "all") params.connectionType = connectionType

      // 使用演示数据进行筛选
      let filteredData = [...callDetails]

      // 应用筛选条件
      if (taskName) {
        filteredData = filteredData.filter((detail) => detail.taskName.includes(taskName))
      }

      if (content && content !== "all") {
        filteredData = filteredData.filter((detail) => detail.content === content)
      }

      if (type && type !== "all") {
        filteredData = filteredData.filter((detail) => detail.type === type)
      }

      if (phoneNumber) {
        filteredData = filteredData.filter((detail) => detail.phoneNumber.includes(phoneNumber))
      }

      if (connectionType && connectionType !== "all") {
        filteredData = filteredData.filter((detail) => detail.connectionType === connectionType)
      }

      // 更新状态
      setFilteredDetails(filteredData)
      setTotalPages(Math.ceil(filteredData.length / itemsPerPage))
    } catch (error) {
      console.error("获取外呼详情失败:", error)
      toast({
        title: "获取外呼详情失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 计算总页数
  // const totalPages = Math.ceil(filteredDetails.length / itemsPerPage)

  // 获取当前页的数据
  const currentDetails = filteredDetails.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  // 导出外呼详情为CSV或Excel
  const exportToCSV = (format = "csv") => {
    const headers = [
      "外呼任务名称",
      "外呼类型",
      "视频外呼内容",
      "客户名称",
      "被叫号码",
      "接通类型",
      "通话开始时间",
      "通话结束时间",
      "通话时长",
      "通话振铃时间",
      "意向程度",
    ]
    const csvData = [
      headers.join(","),
      ...filteredDetails.map((detail) =>
        [
          detail.taskName,
          detail.type,
          detail.content,
          detail.customerName,
          detail.phoneNumber,
          detail.connectionType,
          detail.startTime,
          detail.endTime,
          detail.duration,
          detail.ringTime,
          detail.intention,
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `外呼详情_${new Date().toISOString().split("T")[0]}.${format}`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 获取意向程度颜色
  const getIntentionColor = (intention: string) => {
    switch (intention) {
      case "A":
        return "bg-green-100 text-green-800"
      case "B":
        return "bg-blue-100 text-blue-800"
      case "C":
        return "bg-yellow-100 text-yellow-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Update the handleSearch function to call fetchCallDetails
  const handleSearch = () => {
    setCurrentPage(1) // 重置到第一页
    fetchCallDetails()
  }

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading="外呼详情" text="查看外呼任务的详细通话记录" />
        <Card>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音通话">5G语音通话</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => setStartDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => setCreateDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone-number">用户号码</Label>
                <Input
                  id="phone-number"
                  placeholder="输入用户号码"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="connection-type">接通类型</Label>
                <Select value={connectionType} onValueChange={setConnectionType}>
                  <SelectTrigger id="connection-type">
                    <SelectValue placeholder="选择接通类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="视频接通">视频接通</SelectItem>
                    <SelectItem value="语音接通">语音接通</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                查询
              </Button>
              <div className="relative">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button className="bg-green-600 hover:bg-green-700">
                      <Download className="mr-2 h-4 w-4" />
                      导出表格
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48">
                    <div className="flex flex-col space-y-2">
                      <Button variant="ghost" className="justify-start" onClick={() => exportToCSV("csv")}>
                        导出为 CSV
                      </Button>
                      <Button variant="ghost" className="justify-start" onClick={() => exportToCSV("xlsx")}>
                        导出为 Excel
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 修改表格部分，使用增强的表格样式 */}
        <div className="rounded-md border mt-6">
          <Table className="table-enhanced">
            <TableHeader>
              <TableRow>
                <TableHead>外呼任务名称</TableHead>
                <TableHead>外呼类型</TableHead>
                <TableHead>视频外呼内容</TableHead>
                <TableHead>客户名称</TableHead>
                <TableHead>被叫号码</TableHead>
                <TableHead>接通类型</TableHead>
                <TableHead>通话开始时间</TableHead>
                <TableHead>通话结束时间</TableHead>
                <TableHead>通话时长</TableHead>
                <TableHead>通话振铃时间</TableHead>
                <TableHead>意向程度</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentDetails.map((detail) => (
                <TableRow key={detail.id}>
                  <TableCell className="font-medium">{detail.taskName}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex px-2 py-1 rounded-full text-xs ${
                        detail.type === "5G视频通知"
                          ? "bg-green-100 text-green-800"
                          : detail.type === "5G视频互动"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {detail.type}
                    </span>
                  </TableCell>
                  <TableCell>{detail.content}</TableCell>
                  <TableCell className="font-medium">{detail.customerName}</TableCell>
                  <TableCell>{detail.phoneNumber}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex px-2 py-1 rounded-full text-xs ${
                        detail.connectionType === "视频接通"
                          ? "bg-green-100 text-green-800"
                          : "bg-blue-100 text-blue-800"
                      }`}
                    >
                      {detail.connectionType}
                    </span>
                  </TableCell>
                  <TableCell>{detail.startTime}</TableCell>
                  <TableCell>{detail.endTime}</TableCell>
                  <TableCell className="font-medium text-blue-600">{detail.duration}</TableCell>
                  <TableCell>{detail.ringTime}</TableCell>
                  <TableCell>
                    <span
                      className={
                        detail.intention === "A"
                          ? "status-active"
                          : detail.intention === "B"
                            ? "status-processing"
                            : "status-pending"
                      }
                    >
                      {detail.intention}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
              {currentDetails.length === 0 && (
                <TableRow>
                  <TableCell colSpan={11} className="h-24 text-center">
                    没有找到记录
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页控件 */}
        {filteredDetails.length > 0 && (
          <div className="mt-4 flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                      className="cursor-pointer"
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>
    </>
  )
}

