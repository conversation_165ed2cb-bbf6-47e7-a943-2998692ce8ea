"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon, Play, Pause, Info, Trash2, Upload, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { getTasks } from "@/lib/api/services"
import type { Task } from "@/lib/api/interfaces"
import { videoCallService } from "@/lib/api/video-call-service"

// 如果需要扩展 Task 接口，可以这样做：
interface ExtendedTask extends Task {
  // 前端状态字段，用于UI显示
  uiStatus?: "pending" | "running" | "paused" | "completed"
}

// 2. Add router import at the top of the file
// Add this import near the other imports
import { useRouter } from "next/navigation"

// 引入 startTask 和 pauseTask API
import { startTask, pauseTask, getBotList } from "@/lib/api/video-call-service"

export default function TasksPage() {
  // 用户角色状态
  const { user } = useAuth()
  const userType = user?.role === "super" ? "admin" : "customer"

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // 对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)

  // 新建任务表单状态
  const [newTaskName, setNewTaskName] = useState("")
  const [newTaskContent, setNewTaskContent] = useState("")
  const [newTaskType, setNewTaskType] = useState("")
  const [newTaskStartTime, setNewTaskStartTime] = useState<Date | undefined>(undefined)
  const [newTaskResource, setNewTaskResource] = useState("")
  const [newTaskPhoneNumber, setNewTaskPhoneNumber] = useState("")
  const [newTaskSmsType, setNewTaskSmsType] = useState("")
  const [newTaskSmsTemplate, setNewTaskSmsTemplate] = useState("")
  const [newTaskSmsPhase, setNewTaskSmsPhase] = useState("1") // 短信发送时机，默认为拨打电话时
  const [newTaskCalloutMode, setNewTaskCalloutMode] = useState("1") // 起呼方式，默认为双向视频起呼
  const [showDateTimePicker, setShowDateTimePicker] = useState(false)
  const [selectedHour, setSelectedHour] = useState("00")
  const [selectedMinute, setSelectedMinute] = useState("00")
  const [selectedSecond, setSelectedSecond] = useState("00")
  const [activeTab, setActiveTab] = useState("manual")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [tasks, setTasks] = useState<ExtendedTask[]>([])
  const [totalTasks, setTotalTasks] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // Add a function to fetch Bot list for resources
  const [botList, setBotList] = useState<any[]>([])

  // 3. Add router initialization in the component
  // Add this near the other state declarations
  const router = useRouter()

  /**
   * 页面加载时获取任务列表
   */
  useEffect(() => {
    // Only fetch Bot list on initial load, not tasks
    fetchBotList()

    // Load demonstration data instead of fetching from API
    setIsLoading(true)
    setTasks(getDemoTasks())
    setTotalTasks(getDemoTasks().length)
    setTotalPages(Math.ceil(getDemoTasks().length / itemsPerPage))
    setIsLoading(false)
  }, [])

  /**
   * 获取任务列表
   * 调用API获取任务数据
   */
  const fetchTasks = async () => {
    setIsLoading(true)
    try {
      const response = await getTasks({
        page: currentPage,
        pageSize: itemsPerPage,
        search: taskName || undefined,
        type: type === "all" ? undefined : type,
        startDate: startDateRange.from ? format(startDateRange.from, "yyyy-MM-dd") : undefined,
        endDate: startDateRange.to ? format(startDateRange.to, "yyyy-MM-dd") : undefined,
      })

      if (response.success) {
        setTasks(response.data.list)
        setTotalTasks(response.data.total)
        setTotalPages(response.data.totalPages)
      } else {
        toast({
          title: "获取任务列表失败",
          description: response.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("获取任务列表失败:", error)
      toast({
        title: "获取任务列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取演示任务数据
   * 生成模拟任务数据用于展示
   */
  const getDemoTasks = (): ExtendedTask[] => {
    return [
      {
        id: "task-001",
        name: "汽车保养提醒活动",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-15 09:30:45",
        startTime: "2024-10-16 10:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-16 18:35:22",
        creator: "张经理",
      },
      {
        id: "task-002",
        name: "轮胎更换促销活动",
        type: "5G视频互动",
        content: "轮胎更换",
        importTime: "2024-10-14 14:22:10",
        startTime: "2024-10-15 09:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-15 17:45:33",
        creator: "李主管",
      },
      {
        id: "task-003",
        name: "车险续保通知",
        type: "5G语音通话",
        content: "车险续保",
        importTime: "2024-10-16 11:15:30",
        startTime: "2024-10-17 09:30:00",
        progress: 75,
        status: "外呼中",
        uiStatus: "running",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-004",
        name: "汽车美容服务推广",
        type: "5G视频通知",
        content: "汽车美容",
        importTime: "2024-10-13 16:40:22",
        startTime: "2024-10-14 10:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-14 19:20:15",
        creator: "赵主管",
      },
      {
        id: "task-005",
        name: "车辆年检提醒",
        type: "5G视频互动",
        content: "车辆年检",
        importTime: "2024-10-17 08:55:40",
        startTime: "2024-10-18 09:00:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-006",
        name: "汽车清洗优惠活动",
        type: "5G视频通知",
        content: "汽车清洗",
        importTime: "2024-10-12 10:30:15",
        startTime: "2024-10-13 09:00:00",
        progress: 100,
        status: "已完成",
        completionTime: "2024-10-13 18:15:40",
        creator: "李主管",
      },
      {
        id: "task-007",
        name: "维修大促活动通知",
        type: "5G视频互动",
        content: "维修大促",
        importTime: "2024-10-18 09:20:30",
        startTime: "2024-10-19 10:00:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-008",
        name: "贴膜大促活动推广",
        type: "5G语音通话",
        content: "贴膜大促",
        importTime: "2024-10-11 15:45:20",
        startTime: "2024-10-12 09:30:00",
        progress: 100,
        status: "已完成",
        completionTime: "2024-10-12 17:50:25",
        creator: "赵主管",
      },
      {
        id: "task-009",
        name: "冬季保养套餐推广",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-19 11:10:35",
        startTime: "2024-10-20 09:00:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-010",
        name: "雨刮器更换提醒",
        type: "5G视频互动",
        content: "维修大促",
        importTime: "2024-10-10 13:25:50",
        startTime: "2024-10-11 10:30:00",
        progress: 100,
        status: "已完成",
        completionTime: "2024-10-11 19:05:30",
        creator: "李主管",
      },
      {
        id: "task-011",
        name: "空调系统检查活动",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-20 08:40:15",
        startTime: "2024-10-21 09:30:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-012",
        name: "刹车系统检测活动",
        type: "5G语音通话",
        content: "维修大促",
        importTime: "2024-10-09 14:50:25",
        startTime: "2024-10-10 09:00:00",
        progress: 100,
        status: "已完成",
        completionTime: "2024-10-10 18:30:45",
        creator: "赵主管",
      },
      {
        id: "task-013",
        name: "新车上市通知",
        type: "5G视频互动",
        content: "汽车美容",
        importTime: "2024-10-21 10:15:40",
        startTime: "2024-10-22 10:00:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-014",
        name: "机油更换提醒",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-08 11:35:55",
        startTime: "2024-10-09 09:30:00",
        progress: 100,
        status: "已完成",
        completionTime: "2024-10-09 17:45:20",
        creator: "李主管",
      },
      {
        id: "task-015",
        name: "轮胎平衡检测活动",
        type: "5G语音通话",
        content: "轮胎更换",
        importTime: "2024-10-22 09:25:30",
        startTime: "2024-10-23 09:00:00",
        progress: 0,
        status: "待启动",
        completionTime: null,
        creator: "王经理",
      },
    ]
  }

  /**
   * 处理任务删除
   * 从演示数据中删除指定任务
   */
  const handleDeleteTask = async () => {
    if (!taskToDelete) return

    setIsLoading(true)
    try {
      // For demo data, remove the task from the local state
      const updatedTasks = tasks.filter((task) => task.id !== taskToDelete)
      setTasks(updatedTasks)
      setTotalTasks(updatedTasks.length)
      setTotalPages(Math.ceil(updatedTasks.length / itemsPerPage))

      toast({
        title: "删除成功",
        description: "任务已成功删除",
        variant: "success",
      })
    } catch (error) {
      console.error("删除任务失败:", error)
      toast({
        title: "删除任务失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setTaskToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  /**
   * 处理任务启动
   * 调用API启动指定任务或更新演示数据
   */
  const handleStartTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 调用 API 启动任务
      const response = await startTask(taskId)

      if (response.success) {
        // 更新本地任务状态
        const updatedTasks = tasks.map((task) => {
          if (task.id === taskId) {
            return {
              ...task,
              uiStatus: "running",
              progress: task.progress > 0 ? task.progress : 25, // 如果进度为0，设置为25%
            }
          }
          return task
        })

        setTasks(updatedTasks)

        // 显示成功提示，使用非透明背景
        toast({
          title: "启动成功",
          description: "任务已成功启动",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })
      } else {
        // 显示错误提示
        toast({
          title: "启动失败",
          description: response.message,
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("启动任务失败:", error)
      toast({
        title: "启动任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理任务暂停
   * 调用API暂停指定任务或更新演示数据
   */
  const handlePauseTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 调用 API 暂停任务
      const response = await pauseTask(taskId)

      if (response.success) {
        // 更新本地任务状态
        const updatedTasks = tasks.map((task) => {
          if (task.id === taskId) {
            return {
              ...task,
              uiStatus: "paused",
            }
          }
          return task
        })

        setTasks(updatedTasks)

        // 显示成功提示，使用非透明背景
        toast({
          title: "暂停成功",
          description: "任务已成功暂停",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })
      } else {
        // 显示错误提示
        toast({
          title: "暂停失败",
          description: response.message,
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("暂停任务失败:", error)
      toast({
        title: "暂停任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理新建任务
   * 调用API创建实际任务
   */
  const handleCreateTask = async () => {
    // Form validation (keep existing validation code)
    if (!newTaskName) {
      toast({
        title: "表单错误",
        description: "请输入任务名称",
        variant: "destructive",
      })
      return
    }

    if (!newTaskContent) {
      toast({
        title: "表单错误",
        description: "请选择外呼内容",
        variant: "destructive",
      })
      return
    }

    if (!newTaskType) {
      toast({
        title: "表单错误",
        description: "请选择外呼类型",
        variant: "destructive",
      })
      return
    }

    if (!newTaskStartTime) {
      toast({
        title: "表单错误",
        description: "请选择外呼开始时间",
        variant: "destructive",
      })
      return
    }

    if (!newTaskResource) {
      toast({
        title: "表单错误",
        description: "请选择外呼资源",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "manual" && !newTaskPhoneNumber) {
      toast({
        title: "表单错误",
        description: "请输入呼叫号码",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "import" && !importFile) {
      toast({
        title: "表单错误",
        description: "请选择导入文件",
        variant: "destructive",
      })
      return
    }

    // Format date and time
    let formattedDateTime = ""
    if (newTaskStartTime) {
      const date = format(newTaskStartTime, "yyyy-MM-dd")
      formattedDateTime = `${date} ${selectedHour}:${selectedMinute}:${selectedSecond}`
    }

    setIsLoading(true)

    try {
      // 处理电话号码
      let phoneNumbers: string[] = []

      if (activeTab === "manual") {
        // 处理手动输入的电话号码（支持中文逗号分隔）
        phoneNumbers = newTaskPhoneNumber.split(/[,，]/).map(phone => phone.trim()).filter(phone => phone)
      } else if (activeTab === "import" && importFile) {
        toast({
          title: "文件处理",
          description: "正在处理导入文件...",
          variant: "default",
        })

        try {
          // 读取文件内容
          const fileContent = await readFileContent(importFile);

          // 解析文件内容提取电话号码
          phoneNumbers = parsePhoneNumbers(fileContent, importFile.name);

          if (phoneNumbers.length === 0) {
            toast({
              title: "文件解析错误",
              description: "没有从文件中提取到有效的电话号码",
              variant: "destructive",
            })
            setIsLoading(false)
            return
          }

          // 显示提取到的电话号码数量
          toast({
            title: "文件解析成功",
            description: `已提取 ${phoneNumbers.length} 个电话号码`,
            variant: "default",
          })
        } catch (error) {
          console.error("文件解析错误:", error);
          toast({
            title: "文件解析错误",
            description: error instanceof Error ? error.message : "文件解析失败",
            variant: "destructive",
          })
          setIsLoading(false)
          return
        }
      }

      // 确保有电话号码
      if (phoneNumbers.length === 0) {
        toast({
          title: "表单错误",
          description: "没有有效的电话号码",
          variant: "destructive",
        })
        setIsLoading(false)
        return
      }

      // 准备API调用参数
      const taskData = {
        name: newTaskName,
        content: newTaskContent,
        callType: newTaskType,
        startTime: formattedDateTime,
        resource: newTaskResource,
        phoneNumbers: phoneNumbers,
        smsType: newTaskSmsType,
        smsTemplate: newTaskSmsTemplate,
        smsPhase: parseInt(newTaskSmsPhase), // 短信发送时机
        calloutMode: parseInt(newTaskCalloutMode), // 起呼方式
        userId: user?.id, // 添加用户ID用于权限控制
      }

      // 调用API创建任务
      const response = await videoCallService.importTask(taskData)

      if (response.success) {
        // 创建成功，添加到任务列表
        const newTask: ExtendedTask = {
          id: response.data?.id || `task-${Date.now()}`,
          name: newTaskName,
          type: newTaskType,
          content: newTaskContent,
          importTime: format(new Date(), "yyyy-MM-dd HH:mm:ss"),
          startTime: formattedDateTime,
          progress: 0,
          status: "待启动",
          uiStatus: "pending",
          completionTime: null,
          creator: user?.name || "系统用户",
        }

        // 添加新任务到现有任务
        const updatedTasks = [newTask, ...tasks]
        setTasks(updatedTasks)
        setTotalTasks(updatedTasks.length)
        setTotalPages(Math.ceil(updatedTasks.length / itemsPerPage))

        // 显示成功消息
        toast({
          title: "创建成功",
          description: response.message || "任务已成功创建",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })

        // 重置表单并关闭对话框
        resetNewTaskForm()
        setShowImportDialog(false)
      } else {
        // 创建失败
        toast({
          title: "创建失败",
          description: response.message || "任务创建失败，请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("任务创建错误:", error)
      toast({
        title: "创建失败",
        description: "任务创建过程中发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchBotList = async () => {
    try {
      const response = await getBotList()
      if (response.success) {
        setBotList(response.data)
      } else {
        console.error("获取Bot列表失败:", response.message)
      }
    } catch (error) {
      console.error("获取Bot列表错误:", error)
    }
  }

  /**
   * 重置新建任务表单
   * 清空所有表单字段
   */
  const resetNewTaskForm = () => {
    setNewTaskName("")
    setNewTaskContent("")
    setNewTaskType("")
    setNewTaskStartTime(undefined)
    setNewTaskResource("")
    setNewTaskPhoneNumber("")
    setNewTaskSmsType("")
    setNewTaskSmsTemplate("")
    setNewTaskSmsPhase("1") // 重置短信发送时机
    setNewTaskCalloutMode("1") // 重置起呼方式
    setSelectedHour("00")
    setSelectedMinute("00")
    setSelectedSecond("00")
    setActiveTab("manual")
    setImportFile(null)
  }

  /**
   * 处理文件选择
   * 当用户选择导入文件时触发
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0])
    }
  }

  /**
   * 处理搜索
   * 根据搜索条件过滤演示数据
   */
  const handleSearch = () => {
    setIsLoading(true)

    // Get all demo tasks
    const allDemoTasks = getDemoTasks()

    // Filter based on search criteria
    const filteredTasks = allDemoTasks.filter((task) => {
      // Filter by task name
      if (taskName && !task.name.includes(taskName)) {
        return false
      }

      // Filter by content
      if (content && content !== "all" && task.content !== content) {
        return false
      }

      // Filter by type
      if (type && type !== "all" && task.type !== type) {
        return false
      }

      // Filter by start date range
      if (startDateRange.from || startDateRange.to) {
        const taskStartDate = new Date(task.startTime)

        if (startDateRange.from && taskStartDate < startDateRange.from) {
          return false
        }

        if (startDateRange.to && taskStartDate > startDateRange.to) {
          return false
        }
      }

      // Filter by create date range
      if (createDateRange.from || createDateRange.to) {
        const taskImportDate = new Date(task.importTime)

        if (createDateRange.from && taskImportDate < createDateRange.from) {
          return false
        }

        if (createDateRange.to && taskImportDate > createDateRange.to) {
          return false
        }
      }

      return true
    })

    // Update state with filtered tasks
    setTasks(filteredTasks)
    setTotalTasks(filteredTasks.length)
    setTotalPages(Math.ceil(filteredTasks.length / itemsPerPage))
    setCurrentPage(1)
    setIsLoading(false)
  }

  /**
   * 生成时间选择器选项
   * 生成小时、分钟、秒钟的选择器选项
   */
  const generateTimeOptions = (max: number) => {
    const options = []
    for (let i = 0; i < max; i++) {
      const value = i.toString().padStart(2, "0")
      options.push(
        <option key={value} value={value}>
          {value}
        </option>,
      )
    }
    return options
  }

  /**
   * 读取文件内容
   * @param file 文件对象
   * @returns 文件内容字符串
   */
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          resolve(event.target.result as string);
        } else {
          reject(new Error('文件读取失败'));
        }
      };

      reader.onerror = () => {
        reject(new Error('文件读取错误'));
      };

      // 根据文件类型决定读取方式
      if (file.name.endsWith('.csv') || file.name.endsWith('.txt')) {
        reader.readAsText(file);
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        // 对于Excel文件，我们读取二进制数据
        // 实际项目中应该使用xlsx库解析
        reader.readAsBinaryString(file);
      } else {
        reject(new Error('不支持的文件格式，请使用CSV或Excel文件'));
      }
    });
  };

  /**
   * 解析文件内容提取电话号码
   * @param content 文件内容
   * @param fileName 文件名
   * @returns 电话号码数组
   */
  const parsePhoneNumbers = (content: string, fileName: string): string[] => {
    // 电话号码正则表达式（匹配中国大陆手机号）
    const phoneRegex = /1[3-9]\d{9}/g;
    const phoneNumbers: string[] = [];

    if (fileName.endsWith('.csv') || fileName.endsWith('.txt')) {
      // 处理CSV或文本文件
      // 将内容按行分割
      const lines = content.split(/\r?\n/);

      for (const line of lines) {
        // 尝试从每行中提取电话号码
        const matches = line.match(phoneRegex);
        if (matches) {
          phoneNumbers.push(...matches);
        } else {
          // 如果没有直接匹配到电话号码，尝试将行分割为列
          const columns = line.split(/[,;\t]/);
          for (const column of columns) {
            const colMatches = column.trim().match(phoneRegex);
            if (colMatches) {
              phoneNumbers.push(...colMatches);
            }
          }
        }
      }
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      // 对于Excel文件，我们只能尝试从二进制内容中提取电话号码
      // 这不是最理想的方式，实际项目中应该使用xlsx库
      const matches = content.match(phoneRegex);
      if (matches) {
        phoneNumbers.push(...matches);
      }
    }

    // 去重
    return [...new Set(phoneNumbers)];
  };

  // 根据用户角色调整页面标题和描述
  const pageTitle = userType === "admin" ? "任务上传" : "我的任务"
  const pageDescription = userType === "admin" ? "管理外呼任务" : "管理您的外呼任务"

  return (
    <>
      <DashboardShell>
        <DashboardHeader heading={pageTitle} text={pageDescription} />
        <Card>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音通话">5G语音通话</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => setStartDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => setCreateDateRange(range || { from: undefined, to: undefined })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch} disabled={isLoading}>
                {isLoading ? "查询中..." : "查询"}
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  resetNewTaskForm()
                  setShowImportDialog(true)
                }}
                disabled={isLoading}
              >
                <Upload className="mr-2 h-4 w-4" />
                任务导入
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 任务列表表格 */}
        <div className="rounded-md border mt-6">
          <div className="overflow-x-auto">
            <Table className="table-enhanced relative">
              <TableHeader>
                <TableRow>
                  <TableHead>外呼任务名称</TableHead>
                  <TableHead>外呼类型</TableHead>
                  <TableHead>视频外呼内容</TableHead>
                  <TableHead>任务导入时间</TableHead>
                  <TableHead>任务开始时间</TableHead>
                  <TableHead>任务进度</TableHead>
                  <TableHead>任务完成时间</TableHead>
                  <TableHead>创建人</TableHead>
                  <TableHead className="sticky right-0 bg-white shadow-l z-10">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : tasks.length > 0 ? (
                  tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell className="font-medium">{task.name}</TableCell>
                      <TableCell>
                        <span
                          className={
                            task.type === "5G视频通知"
                              ? "status-active"
                              : task.type === "5G视频互动"
                                ? "status-processing"
                                : "status-pending"
                          }
                        >
                          {task.type}
                        </span>
                      </TableCell>
                      <TableCell>{task.content}</TableCell>
                      <TableCell>{task.importTime}</TableCell>
                      <TableCell>{task.startTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={task.progress}
                            className={`h-2 w-[100px] ${
                              task.progress === 100
                                ? "bg-green-600"
                                : task.progress > 50
                                  ? "bg-blue-600"
                                  : task.progress > 0
                                    ? "bg-yellow-600"
                                    : "bg-gray-300"
                            }`}
                          />
                          <span
                            className={
                              task.progress === 100
                                ? "text-green-600 font-medium"
                                : task.progress > 50
                                  ? "text-blue-600 font-medium"
                                  : task.progress > 0
                                    ? "text-yellow-600 font-medium"
                                    : "text-gray-500"
                            }
                          >
                            {task.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {task.completionTime ? (
                          task.completionTime
                        ) : (
                          <span
                            className={
                              task.uiStatus === "completed"
                                ? "status-active"
                                : task.uiStatus === "running"
                                  ? "status-processing"
                                  : task.uiStatus === "paused"
                                    ? "status-warning"
                                    : "status-pending"
                            }
                          >
                            {task.uiStatus === "pending"
                              ? "待启动"
                              : task.uiStatus === "running"
                                ? "外呼中"
                                : task.uiStatus === "paused"
                                  ? "已暂停"
                                  : "已完成"}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>{task.creator}</TableCell>
                      <TableCell className="sticky right-0 bg-white shadow-l z-10">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-green-600"
                            disabled={task.uiStatus === "completed" || task.uiStatus === "running" || isLoading}
                            onClick={() => handleStartTask(task.id)}
                          >
                            <Play className="h-4 w-4" />
                            <span className="sr-only">启动</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-yellow-600"
                            disabled={task.uiStatus !== "running" || isLoading}
                            onClick={() => handlePauseTask(task.id)}
                          >
                            <Pause className="h-4 w-4" />
                            <span className="sr-only">暂停</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-blue-600"
                            onClick={() => router.push(`/calls/${task.id}`)}
                          >
                            <Info className="h-4 w-4" />
                            <span className="sr-only">详情</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-red-600"
                            disabled={isLoading}
                            onClick={() => {
                              setTaskToDelete(task.id)
                              setShowDeleteDialog(true)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">删除</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      没有找到任务
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* 分页控件 */}
        {totalTasks > 0 && (
          <div className="mt-4 flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    disabled={isLoading}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => setCurrentPage(page)}
                      isActive={currentPage === page}
                      className="cursor-pointer"
                      disabled={isLoading}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    disabled={isLoading}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>

      {/* 删除任务确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>您确定要删除此任务吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTask} disabled={isLoading}>
              {isLoading ? "删除中..." : "删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 新建5G视频外呼任务对话框 */}
      <Dialog
        open={showImportDialog}
        onOpenChange={(open) => {
          if (!isLoading) {
            setShowImportDialog(open)
            if (!open) resetNewTaskForm()
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>新建5G视频外呼任务</DialogTitle>
          </DialogHeader>
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </DialogClose>

          <ScrollArea className="h-[60vh] pr-4">
            <div className="grid gap-3 py-2">
              <div className="space-y-1">
                <Label htmlFor="new-task-name">外呼任务名称</Label>
                <Input
                  id="new-task-name"
                  placeholder="请输入任务名称"
                  value={newTaskName}
                  onChange={(e) => setNewTaskName(e.target.value)}
                />
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-content">视频外呼内容</Label>
                <Select value={newTaskContent} onValueChange={setNewTaskContent}>
                  <SelectTrigger id="new-task-content">
                    <SelectValue placeholder="请选择外呼内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="汽车大促">汽车大促</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-type">外呼类型</Label>
                <Select value={newTaskType} onValueChange={(value) => {
                  setNewTaskType(value)
                  // 根据外呼类型自动设置起呼方式
                  if (value === "5G语音互动" || value === "5G语音通话") {
                    setNewTaskCalloutMode("2") // 语音起呼
                  } else {
                    setNewTaskCalloutMode("1") // 双向视频起呼
                  }
                }}>
                  <SelectTrigger id="new-task-type">
                    <SelectValue placeholder="请选择外呼类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音互动">5G语音互动</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-start-time">外呼开始时间</Label>
                <div className="flex space-x-2">
                  <Popover open={showDateTimePicker} onOpenChange={setShowDateTimePicker}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !newTaskStartTime && "text-muted-foreground",
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {newTaskStartTime ? (
                          format(newTaskStartTime, "yyyy-MM-dd") +
                          ` ${selectedHour}:${selectedMinute}:${selectedSecond}`
                        ) : (
                          <span>选择日期和时间</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <div className="p-2">
                        <Calendar
                          mode="single"
                          selected={newTaskStartTime}
                          onSelect={(date) => setNewTaskStartTime(date)}
                          initialFocus
                        />
                        <div className="flex items-center justify-between px-3 py-2 border-t">
                          <Label>时间:</Label>
                          <div className="flex space-x-1">
                            <select
                              value={selectedHour}
                              onChange={(e) => setSelectedHour(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(24)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedMinute}
                              onChange={(e) => setSelectedMinute(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                            <span className="py-1">:</span>
                            <select
                              value={selectedSecond}
                              onChange={(e) => setSelectedSecond(e.target.value)}
                              className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                            >
                              {generateTimeOptions(60)}
                            </select>
                          </div>
                        </div>
                        <div className="flex justify-end p-2">
                          <Button
                            size="sm"
                            onClick={() => setShowDateTimePicker(false)}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            确定
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-resource">视频外呼资源</Label>
                <Select value={newTaskResource} onValueChange={setNewTaskResource}>
                  <SelectTrigger id="new-task-resource">
                    <SelectValue placeholder="请选择外呼资源" />
                  </SelectTrigger>
                  <SelectContent>
                    {botList.length > 0 ? (
                      botList.map((bot) => (
                        <SelectItem key={bot._id} value={bot._id}>
                          {bot.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="loading">加载中...</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-phone">呼叫号码</Label>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="manual">手动输入</TabsTrigger>
                    <TabsTrigger value="import">导入表格</TabsTrigger>
                  </TabsList>
                  <TabsContent value="manual" className="mt-2">
                    <Input
                      id="new-task-phone"
                      placeholder="请输入呼叫号码"
                      value={newTaskPhoneNumber}
                      onChange={(e) => setNewTaskPhoneNumber(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">多个号码请用中文逗号（，）分隔</p>
                  </TabsContent>
                  <TabsContent value="import" className="mt-2">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Input
                          id="phone-file"
                          type="file"
                          accept=".csv,.xlsx,.xls"
                          onChange={handleFileChange}
                          className="flex-1"
                        />
                      </div>
                      {importFile && <p className="text-sm text-muted-foreground">已选择文件: {importFile.name}</p>}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-sms-type">短信方式</Label>
                <Select value={newTaskSmsType} onValueChange={setNewTaskSmsType}>
                  <SelectTrigger id="new-task-sms-type">
                    <SelectValue placeholder="请选择短信方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="文本短信">文本短信</SelectItem>
                    <SelectItem value="视频短信">视频短信</SelectItem>
                    <SelectItem value="无">无</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-sms-template">短信模板</Label>
                <Select
                  value={newTaskSmsTemplate}
                  onValueChange={setNewTaskSmsTemplate}
                  disabled={newTaskSmsType === "无"}
                >
                  <SelectTrigger id="new-task-sms-template">
                    <SelectValue placeholder="请选择短信模板" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="模板1">模板1：我们门店的地址是……</SelectItem>
                    <SelectItem value="模板2">模板2：我们门店的地址是……</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-sms-phase">短信发送时机</Label>
                <Select
                  value={newTaskSmsPhase}
                  onValueChange={setNewTaskSmsPhase}
                  disabled={newTaskSmsType === "无"}
                >
                  <SelectTrigger id="new-task-sms-phase">
                    <SelectValue placeholder="请选择短信发送时机" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">拨打电话时</SelectItem>
                    <SelectItem value="2">电话接通时</SelectItem>
                    <SelectItem value="3">通话结束后</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1">
                <Label htmlFor="new-task-callout-mode">起呼方式</Label>
                <Select
                  value={newTaskCalloutMode}
                  onValueChange={setNewTaskCalloutMode}
                >
                  <SelectTrigger id="new-task-callout-mode">
                    <SelectValue placeholder="请选择起呼方式" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">双向视频起呼</SelectItem>
                    <SelectItem value="2">语音起呼</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className="mt-4">
            <Button variant="outline" onClick={() => setShowImportDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button onClick={handleCreateTask} className="bg-blue-600 hover:bg-blue-700" disabled={isLoading}>
              {isLoading ? "创建中..." : "确定"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

