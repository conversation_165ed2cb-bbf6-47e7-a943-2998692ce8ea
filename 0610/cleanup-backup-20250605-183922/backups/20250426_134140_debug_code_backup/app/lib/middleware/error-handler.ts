import { NextResponse } from 'next/server'
import { ZodError } from 'zod'

/**
 * API错误响应接口
 */
interface ApiErrorResponse {
  success: boolean
  message: string
  code: string
  data?: any
}

/**
 * API错误类
 */
export class ApiError extends Error {
  constructor(
    public readonly statusCode: number,
    message: string,
    public readonly code: string = 'API_ERROR'
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

/**
 * 错误处理中间件
 * @param error - 错误对象
 * @returns NextResponse 错误响应
 */
export function errorHandler(error: any) {
  console.error("API Error:", error)

  // Prisma 错误处理
  if (error.code) {
    switch (error.code) {
      case "P2002": // 唯一约束冲突
        return NextResponse.json(
          {
            success: false,
            message: "该记录已存在",
            code: "DUPLICATE_RECORD"
          },
          { status: 409 }
        )
      case "P2025": // 记录不存在
        return NextResponse.json(
          {
            success: false,
            message: "记录不存在",
            code: "RECORD_NOT_FOUND"
          },
          { status: 404 }
        )
      default:
        return NextResponse.json(
          {
            success: false,
            message: "数据库操作失败",
            code: "DATABASE_ERROR"
          },
          { status: 500 }
        )
    }
  }

  // JWT 错误处理
  if (error.name === "JsonWebTokenError") {
    return NextResponse.json(
      {
        success: false,
        message: "无效的令牌",
        code: "INVALID_TOKEN"
      },
      { status: 401 }
    )
  }

  if (error.name === "TokenExpiredError") {
    return NextResponse.json(
      {
        success: false,
        message: "令牌已过期",
        code: "TOKEN_EXPIRED"
      },
      { status: 401 }
    )
  }

  // Zod 验证错误
  if (error instanceof ZodError) {
    return NextResponse.json(
      {
        success: false,
        message: error.errors[0].message,
        code: "VALIDATION_ERROR"
      },
      { status: 400 }
    )
  }

  // API错误
  if (error instanceof ApiError) {
    return NextResponse.json(
      {
        success: false,
        message: error.message,
        code: error.code
      },
      { status: error.statusCode }
    )
  }

  // 默认错误响应
  return NextResponse.json(
    {
      success: false,
      message: "服务器内部错误",
      code: "INTERNAL_ERROR"
    },
    { status: 500 }
  )
} 