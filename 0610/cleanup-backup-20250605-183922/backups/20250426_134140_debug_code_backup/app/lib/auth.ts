import { NextAuthOptions } from "next-auth"
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import { compare } from "bcryptjs"
import { cookies } from 'next/headers';
import * as jose from 'jose';
import { NextRequest } from 'next/server';

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

export interface TokenPayload {
  id: string;
  username: string;
  roleCode: string;
  exp: number;
}

export async function getToken(): Promise<TokenPayload | null> {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return null;
    }

    const { payload } = await jose.jwtVerify(token, JWT_SECRET);

    return {
      id: payload.sub as string,
      username: payload.username as string,
      roleCode: payload.roleCode as string,
      exp: payload.exp as number
    };
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("请输入邮箱和密码")
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
          include: {
            role: {
              include: {
                menus: true,
              },
            },
          },
        })

        if (!user) {
          throw new Error("用户不存在")
        }

        const isValid = await compare(credentials.password, user.password)
        if (!isValid) {
          throw new Error("密码错误")
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          username: user.username,
          image: user.image,
          roleCode: user.roleCode,
          role: user.role,
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        return {
          ...token,
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          image: user.image,
          roleCode: user.roleCode,
          role: user.role,
        }
      }
      return token
    },
    async session({ session, token }) {
      return {
        ...session,
        user: {
          ...session.user,
          id: token.id as string,
          username: token.username as string,
          email: token.email as string,
          name: token.name as string | null,
          image: token.image as string | null,
          roleCode: token.roleCode as string,
          role: token.role as any,
        },
      }
    },
  },
}

/**
 * 从请求中获取并验证token
 */
export async function getTokenData(req: NextRequest) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    return null;
  }

  try {
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    return payload;
  } catch (error) {
    console.error('Token验证失败:', error);
    return null;
  }
}

/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @returns 解析后的载荷数据
 */
export async function verifyToken(token: string) {
  try {
    console.log('开始验证令牌:', token.substring(0, 20) + '...');
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    console.log('令牌验证成功, 载荷:', payload);
    return payload;
  } catch (error) {
    console.error('令牌验证失败:', error);
    throw error;
  }
}

/**
 * 检查用户是否有指定权限
 */
export async function checkPermission(req: NextRequest, permission: string): Promise<boolean> {
  try {
    const payload = await getTokenData(req);
    if (!payload || !payload.sub) {
      return false;
    }

    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: { roleCode: true }
    });

    if (!user) {
      return false;
    }

    // 获取角色权限
    const role = await prisma.role.findUnique({
      where: { code: user.roleCode },
      select: { permissions: true }
    });

    if (!role) {
      return false;
    }

    // 检查是否有所需权限
    return role.permissions.includes('*') || role.permissions.includes(permission);
  } catch (error) {
    console.error('权限检查失败:', error);
    return false;
  }
}