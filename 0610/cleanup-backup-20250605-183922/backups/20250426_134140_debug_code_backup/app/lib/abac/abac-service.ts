/**
 * ABAC (基于属性的访问控制) 服务
 * 与现有 RBAC 系统集成，提供更细粒度的权限控制
 */

import prisma from "@/lib/prisma";
import { 
  PermissionCondition, 
  ConditionEvaluationResult,
  PermissionContext,
  ConditionOperator
} from "./types";

/**
 * ABAC 服务类
 * 负责基于属性的权限评估
 */
export class AbacService {
  /**
   * 评估特定权限的条件
   * @param permissionKey 权限键 (如: "role:create")
   * @param roleCode 角色代码
   * @param context 权限上下文 (用户、资源、环境属性)
   * @returns 条件评估结果
   */
  static async evaluateConditions(
    permissionKey: string,
    roleCode: string,
    context: PermissionContext
  ): Promise<ConditionEvaluationResult> {
    try {
      // 1. 获取指定权限的所有条件
      const conditions = await this.getPermissionConditions(permissionKey, roleCode);
      
      // 2. 如果没有条件，默认通过
      if (!conditions || conditions.length === 0) {
        return { permitted: true };
      }
      
      // 3. 评估所有条件
      const failedConditions: PermissionCondition[] = [];
      
      for (const condition of conditions) {
        const isConditionMet = this.evaluateSingleCondition(condition, context);
        if (!isConditionMet) {
          failedConditions.push(condition);
        }
      }
      
      // 4. 所有条件都满足才允许
      const permitted = failedConditions.length === 0;
      
      return {
        permitted,
        failedConditions: permitted ? undefined : failedConditions,
        message: permitted ? undefined : this.generateFailureMessage(failedConditions)
      };
    } catch (error) {
      console.error("ABAC 条件评估错误:", error);
      return {
        permitted: false,
        message: "权限评估过程中发生错误"
      };
    }
  }
  
  /**
   * 获取指定权限的所有条件
   * @param permissionKey 权限键
   * @param roleCode 角色代码
   * @returns 权限条件数组
   */
  static async getPermissionConditions(
    permissionKey: string,
    roleCode: string
  ): Promise<PermissionCondition[]> {
    // 从数据库或其他存储中获取条件
    // 注意: 这里需要与您的数据存储方式匹配
    // 目前返回空数组，在实际应用中需要实现数据获取逻辑
    
    // 示例实现 (未使用数据库)
    return [];
    
    // 数据库实现示例 (取决于您的数据模型)
    /*
    const conditions = await prisma.permissionCondition.findMany({
      where: {
        permissionKey,
        roleCode
      }
    });
    return conditions;
    */
  }
  
  /**
   * 评估单个条件
   * @param condition 条件
   * @param context 上下文
   * @returns 是否满足条件
   */
  static evaluateSingleCondition(
    condition: PermissionCondition,
    context: PermissionContext
  ): boolean {
    try {
      // 1. 获取属性值
      const attributeValue = this.getAttributeValue(
        condition.conditionType,
        condition.attributeName,
        context
      );
      
      // 2. 如果属性不存在，条件失败
      if (attributeValue === undefined) {
        return false;
      }
      
      // 3. 根据操作符评估条件
      return this.evaluateByOperator(
        attributeValue,
        condition.operator,
        condition.attributeValue
      );
    } catch (error) {
      console.error(`条件评估错误 (${condition.attributeName}):`, error);
      return false;
    }
  }
  
  /**
   * 从上下文中获取属性值
   * @param conditionType 条件类型
   * @param attributeName 属性名
   * @param context 上下文
   * @returns 属性值
   */
  static getAttributeValue(
    conditionType: string,
    attributeName: string,
    context: PermissionContext
  ): any {
    switch (conditionType) {
      case 'user':
        return context.user?.[attributeName];
      case 'resource':
        return context.resource?.[attributeName];
      case 'context':
        return context.environment?.[attributeName];
      default:
        return undefined;
    }
  }
  
  /**
   * 根据操作符评估条件
   * @param actualValue 实际值
   * @param operator 操作符
   * @param expectedValue 预期值
   * @returns 是否满足条件
   */
  static evaluateByOperator(
    actualValue: any,
    operator: ConditionOperator,
    expectedValue: any
  ): boolean {
    switch (operator) {
      case 'eq':
        return actualValue === expectedValue;
      case 'ne':
        return actualValue !== expectedValue;
      case 'gt':
        return actualValue > expectedValue;
      case 'lt':
        return actualValue < expectedValue;
      case 'gte':
        return actualValue >= expectedValue;
      case 'lte':
        return actualValue <= expectedValue;
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue);
      case 'contains':
        return typeof actualValue === 'string' && 
               actualValue.includes(expectedValue);
      case 'startsWith':
        return typeof actualValue === 'string' && 
               actualValue.startsWith(expectedValue);
      case 'endsWith':
        return typeof actualValue === 'string' && 
               actualValue.endsWith(expectedValue);
      default:
        return false;
    }
  }
  
  /**
   * 生成失败消息
   * @param failedConditions 失败的条件
   * @returns 失败消息
   */
  static generateFailureMessage(failedConditions: PermissionCondition[]): string {
    if (!failedConditions || failedConditions.length === 0) {
      return "权限验证失败";
    }
    
    // 生成简洁的错误消息
    if (failedConditions.length === 1) {
      const condition = failedConditions[0];
      return `不满足条件: ${condition.attributeName} ${this.getOperatorText(condition.operator)} ${condition.attributeValue}`;
    }
    
    return `不满足 ${failedConditions.length} 个条件`;
  }
  
  /**
   * 获取操作符的文本表示
   * @param operator 操作符
   * @returns 文本表示
   */
  static getOperatorText(operator: ConditionOperator): string {
    switch (operator) {
      case 'eq': return '等于';
      case 'ne': return '不等于';
      case 'gt': return '大于';
      case 'lt': return '小于';
      case 'gte': return '大于等于';
      case 'lte': return '小于等于';
      case 'in': return '在列表中';
      case 'contains': return '包含';
      case 'startsWith': return '以...开始';
      case 'endsWith': return '以...结束';
      default: return operator;
    }
  }
}

// 导出默认实例
export default AbacService; 