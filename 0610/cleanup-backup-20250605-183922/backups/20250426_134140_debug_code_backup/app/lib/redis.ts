import Redis from 'ioredis'

/**
 * Redis配置
 */
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000)
    return delay
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  reconnectOnError: (err) => {
    const targetError = 'READONLY'
    if (err.message.includes(targetError)) {
      return true
    }
    return false
  }
}

/**
 * Redis客户端实例
 */
const redis = new Redis(redisConfig)

// 监听连接错误
redis.on('error', (error) => {
  console.error('Redis连接错误:', error)
})

// 监听连接成功
redis.on('connect', () => {
  console.log('Redis连接成功')
})

export { redis } 