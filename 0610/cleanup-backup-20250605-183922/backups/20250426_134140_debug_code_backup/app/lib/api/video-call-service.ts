import AesUtils from "./crypto"

// API base URL from environment variable
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000"

// Organization code from environment variable
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || ""

// Login name from environment variable
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || ""

// App ID (provided by the API service)
const APP_ID = process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || ""

/**
 * 视频通话服务类
 */
export class VideoCallService {
  private static instance: VideoCallService

  private constructor() {}

  public static getInstance(): VideoCallService {
    if (!VideoCallService.instance) {
      VideoCallService.instance = new VideoCallService()
    }
    return VideoCallService.instance
  }

  /**
   * 获取请求头部
   */
  private getHeaders() {
    const signature = AesUtils.generateSignature(ORG_CODE)
    return {
      "Content-Type": "application/json; charset=utf-8",
      "access-token": signature,
    }
  }

  /**
   * 任务导入接口
   */
  public async importTask(taskData: {
    name: string
    content: string
    callType: string
    startTime: string
    resource: string
    phoneNumbers: string[]
    smsType?: string
    smsTemplate?: string
  }) {
    try {
      // 构建请求数据
      const deliverList = taskData.phoneNumbers.map((phone) => ({
        phone,
        otherInfo: [{ title: "content", value: taskData.content }],
      }))

      const requestData = {
        orgCode: ORG_CODE,
        sign: AesUtils.generateSignature(ORG_CODE),
        loginName: LOGIN_NAME,
        name: taskData.name,
        mediaType: "videoBot",
        videoBotId: taskData.resource,
        deliverList,
        isRelateSendMessage: !!taskData.smsType && taskData.smsType !== "无",
        msgTemplateId: taskData.smsTemplate || "",
        msgTitle: "系统通知",
        sendMessagePhase: 1,
      }

      // 发送请求
      const response = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/create`, {
        method: "POST",
        headers: this.getHeaders(),
        body: JSON.stringify(requestData),
      })

      const data = await response.json()

      if (data.errCode === 0) {
        return {
          success: true,
          message: "任务导入成功",
          data: data.result,
        }
      } else {
        return {
          success: false,
          message: `任务导入失败: ${data.errInfo || "未知错误"}`,
          data: null,
        }
      }
    } catch (error) {
      console.error("任务导入错误:", error)
      return {
        success: false,
        message: "任务导入失败，请稍后重试",
        data: null,
      }
    }
  }

  /**
   * 获取外呼详情列表
   */
  public async getCallDetails(params: {
    taskId?: string
    phone?: string
    startDate?: string
    endDate?: string
    page?: number
    pageSize?: number
  }) {
    try {
      // 构建请求参数
      const requestParams: any = {
        orgCode: ORG_CODE,
        loginName: LOGIN_NAME,
      }

      if (params.phone) {
        // 使用手机号查询
        const beginTime = params.startDate ? new Date(params.startDate).getTime() : undefined
        const endTime = params.endDate ? new Date(params.endDate).getTime() : undefined

        const requestData = {
          ...requestParams,
          sign: AesUtils.generateSignature(ORG_CODE),
          phone: params.phone,
          beginTime,
          endTime,
        }

        // 发送请求
        const response = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/queryCallId`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify(requestData),
        })

        const data = await response.json()

        if (data.errCode === 0) {
          return {
            success: true,
            message: "获取外呼详情成功",
            data: {
              list: data.result.callList || [],
              total: data.result.callList?.length || 0,
              page: 1,
              pageSize: data.result.callList?.length || 10,
              totalPages: 1,
            },
          }
        } else {
          return {
            success: false,
            message: `获取外呼详情失败: ${data.errInfo || "未知错误"}`,
            data: null,
          }
        }
      } else {
        // 使用任务ID查询或获取任务统计分析数据
        const mediaDeliverIds = params.taskId ? [params.taskId] : []

        const requestData = {
          ...requestParams,
          mediaDeliverIds,
        }

        // 发送请求
        const response = await fetch(`${API_BASE_URL}/openapi/task/panels`, {
          method: "POST",
          headers: this.getHeaders(),
          body: JSON.stringify(requestData),
        })

        const data = await response.json()

        if (data.errCode === 0) {
          const callDetails = this.processCallStatistics(data.result)

          return {
            success: true,
            message: "获取外呼详情成功",
            data: {
              list: callDetails,
              total: callDetails.length,
              page: params.page || 1,
              pageSize: params.pageSize || 10,
              totalPages: Math.ceil(callDetails.length / (params.pageSize || 10)),
            },
          }
        } else {
          return {
            success: false,
            message: `获取外呼详情失败: ${data.errInfo || "未知错误"}`,
            data: null,
          }
        }
      }
    } catch (error) {
      console.error("获取外呼详情错误:", error)
      return {
        success: false,
        message: "获取外呼详情失败，请稍后重试",
        data: null,
      }
    }
  }

  /**
   * 处理统计数据，转换为详情列表格式
   */
  private processCallStatistics(statistics: any) {
    const callDetails: any[] = []

    if (statistics.callonTrendStatisticData && Array.isArray(statistics.callonTrendStatisticData)) {
      statistics.callonTrendStatisticData.forEach((trend: any) => {
        callDetails.push({
          id: `trend-${trend.hour}`,
          taskName: "统计数据",
          type: "5G视频通知",
          content: "统计数据",
          customerName: "-",
          phoneNumber: "-",
          connectionType: trend.videoCallonCount > 0 ? "视频接通" : "语音接通",
          startTime: trend.hour,
          endTime: "-",
          duration: "-",
          ringTime: "-",
          intention: "A",
          videoCallCount: trend.videoCallonCount,
          audioCallCount: trend.audioCallonCount,
          totalCallCount: trend.callTotalCount,
        })
      })
    }

    if (statistics.intentStatisticData && Array.isArray(statistics.intentStatisticData)) {
      statistics.intentStatisticData.forEach((intent: any) => {
        callDetails.push({
          id: `intent-${intent.intentLevel}`,
          taskName: "意图统计",
          type: "5G视频通知",
          content: "意图统计",
          customerName: "-",
          phoneNumber: "-",
          connectionType: intent.videoCount > 0 ? "视频接通" : "语音接通",
          startTime: "-",
          endTime: "-",
          duration: "-",
          ringTime: "-",
          intention: intent.intentLevel,
          videoCallCount: intent.videoCount,
          audioCallCount: intent.audioCount,
          totalCallCount: intent.totalCount,
        })
      })
    }

    return callDetails
  }
}

// 导出单例实例
export const videoCallService = VideoCallService.getInstance()

/**
 * 获取任务组列表
 */
export const getTaskGroups = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/openapi/callout/taskList`, {
      method: "GET",
      headers: getHeaders(),
    })

    const data = await response.json()

    if (data.errCode === 0) {
      return {
        success: true,
        message: "获取任务组列表成功",
        data: data.result.list || [],
      }
    } else {
      return {
        success: false,
        message: `获取任务组列表失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }
    }
  } catch (error) {
    console.error("获取任务组列表错误:", error)
    return {
      success: false,
      message: "获取任务组列表失败，请稍后重试",
      data: null,
    }
  }
}

/**
 * 获取Bot列表
 */
export const getBotList = async () => {
  try {
    const requestData = {
      orgCode: ORG_CODE,
      sign: AesUtils.generateSignature(ORG_CODE),
      loginName: LOGIN_NAME,
    }

    const response = await fetch(`${API_BASE_URL}/api/bots/external`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json; charset=utf-8",
      },
      body: JSON.stringify(requestData),
    })

    const data = await response.json()

    if (data.errCode === 0) {
      return {
        success: true,
        message: "获取Bot列表成功",
        data: data.result || [],
      }
    } else {
      return {
        success: false,
        message: `获取Bot列表失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }
    }
  } catch (error) {
    console.error("获取Bot列表错误:", error)
    return {
      success: false,
      message: "获取Bot列表失败，请稍后重试",
      data: null,
    }
  }
}

// 添加启动和暂停任务的函数

/**
 * 启动任务
 * @param taskId 任务ID
 * @returns 启动结果
 */
export async function startTask(taskId: string) {
  const generateSignature = () => {
    const timestamp = Date.now()
    const signature = AesUtils.generateSignature(ORG_CODE + timestamp)
    return { signature, timestamp }
  }

  const API_CONFIG = {
    baseUrl: API_BASE_URL,
    appId: APP_ID,
    loginName: LOGIN_NAME,
  }
  try {
    const { signature, timestamp } = generateSignature()

    const response = await fetch(`${API_CONFIG.baseUrl}/task/${taskId}/start`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Auth-Timestamp": timestamp,
        "X-Auth-Signature": signature,
        "X-Auth-App-Id": API_CONFIG.appId,
        "X-Auth-Login-Name": API_CONFIG.loginName,
      },
    })

    const data = await response.json()
    return {
      success: data.errCode === 0,
      message: data.errInfo || "启动任务成功",
      data: data.result,
    }
  } catch (error) {
    console.error("启动任务失败:", error)
    return {
      success: false,
      message: "启动任务失败，请稍后重试",
      data: null,
    }
  }
}

/**
 * 暂停任务
 * @param taskId 任务ID
 * @returns 暂停结果
 */
export async function pauseTask(taskId: string) {
  const generateSignature = () => {
    const timestamp = Date.now()
    const signature = AesUtils.generateSignature(ORG_CODE + timestamp)
    return { signature, timestamp }
  }

  const API_CONFIG = {
    baseUrl: API_BASE_URL,
    appId: APP_ID,
    loginName: LOGIN_NAME,
  }
  try {
    const { signature, timestamp } = generateSignature()

    const response = await fetch(`${API_CONFIG.baseUrl}/task/${taskId}/pause`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Auth-Timestamp": timestamp,
        "X-Auth-Signature": signature,
        "X-Auth-App-Id": API_CONFIG.appId,
        "X-Auth-Login-Name": API_CONFIG.loginName,
      },
    })

    const data = await response.json()
    return {
      success: data.errCode === 0,
      message: data.errInfo || "暂停任务成功",
      data: data.result,
    }
  } catch (error) {
    console.error("暂停任务失败:", error)
    return {
      success: false,
      message: "暂停任务失败，请稍后重试",
      data: null,
    }
  }
}

