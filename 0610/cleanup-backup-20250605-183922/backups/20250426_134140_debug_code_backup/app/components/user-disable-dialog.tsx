import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

interface UserDisableDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  onSuccess: (userId: string, reason: string) => void
}

export function UserDisableDialog({
  open,
  onOpenChange,
  userId,
  userName,
  onSuccess,
}: UserDisableDialogProps) {
  const [reason, setReason] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!reason.trim()) {
      toast({
        title: "请输入禁用原因",
        description: "禁用用户时必须提供禁用原因",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/customers/${userId}/disable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'inactive', reason }),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "操作成功",
          description: "用户已成功禁用",
        })

        // 调用成功回调
        onSuccess(userId, reason)

        // 关闭对话框并重置表单
        onOpenChange(false)
        setReason("")
      } else {
        toast({
          title: "操作失败",
          description: data.message || "禁用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('禁用用户错误:', error)
      toast({
        title: "操作失败",
        description: "禁用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>禁用用户</DialogTitle>
            <DialogDescription>
              您正在禁用用户 <span className="font-semibold">{userName}</span>。请输入禁用原因，该原因将通过邮件通知用户。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Textarea
                id="reason"
                placeholder="请输入禁用原因..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[100px]"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "处理中..." : "确认禁用"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
