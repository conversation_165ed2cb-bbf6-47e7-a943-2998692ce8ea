"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Check, X, AlertCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface VerificationDetailsProps {
  userId: string
  userName: string
}

interface VerificationData {
  status: string
  type: string
  reviewedAt: string | null
  remark: string | null
  data: {
    // 个人认证信息
    realName: string | null
    idCardNumber: string | null
    idCardFront: string | null
    idCardBack: string | null
    idCardHolding: string | null

    // 企业认证信息
    companyName: string | null
    legalPerson: string | null
    legalPersonIdCard: string | null
    socialCreditCode: string | null
    businessLicense: string | null
    otherDocuments: string[] | null
  }
}

export function VerificationDetails({ userId, userName }: VerificationDetailsProps) {
  const [verificationData, setVerificationData] = useState<VerificationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [processing, setProcessing] = useState(false)
  
  // 驳回对话框状态
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false)
  const [rejectReason, setRejectReason] = useState("")

  useEffect(() => {
    fetchVerificationData()
  }, [userId])

  const fetchVerificationData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/admin/user/${userId}/verification`)
      
      if (!response.ok) {
        throw new Error('获取认证资料失败')
      }
      
      const data = await response.json()
      
      if (data.success) {
        setVerificationData(data.data)
      } else {
        throw new Error(data.message || '获取认证资料失败')
      }
    } catch (err) {
      console.error('获取认证资料错误:', err)
      setError(err instanceof Error ? err.message : '获取认证资料时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async () => {
    if (!confirm(`确定要通过 ${userName} 的认证申请吗？`)) {
      return
    }
    
    setProcessing(true)
    
    try {
      const response = await fetch(`/api/admin/user/${userId}/verification/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error('审核操作失败')
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "审核成功",
          description: `已通过 ${userName} 的认证申请`,
          variant: "success"
        })
        
        // 刷新数据
        fetchVerificationData()
      } else {
        throw new Error(data.message || '审核操作失败')
      }
    } catch (err) {
      console.error('审核操作错误:', err)
      toast({
        title: "审核失败",
        description: err instanceof Error ? err.message : '审核操作时发生错误',
        variant: "destructive"
      })
    } finally {
      setProcessing(false)
    }
  }

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      toast({
        title: "请输入驳回原因",
        description: "驳回认证申请需要提供原因",
        variant: "destructive"
      })
      return
    }
    
    setProcessing(true)
    
    try {
      const response = await fetch(`/api/admin/user/${userId}/verification/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: rejectReason })
      })
      
      if (!response.ok) {
        throw new Error('驳回操作失败')
      }
      
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: "驳回成功",
          description: `已驳回 ${userName} 的认证申请`,
          variant: "success"
        })
        
        // 关闭对话框
        setRejectDialogOpen(false)
        setRejectReason("")
        
        // 刷新数据
        fetchVerificationData()
      } else {
        throw new Error(data.message || '驳回操作失败')
      }
    } catch (err) {
      console.error('驳回操作错误:', err)
      toast({
        title: "驳回失败",
        description: err instanceof Error ? err.message : '驳回操作时发生错误',
        variant: "destructive"
      })
    } finally {
      setProcessing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <span className="ml-2 text-lg">加载认证资料中...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4 my-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <div>
            <h3 className="text-red-800 font-medium">加载失败</h3>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  if (!verificationData) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-md p-4 my-4">
        <p className="text-gray-600 text-center">该用户尚未提交认证资料</p>
      </div>
    )
  }

  const { status, type, data } = verificationData

  return (
    <>
      <Card className="border-none shadow-md">
        <CardHeader className="bg-white border-b">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-xl font-bold text-blue-800">认证资料</CardTitle>
              <CardDescription>查看和审核用户提交的认证资料</CardDescription>
            </div>
            <div>
              {status === "pending" && (
                <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">
                  审核中
                </Badge>
              )}
              {status === "approved" && (
                <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                  已认证
                </Badge>
              )}
              {status === "rejected" && (
                <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">
                  未通过
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-6 pb-4 bg-white">
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-md">
              <h3 className="font-medium text-gray-700 mb-2">认证类型</h3>
              <p className="text-gray-800">{type === "personal" ? "个人认证" : "企业认证"}</p>
            </div>

            {/* 个人认证信息 */}
            {type === "personal" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">真实姓名</h3>
                    <p className="text-gray-800">{data.realName || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">身份证号码</h3>
                    <p className="text-gray-800">{data.idCardNumber || "-"}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">身份证正面照片</h3>
                  {data.idCardFront ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img 
                        src={data.idCardFront} 
                        alt="身份证正面" 
                        className="max-h-60 object-contain mx-auto"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">身份证反面照片</h3>
                  {data.idCardBack ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img 
                        src={data.idCardBack} 
                        alt="身份证反面" 
                        className="max-h-60 object-contain mx-auto"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">手持身份证照片</h3>
                  {data.idCardHolding ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img 
                        src={data.idCardHolding} 
                        alt="手持身份证" 
                        className="max-h-60 object-contain mx-auto"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>
              </div>
            )}

            {/* 企业认证信息 */}
            {type === "enterprise" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">企业名称</h3>
                    <p className="text-gray-800">{data.companyName || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">统一社会信用代码</h3>
                    <p className="text-gray-800">{data.socialCreditCode || "-"}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">法人姓名</h3>
                    <p className="text-gray-800">{data.legalPerson || "-"}</p>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">法人身份证号</h3>
                    <p className="text-gray-800">{data.legalPersonIdCard || "-"}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">营业执照照片</h3>
                  {data.businessLicense ? (
                    <div className="border rounded-md p-2 max-w-md">
                      <img 
                        src={data.businessLicense} 
                        alt="营业执照" 
                        className="max-h-60 object-contain mx-auto"
                      />
                    </div>
                  ) : (
                    <p className="text-gray-500">未上传</p>
                  )}
                </div>

                {data.otherDocuments && data.otherDocuments.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="font-medium text-gray-700">其他资质文件</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {data.otherDocuments.map((doc, index) => (
                        <div key={index} className="border rounded-md p-2">
                          <img 
                            src={doc} 
                            alt={`其他资质文件 ${index + 1}`} 
                            className="max-h-40 object-contain mx-auto"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {verificationData.remark && (
              <div className="bg-gray-50 p-4 rounded-md">
                <h3 className="font-medium text-gray-700 mb-2">备注说明</h3>
                <p className="text-gray-800 whitespace-pre-line">{verificationData.remark}</p>
              </div>
            )}

            {status === "rejected" && (
              <div className="bg-red-50 p-4 rounded-md">
                <h3 className="font-medium text-red-700 mb-2">驳回原因</h3>
                <p className="text-red-800 whitespace-pre-line">{verificationData.remark || "未提供驳回原因"}</p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="bg-white border-t flex justify-end space-x-2 py-4">
          {status === "pending" && (
            <>
              <Button
                variant="outline"
                onClick={() => setRejectDialogOpen(true)}
                disabled={processing}
                className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700"
              >
                {processing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <X className="mr-2 h-4 w-4" />
                    驳回
                  </>
                )}
              </Button>
              <Button
                onClick={handleApprove}
                disabled={processing}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {processing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    处理中...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    通过
                  </>
                )}
              </Button>
            </>
          )}
        </CardFooter>
      </Card>

      {/* 驳回对话框 */}
      <Dialog open={rejectDialogOpen} onOpenChange={setRejectDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>驳回认证申请</DialogTitle>
            <DialogDescription>
              请输入驳回原因，该原因将通过邮件和系统通知发送给用户。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Textarea
              placeholder="请输入驳回原因..."
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setRejectDialogOpen(false)} disabled={processing}>
              取消
            </Button>
            <Button 
              onClick={handleReject} 
              disabled={!rejectReason.trim() || processing}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              {processing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  提交中...
                </>
              ) : (
                "确认驳回"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
