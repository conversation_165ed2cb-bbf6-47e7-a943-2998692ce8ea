"use client"

import { useState, useEffect, useMemo } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "sonner"
import { ChevronDown, ChevronRight, FolderTree, Search, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface Operation {
  id: string
  code: string
  name: string
  type: string
  description?: string
  menuId?: string
  menuName?: string
  menuPath?: string
  enabled: boolean
}

interface OperationSelectorProps {
  selectedOperations: string[]
  onChange: (operations: string[]) => void
  maxHeight?: number
  showBadges?: boolean
  showSearch?: boolean
}

export function OperationSelector({
  selectedOperations,
  onChange,
  maxHeight = 400,
  showBadges = true,
  showSearch = true
}: OperationSelectorProps) {
  const [operations, setOperations] = useState<Operation[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState<string>('all')

  // 加载操作列表
  const loadOperations = async (isRefreshing = false) => {
    try {
      if (isRefreshing) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      const response = await fetch("/api/operations", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache"
        },
        cache: "no-store",
        next: { revalidate: 0 }
      })
      const data = await response.json()

      console.log('加载操作数据响应:', data)

      if (data.success) {
        setOperations(data.data || [])

        if (isRefreshing) {
          toast.success('操作数据已刷新')
        }
      } else {
        console.error('加载操作列表失败:', data.error || data.message || '未知错误')
        toast.error(`加载操作列表失败: ${data.error || data.message || '未知错误'}`)
      }
    } catch (error) {
      console.error("加载操作列表失败:", error)
      toast.error(`加载操作列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 处理操作选择变化
  const handleOperationChange = (operationId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedOperations, operationId])
    } else {
      onChange(selectedOperations.filter(id => id !== operationId))
    }
  }

  // 选择所有操作
  const selectAllOperations = () => {
    onChange(operations.map(op => op.id))
  }

  // 取消选择所有操作
  const deselectAllOperations = () => {
    onChange([])
  }

  // 刷新操作数据
  const refreshOperations = () => {
    loadOperations(true)
  }

  // 按菜单分组操作
  const operationsByMenu = useMemo(() => {
    const grouped: Record<string, Operation[]> = {}

    operations.forEach(op => {
      const menuName = op.menuName || '未分类'
      if (!grouped[menuName]) {
        grouped[menuName] = []
      }
      grouped[menuName].push(op)
    })

    return grouped
  }, [operations])

  // 按类型分组操作
  const operationsByType = useMemo(() => {
    const grouped: Record<string, Operation[]> = {}

    operations.forEach(op => {
      const type = op.type || '未分类'
      if (!grouped[type]) {
        grouped[type] = []
      }
      grouped[type].push(op)
    })

    return grouped
  }, [operations])

  // 搜索过滤操作
  const filteredOperations = useMemo(() => {
    if (!searchQuery.trim()) {
      return operations
    }

    const query = searchQuery.toLowerCase()

    return operations.filter(op =>
      op.name.toLowerCase().includes(query) ||
      op.code.toLowerCase().includes(query) ||
      op.description?.toLowerCase().includes(query) ||
      op.menuName?.toLowerCase().includes(query)
    )
  }, [operations, searchQuery])

  // 初始加载
  useEffect(() => {
    loadOperations()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {showBadges && (
        <div className="flex justify-between items-center">
          <div className="space-x-2">
            <Badge variant="outline">已选择 {selectedOperations.length} 项</Badge>
            <Badge variant="outline">共 {operations.length} 项</Badge>
          </div>
          <div className="space-x-2">
            <button
              type="button"
              className="text-xs text-blue-500 hover:underline"
              onClick={selectAllOperations}
            >
              全选
            </button>
            <button
              type="button"
              className="text-xs text-blue-500 hover:underline"
              onClick={deselectAllOperations}
            >
              取消全选
            </button>
          </div>
        </div>
      )}

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">按钮权限</CardTitle>
          <CardDescription>
            选择此角色可以使用的操作权限
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {showSearch && (
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索操作..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={refreshOperations}
                  disabled={refreshing}
                >
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            )}

            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="byMenu">按菜单</TabsTrigger>
                <TabsTrigger value="byType">按类型</TabsTrigger>
              </TabsList>

              <TabsContent value="all">
                <ScrollArea className="pr-4" style={{ height: maxHeight }}>
                  {filteredOperations.length > 0 ? (
                    <div className="space-y-2">
                      {filteredOperations.map(operation => (
                        <div key={operation.id} className="flex items-center space-x-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                          <Checkbox
                            id={`operation-${operation.id}`}
                            checked={selectedOperations.includes(operation.id)}
                            onCheckedChange={(checked) =>
                              handleOperationChange(operation.id, checked === true)
                            }
                          />
                          <Label
                            htmlFor={`operation-${operation.id}`}
                            className="font-medium cursor-pointer flex-1"
                          >
                            {operation.name}
                            <span className="text-xs text-muted-foreground ml-2">
                              {operation.code}
                            </span>
                          </Label>

                          <Badge variant="outline" className="ml-2">
                            {operation.type === 'page' ? '页面' : '操作'}
                          </Badge>

                          {operation.menuName && (
                            <Badge variant="secondary" className="ml-2">
                              {operation.menuName}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-6 text-center text-muted-foreground">
                      {searchQuery ? '没有找到匹配的操作' : '没有可用的操作'}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="byMenu">
                <ScrollArea className="pr-4" style={{ height: maxHeight }}>
                  {Object.keys(operationsByMenu).length > 0 ? (
                    <div className="space-y-4">
                      {Object.entries(operationsByMenu).map(([menuName, menuOperations]) => (
                        <div key={menuName} className="space-y-2">
                          <h3 className="font-medium text-sm">{menuName}</h3>
                          <div className="space-y-1 ml-4">
                            {menuOperations
                              .filter(op =>
                                !searchQuery.trim() ||
                                op.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                op.code.toLowerCase().includes(searchQuery.toLowerCase())
                              )
                              .map(operation => (
                                <div key={operation.id} className="flex items-center space-x-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                                  <Checkbox
                                    id={`menu-operation-${operation.id}`}
                                    checked={selectedOperations.includes(operation.id)}
                                    onCheckedChange={(checked) =>
                                      handleOperationChange(operation.id, checked === true)
                                    }
                                  />
                                  <Label
                                    htmlFor={`menu-operation-${operation.id}`}
                                    className="font-medium cursor-pointer flex-1"
                                  >
                                    {operation.name}
                                    <span className="text-xs text-muted-foreground ml-2">
                                      {operation.code}
                                    </span>
                                  </Label>

                                  <Badge variant="outline" className="ml-2">
                                    {operation.type === 'page' ? '页面' : '操作'}
                                  </Badge>
                                </div>
                              ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-6 text-center text-muted-foreground">
                      {searchQuery ? '没有找到匹配的操作' : '没有可用的操作'}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>

              <TabsContent value="byType">
                <ScrollArea className="pr-4" style={{ height: maxHeight }}>
                  {Object.keys(operationsByType).length > 0 ? (
                    <div className="space-y-4">
                      {Object.entries(operationsByType).map(([type, typeOperations]) => (
                        <div key={type} className="space-y-2">
                          <h3 className="font-medium text-sm">{type === 'page' ? '页面权限' : '操作权限'}</h3>
                          <div className="space-y-1 ml-4">
                            {typeOperations
                              .filter(op =>
                                !searchQuery.trim() ||
                                op.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                                op.code.toLowerCase().includes(searchQuery.toLowerCase())
                              )
                              .map(operation => (
                                <div key={operation.id} className="flex items-center space-x-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                                  <Checkbox
                                    id={`type-operation-${operation.id}`}
                                    checked={selectedOperations.includes(operation.id)}
                                    onCheckedChange={(checked) =>
                                      handleOperationChange(operation.id, checked === true)
                                    }
                                  />
                                  <Label
                                    htmlFor={`type-operation-${operation.id}`}
                                    className="font-medium cursor-pointer flex-1"
                                  >
                                    {operation.name}
                                    <span className="text-xs text-muted-foreground ml-2">
                                      {operation.code}
                                    </span>
                                  </Label>

                                  {operation.menuName && (
                                    <Badge variant="secondary" className="ml-2">
                                      {operation.menuName}
                                    </Badge>
                                  )}
                                </div>
                              ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="py-6 text-center text-muted-foreground">
                      {searchQuery ? '没有找到匹配的操作' : '没有可用的操作'}
                    </div>
                  )}
                </ScrollArea>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
