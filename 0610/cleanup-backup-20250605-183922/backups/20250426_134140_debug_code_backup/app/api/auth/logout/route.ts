/**
 * 登出 API 路由
 * 处理用户登出请求，清除会话状态
 *
 * 请求方法：POST
 * 不需要请求体
 *
 * 成功响应：
 * ```json
 * {
 *   "success": true
 * }
 * ```
 *
 * 错误响应：
 * ```json
 * {
 *   "success": false,
 *   "error": "登出失败"
 * }
 * ```
 *
 * @module LogoutAPI
 */

import { cookies } from "next/headers"
import { NextRequest, NextResponse } from "next/server"
import { csrfProtection } from "@/app/api/csrf-middleware"

// 认证 cookie 名称
const TOKEN_NAME = "token"

/**
 * 处理用户登出请求
 */
async function handleLogout(request: NextRequest) {
  try {
    console.log("处理用户登出请求")

    // 清除所有可能的认证相关 cookie
    const cookieStore = cookies()

    // 清除所有可能的认证 cookie
    const cookiesToClear = [
      TOKEN_NAME,
      "auth_token",
      "logged_in",
      "auth_backup",
      "auth_session",
      "admin_user_id",
      "admin_username",
      "next-auth.session-token",
      "next-auth.callback-url",
      "next-auth.csrf-token",
      "__Secure-next-auth.callback-url",
      "__Secure-next-auth.session-token",
      "__Host-next-auth.csrf-token"
    ]

    for (const cookieName of cookiesToClear) {
      cookieStore.delete(cookieName)
      console.log(`删除 cookie: ${cookieName}`)
    }

    // 返回清除前端存储的脚本
    const clearScript = `
      <script>
        try {
          // 清除所有本地存储
          localStorage.clear();
          sessionStorage.clear();

          // 清除所有cookie
          document.cookie.split(';').forEach(function(c) {
            document.cookie = c.trim().split('=')[0] + '=;' + 'expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
          });

          console.log('所有存储和会话数据已清除');

          // 延迟后跳转到登录页面
          setTimeout(function() {
            window.location.href = '/login?clear=' + Date.now();
          }, 100);
        } catch (e) {
          console.error('清除数据失败:', e);
          // 即使失败也跳转到登录页面
          window.location.href = '/login?clear=' + Date.now();
        }
      </script>
    `

    console.log("已删除认证 cookie")

    // 返回包含清除脚本的HTML响应
    return new Response(
      `<!DOCTYPE html>
      <html>
      <head>
        <title>正在登出...</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
      </head>
      <body>
        <h1>正在登出系统...</h1>
        <p>请稍等，正在清除您的会话数据...</p>
        ${clearScript}
      </body>
      </html>`,
      {
        status: 200,
        headers: {
          "Content-Type": "text/html; charset=utf-8",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate"
        }
      }
    )
  } catch (error) {
    console.error("登出失败:", error)
    return new Response(
      JSON.stringify({ success: false, error: "登出失败" }),
      { status: 500 }
    )
  }
}

// 应用CSRF保护中间件
export async function POST(request: NextRequest) {
  return csrfProtection(request, handleLogout)
}