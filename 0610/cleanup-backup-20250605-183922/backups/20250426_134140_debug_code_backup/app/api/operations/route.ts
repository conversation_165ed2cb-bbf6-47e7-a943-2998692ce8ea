/**
 * 操作权限管理 API 路由
 * 提供操作权限的 CRUD 操作
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取操作列表
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "获取操作列表API");
    if (response) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const operations = await prisma.operation.findMany({
      include: {
        resources: true,
        menu: {
          select: {
            id: true,
            name: true,
            path: true,
          },
        },
      },
      orderBy: {
        type: "asc",
      },
    })

    // 格式化操作数据，添加菜单名称
    const formattedOperations = operations.map((op) => ({
      id: op.id,
      code: op.code,
      name: op.name,
      type: op.type,
      description: op.description,
      menuId: op.menuId,
      menuName: op.menu?.name,
      menuPath: op.menu?.path,
      enabled: op.enabled,
      resources: op.resources,
    }))

    return NextResponse.json({
      success: true,
      data: formattedOperations,
    })
  } catch (error) {
    console.error("获取操作列表错误:", error)
    return NextResponse.json(
      { success: false, message: "获取操作列表失败" },
      { status: 500 }
    )
  }
}

/**
 * 创建操作
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "创建操作API");
    if (response) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const data = await request.json()
    const { code, name, type, description } = data

    // 验证操作代码是否已存在
    const existingOperation = await prisma.operation.findUnique({
      where: { code },
    })

    if (existingOperation) {
      return NextResponse.json(
        { error: "操作代码已存在" },
        { status: 400 }
      )
    }

    // 创建操作
    const operation = await prisma.operation.create({
      data: {
        code,
        name,
        type,
        description,
      },
      include: {
        resources: true,
      },
    })

    return NextResponse.json(operation)
  } catch (error) {
    console.error("创建操作错误:", error)
    return NextResponse.json(
      { error: "创建操作失败" },
      { status: 500 }
    )
  }
}

/**
 * 更新操作
 */
export async function PUT(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "更新操作API");
    if (response) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const data = await request.json()
    const { id, code, name, type, description } = data

    // 验证操作是否存在
    const existingOperation = await prisma.operation.findUnique({
      where: { id },
    })

    if (!existingOperation) {
      return NextResponse.json(
        { error: "操作不存在" },
        { status: 404 }
      )
    }

    // 验证操作代码是否已存在
    if (code !== existingOperation.code) {
      const codeExists = await prisma.operation.findUnique({
        where: { code },
      })

      if (codeExists) {
        return NextResponse.json(
          { error: "操作代码已存在" },
          { status: 400 }
        )
      }
    }

    // 更新操作
    const operation = await prisma.operation.update({
      where: { id },
      data: {
        code,
        name,
        type,
        description,
      },
      include: {
        resources: true,
      },
    })

    return NextResponse.json(operation)
  } catch (error) {
    console.error("更新操作错误:", error)
    return NextResponse.json(
      { error: "更新操作失败" },
      { status: 500 }
    )
  }
}

/**
 * 删除操作
 */
export async function DELETE(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "删除操作API");
    if (response) {
      return NextResponse.json({ error: "未授权" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "操作ID不能为空" },
        { status: 400 }
      )
    }

    // 验证操作是否存在
    const existingOperation = await prisma.operation.findUnique({
      where: { id },
    })

    if (!existingOperation) {
      return NextResponse.json(
        { error: "操作不存在" },
        { status: 404 }
      )
    }

    // 删除操作
    await prisma.operation.delete({
      where: { id },
    })

    return NextResponse.json({ message: "删除成功" })
  } catch (error) {
    console.error("删除操作错误:", error)
    return NextResponse.json(
      { error: "删除操作失败" },
      { status: 500 }
    )
  }
}