import { NextRequest } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "../../../../lib/unified-auth-service"

export async function GET(request: NextRequest) {
  try {
    // 使用统一认证服务获取用户
    const user = await UnifiedAuthService.getCurrentUser(request, "登录历史API")
    if (!user) {
      return Response.json({ success: false, message: "未认证" }, { status: 401 })
    }

    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const pageSize = parseInt(searchParams.get('pageSize') || '10')
    const status = searchParams.get('status')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const search = searchParams.get('search')

    // 构建查询条件
    const where = {
      userId: user.id,
      ...(status && status !== 'all' ? { status } : {}),
      ...(startDate || endDate ? {
        createdAt: {
          ...(startDate ? { gte: new Date(startDate) } : {}),
          ...(endDate ? { lte: new Date(endDate) } : {})
        }
      } : {}),
      ...(search ? {
        OR: [
          { ipAddress: { contains: search } },
          { location: { contains: search } },
          { device: { contains: search } }
        ]
      } : {})
    }

    // 获取总数
    const total = await prisma.loginHistory.count({ where })

    // 获取分页数据
    const loginHistory = await prisma.loginHistory.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      select: {
        id: true,
        ipAddress: true,
        location: true,
        device: true,
        status: true,
        failReason: true,
        createdAt: true
      }
    })

    return Response.json({
      success: true,
      data: {
        items: loginHistory,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error("获取登录历史失败:", error)
    return Response.json({ success: false, message: "获取登录历史失败" }, { status: 500 })
  }
}