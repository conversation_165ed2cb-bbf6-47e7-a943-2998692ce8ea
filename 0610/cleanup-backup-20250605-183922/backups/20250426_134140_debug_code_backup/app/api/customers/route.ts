/**
 * 客户管理API
 * 处理客户相关的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { ErrorService } from "@/lib/error-service"
import { ApiError } from "@/app/lib/middleware/error-handler"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { checkPermission } from "@/lib/casbin/enforcer"

/**
 * 获取客户列表
 *
 * @route GET /api/customers
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "客户管理API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const userId = admin.id;
    const userRole = admin.roleCode;

    // 检查特定操作权限
    try {
      // 尝试使用jCasbin检查权限
      const hasPermission = await checkPermission(request, 'customers', 'read');
      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          message: '没有权限访问客户列表'
        }, { status: 403 })
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      // 如果权限检查失败，则继续执行，因为管理员已经通过了权限检查
    }


    // 查询客户列表（非管理员用户）
    const customers = await prisma.user.findMany({
      where: {
        roleCode: {
          not: 'ADMIN'
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        image: true,
        roleCode: true,
        status: true,
        balance: true,
        creditLimit: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        },
        // 添加费率关联查询
        rates: {
          select: {
            id: true,
            amount: true,
            period: true,
            businessType: true,
            createdAt: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1 // 只取最新的一条费率记录
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 处理日期格式和转换为前端需要的格式
    const formattedCustomers = customers.map(customer => {
      // 获取最新的费率信息
      const latestRate = customer.rates && customer.rates.length > 0 ? customer.rates[0] : null;

      return {
        id: customer.id,
        username: customer.username,
        email: customer.email,
        name: customer.name,
        phone: customer.phone || "",
        image: customer.image,
        roleCode: customer.roleCode,
        accountType: customer.role.type === "system" ? "admin" : customer.role.type,
        personalVerification: customer.verificationType === "personal" ? customer.verificationStatus : null,
        enterpriseVerification: customer.verificationType === "enterprise" ? customer.verificationStatus : null,
        role: customer.roleCode,
        balance: customer.balance || 0,
        creditLimit: customer.creditLimit || 0,
        status: customer.status,
        // 添加费率和业务类型信息
        rate: latestRate ? latestRate.amount : null,
        businessType: latestRate ? latestRate.businessType : null,
        createdBy: customer.createdBy ? {
          id: customer.createdBy.id,
          name: customer.createdBy.name || customer.createdBy.username
        } : null,
        createdAt: customer.createdAt.toISOString(),
        updatedAt: customer.updatedAt.toISOString()
      };
    })

    return NextResponse.json({
      success: true,
      message: '获取客户列表成功',
      data: formattedCustomers
    })
  } catch (error) {
    // 使用错误处理服务记录和格式化错误
    ErrorService.logError(error, { context: '获取客户列表' })

    // 处理不同类型的错误
    if (error instanceof ApiError) {
      return NextResponse.json(
        ErrorService.handleApiError(error),
        { status: error.statusCode }
      )
    }

    // 处理其他错误
    return NextResponse.json(
      ErrorService.handleApiError(error),
      { status: 500 }
    )
  }
}

/**
 * 创建客户
 *
 * @route POST /api/customers
 * @access 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "创建客户API");
    if (response) {
      return response;
    }

    // 获取管理员ID
    const userId = admin.id;
    const userRole = admin.roleCode;

    // 检查特定操作权限
    try {
      // 尝试使用jCasbin检查权限
      const hasPermission = await checkPermission(request, 'customers', 'create');
      if (!hasPermission) {
        return NextResponse.json({
          success: false,
          message: '没有权限创建客户'
        }, { status: 403 })
      }
    } catch (error) {
      console.error('权限检查失败:', error);
      // 如果权限检查失败，则继续执行，因为管理员已经通过了权限检查
    }


    // 解析请求体
    const body = await request.json()
    console.log('收到创建客户请求:', body)
    const { name, username, email, phone, password, accountType, creditLimit } = body

    // 验证必填字段
    if (!username || !email) {
      return NextResponse.json({
        success: false,
        message: '用户名和邮箱为必填项'
      }, { status: 400 })
    }

    // 检查用户名是否已存在
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUsername) {
      return NextResponse.json({
        success: false,
        message: '用户名已存在'
      }, { status: 400 })
    }

    // 检查邮箱是否已存在
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingEmail) {
      return NextResponse.json({
        success: false,
        message: '邮箱已被注册'
      }, { status: 400 })
    }

    // 确定角色代码
    let roleCode = 'USER'
    if (accountType === 'enterprise') {
      roleCode = 'ENTERPRISE'
    }

    // 生成随机密码（如果未提供）
    const userPassword = password || Math.random().toString(36).slice(-8) + 'A1'
    console.log('用户密码:', userPassword)

    // 加密密码
    const hashedPassword = await hash(userPassword, 10)

    // 创建用户
    console.log('开始创建客户:', { username, email, name: name || username, roleCode })
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        name: username, // 始终使用用户名作为昵称
        phone,
        roleCode,
        permissions: [],
        image: null,
        emailVerified: null,
        creditLimit: creditLimit || 0,
        createdById: userId, // 设置创建者ID
        status: 'active' // 确保新用户状态为激活
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        roleCode: true,
        creditLimit: true,
        status: true,
        balance: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      }
    })

    // 格式化返回数据
    const formattedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      phone: user.phone || "",
      roleCode: user.roleCode,
      accountType: user.role.type === "system" ? "admin" : user.role.type,
      role: user.roleCode,
      balance: user.balance || 0,
      creditLimit: user.creditLimit || 0,
      status: user.status,
      createdBy: user.createdBy ? {
        id: user.createdBy.id,
        name: user.createdBy.name || user.createdBy.username
      } : null,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString()
    }

    return NextResponse.json({
      success: true,
      message: '客户创建成功',
      data: formattedUser
    }, { status: 201 })
  } catch (error) {
    // 使用错误处理服务记录和格式化错误
    ErrorService.logError(error, {
      context: '创建客户',
      userData: { username, email }
    })

    // 处理不同类型的错误
    if (error instanceof ApiError) {
      return NextResponse.json(
        ErrorService.handleApiError(error),
        { status: error.statusCode }
      )
    }

    // 处理其他错误
    return NextResponse.json(
      ErrorService.handleApiError(error),
      { status: 500 }
    )
  }
}
