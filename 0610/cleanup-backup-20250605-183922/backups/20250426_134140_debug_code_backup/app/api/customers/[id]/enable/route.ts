/**
 * 启用客户API
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import UserNotificationService from "@/lib/services/user-notification-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { emailService } from "@/lib/services/email"

/**
 * 启用客户账户
 *
 * @route POST /api/customers/[id]/enable
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "启用客户账户API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 })
    }

    const adminId = admin.id;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 更新用户状态
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: 'active'
      }
    })

    // 记录状态变更日志
    await prisma.userStatusLog.create({
      data: {
        userId,
        adminId,
        status: 'active',
        reason: '管理员启用账户'
      }
    })

    // 发送用户特定通知（管理员不会看到）
    try {
      const notification = await UserNotificationService.createAccountEnabledNotification(userId)
      console.log('启用账户通知创建成功:', notification.id)
    } catch (notificationError) {
      console.error('发送启用账户通知失败:', notificationError)
      // 通知失败不影响主流程
    }

    // 发送邮件通知
    try {
      if (user.email) {
        await emailService.sendAccountEnabledEmail(
          user.email,
          user.name || user.username
        )
        console.log('启用账户邮件已发送或加入队列:', user.email)
      }
    } catch (emailError) {
      console.error('发送启用账户邮件失败:', emailError)
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '用户已启用'
    })
  } catch (error) {
    console.error("启用用户错误:", error)
    return NextResponse.json({
      success: false,
      message: '启用用户失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
