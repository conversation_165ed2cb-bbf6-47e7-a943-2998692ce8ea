/**
 * 审批客户API
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"
import { checkPermission } from "@/lib/casbin/enforcer"

/**
 * 审批通过客户
 *
 * @route POST /api/customers/[id]/approve
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 使用统一认证服务获取当前用户
    const admin = await UnifiedAuthService.getCurrentUser(request, "客户审批API")
    if (!admin) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 使用jCasbin检查权限
    const hasPermission = await checkPermission(
      admin.id,
      "customer",
      "approve",
      { role: admin.roleCode }
    )

    // 如果不是管理员或没有权限，返回403
    if (!hasPermission && admin.roleCode !== 'super' && admin.roleCode !== 'admin') {
      return NextResponse.json({
        success: false,
        message: '没有权限执行此操作'
      }, { status: 403 })
    }

    const adminId = admin.id

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 获取用户认证资料
    const verification = await prisma.userVerification.findUnique({
      where: { userId },
    })

    // 更新用户状态
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: 'active',
        verificationStatus: 'approved'
      }
    })

    // 如果有认证资料，更新认证状态
    if (verification) {
      await prisma.userVerification.update({
        where: { userId },
        data: {
          status: 'approved',
          reviewerId: adminId as string,
          reviewedAt: new Date()
        }
      })

      // 发送邮件和通知
      try {
        // 导入通知服务
        const { NotificationService } = await import('@/lib/services/notification')
        const { emailService } = await import('@/lib/services/email')

        // 创建系统通知
        await NotificationService.createVerificationApprovedNotification(
          userId,
          verification.type as 'personal' | 'enterprise'
        )

        // 发送邮件通知
        if (user.email) {
          await emailService.sendVerificationApprovedEmail(
            user.email,
            user.name || user.username,
            verification.type
          )
        }
      } catch (error) {
        console.error('发送通知失败:', error)
        // 通知发送失败不影响主流程
      }
    }

    return NextResponse.json({
      success: true,
      message: '用户审批通过成功'
    })
  } catch (error) {
    console.error("审批用户错误:", error)
    return NextResponse.json({
      success: false,
      message: '审批用户失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
