import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取所有角色
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色API:${requestId}] 获取角色列表`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "角色管理API");
    if (response) {
      console.log(`[角色API:${requestId}] 未授权访问`)
      return response;
    }

    // 获取所有角色
    const roles = await prisma.role.findMany({
      include: {
        menus: true,
        operations: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 转换为前端需要的格式
    const formattedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      code: role.code,
      type: role.type,
      description: role.description,
      permissions: role.permissions,
      menuItems: role.menus.map(menu => menu.id),
      operationIds: role.operations.map(op => op.id),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }))

    return NextResponse.json({
      success: true,
      data: formattedRoles,
      requestId
    })
  } catch (error) {
    console.error(`[角色API:${requestId}] 获取角色列表失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取角色列表失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 创建新角色
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色API:${requestId}] 创建新角色`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "角色管理API");
    if (response) {
      console.log(`[角色API:${requestId}] 未授权访问`)
      return response;
    }

    // 获取请求体
    const body = await request.json()
    console.log(`[角色API:${requestId}] 请求体:`, body)

    // 验证必要字段
    if (!body.name || !body.code || !body.type) {
      return NextResponse.json({
        success: false,
        message: "缺少必要字段: name, code, type",
        requestId
      }, { status: 400 })
    }

    // 检查角色代码是否已存在
    const existingRole = await prisma.role.findUnique({
      where: { code: body.code }
    })

    if (existingRole) {
      return NextResponse.json({
        success: false,
        message: `角色代码 '${body.code}' 已存在`,
        requestId
      }, { status: 400 })
    }

    // 创建角色
    const role = await prisma.role.create({
      data: {
        name: body.name,
        code: body.code,
        type: body.type,
        description: body.description || "",
        permissions: body.permissions || []
      }
    })

    // 如果提供了菜单ID，关联菜单
    if (body.menuIds && Array.isArray(body.menuIds) && body.menuIds.length > 0) {
      // 获取有效的菜单ID
      const validMenus = await prisma.menu.findMany({
        where: {
          id: {
            in: body.menuIds
          }
        },
        select: {
          id: true,
          code: true
        }
      })

      if (validMenus.length > 0) {
        // 关联菜单
        await prisma.role.update({
          where: { id: role.id },
          data: {
            menus: {
              connect: validMenus.map(menu => ({ id: menu.id }))
            }
          }
        })

        // 为每个菜单添加jCasbin权限
        for (const menu of validMenus) {
          await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
        }
      }
    }

    // 如果提供了操作ID，关联操作
    if (body.operationIds && Array.isArray(body.operationIds) && body.operationIds.length > 0) {
      // 获取有效的操作ID
      const validOperations = await prisma.operation.findMany({
        where: {
          id: {
            in: body.operationIds
          }
        },
        select: {
          id: true,
          code: true
        }
      })

      if (validOperations.length > 0) {
        // 关联操作
        await prisma.role.update({
          where: { id: role.id },
          data: {
            operations: {
              connect: validOperations.map(op => ({ id: op.id }))
            }
          }
        })

        // 为每个操作添加jCasbin权限
        for (const op of validOperations) {
          await CasbinService.addPermissionForRole(role.code, `operation:${op.code}`, 'execute')
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: role,
      message: '角色创建成功',
      requestId
    })
  } catch (error) {
    console.error(`[角色API:${requestId}] 创建角色失败:`, error)
    return NextResponse.json({
      success: false,
      message: "创建角色失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
