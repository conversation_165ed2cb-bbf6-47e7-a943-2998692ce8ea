/**
 * 系统日志清理定时任务
 * 用于定期清理旧的系统日志
 */

import { NextRequest, NextResponse } from "next/server"
import CronService from "@/lib/services/cron-service"

/**
 * 清理旧系统日志
 *
 * @route GET /api/cron/clean-logs
 * @access 公开访问，但需要验证密钥
 */
export async function GET(request: NextRequest) {
  try {
    // 验证请求密钥
    const apiKey = request.headers.get('x-api-key');
    const cronSecret = process.env.CRON_SECRET || 'default-cron-secret';

    if (apiKey !== cronSecret) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    // 获取保留天数参数
    const url = new URL(request.url);
    const days = parseInt(url.searchParams.get('days') || '30');

    // 使用定时任务服务清理旧日志
    const result = await CronService.cleanOldLogs(days);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("清理旧日志错误:", error);
    return NextResponse.json({
      success: false,
      message: '清理旧日志失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
