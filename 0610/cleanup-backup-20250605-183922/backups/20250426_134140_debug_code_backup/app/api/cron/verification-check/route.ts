import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

/**
 * 定时任务：检查认证类型变更截止日期
 * 如果用户未在截止日期前补充资料，则恢复到原始认证状态
 *
 * @route GET /api/cron/verification-check
 */
export async function GET(req: NextRequest) {
  try {
    // 验证请求是否来自Cron Job
    const authHeader = req.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.split(' ')[1]
    // 这里应该使用环境变量中的密钥进行验证
    // 为了简化，我们暂时使用一个固定的密钥
    const cronSecret = process.env.CRON_SECRET || 'your-cron-secret'

    if (token !== cronSecret) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401 }
      )
    }

    // 获取当前日期
    const now = new Date()

    // 查找所有已过期但未完成的认证类型变更记录
    const expiredChanges = await prisma.verificationTypeChange.findMany({
      where: {
        deadline: {
          lt: now
        },
        completed: false
      },
      include: {
        user: true
      }
    })

    console.log(`找到 ${expiredChanges.length} 条已过期的认证类型变更记录`)

    // 处理每条过期记录
    const results = await Promise.all(
      expiredChanges.map(async (change) => {
        try {
          // 恢复用户的原始认证状态和类型
          await prisma.user.update({
            where: { id: change.userId },
            data: {
              verificationType: change.originalType || 'none',
              verificationStatus: change.originalStatus || 'none'
            }
          })

          // 删除用户的认证资料
          await prisma.userVerification.deleteMany({
            where: { userId: change.userId }
          })

          // 标记认证类型变更记录为已完成
          await prisma.verificationTypeChange.update({
            where: { id: change.id },
            data: { completed: true }
          })

          // 创建系统通知
          // 注意：由于没有notification模型，我们这里暂时跳过通知创建
          // TODO: 实现系统通知功能

          return {
            userId: change.userId,
            username: change.user.username,
            success: true
          }
        } catch (error) {
          console.error(`处理用户 ${change.userId} 的认证类型变更记录时出错:`, error)
          return {
            userId: change.userId,
            username: change.user.username,
            success: false,
            error: (error as Error).message
          }
        }
      })
    )

    return NextResponse.json(
      {
        success: true,
        message: `已处理 ${expiredChanges.length} 条过期的认证类型变更记录`,
        data: results
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('处理认证类型变更定时任务时出错:', error)
    return NextResponse.json(
      { success: false, message: '服务器错误', error: (error as Error).message },
      { status: 500 }
    )
  }
}
