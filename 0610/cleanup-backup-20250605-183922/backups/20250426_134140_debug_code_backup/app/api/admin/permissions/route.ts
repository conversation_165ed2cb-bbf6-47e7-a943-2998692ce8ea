import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 获取所有权限
 * @route GET /api/admin/permissions
 */
export async function GET(req: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "获取权限列表API");
    if (response) {
      return response;
    }

    // 获取所有权限
    const permissions = await prisma.operation.findMany({
      orderBy: { id: "asc" }
    });

    return NextResponse.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error("获取权限列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取权限列表失败" },
      { status: 500 }
    );
  }
}

/**
 * 创建新权限
 * @route POST /api/admin/permissions
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "创建权限API");
    if (response) {
      return response;
    }

    // 解析请求体
    const body = await req.json();
    const { name, code, description } = body;

    // 验证必填字段
    if (!name || !code) {
      return NextResponse.json(
        { success: false, message: "名称和代码不能为空" },
        { status: 400 }
      );
    }

    // 检查代码是否已存在
    const existingPermission = await prisma.operation.findUnique({
      where: { code }
    });

    if (existingPermission) {
      return NextResponse.json(
        { success: false, message: "权限代码已存在" },
        { status: 400 }
      );
    }

    // 创建权限
    const permission = await prisma.operation.create({
      data: {
        name,
        code,
        type: "function", // 默认类型
        description
      }
    });

    return NextResponse.json({
      success: true,
      message: "创建权限成功",
      data: permission
    });
  } catch (error) {
    console.error("创建权限失败:", error);
    return NextResponse.json(
      { success: false, message: "创建权限失败" },
      { status: 500 }
    );
  }
}