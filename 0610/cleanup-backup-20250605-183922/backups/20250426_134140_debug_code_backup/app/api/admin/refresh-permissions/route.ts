import { NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { PermissionInitService } from "@/lib/services/permission-init-service"

/**
 * 刷新权限系统
 * 将数据库中的角色-菜单关联同步到 jCasbin
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限刷新API:${requestId}] 开始刷新权限系统`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "刷新权限系统API");
    if (response) {
      console.log(`[权限刷新API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以刷新权限系统",
        requestId
      }, { status: 403 })
    }

    // 初始化所有权限
    const success = await PermissionInitService.initAllPermissions()

    if (success) {
      return NextResponse.json({
        success: true,
        message: "权限系统刷新成功",
        requestId
      })
    } else {
      return NextResponse.json({
        success: false,
        message: "权限系统刷新失败",
        requestId
      }, { status: 500 })
    }
  } catch (error) {
    console.error(`[权限刷新API:${requestId}] 刷新权限系统失败:`, error)
    return NextResponse.json({
      success: false,
      message: "刷新权限系统失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
