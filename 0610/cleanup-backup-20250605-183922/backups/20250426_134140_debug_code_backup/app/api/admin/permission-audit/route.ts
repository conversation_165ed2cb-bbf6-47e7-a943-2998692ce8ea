/**
 * 权限审计API
 * 提供权限变更历史查询功能
 */

import { NextRequest, NextResponse } from "next/server"
import { PermissionAuditService } from "@/lib/services/permission-audit-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取权限审计日志
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限审计API:${requestId}] 获取权限审计日志`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "获取权限审计日志API");
    if (response) {
      console.log(`[权限审计API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以查看权限审计日志",
        requestId
      }, { status: 403 })
    }

    // 获取查询参数
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const userId = url.searchParams.get('userId') || undefined
    const action = url.searchParams.get('action') || undefined
    const targetType = url.searchParams.get('targetType') || undefined
    const targetId = url.searchParams.get('targetId') || undefined
    const startDate = url.searchParams.get('startDate') ? new Date(url.searchParams.get('startDate') as string) : undefined
    const endDate = url.searchParams.get('endDate') ? new Date(url.searchParams.get('endDate') as string) : undefined

    // 查询权限审计日志
    const result = await PermissionAuditService.query({
      userId,
      action,
      targetType,
      targetId,
      startDate,
      endDate,
      page,
      pageSize
    })

    return NextResponse.json({
      success: true,
      data: result,
      requestId
    })
  } catch (error) {
    console.error(`[权限审计API:${requestId}] 获取权限审计日志失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取权限审计日志失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
