import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuthMiddleware } from '@/lib/middleware/auth-middleware';

/**
 * 通知标记已读API
 *
 * 此API用于标记通知为已读，并更新通知的阅读统计数据，包括：
 * 1. 标记单个通知为已读
 * 2. 更新通知的阅读统计数据（已读数、阅读率等）
 * 3. 记录用户的阅读时间
 */

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "通知标记已读API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    // 检查通知是否存在
    const notification = await prisma.notification.findUnique({
      where: {
        id: params.id
      },
      select: {
        id: true,
        readCount: true,
        totalRecipients: true,
        sendToAll: true,
        recipients: true
      }
    });

    if (!notification) {
      return NextResponse.json({
        success: false,
        message: '通知不存在'
      }, { status: 404 });
    }

    // 查找现有的用户通知记录
    const existingUserNotification = await prisma.userNotification.findFirst({
      where: {
        userId: user.id,
        notificationId: params.id
      }
    });

    // 当前时间
    const now = new Date();

    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      let isNewRead = false;

      if (existingUserNotification) {
        // 如果记录存在且未读，更新它
        if (!existingUserNotification.read) {
          await tx.userNotification.update({
            where: {
              id: existingUserNotification.id
            },
            data: {
              read: true,
              readAt: now,
              viewCount: { increment: 1 },
              lastViewedAt: now
            }
          });
          isNewRead = true;
        } else {
          // 如果已经是已读状态，只更新访问次数
          await tx.userNotification.update({
            where: {
              id: existingUserNotification.id
            },
            data: {
              viewCount: { increment: 1 },
              lastViewedAt: now
            }
          });
        }
      } else {
        // 如果记录不存在，创建新记录
        await tx.userNotification.create({
          data: {
            userId: user.id,
            notificationId: params.id,
            read: true,
            readAt: now,
            viewCount: 1,
            lastViewedAt: now
          }
        });
        isNewRead = true;
      }

      // 如果是新的已读记录，更新通知的阅读统计
      if (isNewRead) {
        // 计算总接收者数
        let totalRecipients = notification.totalRecipients;

        // 如果总接收者数为0，重新计算
        if (totalRecipients === 0) {
          if (notification.sendToAll) {
            // 如果是全局通知，总接收者数为所有用户数
            totalRecipients = await tx.user.count();
          } else if (notification.recipients) {
            // 如果是指定用户通知，总接收者数为指定用户数
            try {
              const recipients = typeof notification.recipients === 'string'
                ? JSON.parse(notification.recipients)
                : notification.recipients;
              totalRecipients = Array.isArray(recipients) ? recipients.length : 1;
            } catch (e) {
              console.error('解析recipients错误:', e);
              totalRecipients = 1; // 默认值
            }
          }
        }

        // 更新通知的阅读统计
        const newReadCount = (notification.readCount || 0) + 1;
        const readRate = totalRecipients > 0 ? newReadCount / totalRecipients : 0;

        await tx.notification.update({
          where: { id: params.id },
          data: {
            readCount: newReadCount,
            totalRecipients,
            readRate,
            lastReadAt: now
          }
        });
      }
    });

    return NextResponse.json({
      success: true,
      message: '标记已读成功'
    }, { status: 200 });
  } catch (error) {
    console.error('标记通知为已读错误:', error);
    return NextResponse.json({
      success: false,
      message: '标记已读失败'
    }, { status: 500 });
  }
}