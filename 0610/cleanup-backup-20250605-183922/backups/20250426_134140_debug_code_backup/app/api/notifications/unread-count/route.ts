import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 获取未读通知数量
 *
 * @route GET /api/notifications/unread-count
 * @access 所有已登录用户可访问
 */
export async function GET(req: NextRequest) {
  console.log("获取未读通知数量API被调用");

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "获取未读通知数量API");
    if (response) {
      console.log("获取未读通知数量 - 未授权访问");
      return NextResponse.json({
        success: false,
        message: '未授权访问',
      }, { status: 401 });
    }

    const userId = user.id;
    console.log('当前用户ID:', userId);

    // 查询未读通知数量 - 使用原始 SQL 以确保准确性
    console.log(`查询用户 ${userId} 的未读通知数量...`);

    // 打印调试信息，查看所有通知
    const allNotifications = await prisma.notification.findMany({
      select: {
        id: true,
        title: true,
        type: true,
        sendToAll: true,
        recipients: true,
        status: true,
        userNotifications: {
          where: {
            userId: user.id
          },
          select: {
            read: true
          }
        }
      }
    });

    console.log('所有通知:', JSON.stringify(allNotifications, null, 2));

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: user.id },
      select: { roleCode: true }
    });

    // 检查用户的通知设置
    const userSettings = await prisma.userNotificationSettings.findUnique({
      where: { userId: user.id }
    });

    // 如果用户关闭了应用内通知，返回0
    if (userSettings && userSettings.appEnabled === false) {
      console.log(`用户 ${userId} 已关闭应用内通知，返回未读数量为0`);
      return NextResponse.json({
        success: true,
        message: '获取未读通知数量成功',
        data: {
          total: 0
        }
      });
    }

    const isAdmin = userRole?.roleCode === 'ADMIN';
    console.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    let whereClause;

    if (isAdmin) {
      // 管理员不应该看到与用户相关的特定通知
      whereClause = `n.status = 'published' AND (un.id IS NULL OR un.read = FALSE) AND
        (n."sendToAll" = true OR (n."sendToAll" = false AND (n.recipients::text ILIKE '%"${user.id}"%' OR n.recipients::text ILIKE '%${user.id}%'))) AND
        (n.title NOT IN ('账户已启用', '账户已禁用', '认证申请未通过', '认证申请已通过', '密码已重置', '您的个人认证已通过', '您的企业认证已通过', '您的个人认证未通过', '您的企业认证未通过')) AND
        (n."typeId" NOT IN (5, 6))`;
      console.log('管理员用户，只计算全局通知和发送给管理员的未读通知，但不包括用户特定的通知（如账户启用/禁用、认证结果、密码重置等）');
    } else {
      // 普通用户只计算发送给自己的未读通知
      whereClause = `n.status = 'published' AND (un.id IS NULL OR un.read = FALSE) AND (n."sendToAll" = true OR (n."sendToAll" = false AND (n.recipients::text ILIKE '%"${user.id}"%' OR n.recipients::text ILIKE '%${user.id}%')))`;
      console.log('普通用户，只计算发送给自己的未读通知');
    }

    const query = `
      SELECT COUNT(*) as count
      FROM notification n
      LEFT JOIN "user_notification" un
        ON n.id = un."notificationId"
        AND un."userId" = '${user.id}'
      WHERE ${whereClause}
    `;

    console.log('执行的SQL查询:', query);
    const result = await prisma.$queryRawUnsafe(query);


    const count = Number((result as any[])[0]?.count || 0);
    console.log(`用户 ${userId} 的未读通知数量: ${count}`);

    return NextResponse.json({
      success: true,
      message: '获取未读通知数量成功',
      data: {
        total: count
      }
    });
  } catch (error) {
    console.error("获取未读通知数量错误:", error);
    return NextResponse.json({
      success: false,
      message: '获取未读通知数量失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}