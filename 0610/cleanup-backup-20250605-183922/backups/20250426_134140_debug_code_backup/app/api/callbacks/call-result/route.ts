import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import CallbackUtils from "@/lib/api/callback-utils"
import { UnifiedAuthService } from "@/lib/services/auth-service"

/**
 * 外呼结果回调接口
 * 接收外呼平台的回调请求，处理外呼结果数据
 * 
 * 回调地址: https://your-domain.com/api/callbacks/call-result
 * 请求方式: POST
 * 请求头: Content-Type: application/json; charset=utf-8
 * 请求体: { "data": "加密数据" }
 */
export async function POST(req: Request) {
  try {
    // 权限检查
    const hasPermission = await CallbackUtils.checkPermission(req)
    if (!hasPermission) {
      return NextResponse.json(
        { errCode: 403, errInfo: "没有权限访问此接口" },
        { status: 403 }
      )
    }

    // 解析请求体
    const body = await req.json()
    
    // 检查请求参数
    if (!body.data) {
      return NextResponse.json(
        { errCode: 400, errInfo: "缺少必要参数" },
        { status: 400 }
      )
    }

    // 解密数据
    const decryptedData = CallbackUtils.decryptData(body.data)
    
    // 记录回调日志
    await CallbackUtils.logCallback("CALL_RESULT", decryptedData, "SUCCESS")

    // 处理外呼结果数据
    await processCallResult(decryptedData)

    // 返回成功响应
    return NextResponse.json({ errCode: 0, errInfo: "" })
  } catch (error) {
    console.error("处理外呼结果回调失败:", error)
    
    // 记录错误日志
    await CallbackUtils.logCallback("CALL_RESULT", { error: (error as Error).message }, "ERROR")
    
    // 返回错误响应
    return NextResponse.json(
      { errCode: 500, errInfo: "处理回调请求失败" },
      { status: 500 }
    )
  }
}

/**
 * 处理外呼结果数据
 * @param data 解密后的外呼结果数据
 */
async function processCallResult(data: any) {
  try {
    // 提取关键数据
    const {
      mediaDeliverId, // 任务ID
      callId, // 呼叫ID
      phone, // 手机号码
      isConnected, // 接通状态
      holdingTime, // 接通时长
      connectType, // 接通类型
      intention, // 意愿
      callStartTime, // 呼叫开始时间
      callOnTime, // 呼叫接通时间
      callEndTime, // 呼叫结束时间
      waitingTime, // 呼叫等待时间
      fullAudioUrl, // 录音地址
      completionRate, // 完播率
    } = data

    // 查找关联的任务
    const task = await prisma.task.findUnique({
      where: { externalId: mediaDeliverId },
      include: { user: true },
    })

    // 如果找不到任务，记录错误并返回
    if (!task) {
      console.error(`找不到任务: ${mediaDeliverId}`)
      return
    }

    // 创建或更新呼叫记录
    await prisma.callRecord.upsert({
      where: { externalCallId: callId },
      update: {
        phone,
        isConnected: isConnected === 2, // 1:未接通, 2:已接通
        duration: holdingTime || 0,
        connectionType: connectType === 1 ? "VIDEO" : "AUDIO", // 1:视频接通, 2:语音接通
        intention,
        startTime: callStartTime ? new Date(callStartTime) : null,
        connectTime: callOnTime ? new Date(callOnTime) : null,
        endTime: callEndTime ? new Date(callEndTime) : null,
        waitingTime: waitingTime || 0,
        recordingUrl: fullAudioUrl || null,
        completionRate: completionRate ? parseFloat(completionRate) : null,
        status: "COMPLETED",
        taskId: task.id,
        userId: task.userId,
      },
      create: {
        externalCallId: callId,
        phone,
        isConnected: isConnected === 2,
        duration: holdingTime || 0,
        connectionType: connectType === 1 ? "VIDEO" : "AUDIO",
        intention,
        startTime: callStartTime ? new Date(callStartTime) : null,
        connectTime: callOnTime ? new Date(callOnTime) : null,
        endTime: callEndTime ? new Date(callEndTime) : null,
        waitingTime: waitingTime || 0,
        recordingUrl: fullAudioUrl || null,
        completionRate: completionRate ? parseFloat(completionRate) : null,
        status: "COMPLETED",
        taskId: task.id,
        userId: task.userId,
      },
    })

    // 更新任务状态和进度
    await updateTaskProgress(mediaDeliverId)

    // 创建通知
    await createNotification(task, data)
  } catch (error) {
    console.error("处理外呼结果数据失败:", error)
    throw error
  }
}

/**
 * 更新任务进度
 * @param taskExternalId 任务外部ID
 */
async function updateTaskProgress(taskExternalId: string) {
  try {
    // 查找任务
    const task = await prisma.task.findUnique({
      where: { externalId: taskExternalId },
    })

    if (!task) {
      return
    }

    // 查询该任务的所有呼叫记录
    const callRecords = await prisma.callRecord.findMany({
      where: { taskId: task.id },
    })

    // 计算进度
    const totalCalls = task.totalCalls || callRecords.length
    const completedCalls = callRecords.filter(record => record.status === "COMPLETED").length
    const progress = totalCalls > 0 ? Math.floor((completedCalls / totalCalls) * 100) : 0

    // 更新任务进度
    await prisma.task.update({
      where: { id: task.id },
      data: {
        progress,
        status: progress >= 100 ? "COMPLETED" : task.status,
        completionTime: progress >= 100 ? new Date() : null,
      },
    })
  } catch (error) {
    console.error("更新任务进度失败:", error)
  }
}

/**
 * 创建通知
 * @param task 任务
 * @param callData 呼叫数据
 */
async function createNotification(task: any, callData: any) {
  try {
    // 只为接通的呼叫创建通知
    if (callData.isConnected !== 2) {
      return
    }

    // 创建通知内容
    const title = `外呼任务 "${task.name}" 有新的接通记录`
    const content = `
      <p>电话号码: ${callData.phone}</p>
      <p>接通类型: ${callData.connectType === 1 ? '视频接通' : '语音接通'}</p>
      <p>接通时长: ${callData.holdingTime || 0}秒</p>
      <p>客户意愿: ${callData.intention || '-'}</p>
      <p>完播率: ${callData.completionRate || 0}%</p>
    `

    // 创建通知
    await prisma.notification.create({
      data: {
        title,
        content,
        type: "TASK",
        status: "UNREAD",
        userId: task.userId,
      },
    })
  } catch (error) {
    console.error("创建通知失败:", error)
  }
}
