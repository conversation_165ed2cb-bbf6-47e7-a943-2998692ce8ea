import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import CallbackUtils from "@/lib/api/callback-utils"
import { UnifiedAuthService } from "@/lib/services/auth-service"

/**
 * 短信状态推送回调接口
 * 接收外呼平台的回调请求，处理短信状态数据
 * 
 * 回调地址: https://your-domain.com/api/callbacks/sms-status
 * 请求方式: POST
 * 请求头: Content-Type: application/json; charset=utf-8
 * 请求体: { "data": "加密数据" }
 */
export async function POST(req: Request) {
  try {
    // 权限检查
    const hasPermission = await CallbackUtils.checkPermission(req)
    if (!hasPermission) {
      return NextResponse.json(
        { errCode: 403, errInfo: "没有权限访问此接口" },
        { status: 403 }
      )
    }

    // 解析请求体
    const body = await req.json()
    
    // 检查请求参数
    if (!body.data) {
      return NextResponse.json(
        { errCode: 400, errInfo: "缺少必要参数" },
        { status: 400 }
      )
    }

    // 解密数据
    const decryptedData = CallbackUtils.decryptData(body.data)
    
    // 记录回调日志
    await CallbackUtils.logCallback("SMS_STATUS", decryptedData, "SUCCESS")

    // 处理短信状态数据
    await processSmsStatus(decryptedData)

    // 返回成功响应
    return NextResponse.json({ errCode: 0, errInfo: "" })
  } catch (error) {
    console.error("处理短信状态回调失败:", error)
    
    // 记录错误日志
    await CallbackUtils.logCallback("SMS_STATUS", { error: (error as Error).message }, "ERROR")
    
    // 返回错误响应
    return NextResponse.json(
      { errCode: 500, errInfo: "处理回调请求失败" },
      { status: 500 }
    )
  }
}

/**
 * 处理短信状态数据
 * @param data 解密后的短信状态数据
 */
async function processSmsStatus(data: any) {
  try {
    // 提取关键数据
    const {
      msgId, // 短信记录ID
      taskId, // 任务ID
      callId, // 通话ID
      phone, // 手机号码
      commitResult, // 短信提交状态 0:提交失败, 1:提交成功
      sendResult, // 短信发送状态 0:接收成功, 1:接收失败
      sendTime, // 短信发送时间
      recvTime, // 短信接收时间
      signature, // 短信签名
      content, // 短信内容
    } = data

    // 查找关联的任务
    const task = await prisma.task.findUnique({
      where: { externalId: taskId },
      include: { user: true },
    })

    // 如果找不到任务，记录错误并返回
    if (!task) {
      console.error(`找不到任务: ${taskId}`)
      return
    }

    // 查找关联的呼叫记录
    const callRecord = await prisma.callRecord.findFirst({
      where: { externalCallId: callId },
    })

    // 创建或更新短信记录
    await prisma.smsRecord.upsert({
      where: { externalMsgId: msgId },
      update: {
        phone,
        content: content || "",
        signature: signature || "",
        isSubmitted: commitResult === 1,
        isDelivered: sendResult === 0,
        sendTime: sendTime ? new Date(sendTime) : null,
        receiveTime: recvTime ? new Date(recvTime) : null,
        status: sendResult === 0 ? "DELIVERED" : "FAILED",
        taskId: task.id,
        userId: task.userId,
        callRecordId: callRecord?.id,
      },
      create: {
        externalMsgId: msgId,
        phone,
        content: content || "",
        signature: signature || "",
        isSubmitted: commitResult === 1,
        isDelivered: sendResult === 0,
        sendTime: sendTime ? new Date(sendTime) : null,
        receiveTime: recvTime ? new Date(recvTime) : null,
        status: sendResult === 0 ? "DELIVERED" : "FAILED",
        taskId: task.id,
        userId: task.userId,
        callRecordId: callRecord?.id,
      },
    })

    // 如果短信发送失败，创建通知
    if (commitResult === 0 || sendResult === 1) {
      await createSmsFailureNotification(task, data)
    }
  } catch (error) {
    console.error("处理短信状态数据失败:", error)
    throw error
  }
}

/**
 * 创建短信失败通知
 * @param task 任务
 * @param smsData 短信数据
 */
async function createSmsFailureNotification(task: any, smsData: any) {
  try {
    // 创建通知内容
    const title = `外呼任务 "${task.name}" 短信发送失败`
    const content = `
      <p>电话号码: ${smsData.phone}</p>
      <p>发送时间: ${smsData.sendTime || '-'}</p>
      <p>失败原因: ${smsData.commitResult === 0 ? '提交失败' : '接收失败'}</p>
    `

    // 创建通知
    await prisma.notification.create({
      data: {
        title,
        content,
        type: "SMS",
        status: "UNREAD",
        userId: task.userId,
      },
    })
  } catch (error) {
    console.error("创建短信失败通知失败:", error)
  }
}
