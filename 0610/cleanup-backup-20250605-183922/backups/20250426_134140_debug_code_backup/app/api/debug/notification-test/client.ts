/**
 * 测试通知和邮件功能
 * 
 * @param userId 用户ID
 * @param reason 禁用原因
 * @param email 测试邮箱地址
 * @returns 测试结果
 */
export async function testNotification(userId: string, reason?: string, email?: string) {
  try {
    const response = await fetch('/api/debug/notification-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId,
        reason,
        email
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('测试通知失败:', error);
    throw error;
  }
}
