import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取单个菜单
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 获取菜单详情: ${params.id}`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取菜单详情API");
    if (response) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    // 获取菜单
    const menu = await prisma.menu.findUnique({
      where: { id: params.id },
      include: {
        children: true,
        roles: true
      }
    })

    if (!menu) {
      return NextResponse.json({
        success: false,
        message: "菜单不存在",
        requestId
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: menu,
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 获取菜单详情失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取菜单详情失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 更新菜单
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 更新菜单: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "更新菜单API");
    if (response) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以更新菜单",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()

    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: { id: params.id }
    })

    if (!existingMenu) {
      return NextResponse.json({
        success: false,
        message: "菜单不存在",
        requestId
      }, { status: 404 })
    }

    // 更新菜单
    const menu = await prisma.menu.update({
      where: { id: params.id },
      data: {
        name: body.name !== undefined ? body.name : existingMenu.name,
        path: body.path !== undefined ? body.path : existingMenu.path,
        icon: body.icon !== undefined ? body.icon : existingMenu.icon,
        visible: body.visible !== undefined ? body.visible : existingMenu.visible,
        order: body.order !== undefined ? body.order : existingMenu.order,
        parentId: body.parentId !== undefined ? body.parentId : existingMenu.parentId
      }
    })

    // 如果代码发生变化，更新jCasbin权限
    if (body.code && body.code !== existingMenu.code) {
      // 获取所有角色
      const roles = await prisma.role.findMany({
        include: {
          menus: {
            where: { id: params.id }
          }
        }
      })

      // 更新每个角色的权限
      for (const role of roles) {
        if (role.menus.length > 0) {
          // 删除旧权限
          await CasbinService.removePermissionForRole(role.code, `menu:${existingMenu.code}`, 'view')
          // 添加新权限
          await CasbinService.addPermissionForRole(role.code, `menu:${body.code}`, 'view')
        }
      }

      // 更新菜单代码
      await prisma.menu.update({
        where: { id: params.id },
        data: { code: body.code }
      })
    }

    return NextResponse.json({
      success: true,
      data: menu,
      message: '菜单更新成功',
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 更新菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "更新菜单失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 部分更新菜单（如可见性）
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 部分更新菜单: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "部分更新菜单API");
    if (response) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以更新菜单",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()

    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: { id: params.id }
    })

    if (!existingMenu) {
      return NextResponse.json({
        success: false,
        message: "菜单不存在",
        requestId
      }, { status: 404 })
    }

    // 更新菜单
    const menu = await prisma.menu.update({
      where: { id: params.id },
      data: body
    })

    return NextResponse.json({
      success: true,
      data: menu,
      message: '菜单更新成功',
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 部分更新菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "更新菜单失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 删除菜单
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 删除菜单: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "删除菜单API");
    if (response) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以删除菜单",
        requestId
      }, { status: 403 })
    }

    // 检查菜单是否存在
    const existingMenu = await prisma.menu.findUnique({
      where: { id: params.id },
      include: {
        roles: true
      }
    })

    if (!existingMenu) {
      return NextResponse.json({
        success: false,
        message: "菜单不存在",
        requestId
      }, { status: 404 })
    }

    // 从所有角色中移除菜单
    for (const role of existingMenu.roles) {
      // 从jCasbin中移除权限
      await CasbinService.removePermissionForRole(role.code, `menu:${existingMenu.code}`, 'view')
    }

    // 删除菜单
    await prisma.menu.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: '菜单删除成功',
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 删除菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "删除菜单失败",
      requestId
    }, { status: 500 })
  }
}
