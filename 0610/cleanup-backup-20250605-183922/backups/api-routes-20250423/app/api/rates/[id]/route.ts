import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from "next/headers";
import * as jose from 'jose';

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

/**
 * 获取单个费率详情
 * @route GET /api/rates/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const rate = await prisma.rate.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true
          }
        }
      }
    });

    if (!rate) {
      return NextResponse.json(
        {
          success: false,
          message: '费率不存在'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: rate
    });
  } catch (error) {
    console.error('获取费率详情失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取费率详情失败'
      },
      { status: 500 }
    );
  }
}

/**
 * 更新费率
 * @route PUT /api/rates/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();
    const { amount, period, customerId, businessType } = body;

    // 验证必填字段
    if (amount === undefined || !businessType) {
      return NextResponse.json(
        {
          success: false,
          message: '费率和业务类型不能为空'
        },
        { status: 400 }
      );
    }

    // 检查费率是否存在
    const existingRate = await prisma.rate.findUnique({
      where: { id }
    });

    if (!existingRate) {
      return NextResponse.json(
        {
          success: false,
          message: '费率不存在'
        },
        { status: 404 }
      );
    }

    // 更新费率
    const updatedRate = await prisma.rate.update({
      where: { id },
      data: {
        amount: parseFloat(amount),
        period: period ? parseInt(period) : null,
        customerId,
        businessType
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            username: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      message: '费率更新成功',
      data: updatedRate
    });
  } catch (error) {
    console.error('更新费率失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新费率失败'
      },
      { status: 500 }
    );
  }
}

/**
 * 删除费率
 * @route DELETE /api/rates/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    // 检查费率是否存在
    const existingRate = await prisma.rate.findUnique({
      where: { id }
    });

    if (!existingRate) {
      return NextResponse.json(
        {
          success: false,
          message: '费率不存在'
        },
        { status: 404 }
      );
    }

    // 删除费率
    await prisma.rate.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: '费率删除成功'
    });
  } catch (error) {
    console.error('删除费率失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '删除费率失败'
      },
      { status: 500 }
    );
  }
}
