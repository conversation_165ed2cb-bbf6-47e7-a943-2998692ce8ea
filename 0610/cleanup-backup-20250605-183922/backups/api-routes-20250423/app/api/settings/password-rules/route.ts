import { NextResponse } from "next/server"
import { getPasswordRules, getPasswordRuleDescriptions } from "@/lib/password-rules"

/**
 * 获取系统密码规则
 * 
 * @route GET /api/settings/password-rules
 * @access 公开
 */
export async function GET() {
  try {
    // 获取系统密码规则
    const rules = await getPasswordRules()
    
    // 获取规则描述
    const descriptions = getPasswordRuleDescriptions(rules)
    
    return NextResponse.json({
      success: true,
      data: {
        rules,
        descriptions
      }
    })
  } catch (error) {
    console.error("获取密码规则失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取密码规则失败"
      },
      { status: 500 }
    )
  }
}
