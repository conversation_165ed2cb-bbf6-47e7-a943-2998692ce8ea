"use client"

import { useState, useEffect } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "sonner"
import { ChevronDown, ChevronRight } from "lucide-react"

interface MenuItem {
  id: string
  name: string
  path?: string
  icon?: string
  code: string
  visible: boolean
  order: number
  parentId?: string | null
  children?: MenuItem[]
  checked?: boolean
}

interface MenuSelectorProps {
  selectedMenus: string[]
  onChange: (menus: string[]) => void
}

export function MenuSelector({ selectedMenus, onChange }: MenuSelectorProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({})

  // 加载菜单列表
  const loadMenuItems = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/menus")
      const data = await response.json()

      if (data.success) {
        // 添加选中状态
        const menuItemsWithChecked = addCheckedState(data.data, selectedMenus)
        setMenuItems(menuItemsWithChecked)

        // 默认展开所有有子菜单的项
        const newExpandedItems: Record<string, boolean> = {}
        const expandParents = (items: MenuItem[]) => {
          items.forEach(item => {
            if (item.children && item.children.length > 0) {
              newExpandedItems[item.id] = true
              expandParents(item.children)
            }
          })
        }
        expandParents(menuItemsWithChecked)
        setExpandedItems(newExpandedItems)
      } else {
        toast.error("加载菜单列表失败")
      }
    } catch (error) {
      console.error("加载菜单列表失败:", error)
      toast.error("加载菜单列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 添加选中状态
  const addCheckedState = (items: MenuItem[], selectedIds: string[]): MenuItem[] => {
    return items.map(item => ({
      ...item,
      checked: selectedIds.includes(item.id),
      children: item.children ? addCheckedState(item.children, selectedIds) : undefined
    }))
  }

  // 处理菜单选择变化
  const handleMenuChange = (menuId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedMenus, menuId])
    } else {
      onChange(selectedMenus.filter(id => id !== menuId))
    }
  }

  // 切换展开/折叠状态
  const toggleExpand = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }))
  }

  // 选择所有菜单
  const selectAllMenus = () => {
    const allMenuIds = getAllMenuIds(menuItems)
    onChange(allMenuIds)
  }

  // 取消选择所有菜单
  const deselectAllMenus = () => {
    onChange([])
  }

  // 获取所有菜单ID
  const getAllMenuIds = (items: MenuItem[]): string[] => {
    return items.reduce((acc: string[], item) => {
      if (item.id) {
        acc.push(item.id)
      }
      if (item.children && item.children.length > 0) {
        acc = [...acc, ...getAllMenuIds(item.children)]
      }
      return acc
    }, [])
  }

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems[item.id]

    return (
      <div key={item.id} className="mb-2">
        <div
          className={`flex items-center space-x-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 ${
            level > 0 ? `ml-${level * 4}` : ''
          }`}
        >
          {hasChildren && (
            <button
              type="button"
              onClick={() => toggleExpand(item.id)}
              className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            >
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </button>
          )}
          {!hasChildren && <div className="w-6"></div>}

          <Checkbox
            id={`menu-${item.id}`}
            checked={selectedMenus.includes(item.id)}
            onCheckedChange={(checked) =>
              handleMenuChange(item.id, checked === true)
            }
          />

          <Label
            htmlFor={`menu-${item.id}`}
            className="font-medium cursor-pointer flex-1"
          >
            {item.name}
            {item.path && (
              <span className="text-xs text-muted-foreground ml-2">
                {item.path}
              </span>
            )}
          </Label>
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-2">
            {item.children.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  // 初始加载
  useEffect(() => {
    loadMenuItems()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-x-2">
          <Badge variant="outline">已选择 {selectedMenus.length} 项</Badge>
          <Badge variant="outline">共 {getAllMenuIds(menuItems).length} 项</Badge>
        </div>
        <div className="space-x-2">
          <button
            type="button"
            className="text-xs text-blue-500 hover:underline"
            onClick={selectAllMenus}
          >
            全选
          </button>
          <button
            type="button"
            className="text-xs text-blue-500 hover:underline"
            onClick={deselectAllMenus}
          >
            取消全选
          </button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">菜单权限</CardTitle>
          <CardDescription>
            选择此角色可以访问的菜单项
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="max-h-96 overflow-y-auto pr-2">
            {menuItems.map(item => renderMenuItem(item))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
