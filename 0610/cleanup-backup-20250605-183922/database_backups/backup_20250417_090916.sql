--
-- PostgreSQL database dump
--

-- Dumped from database version 14.17 (Homebrew)
-- Dumped by pg_dump version 14.17 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: Account; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Account" (
    "userId" text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    "providerAccountId" text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text
);


ALTER TABLE public."Account" OWNER TO postgres;

--
-- Name: Session; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."Session" (
    id text NOT NULL,
    "sessionToken" text NOT NULL,
    "userId" text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."Session" OWNER TO postgres;

--
-- Name: VerificationToken; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."VerificationToken" (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public."VerificationToken" OWNER TO postgres;

--
-- Name: _ResourceOperations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."_ResourceOperations" (
    "A" text NOT NULL,
    "B" text NOT NULL
);


ALTER TABLE public."_ResourceOperations" OWNER TO postgres;

--
-- Name: _RoleMenus; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."_RoleMenus" (
    "A" text NOT NULL,
    "B" text NOT NULL
);


ALTER TABLE public."_RoleMenus" OWNER TO postgres;

--
-- Name: _RoleOperations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."_RoleOperations" (
    "A" text NOT NULL,
    "B" text NOT NULL
);


ALTER TABLE public."_RoleOperations" OWNER TO postgres;

--
-- Name: _RoleResources; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."_RoleResources" (
    "A" text NOT NULL,
    "B" text NOT NULL
);


ALTER TABLE public."_RoleResources" OWNER TO postgres;

--
-- Name: balance_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.balance_transactions (
    id text NOT NULL,
    "userId" text NOT NULL,
    amount double precision NOT NULL,
    "balanceAfter" double precision NOT NULL,
    type text NOT NULL,
    "paymentMethod" text,
    remarks text,
    "adminId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "creditLimitAfter" double precision,
    "creditLimitChange" double precision
);


ALTER TABLE public.balance_transactions OWNER TO postgres;

--
-- Name: casbin_rule; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.casbin_rule (
    id integer NOT NULL,
    ptype text NOT NULL,
    v0 text,
    v1 text,
    v2 text,
    v3 text,
    v4 text,
    v5 text
);


ALTER TABLE public.casbin_rule OWNER TO postgres;

--
-- Name: casbin_rule_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.casbin_rule_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.casbin_rule_id_seq OWNER TO postgres;

--
-- Name: casbin_rule_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.casbin_rule_id_seq OWNED BY public.casbin_rule.id;


--
-- Name: condition; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.condition (
    id text NOT NULL,
    attribute text NOT NULL,
    operator text NOT NULL,
    value text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "policyId" text NOT NULL
);


ALTER TABLE public.condition OWNER TO postgres;

--
-- Name: email_queue; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.email_queue (
    id text NOT NULL,
    recipient text NOT NULL,
    subject text NOT NULL,
    content text NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    "sentAt" timestamp(3) without time zone,
    "retryCount" integer DEFAULT 0 NOT NULL,
    "lastRetryAt" timestamp(3) without time zone,
    "errorMessage" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.email_queue OWNER TO postgres;

--
-- Name: loginAttempt; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."loginAttempt" (
    id text NOT NULL,
    attempts integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    identifier text NOT NULL,
    "lockedUntil" timestamp(3) without time zone,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    ip text NOT NULL,
    "userAgent" text,
    success boolean NOT NULL
);


ALTER TABLE public."loginAttempt" OWNER TO postgres;

--
-- Name: login_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.login_history (
    id text NOT NULL,
    "userId" text NOT NULL,
    "ipAddress" text NOT NULL,
    location text NOT NULL,
    device text NOT NULL,
    status text DEFAULT 'success'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "failReason" text
);


ALTER TABLE public.login_history OWNER TO postgres;

--
-- Name: menu; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.menu (
    id text NOT NULL,
    code text NOT NULL,
    name text NOT NULL,
    path text NOT NULL,
    icon text,
    "parentId" text,
    "order" integer DEFAULT 0 NOT NULL,
    visible boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.menu OWNER TO postgres;

--
-- Name: modalConfig; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."modalConfig" (
    id text NOT NULL,
    title text NOT NULL,
    description text,
    "maxWidth" text DEFAULT 'lg'::text NOT NULL,
    "customStyles" jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "createdById" text,
    "updatedById" text
);


ALTER TABLE public."modalConfig" OWNER TO postgres;

--
-- Name: notification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notification (
    id text NOT NULL,
    title text NOT NULL,
    content text NOT NULL,
    status text DEFAULT 'published'::text NOT NULL,
    priority text DEFAULT 'medium'::text NOT NULL,
    "createdBy" text DEFAULT 'system'::text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "publishedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "sendToAll" boolean DEFAULT true NOT NULL,
    recipients jsonb,
    "typeId" integer NOT NULL,
    "allowBatchMarkRead" boolean DEFAULT true NOT NULL,
    "lastReadAt" timestamp(3) without time zone,
    "readCount" integer DEFAULT 0 NOT NULL,
    "readRate" double precision DEFAULT 0 NOT NULL,
    "totalRecipients" integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.notification OWNER TO postgres;

--
-- Name: notificationType; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."notificationType" (
    code text NOT NULL,
    name text NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    id integer NOT NULL,
    "parentId" integer
);


ALTER TABLE public."notificationType" OWNER TO postgres;

--
-- Name: notificationType_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public."notificationType_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public."notificationType_id_seq" OWNER TO postgres;

--
-- Name: notificationType_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public."notificationType_id_seq" OWNED BY public."notificationType".id;


--
-- Name: operation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.operation (
    id text NOT NULL,
    code text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    description text,
    "menuId" text,
    enabled boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.operation OWNER TO postgres;

--
-- Name: policy; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.policy (
    id text NOT NULL,
    "resourceCode" text NOT NULL,
    "operationCode" text NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.policy OWNER TO postgres;

--
-- Name: rates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.rates (
    id text NOT NULL,
    amount double precision NOT NULL,
    period integer,
    "customerId" text,
    "businessType" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.rates OWNER TO postgres;

--
-- Name: resource; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.resource (
    id text NOT NULL,
    code text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    description text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.resource OWNER TO postgres;

--
-- Name: role; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.role (
    id text NOT NULL,
    code text NOT NULL,
    name text NOT NULL,
    type text NOT NULL,
    description text,
    permissions text[],
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "notificationEnabled" boolean DEFAULT true NOT NULL
);


ALTER TABLE public.role OWNER TO postgres;

--
-- Name: system_log; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.system_log (
    id text NOT NULL,
    "userId" text NOT NULL,
    action text NOT NULL,
    module text NOT NULL,
    "resourceId" text,
    "resourceType" text,
    details jsonb,
    "ipAddress" text,
    "userAgent" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    status text DEFAULT 'info'::text NOT NULL
);


ALTER TABLE public.system_log OWNER TO postgres;

--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.system_settings (
    id integer DEFAULT 1 NOT NULL,
    "siteName" text NOT NULL,
    logo text NOT NULL,
    "footerText" text,
    theme jsonb NOT NULL,
    features jsonb NOT NULL,
    security jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "appleMobileWebAppTitle" text,
    "applicationName" text,
    description text,
    keywords text,
    "loginPage" jsonb
);


ALTER TABLE public.system_settings OWNER TO postgres;

--
-- Name: user; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."user" (
    id text NOT NULL,
    username text NOT NULL,
    password text NOT NULL,
    email text NOT NULL,
    permissions text[],
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "roleCode" text NOT NULL,
    "emailVerified" timestamp(3) without time zone,
    image text,
    name text,
    "lastLoginAt" timestamp(3) without time zone,
    address text,
    city text,
    district text,
    phone text,
    province text,
    "verificationStatus" text DEFAULT 'none'::text,
    "verificationType" text DEFAULT 'none'::text,
    wechat text,
    balance double precision DEFAULT 0 NOT NULL,
    "creditLimit" double precision DEFAULT 0 NOT NULL,
    status text DEFAULT 'active'::text NOT NULL,
    "createdById" text
);


ALTER TABLE public."user" OWNER TO postgres;

--
-- Name: user_credit_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_credit_logs (
    id text NOT NULL,
    "userId" text NOT NULL,
    "adminId" text NOT NULL,
    "oldCreditLimit" double precision NOT NULL,
    "newCreditLimit" double precision NOT NULL,
    remarks text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.user_credit_logs OWNER TO postgres;

--
-- Name: user_notification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_notification (
    id text NOT NULL,
    "userId" text NOT NULL,
    "notificationId" text NOT NULL,
    read boolean DEFAULT false NOT NULL,
    "readAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "lastViewedAt" timestamp(3) without time zone,
    starred boolean DEFAULT false NOT NULL,
    "starredAt" timestamp(3) without time zone,
    "viewCount" integer DEFAULT 0 NOT NULL,
    visible boolean DEFAULT true NOT NULL,
    "emailSent" boolean DEFAULT false NOT NULL,
    "emailSentAt" timestamp(3) without time zone
);


ALTER TABLE public.user_notification OWNER TO postgres;

--
-- Name: user_notification_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_notification_settings (
    id text NOT NULL,
    "userId" text NOT NULL,
    "emailEnabled" boolean DEFAULT true NOT NULL,
    "smsEnabled" boolean DEFAULT true NOT NULL,
    "appEnabled" boolean DEFAULT true NOT NULL,
    types text[] DEFAULT ARRAY['SYSTEM'::text, 'SECURITY'::text, 'TASK'::text],
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.user_notification_settings OWNER TO postgres;

--
-- Name: user_status_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_status_logs (
    id text NOT NULL,
    "userId" text NOT NULL,
    "adminId" text NOT NULL,
    status text NOT NULL,
    reason text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.user_status_logs OWNER TO postgres;

--
-- Name: user_verification; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_verification (
    id text NOT NULL,
    "userId" text NOT NULL,
    type text NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    remark text,
    "reviewerId" text,
    "reviewedAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "realName" text,
    "idCardNumber" text,
    "idCardFront" text,
    "idCardBack" text,
    "idCardHolding" text,
    "companyName" text,
    "legalPerson" text,
    "legalPersonIdCard" text,
    "socialCreditCode" text,
    "businessLicense" text,
    "otherDocuments" text[] DEFAULT ARRAY[]::text[]
);


ALTER TABLE public.user_verification OWNER TO postgres;

--
-- Name: verificationCode; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public."verificationCode" (
    id text NOT NULL,
    code text NOT NULL,
    email text NOT NULL,
    "expiresAt" timestamp(3) without time zone NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    type text NOT NULL
);


ALTER TABLE public."verificationCode" OWNER TO postgres;

--
-- Name: verification_type_change; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.verification_type_change (
    id text NOT NULL,
    "userId" text NOT NULL,
    "originalType" text,
    "originalStatus" text,
    "newType" text NOT NULL,
    reason text NOT NULL,
    "adminId" text NOT NULL,
    deadline timestamp(3) without time zone NOT NULL,
    completed boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_type_change OWNER TO postgres;

--
-- Name: casbin_rule id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.casbin_rule ALTER COLUMN id SET DEFAULT nextval('public.casbin_rule_id_seq'::regclass);


--
-- Name: notificationType id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."notificationType" ALTER COLUMN id SET DEFAULT nextval('public."notificationType_id_seq"'::regclass);


--
-- Data for Name: Account; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Account" ("userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state) FROM stdin;
\.


--
-- Data for Name: Session; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."Session" (id, "sessionToken", "userId", expires) FROM stdin;
\.


--
-- Data for Name: VerificationToken; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."VerificationToken" (identifier, token, expires) FROM stdin;
\.


--
-- Data for Name: _ResourceOperations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."_ResourceOperations" ("A", "B") FROM stdin;
\.


--
-- Data for Name: _RoleMenus; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."_RoleMenus" ("A", "B") FROM stdin;
cm9hxtcbn0000c7hb94n403pa	cm9frqrzc00005msnnzqvpuv2
cm9hxtcc60002c7hb5g6xyhdp	cm9frqrzc00005msnnzqvpuv2
cm9hxtcc90004c7hbstx4zz8y	cm9frqrzc00005msnnzqvpuv2
cm9hxtcca0005c7hbmbmda4ah	cm9frqrzc00005msnnzqvpuv2
cm9hxtccb0007c7hb440jdmag	cm9frqrzc00005msnnzqvpuv2
\.


--
-- Data for Name: _RoleOperations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."_RoleOperations" ("A", "B") FROM stdin;
\.


--
-- Data for Name: _RoleResources; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."_RoleResources" ("A", "B") FROM stdin;
\.


--
-- Data for Name: balance_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.balance_transactions (id, "userId", amount, "balanceAfter", type, "paymentMethod", remarks, "adminId", "createdAt", "updatedAt", "creditLimitAfter", "creditLimitChange") FROM stdin;
\.


--
-- Data for Name: casbin_rule; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.casbin_rule (id, ptype, v0, v1, v2, v3, v4, v5) FROM stdin;
\.


--
-- Data for Name: condition; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.condition (id, attribute, operator, value, "createdAt", "updatedAt", "policyId") FROM stdin;
\.


--
-- Data for Name: email_queue; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.email_queue (id, recipient, subject, content, status, "sentAt", "retryCount", "lastRetryAt", "errorMessage", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: loginAttempt; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."loginAttempt" (id, attempts, "createdAt", identifier, "lockedUntil", "updatedAt", ip, "userAgent", success) FROM stdin;
\.


--
-- Data for Name: login_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.login_history (id, "userId", "ipAddress", location, device, status, "createdAt", "updatedAt", "failReason") FROM stdin;
login_1744592680346_p7gdqdk	cm9gcltjz0001uipy1sunrlgg	127.0.0.1	未知位置	NextAuth登录	success	2025-04-14 01:04:40.346	2025-04-14 01:04:40.346	\N
login_1744592680527_1bl8ql4	cm9gcltjz0001uipy1sunrlgg	::1	未知位置	Apple Macintosh macOS 10.15.7 Chrome *********	success	2025-04-14 01:04:40.528	2025-04-14 01:04:40.527	\N
login_1744592710043_yeanh5m	cm9gcltjz0001uipy1sunrlgg	127.0.0.1	未知位置	NextAuth登录	success	2025-04-14 01:05:10.043	2025-04-14 01:05:10.043	\N
login_1744592710200_b8jm81h	cm9gcltjz0001uipy1sunrlgg	::1	未知位置	Apple Macintosh macOS 10.15.7 Chrome *********	success	2025-04-14 01:05:10.201	2025-04-14 01:05:10.2	\N
login_1744593015017_bm8r3iz	cm9frqx0u0001e5twif657meu	127.0.0.1	未知位置	NextAuth登录	failed	2025-04-14 01:10:15.018	2025-04-14 01:10:15.017	密码错误
login_1744593015542_3ca4o8r	cm9frqx0u0001e5twif657meu	::1	未知位置	Apple Macintosh macOS 10.15.7 Chrome *********	failed	2025-04-14 01:10:15.543	2025-04-14 01:10:15.542	密码错误
login_1744593022007_lamlzti	cm9gcltjz0001uipy1sunrlgg	127.0.0.1	未知位置	NextAuth登录	failed	2025-04-14 01:10:22.007	2025-04-14 01:10:22.007	密码错误
login_1744593022133_jrwfd7c	cm9gcltjz0001uipy1sunrlgg	::1	未知位置	Apple Macintosh macOS 10.15.7 Chrome *********	failed	2025-04-14 01:10:22.134	2025-04-14 01:10:22.133	密码错误
login_1744593031565_ursfsh8	cm9gcltjz0001uipy1sunrlgg	127.0.0.1	未知位置	NextAuth登录	success	2025-04-14 01:10:31.566	2025-04-14 01:10:31.565	\N
login_1744593031725_ssj025s	cm9gcltjz0001uipy1sunrlgg	::1	未知位置	Apple Macintosh macOS 10.15.7 Chrome *********	success	2025-04-14 01:10:31.726	2025-04-14 01:10:31.725	\N
login_1744593997876_ljp6uph	cm9gcltjz0001uipy1sunrlgg	127.0.0.1	未知位置	NextAuth登录	success	2025-04-14 01:26:37.877	2025-04-14 01:26:37.876	\N
\.


--
-- Data for Name: menu; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.menu (id, code, name, path, icon, "parentId", "order", visible, "createdAt", "updatedAt") FROM stdin;
cm9hxtcbn0000c7hb94n403pa	system	系统管理	/system	Settings	\N	100	t	2025-04-15 03:22:52.451	2025-04-15 03:22:52.451
cm9hxtcc60002c7hb5g6xyhdp	users	用户管理	/system/users	Users	cm9hxtcbn0000c7hb94n403pa	110	t	2025-04-15 03:22:52.47	2025-04-15 03:22:52.47
cm9hxtcc90004c7hbstx4zz8y	roles	角色管理	/system/roles	Shield	cm9hxtcbn0000c7hb94n403pa	120	t	2025-04-15 03:22:52.473	2025-04-15 03:22:52.473
cm9hxtcca0005c7hbmbmda4ah	settings	系统设置	/settings	Settings	\N	200	t	2025-04-15 03:22:52.474	2025-04-15 03:22:52.474
cm9hxtccb0007c7hb440jdmag	roles-settings	角色设置	/settings/roles	Shield	cm9hxtcca0005c7hbmbmda4ah	210	t	2025-04-15 03:22:52.476	2025-04-15 03:22:52.476
\.


--
-- Data for Name: modalConfig; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."modalConfig" (id, title, description, "maxWidth", "customStyles", "createdAt", "updatedAt", "createdById", "updatedById") FROM stdin;
\.


--
-- Data for Name: notification; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notification (id, title, content, status, priority, "createdBy", "createdAt", "updatedAt", "publishedAt", "sendToAll", recipients, "typeId", "allowBatchMarkRead", "lastReadAt", "readCount", "readRate", "totalRecipients") FROM stdin;
\.


--
-- Data for Name: notificationType; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."notificationType" (code, name, description, "createdAt", "updatedAt", id, "parentId") FROM stdin;
SYSTEM	系统通知	系统相关的通知	2025-04-13 12:29:58.412	2025-04-13 12:29:58.412	2	\N
SECURITY	安全通知	账户安全相关的通知	2025-04-13 12:29:58.605	2025-04-13 12:29:58.605	3	\N
TASK	任务通知	任务相关的通知	2025-04-13 12:29:58.628	2025-04-13 12:29:58.628	4	\N
VERIFICATION	认证通知	用户认证相关的通知	2025-04-13 12:29:58.631	2025-04-13 12:29:58.631	5	\N
\.


--
-- Data for Name: operation; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.operation (id, code, name, type, description, "menuId", enabled, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: policy; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.policy (id, "resourceCode", "operationCode", description, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: rates; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.rates (id, amount, period, "customerId", "businessType", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: resource; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.resource (id, code, name, type, description, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: role; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.role (id, code, name, type, description, permissions, "createdAt", "updatedAt", "notificationEnabled") FROM stdin;
cm9frqrzc00005msnnzqvpuv2	ADMIN	管理员	system	系统管理员	{*}	2025-04-13 14:57:22.727	2025-04-13 14:57:22.727	t
cm9frqrzw00015msnibw1siqe	USER	普通用户	personal	普通用户	{TASK_READ,TASK_CREATE}	2025-04-13 14:57:22.749	2025-04-13 14:57:22.749	t
cm9frqrzz00025msn7ur7l5ka	ENTERPRISE	企业用户	enterprise	企业用户	{TASK_READ,TASK_CREATE,TASK_MANAGE}	2025-04-13 14:57:22.751	2025-04-13 14:57:22.751	t
\.


--
-- Data for Name: system_log; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.system_log (id, "userId", action, module, "resourceId", "resourceType", details, "ipAddress", "userAgent", "createdAt", status) FROM stdin;
\.


--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.system_settings (id, "siteName", logo, "footerText", theme, features, security, "createdAt", "updatedAt", "appleMobileWebAppTitle", "applicationName", description, keywords, "loginPage") FROM stdin;
1	外呼管理系统	/logo.png	© 2025 外呼管理系统 版权所有	{"mode": "system", "primaryColor": "#0284c7"}	{"enableRegistration": true, "enableNotifications": true, "enablePasswordReset": true}	{"loginAttempts": 5, "requireNumber": true, "sessionTimeout": 30, "requireUppercase": true, "passwordMinLength": 8, "requireSpecialChar": true}	2025-04-13 12:29:58.413	2025-04-15 03:44:19.548	外呼系统	外呼管理系统	高效的外呼任务管理平台	外呼,管理系统,任务管理	{"title": "外呼管理系统", "features": [{"icon": "user", "text": "专业的客户管理"}, {"icon": "lock", "text": "安全的数据保护"}, {"icon": "mail", "text": "高效的沟通工具"}], "subtitle": "提升工作效率的得力助手", "backgroundImage": "/uploads/images/1744688615192.jpg", "backgroundEffect": "all"}
\.


--
-- Data for Name: user; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."user" (id, username, password, email, permissions, "createdAt", "updatedAt", "roleCode", "emailVerified", image, name, "lastLoginAt", address, city, district, phone, province, "verificationStatus", "verificationType", wechat, balance, "creditLimit", status, "createdById") FROM stdin;
cm9gcltjz0001uipy1sunrlgg	xm2025	$2a$10$xMjH/9RSraYHISOPobRm6..5xdWFjun4fe7JQhZz3UobpbsqQDzIS	<EMAIL>	{TASK_READ,TASK_CREATE}	2025-04-14 00:41:23.422	2025-04-14 01:57:17.09	USER	\N	/uploads/avatars/e-ZucY7sV8hXcNJBN481z.png	xm2025	2025-04-14 01:10:31.723	asdasdas	310100	310114	186386196789	310000	pending	personal	xyucn	0	0	active	\N
cm9frqx0u0001e5twif657meu	admin	$2a$10$liF4nGU2J.k2ov9iflpSTOG9nMB8cd9jk9MeN.Gp3X1lTOHmE1xEG	<EMAIL>	{*}	2025-04-13 14:57:29.263	2025-04-14 01:03:33.53	ADMIN	2025-04-13 14:57:29.261	\N	系统管理员	2025-04-14 00:54:06.671	1231231	310100	310115	18638619889	310000	none	none	xyucn	0	0	active	\N
\.


--
-- Data for Name: user_credit_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_credit_logs (id, "userId", "adminId", "oldCreditLimit", "newCreditLimit", remarks, "createdAt") FROM stdin;
\.


--
-- Data for Name: user_notification; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_notification (id, "userId", "notificationId", read, "readAt", "createdAt", "updatedAt", "lastViewedAt", starred, "starredAt", "viewCount", visible, "emailSent", "emailSentAt") FROM stdin;
\.


--
-- Data for Name: user_notification_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_notification_settings (id, "userId", "emailEnabled", "smsEnabled", "appEnabled", types, "createdAt", "updatedAt") FROM stdin;
cm9gcnm410003uipyuucvyrzf	cm9frqx0u0001e5twif657meu	t	f	t	{SYSTEM,SECURITY,TASK}	2025-04-14 00:42:47.09	2025-04-14 00:42:47.09
cm9gd8rgp0007uipyb6ctq61b	cm9gcltjz0001uipy1sunrlgg	t	f	t	{SYSTEM,SECURITY,TASK}	2025-04-14 00:59:13.801	2025-04-14 00:59:13.801
\.


--
-- Data for Name: user_status_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_status_logs (id, "userId", "adminId", status, reason, "createdAt") FROM stdin;
\.


--
-- Data for Name: user_verification; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_verification (id, "userId", type, status, remark, "reviewerId", "reviewedAt", "createdAt", "updatedAt", "realName", "idCardNumber", "idCardFront", "idCardBack", "idCardHolding", "companyName", "legalPerson", "legalPersonIdCard", "socialCreditCode", "businessLicense", "otherDocuments") FROM stdin;
cm9gdts350009uipyd737360o	cm9gcltjz0001uipy1sunrlgg	personal	pending	测试备注	\N	\N	2025-04-14 01:15:34.385	2025-04-14 01:15:34.385	测试	1231231	/uploads/verification/cm9gcltjz0001uipy1sunrlgg/idCardFront_2SJKhtDcmC.png	/uploads/verification/cm9gcltjz0001uipy1sunrlgg/idCardBack_DMFTi_yc8L.png	/uploads/verification/cm9gcltjz0001uipy1sunrlgg/idCardHolding_-iWRJV5u9M.png	\N	\N	\N	\N	\N	{}
\.


--
-- Data for Name: verificationCode; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public."verificationCode" (id, code, email, "expiresAt", "createdAt", "updatedAt", type) FROM stdin;
\.


--
-- Data for Name: verification_type_change; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.verification_type_change (id, "userId", "originalType", "originalStatus", "newType", reason, "adminId", deadline, completed, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Name: casbin_rule_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.casbin_rule_id_seq', 1, false);


--
-- Name: notificationType_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public."notificationType_id_seq"', 5, true);


--
-- Name: Account Account_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Account"
    ADD CONSTRAINT "Account_pkey" PRIMARY KEY (provider, "providerAccountId");


--
-- Name: Session Session_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Session"
    ADD CONSTRAINT "Session_pkey" PRIMARY KEY (id);


--
-- Name: VerificationToken VerificationToken_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."VerificationToken"
    ADD CONSTRAINT "VerificationToken_pkey" PRIMARY KEY (identifier, token);


--
-- Name: balance_transactions balance_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.balance_transactions
    ADD CONSTRAINT balance_transactions_pkey PRIMARY KEY (id);


--
-- Name: casbin_rule casbin_rule_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.casbin_rule
    ADD CONSTRAINT casbin_rule_pkey PRIMARY KEY (id);


--
-- Name: condition condition_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.condition
    ADD CONSTRAINT condition_pkey PRIMARY KEY (id);


--
-- Name: email_queue email_queue_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.email_queue
    ADD CONSTRAINT email_queue_pkey PRIMARY KEY (id);


--
-- Name: loginAttempt loginAttempt_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."loginAttempt"
    ADD CONSTRAINT "loginAttempt_pkey" PRIMARY KEY (id);


--
-- Name: login_history login_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.login_history
    ADD CONSTRAINT login_history_pkey PRIMARY KEY (id);


--
-- Name: menu menu_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.menu
    ADD CONSTRAINT menu_pkey PRIMARY KEY (id);


--
-- Name: modalConfig modalConfig_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."modalConfig"
    ADD CONSTRAINT "modalConfig_pkey" PRIMARY KEY (id);


--
-- Name: notificationType notificationType_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."notificationType"
    ADD CONSTRAINT "notificationType_pkey" PRIMARY KEY (id);


--
-- Name: notification notification_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT notification_pkey PRIMARY KEY (id);


--
-- Name: operation operation_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.operation
    ADD CONSTRAINT operation_pkey PRIMARY KEY (id);


--
-- Name: policy policy_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.policy
    ADD CONSTRAINT policy_pkey PRIMARY KEY (id);


--
-- Name: rates rates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.rates
    ADD CONSTRAINT rates_pkey PRIMARY KEY (id);


--
-- Name: resource resource_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.resource
    ADD CONSTRAINT resource_pkey PRIMARY KEY (id);


--
-- Name: role role_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.role
    ADD CONSTRAINT role_pkey PRIMARY KEY (id);


--
-- Name: system_log system_log_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_log
    ADD CONSTRAINT system_log_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (id);


--
-- Name: user_credit_logs user_credit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_credit_logs
    ADD CONSTRAINT user_credit_logs_pkey PRIMARY KEY (id);


--
-- Name: user_notification user_notification_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_notification
    ADD CONSTRAINT user_notification_pkey PRIMARY KEY (id);


--
-- Name: user_notification_settings user_notification_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_notification_settings
    ADD CONSTRAINT user_notification_settings_pkey PRIMARY KEY (id);


--
-- Name: user user_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- Name: user_status_logs user_status_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_status_logs
    ADD CONSTRAINT user_status_logs_pkey PRIMARY KEY (id);


--
-- Name: user_verification user_verification_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_verification
    ADD CONSTRAINT user_verification_pkey PRIMARY KEY (id);


--
-- Name: verificationCode verificationCode_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."verificationCode"
    ADD CONSTRAINT "verificationCode_pkey" PRIMARY KEY (id);


--
-- Name: verification_type_change verification_type_change_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.verification_type_change
    ADD CONSTRAINT verification_type_change_pkey PRIMARY KEY (id);


--
-- Name: Account_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "Account_userId_idx" ON public."Account" USING btree ("userId");


--
-- Name: Session_sessionToken_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "Session_sessionToken_key" ON public."Session" USING btree ("sessionToken");


--
-- Name: Session_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "Session_userId_idx" ON public."Session" USING btree ("userId");


--
-- Name: VerificationToken_token_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "VerificationToken_token_key" ON public."VerificationToken" USING btree (token);


--
-- Name: _ResourceOperations_AB_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "_ResourceOperations_AB_unique" ON public."_ResourceOperations" USING btree ("A", "B");


--
-- Name: _ResourceOperations_B_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "_ResourceOperations_B_index" ON public."_ResourceOperations" USING btree ("B");


--
-- Name: _RoleMenus_AB_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "_RoleMenus_AB_unique" ON public."_RoleMenus" USING btree ("A", "B");


--
-- Name: _RoleMenus_B_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "_RoleMenus_B_index" ON public."_RoleMenus" USING btree ("B");


--
-- Name: _RoleOperations_AB_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "_RoleOperations_AB_unique" ON public."_RoleOperations" USING btree ("A", "B");


--
-- Name: _RoleOperations_B_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "_RoleOperations_B_index" ON public."_RoleOperations" USING btree ("B");


--
-- Name: _RoleResources_AB_unique; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "_RoleResources_AB_unique" ON public."_RoleResources" USING btree ("A", "B");


--
-- Name: _RoleResources_B_index; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "_RoleResources_B_index" ON public."_RoleResources" USING btree ("B");


--
-- Name: balance_transactions_adminId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "balance_transactions_adminId_idx" ON public.balance_transactions USING btree ("adminId");


--
-- Name: balance_transactions_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "balance_transactions_createdAt_idx" ON public.balance_transactions USING btree ("createdAt");


--
-- Name: balance_transactions_type_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX balance_transactions_type_idx ON public.balance_transactions USING btree (type);


--
-- Name: balance_transactions_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "balance_transactions_userId_idx" ON public.balance_transactions USING btree ("userId");


--
-- Name: email_queue_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "email_queue_createdAt_idx" ON public.email_queue USING btree ("createdAt");


--
-- Name: email_queue_recipient_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX email_queue_recipient_idx ON public.email_queue USING btree (recipient);


--
-- Name: email_queue_status_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX email_queue_status_idx ON public.email_queue USING btree (status);


--
-- Name: login_history_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "login_history_createdAt_idx" ON public.login_history USING btree ("createdAt");


--
-- Name: login_history_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "login_history_userId_idx" ON public.login_history USING btree ("userId");


--
-- Name: menu_code_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX menu_code_key ON public.menu USING btree (code);


--
-- Name: notificationType_code_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "notificationType_code_key" ON public."notificationType" USING btree (code);


--
-- Name: notification_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "notification_createdAt_idx" ON public.notification USING btree ("createdAt");


--
-- Name: notification_status_sendToAll_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "notification_status_sendToAll_idx" ON public.notification USING btree (status, "sendToAll");


--
-- Name: notification_typeId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "notification_typeId_idx" ON public.notification USING btree ("typeId");


--
-- Name: operation_code_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX operation_code_key ON public.operation USING btree (code);


--
-- Name: rates_businessType_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "rates_businessType_idx" ON public.rates USING btree ("businessType");


--
-- Name: rates_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "rates_createdAt_idx" ON public.rates USING btree ("createdAt");


--
-- Name: rates_customerId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "rates_customerId_idx" ON public.rates USING btree ("customerId");


--
-- Name: resource_code_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX resource_code_key ON public.resource USING btree (code);


--
-- Name: role_code_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX role_code_key ON public.role USING btree (code);


--
-- Name: system_log_action_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX system_log_action_idx ON public.system_log USING btree (action);


--
-- Name: system_log_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "system_log_createdAt_idx" ON public.system_log USING btree ("createdAt");


--
-- Name: system_log_module_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX system_log_module_idx ON public.system_log USING btree (module);


--
-- Name: system_log_resourceType_resourceId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "system_log_resourceType_resourceId_idx" ON public.system_log USING btree ("resourceType", "resourceId");


--
-- Name: system_log_status_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX system_log_status_idx ON public.system_log USING btree (status);


--
-- Name: system_log_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "system_log_userId_idx" ON public.system_log USING btree ("userId");


--
-- Name: user_credit_logs_adminId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_credit_logs_adminId_idx" ON public.user_credit_logs USING btree ("adminId");


--
-- Name: user_credit_logs_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_credit_logs_createdAt_idx" ON public.user_credit_logs USING btree ("createdAt");


--
-- Name: user_credit_logs_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_credit_logs_userId_idx" ON public.user_credit_logs USING btree ("userId");


--
-- Name: user_email_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX user_email_key ON public."user" USING btree (email);


--
-- Name: user_notification_notificationId_read_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_notification_notificationId_read_idx" ON public.user_notification USING btree ("notificationId", read);


--
-- Name: user_notification_settings_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_notification_settings_userId_idx" ON public.user_notification_settings USING btree ("userId");


--
-- Name: user_notification_settings_userId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "user_notification_settings_userId_key" ON public.user_notification_settings USING btree ("userId");


--
-- Name: user_notification_userId_notificationId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "user_notification_userId_notificationId_key" ON public.user_notification USING btree ("userId", "notificationId");


--
-- Name: user_notification_userId_read_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_notification_userId_read_idx" ON public.user_notification USING btree ("userId", read);


--
-- Name: user_status_logs_adminId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_status_logs_adminId_idx" ON public.user_status_logs USING btree ("adminId");


--
-- Name: user_status_logs_createdAt_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_status_logs_createdAt_idx" ON public.user_status_logs USING btree ("createdAt");


--
-- Name: user_status_logs_status_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_status_logs_status_idx ON public.user_status_logs USING btree (status);


--
-- Name: user_status_logs_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_status_logs_userId_idx" ON public.user_status_logs USING btree ("userId");


--
-- Name: user_username_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX user_username_key ON public."user" USING btree (username);


--
-- Name: user_verification_status_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_verification_status_idx ON public.user_verification USING btree (status);


--
-- Name: user_verification_type_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX user_verification_type_idx ON public.user_verification USING btree (type);


--
-- Name: user_verification_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "user_verification_userId_idx" ON public.user_verification USING btree ("userId");


--
-- Name: user_verification_userId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "user_verification_userId_key" ON public.user_verification USING btree ("userId");


--
-- Name: verification_type_change_adminId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "verification_type_change_adminId_idx" ON public.verification_type_change USING btree ("adminId");


--
-- Name: verification_type_change_completed_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX verification_type_change_completed_idx ON public.verification_type_change USING btree (completed);


--
-- Name: verification_type_change_deadline_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX verification_type_change_deadline_idx ON public.verification_type_change USING btree (deadline);


--
-- Name: verification_type_change_userId_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "verification_type_change_userId_idx" ON public.verification_type_change USING btree ("userId");


--
-- Name: verification_type_change_userId_key; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX "verification_type_change_userId_key" ON public.verification_type_change USING btree ("userId");


--
-- Name: Account Account_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Account"
    ADD CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: Session Session_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."Session"
    ADD CONSTRAINT "Session_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _ResourceOperations _ResourceOperations_A_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_ResourceOperations"
    ADD CONSTRAINT "_ResourceOperations_A_fkey" FOREIGN KEY ("A") REFERENCES public.operation(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _ResourceOperations _ResourceOperations_B_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_ResourceOperations"
    ADD CONSTRAINT "_ResourceOperations_B_fkey" FOREIGN KEY ("B") REFERENCES public.resource(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleMenus _RoleMenus_A_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleMenus"
    ADD CONSTRAINT "_RoleMenus_A_fkey" FOREIGN KEY ("A") REFERENCES public.menu(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleMenus _RoleMenus_B_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleMenus"
    ADD CONSTRAINT "_RoleMenus_B_fkey" FOREIGN KEY ("B") REFERENCES public.role(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleOperations _RoleOperations_A_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleOperations"
    ADD CONSTRAINT "_RoleOperations_A_fkey" FOREIGN KEY ("A") REFERENCES public.operation(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleOperations _RoleOperations_B_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleOperations"
    ADD CONSTRAINT "_RoleOperations_B_fkey" FOREIGN KEY ("B") REFERENCES public.role(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleResources _RoleResources_A_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleResources"
    ADD CONSTRAINT "_RoleResources_A_fkey" FOREIGN KEY ("A") REFERENCES public.resource(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: _RoleResources _RoleResources_B_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."_RoleResources"
    ADD CONSTRAINT "_RoleResources_B_fkey" FOREIGN KEY ("B") REFERENCES public.role(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: balance_transactions balance_transactions_adminId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.balance_transactions
    ADD CONSTRAINT "balance_transactions_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: balance_transactions balance_transactions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.balance_transactions
    ADD CONSTRAINT "balance_transactions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: condition condition_policyId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.condition
    ADD CONSTRAINT "condition_policyId_fkey" FOREIGN KEY ("policyId") REFERENCES public.policy(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: loginAttempt loginAttempt_identifier_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."loginAttempt"
    ADD CONSTRAINT "loginAttempt_identifier_fkey" FOREIGN KEY (identifier) REFERENCES public."user"(username) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: login_history login_history_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.login_history
    ADD CONSTRAINT "login_history_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: menu menu_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.menu
    ADD CONSTRAINT "menu_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public.menu(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: notificationType notificationType_parentId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."notificationType"
    ADD CONSTRAINT "notificationType_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES public."notificationType"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: notification notification_typeId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notification
    ADD CONSTRAINT "notification_typeId_fkey" FOREIGN KEY ("typeId") REFERENCES public."notificationType"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: operation operation_menuId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.operation
    ADD CONSTRAINT "operation_menuId_fkey" FOREIGN KEY ("menuId") REFERENCES public.menu(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: policy policy_operationCode_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.policy
    ADD CONSTRAINT "policy_operationCode_fkey" FOREIGN KEY ("operationCode") REFERENCES public.operation(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: policy policy_resourceCode_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.policy
    ADD CONSTRAINT "policy_resourceCode_fkey" FOREIGN KEY ("resourceCode") REFERENCES public.resource(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: rates rates_customerId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.rates
    ADD CONSTRAINT "rates_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: system_log system_log_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.system_log
    ADD CONSTRAINT "system_log_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user user_createdById_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT "user_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_credit_logs user_credit_logs_adminId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_credit_logs
    ADD CONSTRAINT "user_credit_logs_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_credit_logs user_credit_logs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_credit_logs
    ADD CONSTRAINT "user_credit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_notification user_notification_notificationId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_notification
    ADD CONSTRAINT "user_notification_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES public.notification(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_notification_settings user_notification_settings_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_notification_settings
    ADD CONSTRAINT "user_notification_settings_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_notification user_notification_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_notification
    ADD CONSTRAINT "user_notification_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user user_roleCode_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."user"
    ADD CONSTRAINT "user_roleCode_fkey" FOREIGN KEY ("roleCode") REFERENCES public.role(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_status_logs user_status_logs_adminId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_status_logs
    ADD CONSTRAINT "user_status_logs_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: user_status_logs user_status_logs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_status_logs
    ADD CONSTRAINT "user_status_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_verification user_verification_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_verification
    ADD CONSTRAINT "user_verification_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: verificationCode verificationCode_email_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public."verificationCode"
    ADD CONSTRAINT "verificationCode_email_fkey" FOREIGN KEY (email) REFERENCES public."user"(email) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: verification_type_change verification_type_change_adminId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.verification_type_change
    ADD CONSTRAINT "verification_type_change_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: verification_type_change verification_type_change_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.verification_type_change
    ADD CONSTRAINT "verification_type_change_userId_fkey" FOREIGN KEY ("userId") REFERENCES public."user"(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

