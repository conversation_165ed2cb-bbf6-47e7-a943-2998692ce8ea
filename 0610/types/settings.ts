import { Menu } from './menu'

export interface SystemSettings {
  siteName: string
  logo: string
  footerText?: string
  description?: string
  keywords?: string
  applicationName?: string
  appleMobileWebAppTitle?: string
  theme: {
    primaryColor: string
    mode: 'light' | 'dark' | 'system'
  }
  features: {
    enableRegistration: boolean
    enablePasswordReset: boolean
    enableNotifications: boolean
  }
  menu: {
    items: Menu[]
    settings: {
      enableCustomIcons: boolean
      enableDragAndDrop: boolean
    }
  }
}
