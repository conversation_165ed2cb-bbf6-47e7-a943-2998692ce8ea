export interface Menu {
  id: string
  code?: string
  name: string
  path?: string
  icon?: string
  parentId?: string | null
  order?: number
  visible?: boolean
  createdAt?: string
  updatedAt?: string
  children?: Menu[]
}

export interface User {
  id: string
  username: string
  email?: string
  name?: string
  image?: string
  roleCode?: string
  role?: {
    code: string
    name: string
    type?: string
    permissions?: string[]
    menus?: Menu[]
    operations?: any[]
  }
  roleMenus?: Menu[]
  emailVerified?: Date
  createdAt?: string
  updatedAt?: string
}