import type { modalConfig as PrismaModalConfig } from "@prisma/client"
import type { CSSProperties } from "react"

export type ModalSize = "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "full"

export interface ModalCustomStyles {
  overlay?: CSSProperties
  content?: CSSProperties
  header?: CSSProperties
  body?: CSSProperties
  footer?: CSSProperties
}

export interface CreateModalConfigInput {
  id: string
  title: string
  description?: string
  maxWidth?: ModalSize
  customStyles?: ModalCustomStyles
}

export interface UpdateModalConfigInput extends Partial<CreateModalConfigInput> {
  id: string
}

export type ModalConfig = PrismaModalConfig 