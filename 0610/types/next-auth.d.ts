import type { DefaultSession } from "next-auth"
import type { Role } from "@prisma/client"
import NextAuth, { DefaultSession } from "next-auth"
import { JWT } from "next-auth/jwt"

declare module "next-auth" {
  /**
   * 扩展 Session 类型，添加自定义字段
   */
  interface Session {
    user: {
      id: string
      roleCode: string
      permissions: string[]
    } & DefaultSession["user"]
  }

  /**
   * 扩展 User 类型，添加自定义字段
   */
  interface User {
    id: string
    name: string
    email: string
    roleCode: string
    permissions: string[]
  }
}

declare module "next-auth/jwt" {
  /**
   * 扩展 JWT 令牌类型，添加自定义字段
   */
  interface JWT {
    roleCode: string
    permissions: string[]
  }
}