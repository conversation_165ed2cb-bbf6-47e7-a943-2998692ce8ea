/**
 * 数据库菜单项类型定义
 */
export interface Menu {
  id: string
  code: string
  name: string
  path: string
  icon?: string
  order: number
  visible: boolean
  parentId?: string
  children?: Menu[]
  roles?: {
    id: string
    name: string
  }[]
}

/**
 * 前端菜单项类型定义
 */
export interface MenuItem {
  id: string
  title: string
  path: string
  href: string
  icon: string
  isVisible: boolean
  hasChildren: boolean
  children: MenuItem[]
  order?: number
  customIconUrl?: string // 添加自定义图标URL字段
  code?: string // 与jCasbin兼容的菜单代码
  resource?: string // 与jCasbin兼容的资源标识符
}