import logger from '@/lib/utils/logger';

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createSuperAdmin() {
  try {
    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        username: 'superadmin'
      }
    });

    if (existingUser) {
      logger.log('超级管理员用户已存在，跳过创建');
      return;
    }

    // 获取ADMIN角色信息
    const adminRole = await prisma.role.findUnique({
      where: {
        code: 'ADMIN'
      }
    });

    if (!adminRole) {
      logger.error('未找到ADMIN角色，无法创建超级管理员');
      return;
    }

    // 创建超级管理员用户
    const hashedPassword = await bcrypt.hash('Superadmin123', 10);
    
    const superAdmin = await prisma.user.create({
      data: {
        username: 'superadmin',
        password: hashedPassword,
        email: '<EMAIL>',
        name: '超级管理员',
        roleCode: 'ADMIN',
        permissions: ['*'], // 所有权限
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    logger.log('超级管理员创建成功:', superAdmin);
  } catch (error) {
    logger.error('创建超级管理员失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSuperAdmin();
