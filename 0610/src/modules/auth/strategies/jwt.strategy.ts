/**
 * JWT认证策略
 * 
 * @description
 * 实现基于JWT的身份验证策略
 * 用于验证请求中的JWT令牌并提取用户信息
 * 
 * @module JwtStrategy
 */

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { User } from '../entities/user.entity';
import { AuthService } from '../auth.service';

/**
 * JWT载荷接口
 */
export interface JwtPayload {
  sub: string;      // 用户ID
  username: string; // 用户名
  role: string;     // 角色
  iat?: number;     // 签发时间
  exp?: number;     // 过期时间
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  /**
   * 构造函数
   * @param configService 配置服务
   * @param authService 认证服务
   */
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    super({
      // 从请求头中提取Bearer token
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      // 是否忽略过期时间
      ignoreExpiration: false,
      // 使用配置的密钥
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  /**
   * 验证JWT载荷并返回用户信息
   * @param payload JWT载荷
   * @returns 用户信息
   * @throws UnauthorizedException 当用户不存在或已被禁用时
   */
  async validate(payload: JwtPayload): Promise<User> {
    try {
      // 从数据库获取用户信息
      const user = await this.authService.validateUserById(payload.sub);
      
      // 检查用户是否存在且处于活动状态
      if (!user || !user.isActive()) {
        throw new UnauthorizedException('用户不存在或已被禁用');
      }

      return user;
    } catch (error) {
      throw new UnauthorizedException('无效的认证令牌');
    }
  }
} 