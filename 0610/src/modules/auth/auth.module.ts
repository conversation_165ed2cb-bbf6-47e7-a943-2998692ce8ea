/**
 * 认证模块
 * 
 * @description
 * 配置和注册认证相关的所有组件，包括：
 * 1. 认证服务
 * 2. JWT策略
 * 3. 认证守卫
 * 4. 角色守卫
 * 5. 权限守卫
 * 
 * @module AuthModule
 */

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './strategies/jwt.strategy';
import { User } from './entities/user.entity';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { PermissionsGuard } from './guards/permissions.guard';

@Module({
  imports: [
    // TypeORM配置 - 注册User实体
    TypeOrmModule.forFeature([User]),

    // Passport配置
    PassportModule.register({ defaultStrategy: 'jwt' }),

    // JWT模块配置
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    // 核心服务
    AuthService,
    
    // 认证策略
    JwtStrategy,

    // 全局守卫
    {
      provide: 'APP_GUARD',
      useClass: JwtAuthGuard,
    },
    {
      provide: 'APP_GUARD',
      useClass: RolesGuard,
    },
    {
      provide: 'APP_GUARD',
      useClass: PermissionsGuard,
    },
  ],
  exports: [AuthService],
})
export class AuthModule {
  constructor(private configService: ConfigService) {
    // 验证必要的环境变量
    const jwtSecret = this.configService.get<string>('JWT_SECRET');
    if (!jwtSecret) {
      throw new Error('JWT_SECRET environment variable is not set');
    }
  }
} 