/**
 * 认证服务
 * 
 * @description
 * 提供用户认证、注册、密码管理等功能
 * 包括：
 * 1. 用户登录验证
 * 2. 用户注册
 * 3. 密码修改
 * 4. JWT令牌管理
 * 
 * @module AuthService
 */

import { Injectable, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { User, UserStatus } from './entities/user.entity';
import { LoginDto, RegisterDto, ChangePasswordDto, AuthResponseDto } from './dto/auth.dto';
import { JwtPayload } from './strategies/jwt.strategy';

@Injectable()
export class AuthService {
  /**
   * 构造函数
   * @param userRepository 用户仓库
   * @param jwtService JWT服务
   * @param configService 配置服务
   */
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  /**
   * 用户登录
   * @param loginDto 登录数据
   * @returns 认证响应数据
   * @throws UnauthorizedException 当用户名或密码错误时
   */
  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    // 查找用户
    const user = await this.userRepository.findOne({
      where: { username: loginDto.username }
    });

    // 验证用户存在性和密码
    if (!user || !(await user.validatePassword(loginDto.password))) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查用户状态
    if (!user.isActive()) {
      throw new UnauthorizedException('账户已被禁用');
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await this.userRepository.save(user);

    // 生成JWT令牌
    return this.generateToken(user);
  }

  /**
   * 用户注册
   * @param registerDto 注册数据
   * @returns 注册成功的用户信息
   * @throws ConflictException 当用户名或邮箱已存在时
   */
  async register(registerDto: RegisterDto): Promise<User> {
    // 检查用户名是否已存在
    const existingUsername = await this.userRepository.findOne({
      where: { username: registerDto.username }
    });
    if (existingUsername) {
      throw new ConflictException('用户名已存在');
    }

    // 检查邮箱是否已存在
    const existingEmail = await this.userRepository.findOne({
      where: { email: registerDto.email }
    });
    if (existingEmail) {
      throw new ConflictException('邮箱已被使用');
    }

    // 如果提供了手机号，检查是否已存在
    if (registerDto.phone) {
      const existingPhone = await this.userRepository.findOne({
        where: { phone: registerDto.phone }
      });
      if (existingPhone) {
        throw new ConflictException('手机号已被使用');
      }
    }

    // 创建新用户
    const user = this.userRepository.create({
      ...registerDto,
      status: UserStatus.ACTIVE,
      permissions: [], // 初始权限为空
    });

    // 保存用户
    return this.userRepository.save(user);
  }

  /**
   * 修改密码
   * @param userId 用户ID
   * @param changePasswordDto 修改密码数据
   * @returns 更新后的用户信息
   * @throws UnauthorizedException 当当前密码错误时
   */
  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<User> {
    // 查找用户
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 验证当前密码
    const isPasswordValid = await user.validatePassword(changePasswordDto.currentPassword);
    if (!isPasswordValid) {
      throw new UnauthorizedException('当前密码错误');
    }

    // 更新密码
    user.password = changePasswordDto.newPassword;
    return this.userRepository.save(user);
  }

  /**
   * 根据ID验证用户
   * @param userId 用户ID
   * @returns 用户信息
   */
  async validateUserById(userId: string): Promise<User> {
    return this.userRepository.findOne({
      where: { id: userId }
    });
  }

  /**
   * 生成JWT令牌
   * @param user 用户信息
   * @returns 认证响应数据
   * @private
   */
  private generateToken(user: User): AuthResponseDto {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      role: user.role,
    };

    // 获取JWT配置
    const expiresIn = this.configService.get<string>('JWT_EXPIRES_IN', '1d');
    const accessToken = this.jwtService.sign(payload);

    return {
      accessToken,
      tokenType: 'Bearer',
      expiresIn: this.getExpiresInSeconds(expiresIn),
    };
  }

  /**
   * 将过期时间字符串转换为秒数
   * @param expiresIn 过期时间字符串 (例如: '1d', '2h', '30m')
   * @returns 过期秒数
   * @private
   */
  private getExpiresInSeconds(expiresIn: string): number {
    const unit = expiresIn.slice(-1);
    const value = parseInt(expiresIn.slice(0, -1));
    
    switch (unit) {
      case 'd':
        return value * 24 * 60 * 60;
      case 'h':
        return value * 60 * 60;
      case 'm':
        return value * 60;
      case 's':
        return value;
      default:
        return 24 * 60 * 60; // 默认1天
    }
  }
} 