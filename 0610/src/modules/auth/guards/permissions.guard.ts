/**
 * 权限守卫
 * 
 * @description
 * 用于实现基于权限的访问控制
 * 检查用户是否具有访问特定路由所需的权限
 * 
 * @module PermissionsGuard
 */

import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRole } from '../entities/user.entity';

/**
 * 权限装饰器的元数据键
 */
export const PERMISSIONS_KEY = 'permissions';

/**
 * 权限装饰器
 * 用于标记需要特定权限才能访问的路由
 * @param permissions 允许访问的权限列表
 */
export const RequirePermissions = (...permissions: string[]) => {
  return (target: any, key?: string | symbol, descriptor?: any) => {
    const reflector = new Reflector();
    reflector.set(PERMISSIONS_KEY, permissions, descriptor.value);
    return descriptor;
  };
};

@Injectable()
export class PermissionsGuard implements CanActivate {
  /**
   * 构造函数
   * @param reflector 反射器，用于获取路由元数据
   */
  constructor(private reflector: Reflector) {}

  /**
   * 判断请求是否可以通过权限验证
   * @param context 执行上下文
   * @returns 是否允许请求通过
   * @throws ForbiddenException 当用户没有所需权限时
   */
  canActivate(context: ExecutionContext): boolean {
    // 获取路由所需的权限
    const requiredPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()],
    );

    // 如果路由没有指定所需权限，则允许访问
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true;
    }

    // 获取请求中的用户信息
    const { user } = context.switchToHttp().getRequest();

    // 如果没有用户信息，拒绝访问
    if (!user) {
      throw new ForbiddenException('未找到用户信息');
    }

    // 如果用户是管理员，允许访问所有资源
    if (user.role === UserRole.ADMIN) {
      return true;
    }

    // 检查用户是否具有所需权限
    const hasAllPermissions = requiredPermissions.every(permission =>
      user.permissions?.includes(permission),
    );

    if (!hasAllPermissions) {
      throw new ForbiddenException('权限不足');
    }

    return true;
  }
}

/**
 * 常用权限常量
 */
export const Permissions = {
  // 用户管理权限
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',

  // 客户管理权限
  CUSTOMER_CREATE: 'customer:create',
  CUSTOMER_READ: 'customer:read',
  CUSTOMER_UPDATE: 'customer:update',
  CUSTOMER_DELETE: 'customer:delete',

  // 任务管理权限
  TASK_CREATE: 'task:create',
  TASK_READ: 'task:read',
  TASK_UPDATE: 'task:update',
  TASK_DELETE: 'task:delete',

  // 视频通话权限
  VIDEO_CALL_CREATE: 'video-call:create',
  VIDEO_CALL_READ: 'video-call:read',
  VIDEO_CALL_UPDATE: 'video-call:update',
  VIDEO_CALL_DELETE: 'video-call:delete',

  // 系统管理权限
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_LOGS: 'system:logs',
} as const; 