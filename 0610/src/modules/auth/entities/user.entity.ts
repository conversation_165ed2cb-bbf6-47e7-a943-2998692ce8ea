/**
 * 用户实体类
 * 
 * @description
 * 定义用户在数据库中的结构和关系
 * 包含用户的基本信息、认证信息和权限信息
 * 
 * @class User
 */

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  BeforeInsert,
  BeforeUpdate
} from 'typeorm';
import { Exclude } from 'class-transformer';
import * as bcrypt from 'bcrypt';

/**
 * 用户角色枚举
 */
export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  OPERATOR = 'operator',
  USER = 'user',
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  LOCKED = 'locked',
}

@Entity('users')
export class User {
  /**
   * 用户ID
   * @primary
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * 用户名
   * @unique
   */
  @Column({ unique: true })
  username: string;

  /**
   * 密码
   * @exclude 在序列化时排除此字段
   */
  @Column()
  @Exclude()
  password: string;

  /**
   * 邮箱
   * @unique
   */
  @Column({ unique: true })
  email: string;

  /**
   * 手机号
   * @unique
   * @nullable
   */
  @Column({ unique: true, nullable: true })
  phone?: string;

  /**
   * 真实姓名
   */
  @Column({ name: 'real_name', nullable: true })
  realName?: string;

  /**
   * 用户角色
   * @default UserRole.USER
   */
  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER
  })
  role: UserRole;

  /**
   * 用户状态
   * @default UserStatus.ACTIVE
   */
  @Column({
    type: 'enum',
    enum: UserStatus,
    default: UserStatus.ACTIVE
  })
  status: UserStatus;

  /**
   * 用户权限列表
   * @array
   */
  @Column('simple-array', { nullable: true })
  permissions: string[];

  /**
   * 最后登录时间
   */
  @Column({ name: 'last_login_at', nullable: true })
  lastLoginAt?: Date;

  /**
   * 创建时间
   */
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  /**
   * 更新时间
   */
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  /**
   * 密码加密处理
   * 在插入和更新前自动进行密码加密
   */
  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    // 如果密码被修改，则进行加密
    if (this.password) {
      const salt = await bcrypt.genSalt();
      this.password = await bcrypt.hash(this.password, salt);
    }
  }

  /**
   * 验证密码是否正确
   * @param password 待验证的密码
   * @returns 密码是否匹配
   */
  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }

  /**
   * 检查用户是否有指定权限
   * @param permission 权限标识
   * @returns 是否具有该权限
   */
  hasPermission(permission: string): boolean {
    if (this.role === UserRole.ADMIN) return true;
    return this.permissions?.includes(permission) ?? false;
  }

  /**
   * 检查用户是否处于活动状态
   * @returns 是否处于活动状态
   */
  isActive(): boolean {
    return this.status === UserStatus.ACTIVE;
  }
} 