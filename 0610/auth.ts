import NextAuth from "next-auth"
import Credentials from "next-auth/providers/credentials"
import { prisma } from "@/lib/prisma"
import { compare } from "bcryptjs"

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findFirst({
          where: {
            OR: [
              { email: credentials.email },
              { username: credentials.email }
            ]
          },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                },
                menus: true
              }
            }
          }
        })

        if (!user) {
          return null
        }

        const isValid = await compare(credentials.password, user.password)
        if (!isValid) {
          return null
        }

        // 提取权限列表
        const permissions = user.role?.rolePermissions.map(rp => rp.permission.code) || []

        return {
          id: user.id,
          username: user.username,
          email: user.email,
          name: user.name,
          image: user.image,
          roleCode: user.roleCode,
          permissions,
          role: {
            ...user.role,
            permissions
          }
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        // 完整保存用户信息到 token
        token.id = user.id
        token.username = user.username
        token.email = user.email
        token.name = user.name
        token.image = user.image
        token.roleCode = user.roleCode
        token.permissions = user.permissions
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        // 确保 session 包含所有必要的用户信息
        session.user.id = token.id as string
        session.user.username = token.username as string
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.image = token.image as string
        session.user.roleCode = token.roleCode as string
        session.user.permissions = token.permissions as string[]
        session.user.role = token.role as any
      }
      return session
    }
  }
})