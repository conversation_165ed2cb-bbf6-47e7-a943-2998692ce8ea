Thought for some time## Docker部署详细指南 - 5G Web管理系统

### 项目结构说明

Cursor，请注意当前项目结构如下：

```plaintext
/
├── backend/                  # Laravel后端应用
│   ├── app/                  # 应用代码
│   ├── config/               # 配置文件
│   ├── database/             # 数据库迁移和种子
│   │   ├── migrations/       # 数据库迁移文件
│   │   └── seeders/         # 数据库种子文件
│   ├── routes/               # API路由
│   ├── .env.example          # 环境变量示例
│   └── Dockerfile            # 后端Docker配置
├── frontend/                 # Vue.js前端应用
│   ├── src/                  # 源代码
│   │   ├── router/           # 路由配置
│   │   ├── views/            # 视图组件
│   │   └── components/       # 通用组件
│   ├── public/               # 静态资源
│   ├── package.json          # 依赖配置
│   └── Dockerfile            # 前端Docker配置
├── docker/                   # Docker配置目录
│   ├── nginx/                # Nginx配置
│   │   ├── nginx.conf        # Nginx主配置
│   │   └── conf.d/           # Nginx站点配置
│   ├── mysql/                # MySQL配置
│   │   └── my.cnf            # MySQL配置文件
│   └── php/                  # PHP配置
│       └── local.ini         # PHP配置文件
└── docker-compose.yml        # Docker Compose配置
```

**重要提示**：请不要随意创建或修改现有文件，除非明确指示。使用现有的Docker配置文件进行部署，如需修改，请按照以下指令操作。

### 详细部署步骤

#### 步骤1：配置Docker加速源

首先，为Docker配置镜像加速源，以提高镜像拉取速度：

```shellscript
# 创建或修改Docker配置文件
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://dockerproxy.net",
    "https://docker.1ms.run",
    "https://docker.xuanyuan.me",
    "https://hub.rat.dev",
    "https://doublezonline.cloud"
  ]
}
EOF

# 重启Docker服务
sudo systemctl daemon-reload
sudo systemctl restart docker
```

#### 步骤2：修改Dockerfile以使用镜像加速

##### 2.1 修改后端Dockerfile

```shellscript
# 编辑后端Dockerfile
nano backend/Dockerfile
```

将后端Dockerfile内容修改为：

```dockerfile
FROM php:8.2-cli

# 使用国内Debian镜像源
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    libpq-dev

# 清理apt缓存
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# 安装PHP扩展
RUN docker-php-ext-install pdo_mysql pdo_pgsql mbstring exif pcntl bcmath gd zip

# 安装Composer并配置国内镜像
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer
RUN composer config -g repo.packagist composer https://mirrors.cloud.tencent.com/composer/

# 设置工作目录
WORKDIR /var/www/html

# 复制composer文件
COPY composer.json composer.lock ./

# 安装依赖
RUN composer install --no-scripts --no-autoloader --no-dev

# 复制项目文件
COPY . .

# 生成自动加载文件
RUN composer dump-autoload --optimize --no-dev

# 创建必要的目录结构并设置权限
RUN mkdir -p storage/framework/sessions \
    && mkdir -p storage/framework/views \
    && mkdir -p storage/framework/cache \
    && mkdir -p storage/logs \
    && mkdir -p bootstrap/cache \
    && chmod -R 777 storage \
    && chmod -R 777 bootstrap/cache

# 暴露端口
EXPOSE 8000

# 启动命令
CMD php artisan serve --host=0.0.0.0 --port=8000
```

##### 2.2 修改前端Dockerfile

```shellscript
# 编辑前端Dockerfile
nano frontend/Dockerfile
```

将前端Dockerfile内容修改为：

```dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装依赖
RUN npm install

# 复制项目文件
COPY . .

# 设置环境变量
ENV VUE_APP_API_BASE_URL=http://localhost/api

# 构建应用
RUN npm run build

# 安装serve用于提供静态文件服务
RUN npm install -g serve

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["serve", "-s", "dist", "-l", "5000"]
```

#### 步骤3：检查并修改docker-compose.yml

```shellscript
# 检查docker-compose.yml
nano docker-compose.yml
```

确保docker-compose.yml内容如下（如有需要进行修改）：

```yaml
version: '3'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: 5gweb-backend
    restart: unless-stopped
    volumes:
      - ./backend:/var/www/html
      - ./backend/storage/logs:/var/www/html/storage/logs
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_URL=http://localhost
      - DATABASE_URL=${DATABASE_URL}
    networks:
      - app-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: 5gweb-frontend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - VUE_APP_API_BASE_URL=http://localhost/api
    depends_on:
      - backend
    networks:
      - app-network

  # Nginx服务
  nginx:
    image: nginx:alpine
    container_name: 5gweb-nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
      - frontend
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
```

#### 步骤4：检查并修改Nginx配置

```shellscript
# 检查Nginx配置
nano docker/nginx/conf.d/app.conf
```

确保app.conf内容如下（如有需要进行修改）：

```plaintext
server {
    listen 80;
    server_name localhost;
    
    # 前端应用
    location / {
        proxy_pass http://frontend:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 后端API
    location /api {
        proxy_pass http://backend:8000/api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 静态文件存储
    location /storage {
        proxy_pass http://backend:8000/storage;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

#### 步骤5：创建环境变量文件

```shellscript
# 为后端创建.env文件
cp backend/.env.example backend/.env

# 编辑后端.env文件
nano backend/.env
```

在backend/.env文件中，确保以下变量正确设置：

```plaintext
APP_NAME=5GWeb管理系统
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_LEVEL=error

DB_CONNECTION=pgsql
DATABASE_URL=**********************************************/database

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

JWT_SECRET=your_jwt_secret_key
JWT_TTL=60
JWT_REFRESH_TTL=20160
```

然后生成应用密钥：

```shellscript
# 进入backend目录
cd backend

# 生成应用密钥
php artisan key:generate

# 生成JWT密钥
php artisan jwt:secret

# 返回项目根目录
cd ..
```

#### 步骤6：构建和启动Docker容器

```shellscript
# 构建容器
docker-compose build

# 启动容器
docker-compose up -d
```

#### 步骤7：初始化数据库

```shellscript
# 进入后端容器
docker-compose exec backend bash

# 在容器内运行数据库迁移
php artisan migrate

# 如果需要填充测试数据
php artisan db:seed

# 退出容器
exit
```

如果你更喜欢使用之前提供的SQL脚本，可以通过以下方式执行：

```shellscript
# 创建SQL脚本文件
nano setup.sql
```

将之前提供的SQL脚本复制到此文件中，然后执行：

```shellscript
# 执行SQL脚本
cat setup.sql | docker-compose exec -T backend php artisan db:command --command="psql -U postgres" --file="-"
```

#### 步骤8：检查服务状态

```shellscript
# 查看所有容器状态
docker-compose ps

# 查看容器日志
docker-compose logs -f
```

#### 步骤9：访问应用

部署完成后，你可以通过以下URL访问应用：

- 前端应用：[http://localhost](http://localhost)
- 后端API：[http://localhost/api](http://localhost/api)


### 故障排除指南

#### 问题1：容器无法启动

```shellscript
# 查看容器日志
docker-compose logs -f

# 检查网络连接
docker network ls
docker network inspect app-network
```

#### 问题2：数据库连接失败

```shellscript
# 检查后端环境变量
docker-compose exec backend bash -c "cat .env | grep DATABASE"

# 测试数据库连接
docker-compose exec backend php artisan tinker --execute="try { DB::connection()->getPdo(); echo 'Connected successfully!'; } catch (\Exception \$e) { echo 'Connection failed: ' . \$e->getMessage(); }"
```

#### 问题3：前端无法连接后端API

```shellscript
# 检查前端环境变量
docker-compose exec frontend sh -c "env | grep VUE_APP"

# 检查Nginx配置
docker-compose exec nginx nginx -t
```

#### 问题4：权限问题

```shellscript
# 修复存储目录权限
docker-compose exec backend bash -c "chmod -R 777 storage bootstrap/cache"
```

### 完整重建指令

如果需要完全重建环境：

```shellscript
# 停止并删除所有容器
docker-compose down

# 删除所有相关卷
docker-compose down -v

# 重新构建并启动
docker-compose up -d --build
```

### 备份数据

定期备份数据库：

```shellscript
# 创建数据库备份
docker-compose exec backend php artisan db:backup
```

### 监控应用

```shellscript
# 查看容器资源使用情况
docker stats
```

请按照这些详细指令操作，如果遇到任何问题，请提供具体的错误信息，我会帮你解决。这些指令已经考虑了国内网络环境，使用了镜像加速源来提高部署效率。