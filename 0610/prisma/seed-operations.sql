-- 插入基础操作权限
INSERT INTO operation (id, code, name, type, description, "menuId", "createdAt", "updatedAt")
VALUES 
  -- 用户管理操作
  ('op_001', 'user:list', '查看用户列表', 'action', '查看用户列表权限', 'menu_users', NOW(), NOW()),
  ('op_002', 'user:create', '创建用户', 'action', '创建用户权限', 'menu_users', NOW(), NOW()),
  ('op_003', 'user:edit', '编辑用户', 'action', '编辑用户权限', 'menu_users', NOW(), NOW()),
  ('op_004', 'user:delete', '删除用户', 'action', '删除用户权限', 'menu_users', NOW(), NOW()),
  
  -- 角色管理操作
  ('op_005', 'role:list', '查看角色列表', 'action', '查看角色列表权限', 'menu_roles', NOW(), NOW()),
  ('op_006', 'role:create', '创建角色', 'action', '创建角色权限', 'menu_roles', NOW(), NOW()),
  ('op_007', 'role:edit', '编辑角色', 'action', '编辑角色权限', 'menu_roles', NOW(), NOW()),
  ('op_008', 'role:delete', '删除角色', 'action', '删除角色权限', 'menu_roles', NOW(), NOW()),
  
  -- 菜单管理操作
  ('op_009', 'menu:list', '查看菜单列表', 'action', '查看菜单列表权限', 'menu_menus', NOW(), NOW()),
  ('op_010', 'menu:create', '创建菜单', 'action', '创建菜单权限', 'menu_menus', NOW(), NOW()),
  ('op_011', 'menu:edit', '编辑菜单', 'action', '编辑菜单权限', 'menu_menus', NOW(), NOW()),
  ('op_012', 'menu:delete', '删除菜单', 'action', '删除菜单权限', 'menu_menus', NOW(), NOW()),
  
  -- 操作权限管理操作
  ('op_013', 'operation:list', '查看操作权限列表', 'action', '查看操作权限列表权限', 'menu_operations', NOW(), NOW()),
  ('op_014', 'operation:create', '创建操作权限', 'action', '创建操作权限权限', 'menu_operations', NOW(), NOW()),
  ('op_015', 'operation:edit', '编辑操作权限', 'action', '编辑操作权限权限', 'menu_operations', NOW(), NOW()),
  ('op_016', 'operation:delete', '删除操作权限', 'action', '删除操作权限权限', 'menu_operations', NOW(), NOW()),
  
  -- 资源管理操作
  ('op_017', 'resource:list', '查看资源列表', 'action', '查看资源列表权限', 'menu_resources', NOW(), NOW()),
  ('op_018', 'resource:create', '创建资源', 'action', '创建资源权限', 'menu_resources', NOW(), NOW()),
  ('op_019', 'resource:edit', '编辑资源', 'action', '编辑资源权限', 'menu_resources', NOW(), NOW()),
  ('op_020', 'resource:delete', '删除资源', 'action', '删除资源权限', 'menu_resources', NOW(), NOW()),

  -- 通知管理操作
  ('op_021', 'notifications:list', '查看通知列表', 'action', '查看通知列表权限', 'menu_notifications', NOW(), NOW()),
  ('op_022', 'notifications:create', '创建通知', 'action', '创建通知权限', 'menu_notifications', NOW(), NOW()),
  ('op_023', 'notifications:edit', '编辑通知', 'action', '编辑通知权限', 'menu_notifications', NOW(), NOW()),
  ('op_024', 'notifications:delete', '删除通知', 'action', '删除通知权限', 'menu_notifications', NOW(), NOW()),
  
  -- 仪表盘操作
  ('op_dashboard_view', 'dashboard.view', '查看仪表盘', 'page', '查看仪表盘页面', 'menu_dashboard', NOW(), NOW()),
  
  -- 任务管理操作
  ('op_tasks_upload', 'tasks.upload', '上传任务', 'action', '上传新任务', 'menu_tasks_upload', NOW(), NOW()),
  ('op_tasks_view', 'tasks.view', '查看任务', 'page', '查看任务列表', 'menu_tasks_upload', NOW(), NOW()),
  ('op_tasks_details', 'tasks.details', '查看详情', 'page', '查看任务详情', 'menu_tasks_details', NOW(), NOW()),
  
  -- 通知中心操作
  ('op_notification_view', 'notification.view', '查看通知', 'page', '查看通知列表', 'menu_notifications', NOW(), NOW()),
  ('op_notification_read', 'notification.read', '标记已读', 'action', '标记通知为已读', 'menu_notifications', NOW(), NOW()),
  
  -- 用户中心操作
  ('op_user_profile_view', 'user.profile.view', '查看个人资料', 'page', '查看个人资料页面', 'menu_user', NOW(), NOW()),
  ('op_user_profile_edit', 'user.profile.edit', '编辑个人资料', 'action', '编辑个人资料', 'menu_user', NOW(), NOW()),
  
  -- 账户管理操作
  ('op_accounts_customer_view', 'accounts.customer.view', '查看客户账户', 'page', '查看客户账户列表', 'menu_accounts_customer', NOW(), NOW()),
  ('op_accounts_customer_add', 'accounts.customer.add', '添加客户账户', 'action', '添加新客户账户', 'menu_accounts_customer', NOW(), NOW()),
  ('op_accounts_customer_edit', 'accounts.customer.edit', '编辑客户账户', 'action', '编辑客户账户信息', 'menu_accounts_customer', NOW(), NOW()),
  ('op_accounts_customer_delete', 'accounts.customer.delete', '删除客户账户', 'action', '删除客户账户', 'menu_accounts_customer', NOW(), NOW()),
  
  ('op_accounts_admin_view', 'accounts.admin.view', '查看管理员账户', 'page', '查看管理员账户列表', 'menu_accounts_admin', NOW(), NOW()),
  ('op_accounts_admin_add', 'accounts.admin.add', '添加管理员账户', 'action', '添加新管理员账户', 'menu_accounts_admin', NOW(), NOW()),
  ('op_accounts_admin_edit', 'accounts.admin.edit', '编辑管理员账户', 'action', '编辑管理员账户信息', 'menu_accounts_admin', NOW(), NOW()),
  ('op_accounts_admin_delete', 'accounts.admin.delete', '删除管理员账户', 'action', '删除管理员账户', 'menu_accounts_admin', NOW(), NOW()),
  
  -- 系统设置操作
  ('op_settings_view', 'settings.view', '查看系统设置', 'page', '查看系统设置页面', 'menu_settings', NOW(), NOW()),
  ('op_settings_edit', 'settings.edit', '修改系统设置', 'action', '修改系统设置', 'menu_settings', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  code = EXCLUDED.code,
  name = EXCLUDED.name,
  type = EXCLUDED.type,
  description = EXCLUDED.description,
  "menuId" = EXCLUDED."menuId",
  "updatedAt" = NOW(); 