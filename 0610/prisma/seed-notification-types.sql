-- 清理现有数据
TRUNCATE TABLE notification_types CASCADE;

-- 插入通知类型
INSERT INTO notification_types (id, code, name, description, icon, "order", active, "createdAt", "updatedAt")
VALUES
  ('clqw1', 'security', '安全消息', '系统安全相关的通知', 'shield', 1, true, NOW(), NOW()),
  ('clqw2', 'service', '服务消息', '系统服务相关的通知', 'bell', 2, true, NOW(), NOW()),
  ('clqw3', 'activity', '活动消息', '系统活动相关的通知', 'calendar', 3, true, NOW(), NOW()),
  ('clqw4', 'history', '历史消息', '历史记录相关的通知', 'clock', 4, true, NOW(), NOW()),
  ('clqw5', 'error', '故障消息', '系统故障相关的通知', 'alert-triangle', 5, true, NOW(), NOW()),
  ('clqw6', 'product', '产品消息', '产品更新相关的通知', 'package', 6, true, NOW(), NOW()); 