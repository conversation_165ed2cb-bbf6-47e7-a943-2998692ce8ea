-- 添加系统日志菜单项
INSERT INTO "menu" (id, code, name, path, icon, "parentId", "order", visible, "createdAt", "updatedAt")
VALUES
  ('menu_system_logs', 'system.logs', '系统日志', '/admin/logs', 'FileText', 'cm9ba2nid00061gdwatbczrpt', 5, true, NOW(), NOW())
ON CONFLICT (code) DO UPDATE SET
  name = EXCLUDED.name,
  path = EXCLUDED.path,
  icon = EXCLUDED.icon,
  "parentId" = EXCLUDED."parentId",
  "order" = EXCLUDED."order",
  visible = EXCLUDED.visible,
  "updatedAt" = NOW();

-- 将系统日志菜单与管理员角色关联
INSERT INTO "_RoleMenus" ("A", "B")
VALUES ('menu_system_logs', '17712061-dc36-4f32-a814-1352483e3777')
ON CONFLICT DO NOTHING;
