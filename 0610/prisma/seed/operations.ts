import { PrismaClient } from '@prisma/client'

/**
 * 初始化操作数据
 * @param prisma Prisma客户端实例
 */
export async function seedOperations(prisma: PrismaClient) {
  console.log('开始初始化操作数据...')

  // 用户管理相关操作
  const userOperations = [
    {
      code: 'USER_VIEW',
      name: '查看用户',
      type: '用户管理',
      description: '查看用户列表和详情',
      menuId: null, // 将在后续步骤中关联到菜单
    },
    {
      code: 'USER_CREATE',
      name: '创建用户',
      type: '用户管理',
      description: '创建新用户',
      menuId: null,
    },
    {
      code: 'USER_EDIT',
      name: '编辑用户',
      type: '用户管理',
      description: '编辑用户信息',
      menuId: null,
    },
    {
      code: 'USER_DELETE',
      name: '删除用户',
      type: '用户管理',
      description: '删除用户',
      menuId: null,
    },
    {
      code: 'USER_DISABLE',
      name: '禁用用户',
      type: '用户管理',
      description: '禁用用户账号',
      menuId: null,
    },
    {
      code: 'USER_ENABLE',
      name: '启用用户',
      type: '用户管理',
      description: '启用用户账号',
      menuId: null,
    },
    {
      code: 'USER_RESET_PASSWORD',
      name: '重置密码',
      type: '用户管理',
      description: '重置用户密码',
      menuId: null,
    },
  ]

  // 角色管理相关操作
  const roleOperations = [
    {
      code: 'ROLE_VIEW',
      name: '查看角色',
      type: '角色管理',
      description: '查看角色列表和详情',
      menuId: null,
    },
    {
      code: 'ROLE_CREATE',
      name: '创建角色',
      type: '角色管理',
      description: '创建新角色',
      menuId: null,
    },
    {
      code: 'ROLE_EDIT',
      name: '编辑角色',
      type: '角色管理',
      description: '编辑角色信息',
      menuId: null,
    },
    {
      code: 'ROLE_DELETE',
      name: '删除角色',
      type: '角色管理',
      description: '删除角色',
      menuId: null,
    },
    {
      code: 'ROLE_ASSIGN',
      name: '分配角色',
      type: '角色管理',
      description: '为用户分配角色',
      menuId: null,
    },
  ]

  // 菜单管理相关操作
  const menuOperations = [
    {
      code: 'MENU_VIEW',
      name: '查看菜单',
      type: '菜单管理',
      description: '查看菜单列表和详情',
      menuId: null,
    },
    {
      code: 'MENU_CREATE',
      name: '创建菜单',
      type: '菜单管理',
      description: '创建新菜单',
      menuId: null,
    },
    {
      code: 'MENU_EDIT',
      name: '编辑菜单',
      type: '菜单管理',
      description: '编辑菜单信息',
      menuId: null,
    },
    {
      code: 'MENU_DELETE',
      name: '删除菜单',
      type: '菜单管理',
      description: '删除菜单',
      menuId: null,
    },
  ]

  // 系统设置相关操作
  const settingsOperations = [
    {
      code: 'SETTINGS_VIEW',
      name: '查看设置',
      type: '系统设置',
      description: '查看系统设置',
      menuId: null,
    },
    {
      code: 'SETTINGS_EDIT',
      name: '修改设置',
      type: '系统设置',
      description: '修改系统设置',
      menuId: null,
    },
  ]

  // 任务管理相关操作
  const taskOperations = [
    {
      code: 'TASK_VIEW',
      name: '查看任务',
      type: '任务管理',
      description: '查看任务列表和详情',
      menuId: null,
    },
    {
      code: 'TASK_CREATE',
      name: '创建任务',
      type: '任务管理',
      description: '创建新任务',
      menuId: null,
    },
    {
      code: 'TASK_EDIT',
      name: '编辑任务',
      type: '任务管理',
      description: '编辑任务信息',
      menuId: null,
    },
    {
      code: 'TASK_DELETE',
      name: '删除任务',
      type: '任务管理',
      description: '删除任务',
      menuId: null,
    },
    {
      code: 'TASK_ASSIGN',
      name: '分配任务',
      type: '任务管理',
      description: '为用户分配任务',
      menuId: null,
    },
  ]

  // 合并所有操作
  const allOperations = [
    ...userOperations,
    ...roleOperations,
    ...menuOperations,
    ...settingsOperations,
    ...taskOperations,
  ]

  // 批量创建操作
  for (const operation of allOperations) {
    await prisma.operation.upsert({
      where: { code: operation.code },
      update: operation,
      create: operation,
    })
  }

  console.log(`成功初始化 ${allOperations.length} 个操作数据`)

  // 关联操作到菜单
  // 这一步需要在菜单数据初始化后执行
  // 可以在主seed文件中调整执行顺序
  
  console.log('操作数据初始化完成')
}
