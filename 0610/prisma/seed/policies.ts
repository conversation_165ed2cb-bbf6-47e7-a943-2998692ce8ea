import { Policy } from '@prisma/client'

export const defaultPolicies: Partial<Policy>[] = [
  // ... existing code ...

  // 普通用户只能查看自己的账户信息
  {
    name: 'user_view_self_account',
    description: '普通用户只能查看自己的账户信息',
    effect: 'allow',
    actions: ['view'],
    resources: ['account'],
    conditions: {
      'user.id': '$.resource.userId'
    },
    priority: 100
  },

  // 普通用户不能查看客户账户
  {
    name: 'user_deny_customer_accounts',
    description: '普通用户不能查看客户账户',
    effect: 'deny',
    actions: ['view', 'manage'],
    resources: ['customer'],
    conditions: {
      'user.role': 'user'
    },
    priority: 90
  },

  // 普通用户不能查看管理员账户
  {
    name: 'user_deny_admin_accounts',
    description: '普通用户不能查看管理员账户',
    effect: 'deny',
    actions: ['view', 'manage'],
    resources: ['admin'],
    conditions: {
      'user.role': 'user'
    },
    priority: 90
  },

  // 普通用户不能访问用户中心
  {
    name: 'user_deny_user_center',
    description: '普通用户不能访问用户中心',
    effect: 'deny',
    actions: ['view', 'manage'],
    resources: ['user_center'],
    conditions: {
      'user.role': 'user'
    },
    priority: 90
  },

  // 普通用户只能查看通知
  {
    name: 'user_view_notifications',
    description: '普通用户只能查看通知',
    effect: 'allow',
    actions: ['view'],
    resources: ['notification'],
    conditions: {
      'user.role': 'user'
    },
    priority: 100
  },

  // 普通用户不能发布通知
  {
    name: 'user_deny_publish_notifications',
    description: '普通用户不能发布通知',
    effect: 'deny',
    actions: ['create', 'update', 'delete'],
    resources: ['notification'],
    conditions: {
      'user.role': 'user'
    },
    priority: 90
  }
] 