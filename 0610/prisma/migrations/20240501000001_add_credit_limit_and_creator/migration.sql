-- 添加授信额度字段到用户表
ALTER TABLE "user" ADD COLUMN IF NOT EXISTS "creditLimit" FLOAT NOT NULL DEFAULT 0;

-- 创建用户授信额度日志表
CREATE TABLE "user_credit_logs" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "adminId" TEXT NOT NULL,
    "oldCreditLimit" FLOAT NOT NULL,
    "newCreditLimit" FLOAT NOT NULL,
    "remarks" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_credit_logs_pkey" PRIMARY KEY ("id")
);

-- 创建索引
CREATE INDEX "user_credit_logs_userId_idx" ON "user_credit_logs"("userId");
CREATE INDEX "user_credit_logs_adminId_idx" ON "user_credit_logs"("adminId");
CREATE INDEX "user_credit_logs_createdAt_idx" ON "user_credit_logs"("createdAt");

-- 添加外键约束
ALTER TABLE "user_credit_logs" ADD CONSTRAINT "user_credit_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "user_credit_logs" ADD CONSTRAINT "user_credit_logs_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- 确保用户表有创建者ID字段
ALTER TABLE "user" ADD COLUMN IF NOT EXISTS "createdById" TEXT;

-- 添加创建者外键约束
ALTER TABLE "user" ADD CONSTRAINT "user_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;
