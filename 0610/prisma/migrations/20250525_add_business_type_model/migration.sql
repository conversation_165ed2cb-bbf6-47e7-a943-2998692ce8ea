-- CreateTable
CREATE TABLE "business_types" (
  "id" TEXT NOT NULL,
  "code" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "callType" INTEGER NOT NULL,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,

  CONSTRAINT "business_types_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "business_types_code_key" ON "business_types"("code");

-- Insert default business types
INSERT INTO "business_types" ("id", "code", "name", "description", "callType", "createdAt", "updatedAt")
VALUES 
  ('bt_001', 'VIDEO_NOTIFICATION', '视频通知', '单向视频通知，无交互', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('bt_002', 'VIDEO_INTERACTION', '视频互动', '双向视频互动，有交互', 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('bt_003', 'VOICE_INTERACTION', '语音互动', '语音互动，有交互', 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Add relation to Rate model
ALTER TABLE "rates" ADD COLUMN "businessTypeId" TEXT;

-- Create index on businessTypeId
CREATE INDEX "rates_businessTypeId_idx" ON "rates"("businessTypeId");

-- Add foreign key constraint
ALTER TABLE "rates" ADD CONSTRAINT "rates_businessTypeId_fkey" FOREIGN KEY ("businessTypeId") REFERENCES "business_types"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Update existing rates with businessTypeId based on businessType
UPDATE "rates" SET "businessTypeId" = 'bt_001' WHERE "businessType" = 'VIDEO_NOTIFICATION';
UPDATE "rates" SET "businessTypeId" = 'bt_002' WHERE "businessType" = 'VIDEO_INTERACTION';
UPDATE "rates" SET "businessTypeId" = 'bt_003' WHERE "businessType" = 'VOICE_INTERACTION';
