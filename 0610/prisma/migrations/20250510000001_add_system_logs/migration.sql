-- 创建系统操作日志表
CREATE TABLE IF NOT EXISTS "system_log" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "module" TEXT NOT NULL,
    "resourceId" TEXT,
    "resourceType" TEXT,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "system_log_pkey" PRIMARY KEY ("id")
);

-- 添加索引
CREATE INDEX "system_log_userId_idx" ON "system_log"("userId");
CREATE INDEX "system_log_action_idx" ON "system_log"("action");
CREATE INDEX "system_log_module_idx" ON "system_log"("module");
CREATE INDEX "system_log_createdAt_idx" ON "system_log"("createdAt");
CREATE INDEX "system_log_resourceType_resourceId_idx" ON "system_log"("resourceType", "resourceId");

-- 添加外键约束
ALTER TABLE "system_log" ADD CONSTRAINT "system_log_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
