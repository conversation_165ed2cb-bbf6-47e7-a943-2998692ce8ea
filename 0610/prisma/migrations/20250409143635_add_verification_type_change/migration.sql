-- CreateTable
CREATE TABLE "verification_type_change" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "originalType" TEXT,
    "originalStatus" TEXT,
    "newType" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "adminId" TEXT NOT NULL,
    "deadline" TIMESTAMP(3) NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "verification_type_change_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "verification_type_change_userId_key" ON "verification_type_change"("userId");

-- CreateIndex
CREATE INDEX "verification_type_change_userId_idx" ON "verification_type_change"("userId");

-- CreateIndex
CREATE INDEX "verification_type_change_adminId_idx" ON "verification_type_change"("adminId");

-- CreateIndex
CREATE INDEX "verification_type_change_completed_idx" ON "verification_type_change"("completed");

-- CreateIndex
CREATE INDEX "verification_type_change_deadline_idx" ON "verification_type_change"("deadline");

-- AddForeignKey
ALTER TABLE "verification_type_change" ADD CONSTRAINT "verification_type_change_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "verification_type_change" ADD CONSTRAINT "verification_type_change_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
