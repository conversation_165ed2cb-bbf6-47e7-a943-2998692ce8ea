-- CreateTable
CREATE TABLE "notification" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "publishedAt" TIMESTAMP(3),
    "expiryDate" TIMESTAMP(3),
    "sendToAll" BOOLEAN NOT NULL DEFAULT false,
    "recipients" JSONB,

    CONSTRAINT "notification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "userNotification" (
    "id" TEXT NOT NULL,
    "notificationId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "read" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "userNotification_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notificationType" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT 'blue',
    "icon" TEXT NOT NULL DEFAULT 'Bell',
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "description" TEXT,
    "priority" TEXT NOT NULL DEFAULT 'medium',
    "parentId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notificationType_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "userNotification_notificationId_idx" ON "userNotification"("notificationId");

-- CreateIndex
CREATE INDEX "userNotification_userId_idx" ON "userNotification"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "userNotification_notificationId_userId_key" ON "userNotification"("notificationId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "notificationType_code_key" ON "notificationType"("code");

-- AddForeignKey
ALTER TABLE "userNotification" ADD CONSTRAINT "userNotification_notificationId_fkey" FOREIGN KEY ("notificationId") REFERENCES "notification"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "userNotification" ADD CONSTRAINT "userNotification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notificationType" ADD CONSTRAINT "notificationType_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "notificationType"("id") ON DELETE SET NULL ON UPDATE CASCADE;
