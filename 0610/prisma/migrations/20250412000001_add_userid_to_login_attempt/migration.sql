-- 添加userId字段到loginAttempt表
ALTER TABLE "loginAttempt" ADD COLUMN "userId" TEXT;

-- 创建索引
CREATE INDEX "loginAttempt_userId_idx" ON "loginAttempt"("userId");

-- 更新现有记录，将userId设置为对应用户的id
UPDATE "loginAttempt" la
SET "userId" = u.id
FROM "user" u
WHERE la.identifier = u.username;

-- 添加外键约束
ALTER TABLE "loginAttempt" ADD CONSTRAINT "loginAttempt_userId_fkey" 
FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
