-- 插入基础资源
INSERT INTO resource ("id", "code", "name", "type", "description", "createdAt", "updatedAt")
VALUES 
  ('res_001', 'user', '用户管理', 'module', '用户管理模块', NOW(), NOW()),
  ('res_002', 'role', '角色管理', 'module', '角色管理模块', NOW(), NOW()),
  ('res_003', 'menu', '菜单管理', 'module', '菜单管理模块', NOW(), NOW()),
  ('res_004', 'operation', '操作权限管理', 'module', '操作权限管理模块', NOW(), NOW()),
  ('res_005', 'resource', '资源管理', 'module', '资源管理模块', NOW(), NOW()),
  ('res_006', 'notifications', '通知管理', 'module', '通知管理模块', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  code = EXCLUDED.code,
  name = EXCLUDED.name,
  type = EXCLUDED.type,
  description = EXCLUDED.description,
  "updatedAt" = NOW();

-- 关联资源与操作权限
INSERT INTO "_ResourceOperations" ("A", "B")
VALUES 
  -- 用户管理资源关联
  ('res_001', 'op_001'), -- 查看用户列表
  ('res_001', 'op_002'), -- 创建用户
  ('res_001', 'op_003'), -- 更新用户
  ('res_001', 'op_004'), -- 删除用户
  
  -- 角色管理资源关联
  ('res_002', 'op_005'), -- 查看角色列表
  ('res_002', 'op_006'), -- 创建角色
  ('res_002', 'op_007'), -- 更新角色
  ('res_002', 'op_008'), -- 删除角色
  
  -- 菜单管理资源关联
  ('res_003', 'op_009'), -- 查看菜单列表
  ('res_003', 'op_010'), -- 创建菜单
  ('res_003', 'op_011'), -- 更新菜单
  ('res_003', 'op_012'), -- 删除菜单
  
  -- 操作权限管理资源关联
  ('res_004', 'op_013'), -- 查看操作权限列表
  ('res_004', 'op_014'), -- 创建操作权限
  ('res_004', 'op_015'), -- 更新操作权限
  ('res_004', 'op_016'), -- 删除操作权限
  
  -- 资源管理资源关联
  ('res_005', 'op_017'), -- 查看资源列表
  ('res_005', 'op_018'), -- 创建资源
  ('res_005', 'op_019'), -- 更新资源
  ('res_005', 'op_020'), -- 删除资源

  -- 通知管理资源关联
  ('res_006', 'op_021'), -- 查看通知列表
  ('res_006', 'op_022'), -- 创建通知
  ('res_006', 'op_023'), -- 编辑通知
  ('res_006', 'op_024')  -- 删除通知
ON CONFLICT ("A", "B") DO NOTHING;