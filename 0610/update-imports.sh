#!/bin/bash
# 导入路径更新脚本
# 在移动文件后使用此脚本更新导入路径

set -e

echo "🔄 更新导入路径..."

# 示例：更新组件导入路径
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/components/customer|@/components/business/customer|g'
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/components/admin|@/components/business/admin|g'

# 示例：更新 hooks 导入路径  
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/hooks/use-customer|@/hooks/business/use-customer|g'

# 示例：更新服务导入路径
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/lib/auth-service|@/services/auth/auth-service|g'

echo "✅ 导入路径更新完成（模板）"
echo "⚠️  请根据实际移动的文件修改此脚本！"
