#!/bin/bash
# 清理验证脚本
# 验证清理和重组后项目的完整性

set -e

echo "🔍 验证项目完整性..."

# 检查构建
echo "📦 检查项目构建..."
npm run build || echo "❌ 构建失败，请检查导入路径"

# 检查 TypeScript
echo "🔧 检查 TypeScript..."
npx tsc --noEmit || echo "❌ TypeScript 检查失败"

# 检查 ESLint
echo "📋 检查 ESLint..."
npm run lint || echo "⚠️  ESLint 检查有警告"

# 统计清理效果
echo "\n📊 清理效果统计:"
echo "当前项目文件数: $(find . -type f -not -path './node_modules/*' | wc -l)"
echo "当前项目大小: $(du -sh . --exclude=node_modules 2>/dev/null || du -sh .)"
echo "剩余 console.log: $(grep -r "console\.log" . --exclude-dir=node_modules | wc -l)"
echo "剩余 TODO/FIXME: $(grep -r "TODO\|FIXME" . --exclude-dir=node_modules | wc -l)"

echo "✅ 验证完成"
