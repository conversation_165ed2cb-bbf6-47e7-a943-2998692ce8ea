# 5G Web 管理系统

这是一个基于Next.js 14和Prisma构建的现代化Web管理系统，提供用户管理、权限控制、通知系统和任务管理等功能。

## 功能特点

- **用户认证与授权**：基于NextAuth.js和jCasbin的强大认证和基于属性的访问控制(ABAC)
- **角色与权限管理**：灵活的角色权限系统，支持细粒度的资源访问控制
- **通知系统**：支持站内通知、邮件通知和短信通知
- **任务管理**：支持创建、监控和管理AI视频外呼任务
- **系统设置**：可配置的系统参数，包括邮件设置、密码规则和界面定制
- **响应式设计**：适配各种设备尺寸的现代化UI
- **多语言支持**：内置中文界面，可扩展支持其他语言

## 技术栈

- **前端**：Next.js 14, React 18, TailwindCSS, shadcn/ui
- **后端**：Next.js API Routes, Prisma ORM
- **数据库**：PostgreSQL
- **认证**：NextAuth.js, JWT
- **权限控制**：jCasbin
- **缓存**：Redis
- **邮件**：Nodemailer
- **部署**：PM2, Docker (可选)

## 开始使用

### 环境要求

- Node.js 18.x 或更高版本
- PostgreSQL 14.x 或更高版本
- Redis 6.x 或更高版本

### 安装步骤

1. 克隆代码库

```bash
git clone https://your-repository-url.git
cd 5gweb_0402
```

2. 安装依赖

```bash
npm install
```

3. 配置环境变量

```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填写必要的配置
```

4. 运行数据库迁移

```bash
npx prisma migrate dev
```

5. 启动开发服务器

```bash
npm run dev
```

6. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 部署

详细的部署指南请参考 [部署指南](./docs/部署指南.md)。

## 开发指南

### 项目结构

```
/
├── app/                # Next.js App Router
│   ├── (auth)/         # 认证相关页面
│   ├── (dashboard)/    # 主应用页面
│   ├── api/            # API路由
│   └── components/     # 共享组件
├── components/         # 全局UI组件
├── lib/                # 工具函数和服务
│   ├── abac/           # 基于属性的访问控制
│   ├── api/            # API客户端
│   ├── auth/           # 认证相关
│   ├── casbin/         # Casbin配置
│   ├── services/       # 业务服务
│   └── utils/          # 工具函数
├── prisma/             # Prisma模型和迁移
└── public/             # 静态资源
```

### 开发命令

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行ESLint检查
- `npm run format` - 使用Prettier格式化代码

## 贡献指南

1. Fork代码库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

[MIT](LICENSE)

## 联系方式

如有任何问题或建议，请联系项目维护者。
