generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

/// 用户模型
/// 用于存储系统用户信息，包括基本信息和权限信息
model user {
  /// 唯一标识符
  id                       String                    @id @default(cuid())
  /// 用户名，用于登录
  username                 String                    @unique
  /// 密码，经过哈希处理
  password                 String
  /// 电子邮箱，用于通知和找回密码
  email                    String                    @unique
  /// 用户特定权限列表
  permissions              String[]
  /// 创建时间
  createdAt                DateTime                  @default(now())
  /// 更新时间
  updatedAt                DateTime                  @updatedAt
  /// 角色代码，关联到角色表
  roleCode                 String
  /// 邮箱验证时间
  emailVerified            DateTime?
  /// 头像图片URL
  image                    String?
  /// 显示名称
  name                     String?
  /// 最后登录时间
  lastLoginAt              DateTime?
  /// 详细地址
  address                  String?
  /// 城市
  city                     String?
  /// 区县
  district                 String?
  /// 手机号码
  phone                    String?
  /// 省份
  province                 String?
  /// 认证状态: pending(待审核), approved(已认证), rejected(已拒绝), none(未认证)
  verificationStatus       String?                   @default("none")
  /// 认证类型: personal(个人认证), enterprise(企业认证), none(未认证)
  verificationType         String?                   @default("none")
  /// 微信号
  wechat                   String?
  /// 账户余额
  balance                  Float                     @default(0)
  /// 授信额度
  creditLimit              Float                     @default(0)
  /// 用户状态: active(启用), inactive(停用), pending(待审核)
  status                   String                    @default("active")
  /// 创建者ID
  createdById              String?
  /// 是否启用邮件余额预警
  balanceAlertEmail        Boolean                   @default(false)
  /// 是否启用短信余额预警
  balanceAlertSms          Boolean                   @default(false)
  /// 余额预警阈值
  balanceAlertThreshold    Float                     @default(0)
  accounts                 Account[]
  sessions                 Session[]
  adminBalanceTransactions BalanceTransaction[]      @relation("AdminBalanceTransactions")
  balanceTransactions      BalanceTransaction[]      @relation("UserBalanceTransactions")
  callDetails              call_detail[]
  emailQueue               emailQueue[]
  loginAttempt             loginAttempt[]
  loginHistory             LoginHistory[]
  rates                    Rate[]                    @relation("CustomerRates")
  smsTemplates             SmsTemplate[]             @relation("CreatedSmsTemplates")
  system_log               system_log[]
  tasks                    task[]
  createdBy                user?                     @relation("UserCreator", fields: [createdById], references: [id])
  createdUsers             user[]                    @relation("UserCreator")
  role                     role                      @relation(fields: [roleCode], references: [code])
  adminCreditLogs          UserCreditLog[]           @relation("AdminCreditLogs")
  userCreditLogs           UserCreditLog[]           @relation("UserCreditLogs")
  userNotifications        userNotification[]
  notificationSettings     UserNotificationSettings?
  preferences              UserPreference?
  adminStatusLogs          UserStatusLog[]           @relation("AdminStatusLogs")
  userStatusLogs           UserStatusLog[]           @relation("UserStatusLogs")
  verification             userVerification?
  verificationCodes        verificationCode[]
  adminVerificationChanges VerificationTypeChange[]  @relation("AdminVerificationChange")
  verificationChange       VerificationTypeChange?   @relation("UserVerificationChange")
}

/// 登录尝试记录模型
/// 用于记录用户登录尝试次数，防止暴力破解
model loginAttempt {
  /// 唯一标识符
  id          String    @id @default(cuid())
  /// 尝试次数
  attempts    Int       @default(0)
  /// 创建时间
  createdAt   DateTime  @default(now())
  identifier  String
  /// 锁定截止时间
  lockedUntil DateTime?
  /// 更新时间
  updatedAt   DateTime  @updatedAt
  /// 登录IP
  ip          String
  /// 登录用户代理
  userAgent   String?
  /// 登录成功与否
  success     Boolean
  user        user      @relation(fields: [identifier], references: [username])
}

/// 验证码模型
/// 用于存储邮箱验证码信息
model verificationCode {
  /// 唯一标识符
  id        String   @id @default(cuid())
  /// 验证码
  code      String
  /// 目标邮箱地址
  email     String
  /// 过期时间
  expiresAt DateTime
  /// 创建时间
  createdAt DateTime @default(now())
  /// 更新时间
  updatedAt DateTime @updatedAt
  /// 验证码类型
  type      String
  user      user     @relation(fields: [email], references: [email])
}

/// 权限策略模型
/// 用于定义系统的访问控制策略
model policy {
  /// 唯一标识符
  id            String      @id @default(cuid())
  /// 资源标识符
  resourceCode  String
  /// 操作标识符
  operationCode String
  /// 策略描述
  description   String?
  /// 创建时间
  createdAt     DateTime    @default(now())
  /// 更新时间
  updatedAt     DateTime    @updatedAt
  conditions    condition[]
  operation     operation   @relation(fields: [operationCode], references: [code])
  resource      resource    @relation(fields: [resourceCode], references: [code])
}

/// 角色模型
/// 用于定义用户角色和权限组
model role {
  /// 唯一标识符
  id                  String      @id @default(cuid())
  /// 角色代码
  code                String      @unique
  /// 角色名称
  name                String
  /// 角色类型（system/customer）
  type                String
  /// 角色描述
  description         String?
  /// 权限列表
  permissions         String[]
  /// 创建时间
  createdAt           DateTime    @default(now())
  /// 更新时间
  updatedAt           DateTime    @updatedAt
  /// 是否启用通知组件
  notificationEnabled Boolean     @default(true)
  users               user[]
  menus               menu[]      @relation("RoleMenus")
  operations          operation[] @relation("RoleOperations")
  resources           resource[]  @relation("RoleResources")
}

/// 菜单模型
/// 用于定义系统菜单结构和访问权限
model menu {
  /// 唯一标识符
  id         String      @id @default(cuid())
  /// 菜单代码
  code       String      @unique
  /// 菜单名称
  name       String
  /// 路由路径
  path       String
  /// 图标名称
  icon       String?
  /// 父菜单ID
  parentId   String?
  /// 排序顺序
  order      Int         @default(0)
  /// 是否可见
  visible    Boolean     @default(true)
  /// 创建时间
  createdAt  DateTime    @default(now())
  /// 更新时间
  updatedAt  DateTime    @updatedAt
  parent     menu?       @relation("MenuHierarchy", fields: [parentId], references: [id])
  children   menu[]      @relation("MenuHierarchy")
  operations operation[]
  roles      role[]      @relation("RoleMenus")
}

/// 操作权限模型
/// 用于定义具体的操作权限，如按钮点击、数据访问等
model operation {
  /// 唯一标识符
  id          String     @id @default(cuid())
  /// 操作代码
  code        String     @unique
  /// 操作名称
  name        String
  /// 操作类型（page/action）
  type        String
  /// 操作描述
  description String?
  /// 所��菜单ID
  menuId      String?
  /// 是否启用
  enabled     Boolean    @default(true)
  /// 创建时间
  createdAt   DateTime   @default(now())
  /// 更新时间
  updatedAt   DateTime   @updatedAt
  menu        menu?      @relation(fields: [menuId], references: [id])
  policies    policy[]
  resources   resource[] @relation("ResourceOperations")
  roles       role[]     @relation("RoleOperations")
}

/// 资源模型
/// 用于定义系统资源，�����API、菜单、按钮等
model resource {
  /// 唯一标识符
  id          String      @id @default(cuid())
  /// 资源代码
  code        String      @unique
  /// 资源名称
  name        String
  /// 资源类型（menu/api/data/file）
  type        String
  /// 资源描述
  description String?
  /// 创建时间
  createdAt   DateTime    @default(now())
  /// 更新时间
  updatedAt   DateTime    @updatedAt
  policies    policy[]
  operations  operation[] @relation("ResourceOperations")
  roles       role[]      @relation("RoleResources")
}

/// 条件模型
/// 用于定义策略的条件表达式
model condition {
  /// 唯一标识符
  id        String   @id @default(cuid())
  /// 条件属性
  attribute String
  /// 条件操作符
  operator  String
  /// 条件值
  value     String
  /// 创建时间
  createdAt DateTime @default(now())
  /// 更新时间
  updatedAt DateTime @updatedAt
  /// 策略ID
  policyId  String
  policy    policy   @relation(fields: [policyId], references: [id])
}

/// 模态框配置模型
/// 用于存储模态框的配置信息
model modalConfig {
  /// 唯一标识符
  id           String   @id
  /// 模态框标题
  title        String
  /// 模态框描述
  description  String?
  /// 模态框宽度
  maxWidth     String   @default("lg")
  /// 自定义样式
  customStyles Json?
  /// 创建时间
  createdAt    DateTime @default(now())
  /// 更新时间
  updatedAt    DateTime @updatedAt
  /// 创建用户ID
  createdById  String?
  /// 更新用户ID
  updatedById  String?
}

/// 通知模型
/// 用于存储系统通知信息
model notification {
  /// 唯一标识符
  id                 String             @id @default(cuid())
  /// 通知标题
  title              String
  /// 通知内容
  content            String
  /// 通知状态
  status             String             @default("published")
  /// 通知优先级
  priority           String             @default("medium")
  /// 创建者
  createdBy          String             @default("system")
  /// 创建时间
  createdAt          DateTime           @default(now())
  /// 更新时间
  updatedAt          DateTime           @updatedAt
  /// 发布时间
  publishedAt        DateTime           @default(now())
  /// 是否发送给所有用户
  sendToAll          Boolean            @default(true)
  /// 指定接收者列表
  recipients         Json?
  /// 通知类型ID
  typeId             Int
  /// 是否允许批量标记已读
  allowBatchMarkRead Boolean            @default(true)
  /// 最后阅读时间
  lastReadAt         DateTime?
  /// 总阅读数
  readCount          Int                @default(0)
  /// 阅读率
  readRate           Float              @default(0)
  /// 总接收者数
  totalRecipients    Int                @default(0)
  type               notificationType   @relation(fields: [typeId], references: [id])
  userNotifications  userNotification[]

  @@index([status, sendToAll])
  @@index([typeId])
  @@index([createdAt])
}

/// 用户通知关联模型
/// 用于记录用户与通知的关系，包括已读状态
model userNotification {
  /// 唯一标识符
  id             String       @id @default(cuid())
  /// 用户ID
  userId         String
  /// 通知ID
  notificationId String
  /// 是否已读
  read           Boolean      @default(false)
  /// 已读时间
  readAt         DateTime?
  /// 创建时间
  createdAt      DateTime     @default(now())
  /// 更新时间
  updatedAt      DateTime     @updatedAt
  /// 最后访问时间
  lastViewedAt   DateTime?
  /// 是否收藏
  starred        Boolean      @default(false)
  /// 收藏时间
  starredAt      DateTime?
  /// 访问次数
  viewCount      Int          @default(0)
  /// 是否当前显示
  visible        Boolean      @default(true)
  /// 是否已发送邮件
  emailSent      Boolean      @default(false)
  /// 邮件发送时间
  emailSentAt    DateTime?
  notification   notification @relation(fields: [notificationId], references: [id], onDelete: Cascade)
  user           user         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, notificationId])
  @@index([userId, read])
  @@index([notificationId, read])
  @@map("user_notification")
}

/// 通知类型模型
/// 用于定义系统支持的通知类型
model notificationType {
  /// 类型代码
  code          String             @unique
  /// 类型名称
  name          String
  /// 类型描述
  description   String?
  /// 创建时间
  createdAt     DateTime           @default(now())
  /// 更新时间
  updatedAt     DateTime           @updatedAt
  /// 唯一标识符
  id            Int                @id @default(autoincrement())
  /// 父类型ID
  parentId      Int?
  /// 颜色
  color         String?            @default("blue")
  /// 是否启用
  enabled       Boolean            @default(true)
  /// 图标
  icon          String?            @default("bell")
  /// 优先级
  priority      Int                @default(0)
  notifications notification[]
  parent        notificationType?  @relation("TypeHierarchy", fields: [parentId], references: [id])
  children      notificationType[] @relation("TypeHierarchy")
}

model UserNotificationSettings {
  id           String   @id @default(cuid())
  userId       String   @unique
  emailEnabled Boolean  @default(true)
  smsEnabled   Boolean  @default(true)
  appEnabled   Boolean  @default(true)
  types        String[] @default(["SYSTEM", "SECURITY", "TASK"])
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_notification_settings")
}

model LoginHistory {
  id         String   @id @default(cuid())
  userId     String
  ipAddress  String
  location   String
  device     String
  status     String   @default("success")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  failReason String?
  user       user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
  @@map("login_history")
}

/// 用户认证信息模型
/// 用于存储用户的认证资料
model userVerification {
  /// 唯一标识符
  id                String    @id @default(cuid())
  /// 用户ID
  userId            String    @unique
  /// 认证类型: personal(个人认证), enterprise(企业认证)
  type              String
  /// 认证状态: pending(待审核), approved(已认证), rejected(已拒绝)
  status            String    @default("pending")
  /// 审核备注
  remark            String?
  /// 审核人
  reviewerId        String?
  /// 审核时间
  reviewedAt        DateTime?
  /// 创建时间
  createdAt         DateTime  @default(now())
  /// 更新时间
  updatedAt         DateTime  @updatedAt
  /// 真实姓名
  realName          String?
  /// 身份证号码
  idCardNumber      String?
  /// 身份证正面照片
  idCardFront       String?
  /// 身份证反面照片
  idCardBack        String?
  /// 手持身份证照片
  idCardHolding     String?
  /// 企业名称
  companyName       String?
  /// 法人姓名
  legalPerson       String?
  /// 法人身份证号
  legalPersonIdCard String?
  /// 统一社会信用代码
  socialCreditCode  String?
  /// 营业执照照片
  businessLicense   String?
  /// 其他资质证件
  otherDocuments    String[]  @default([])
  user              user      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
  @@index([type])
  @@map("user_verification")
}

model VerificationTypeChange {
  /// 唯一标识符
  id             String   @id @default(cuid())
  /// 用户ID
  userId         String   @unique
  /// 原始认证类型
  originalType   String?
  /// 原始认证状态
  originalStatus String?
  /// 新认证类型
  newType        String
  /// 变更原因
  reason         String
  /// 管理员ID
  adminId        String
  /// 截止日期
  deadline       DateTime
  /// 是否已完成资料补���������
  completed      Boolean  @default(false)
  /// 创建时间
  createdAt      DateTime @default(now())
  /// 更新时间
  updatedAt      DateTime @updatedAt
  admin          user     @relation("AdminVerificationChange", fields: [adminId], references: [id])
  user           user     @relation("UserVerificationChange", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([adminId])
  @@index([completed])
  @@index([deadline])
  @@map("verification_type_change")
}

/// 余额交易记录模型
/// 用于记录用户余额变动���史
model BalanceTransaction {
  /// 唯一标识符
  id                String   @id @default(cuid())
  /// 用户ID
  userId            String
  /// 交易金额
  amount            Float
  /// 交易后余额
  balanceAfter      Float
  /// 交易类型: recharge(充值), deduct(扣费), refund(退款), credit_add(授信额度增加), credit_subtract(授信额度减少), credit_set(授信额度调整), other(其他)
  type              String
  /// 支付方式: bank(银行转账), alipay(支付宝), wechat(微信支付), other(其他)
  paymentMethod     String?
  /// 备注信息
  remarks           String?
  /// 操作管理员ID
  adminId           String?
  /// 创建时间
  createdAt         DateTime @default(now())
  /// 更新时间
  updatedAt         DateTime @updatedAt
  /// 变动后授信额度
  creditLimitAfter  Float?
  /// 授信额度变动
  creditLimitChange Float?
  admin             user?    @relation("AdminBalanceTransactions", fields: [adminId], references: [id])
  user              user     @relation("UserBalanceTransactions", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([adminId])
  @@index([createdAt])
  @@index([type])
  @@map("balance_transactions")
}

/// 用户状态日志模型
/// 用于记录用户状态变更历史
model UserStatusLog {
  /// 唯一标识符
  id        String   @id @default(cuid())
  /// 用户ID
  userId    String
  /// 管理员ID
  adminId   String
  /// 状态: active(正常), inactive(禁用)
  status    String
  /// 变更原因
  reason    String
  /// 创建时间
  createdAt DateTime @default(now())
  admin     user     @relation("AdminStatusLogs", fields: [adminId], references: [id])
  user      user     @relation("UserStatusLogs", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([adminId])
  @@index([createdAt])
  @@index([status])
  @@map("user_status_logs")
}

/// 用户授信额度日志模型
/// 用于记录用户授信额度变更历史
model UserCreditLog {
  /// 唯一标识符
  id             String   @id @default(cuid())
  /// 用户ID
  userId         String
  /// 管理员ID
  adminId        String
  /// 原授信额度
  oldCreditLimit Float
  /// 新授信额度
  newCreditLimit Float
  /// 变更原因
  remarks        String
  /// 创建时间
  createdAt      DateTime @default(now())
  admin          user     @relation("AdminCreditLogs", fields: [adminId], references: [id])
  user           user     @relation("UserCreditLogs", fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([adminId])
  @@index([createdAt])
  @@map("user_credit_logs")
}

/// 费率模型
/// 用于定义不同业务类型的费率
model Rate {
  /// 唯一标识符
  id               String   @id @default(cuid())
  /// 费率金额(元)
  amount           Float
  /// 周期(秒)
  period           Int?
  /// 客户ID
  customerId       String?
  /// 业务类型: VIDEO_NOTIFICATION(视频通知), VIDEO_INTERACTION(视频互动), VOICE_INTERACTION(语音互动)
  businessType     String
  /// 创建时间
  createdAt        DateTime @default(now())
  /// 更新时间
  updatedAt        DateTime @updatedAt
  /// 计费周期类型: PER_MINUTE(按分钟计费/60秒), PER_SIX_SECOND(按6秒计费)
  billingIncrement String   @default("PER_MINUTE")
  customer         user?    @relation("CustomerRates", fields: [customerId], references: [id])

  @@index([customerId])
  @@index([businessType])
  @@index([createdAt])
  @@index([billingIncrement])
  @@map("rates")
}

/// 系统设置模型
/// 用于存储系统全局设置
model systemSettings {
  /// 唯一标识符
  id                     Int      @id @default(1)
  /// 站点名称
  siteName               String
  /// Logo URL
  logo                   String
  /// 页脚文本
  footerText             String?
  /// 主题设置
  theme                  Json
  /// 功能设置
  features               Json
  /// 安全设置
  security               Json?
  /// 创建时间
  createdAt              DateTime @default(now())
  /// 更新时间
  updatedAt              DateTime @updatedAt
  /// iOS应用名称
  appleMobileWebAppTitle String?
  /// 应用名称
  applicationName        String?
  /// 网站描述
  description            String?
  /// 关键词
  keywords               String?
  /// 登录页设置
  loginPage              Json?
  /// 邮件设置
  emailSettings          Json?

  @@map("system_settings")
}

/// 系统日志模型
/// 用于记录系统操作和回调信息
model system_log {
  /// 唯一标识符
  id           String   @id @default(cuid())
  /// 用户ID（可选，回调日志可能没有关联用户）
  userId       String
  /// 操作类型（如LOGIN、LOGOUT、CREATE、UPDATE、DELETE、CALLBACK等）
  action       String
  module       String
  /// 资源ID（可选）
  resourceId   String?
  /// 资源类型（可选）
  resourceType String?
  /// 详细信息（JSON格式）
  details      Json?
  /// IP地址（可选）
  ipAddress    String?
  /// 用户代理（可选）
  userAgent    String?
  createdAt    DateTime @default(now())
  /// 状态（info、warning、error、success等）
  status       String   @default("info")
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([action])
  @@index([resourceType, resourceId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
  @@index([module])
}

/// NextAuth.js 所需的模型
/// 账户模型
/// 用于存储用户的第三方账户信息
model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              user    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@index([userId])
}

/// 会话模型
/// 用于存储用户的会话信息
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

/// 验证令牌模型
/// 用于存储邮箱验证等临时令牌
model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@id([identifier, token])
}

model casbin_rule {
  id    Int     @id @default(autoincrement())
  ptype String
  v0    String?
  v1    String?
  v2    String?
  v3    String?
  v4    String?
  v5    String?
}

/// 邮件队列模型
/// 用于存储待发送的邮件
model emailQueue {
  /// 唯一标识符
  id        String    @id @default(cuid())
  /// 邮件主题
  subject   String
  /// 发送状态：pending(待发送), sent(已发送), failed(发送失败)
  status    String    @default("pending")
  /// 发送时间
  sentAt    DateTime?
  /// 创建时间
  createdAt DateTime  @default(now())
  /// 失败原因
  error     String?
  /// 发件人邮箱（可选）
  from      String?
  /// 邮件内容（HTML格式）
  html      String
  /// 重试次数
  retries   Int       @default(0)
  /// 纯文本内容（可选）
  text      String?
  /// 收件人邮箱
  to        String
  /// 相关用户ID
  userId    String?
  user      user?     @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([createdAt])
  @@index([userId])
  @@map("email_queue")
}

/// 用户偏好设置模型
/// 用于存储用户的个性化设置，如界面偏好、通知设置等
model UserPreference {
  /// 唯一标识符
  id          String   @id @default(cuid())
  /// 用户ID
  userId      String   @unique
  /// 偏好设置（JSON格式）
  preferences Json     @default("{}")
  /// 创建时间
  createdAt   DateTime @default(now())
  /// 更新时间
  updatedAt   DateTime @updatedAt
  user        user     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("user_preferences")
}

/// 短信模板模型
/// 用于存储系统短信模板
model SmsTemplate {
  /// 唯一标识符
  id          String   @id @default(cuid())
  /// 模板代码
  code        String   @unique
  /// 模板名称
  name        String
  /// 模板内容
  content     String
  /// 模板类型：text(文本短信), video(视频短信), flash(闪信)
  type        String
  /// 创建时间
  createdAt   DateTime @default(now())
  /// 更新时间
  updatedAt   DateTime @updatedAt
  /// 创建者ID
  createdById String
  /// 是否启用
  isActive    Boolean  @default(true)
  createdBy   user     @relation("CreatedSmsTemplates", fields: [createdById], references: [id])

  @@index([type])
  @@index([isActive])
  @@index([createdById])
  @@map("sms_templates")
}

/// 任务模型
/// 用于存储系统任务信息，包括视频外呼任务等
model task {
  /// 唯一标识符
  id             String        @id @default(cuid())
  /// 任务名称
  name           String
  /// 任务类型：5G视频通知, 5G视频互动, 5G语音通话
  type           String
  /// 任务内容
  content        String
  /// 任务状态：未开始, 外呼中, 已完成, 已取消
  status         String        @default("未开始")
  /// 任务进度（百分比）
  progress       Int           @default(0)
  /// 导入时间
  importTime     DateTime      @default(now())
  /// 开始时间
  startTime      DateTime
  /// 完成时间
  completionTime DateTime?
  /// 创建者
  creator        String
  /// 用户ID
  userId         String
  /// 外部任务ID（用于与外部系统关联）
  externalId     String?       @unique
  /// 创建时间
  createdAt      DateTime      @default(now())
  /// 更新时间
  updatedAt      DateTime      @updatedAt
  callDetails    call_detail[]
  user           user          @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([createdAt])
  @@index([startTime])
}

/// 外呼详情模型
/// 用于存储任务的外呼详情信息
model call_detail {
  /// 唯一标识符
  id             String    @id @default(cuid())
  /// 任务ID
  taskId         String
  /// 任务名称
  taskName       String
  /// 外呼类型
  type           String
  /// 外呼内容
  content        String
  /// 客户名称
  customerName   String?
  /// 电话号码
  phoneNumber    String
  /// 接通类型：视频接通, 语音接通
  connectionType String?
  /// 开始时间
  startTime      DateTime?
  /// 结束时间
  endTime        DateTime?
  /// 通话时长
  duration       String?
  /// 振铃时长
  ringTime       String?
  /// 意向程度：A, B, C, D, E
  intention      String?
  /// 外部呼叫ID（用于与外部系统关联）
  externalCallId String?   @unique
  /// 录音URL
  recordingUrl   String?
  /// 完播率
  completionRate Float?
  /// 用户ID
  userId         String
  /// 创建时间
  createdAt      DateTime  @default(now())
  /// 更新时间
  updatedAt      DateTime  @updatedAt
  task           task      @relation(fields: [taskId], references: [id])
  user           user      @relation(fields: [userId], references: [id])

  @@index([taskId])
  @@index([userId])
  @@index([phoneNumber])
  @@index([startTime])
  @@index([connectionType])
  @@index([intention])
}
