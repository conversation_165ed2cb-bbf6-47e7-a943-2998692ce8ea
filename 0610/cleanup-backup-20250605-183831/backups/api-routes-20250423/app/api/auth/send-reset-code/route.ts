import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { generateVerificationCode } from "@/lib/services/verification"
import { emailService } from "@/lib/services/email"

// 定义常量
const SEND_CODE_INTERVAL = 60 * 1000 // 60秒内不能重复发送
const CODE_EXPIRY = 15 * 60 * 1000 // 验证码15分钟后过期

/**
 * 发送重置密码验证码的 API 路由
 * 
 * 功能描述：
 * 1. 接收用户提供的邮箱地址
 * 2. 验证邮箱格式
 * 3. 检查是否存在频繁请求
 * 4. 生成并发送验证码
 * 
 * 安全考虑：
 * 1. 不直接暴露邮箱是否注册的信息
 * 2. 限制验证码发送频率
 * 3. 验证码有效期限制
 * 4. 错误信息模糊化处理
 */
export async function POST(request: Request) {
  try {
    const { email } = await request.json()

    // 验证邮箱格式
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { success: false, message: "请输入有效的邮箱地址" },
        { status: 400 }
      )
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    })

    // 如果用户不存在，返回提示信息
    if (!user) {
      return NextResponse.json({
        success: false,
        message: "该邮箱尚未注册，请先注册账号"
      }, { status: 404 })
    }

    // 检查是否存在未过期的验证码
    const existingCode = await prisma.verificationCode.findFirst({
      where: {
        email,
        type: "RESET_PASSWORD",
        createdAt: {
          gt: new Date(Date.now() - SEND_CODE_INTERVAL)
        }
      }
    })

    if (existingCode) {
      return NextResponse.json({
        success: false,
        message: "验证码已发送，请稍后再试"
      }, { status: 429 })
    }

    // 生成新的验证码
    const code = generateVerificationCode()

    // 保存验证码
    await prisma.verificationCode.create({
      data: {
        code,
        email,
        type: "RESET_PASSWORD",
        expiresAt: new Date(Date.now() + CODE_EXPIRY),
      }
    })

    // 发送验证码邮件
    await emailService.sendVerificationEmail(email, code, 'reset')

    return NextResponse.json({ 
      success: true, 
      message: "验证码已发送到您的邮箱" 
    })

  } catch (error) {
    console.error("发送重置密码验证码失败:", error)
    return NextResponse.json(
      { success: false, message: "验证码发送失败，请稍后重试" },
      { status: 500 }
    )
  }
} 