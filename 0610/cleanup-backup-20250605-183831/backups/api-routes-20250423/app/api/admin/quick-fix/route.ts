import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 快速修复管理员权限的API
 * 这是一个临时API，用于修复管理员角色的菜单权限问题
 */
export async function GET(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[快速修复API:${requestId}] 开始修复管理员权限`)

  try {
    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    if (!adminRole) {
      return NextResponse.json({
        success: false,
        message: "管理员角色不存在",
        requestId
      }, { status: 404 })
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany()

    // 关联所有菜单到管理员角色
    await prisma.role.update({
      where: { id: adminRole.id },
      data: {
        menus: {
          connect: allMenus.map(menu => ({ id: menu.id }))
        }
      }
    })

    // 为每个菜单添加jCasbin权限
    for (const menu of allMenus) {
      await CasbinService.addPermissionForRole('ADMIN', `menu:${menu.code}`, 'view')
    }

    // 添加管理员的通配符权限
    await CasbinService.addPermissionForRole('ADMIN', '*', '*')

    // 同步所有角色的权限
    const roles = await prisma.role.findMany({
      include: {
        menus: true,
        operations: true
      }
    })

    for (const role of roles) {
      // 获取角色当前的所有权限
      const currentPermissions = await CasbinService.getRolePermissions(role.code)
      
      // 删除所有菜单和操作权限
      for (const permission of currentPermissions) {
        if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
          await CasbinService.removePermissionForRole(role.code, permission[1], permission[2])
        }
      }

      // 添加菜单权限
      for (const menu of role.menus) {
        await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
      }

      // 添加操作权限
      for (const operation of role.operations) {
        await CasbinService.addPermissionForRole(role.code, `operation:${operation.code}`, 'execute')
      }
    }

    return NextResponse.json({
      success: true,
      message: "管理员权限修复成功",
      data: {
        roleId: adminRole.id,
        menuCount: allMenus.length,
        roleCount: roles.length
      },
      requestId
    })
  } catch (error) {
    console.error(`[快速修复API:${requestId}] 修复管理员权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "修复管理员权限失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
