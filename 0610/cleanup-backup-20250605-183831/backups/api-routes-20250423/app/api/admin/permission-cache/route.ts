import { NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { options } from "../../auth/[...nextauth]/options"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 获取权限缓存统计信息
 */
export async function GET(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限缓存API:${requestId}] 获取权限缓存统计信息`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[权限缓存API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以查看权限缓存统计信息",
        requestId
      }, { status: 403 })
    }

    // 获取缓存统计信息
    const stats = CasbinService.getCacheStats()

    return NextResponse.json({
      success: true,
      data: stats,
      requestId
    })
  } catch (error) {
    console.error(`[权限缓存API:${requestId}] 获取权限缓存统计信息失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取权限缓存统计信息失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 清除权限缓存
 */
export async function DELETE(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限缓存API:${requestId}] 清除权限缓存`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[权限缓存API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以清除权限缓存",
        requestId
      }, { status: 403 })
    }

    // 清除缓存
    CasbinService.clearCache()

    return NextResponse.json({
      success: true,
      message: "权限缓存已清除",
      requestId
    })
  } catch (error) {
    console.error(`[权限缓存API:${requestId}] 清除权限缓存失败:`, error)
    return NextResponse.json({
      success: false,
      message: "清除权限缓存失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
