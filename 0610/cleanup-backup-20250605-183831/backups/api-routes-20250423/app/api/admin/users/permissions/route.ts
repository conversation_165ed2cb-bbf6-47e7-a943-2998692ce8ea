import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyToken } from '@/lib/auth'
import { hasPermission } from '@/lib/permission'

/**
 * 获取用户权限列表
 * @route GET /api/admin/users/permissions
 */
export async function GET(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理权限
    const hasAccess = await hasPermission(decoded.sub as string, 'user:manage')
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有操作权限', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取所有权限
    const permissions = await prisma.operation.findMany({
      orderBy: { code: 'asc' },
    })

    return NextResponse.json(
      { success: true, data: permissions },
      { status: 200, headers }
    )
  } catch (error) {
    console.error('获取权限列表失败:', error)
    return NextResponse.json(
      { success: false, message: '获取权限列表失败', error },
      { status: 500, headers }
    )
  }
}

/**
 * 更新用户权限
 * @route POST /api/admin/users/permissions
 */
export async function POST(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 验证管理员权限
    const token = req.cookies.get('token')?.value
    if (!token) {
      return NextResponse.json(
        { success: false, message: '未授权访问', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    const decoded = await verifyToken(token)
    if (!decoded) {
      return NextResponse.json(
        { success: false, message: '无效的令牌', error: 'InvalidToken' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理权限
    const hasAccess = await hasPermission(decoded.sub as string, 'user:manage')
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有操作权限', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    const body = await req.json()
    const { userId, permissionIds } = body

    if (!userId || !Array.isArray(permissionIds)) {
      return NextResponse.json(
        { success: false, message: '参数错误', error: 'InvalidParams' },
        { status: 400, headers }
      )
    }

    // 更新用户权限
    // 直接更新用户的permissions字段
    await prisma.user.update({
      where: { id: userId },
      data: {
        permissions: permissionIds
      }
    })

    return NextResponse.json(
      { success: true, message: '权限更新成功' },
      { status: 200, headers }
    )
  } catch (error) {
    console.error('更新用户权限失败:', error)
    return NextResponse.json(
      { success: false, message: '更新用户权限失败', error },
      { status: 500, headers }
    )
  }
}