/**
 * 拒绝用户认证API
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { checkPermission } from "@/lib/casbin/enforcer"
import { SystemLogService } from "@/lib/services/system-log-service"
import { NotificationService } from "@/lib/services/notification-service"

// JWT密钥配置
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

/**
 * 拒绝用户认证
 *
 * @route POST /api/admin/users/[id]/reject
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查用户是否有拒绝用户认证的权限
    if (!adminId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(request, 'users', 'reject')
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限拒绝用户认证'
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json().catch(() => ({}))
    const reason = body.reason || '未提供拒绝原因'

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        status: true,
        verificationStatus: true,
        verificationType: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 更新用户状态
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        verificationStatus: 'rejected',
        verificationRejectReason: reason
      }
    })

    // 记录拒绝操作
    await SystemLogService.log({
      userId: adminId,
      action: "reject",
      module: "users",
      targetId: userId,
      details: {
        username: user.username,
        email: user.email,
        verificationType: user.verificationType,
        reason
      }
    })

    // 发送通知
    try {
      await NotificationService.sendNotification({
        userId: userId,
        title: '认证审核未通过',
        content: `您的${user.verificationType === 'personal' ? '个人' : '企业'}认证未通过审核，原因：${reason}`,
        type: 'verification',
        data: {
          action: 'reject',
          verificationType: user.verificationType,
          reason
        }
      })

      // 发送邮件通知
      await NotificationService.sendEmail({
        to: user.email,
        subject: '认证审核未通过通知',
        text: `尊敬的${user.name || user.username}，您的${user.verificationType === 'personal' ? '个人' : '企业'}认证未通过审核，原因：${reason}`,
        html: `
          <div style="font-family: Arial, sans-serif; padding: 20px; color: #333;">
            <h2 style="color: #F44336;">认证审核未通过通知</h2>
            <p>尊敬的 ${user.name || user.username}，</p>
            <p>很遗憾，您的${user.verificationType === 'personal' ? '个人' : '企业'}认证未通过审核。</p>
            <p><strong>拒绝原因：</strong> ${reason}</p>
            <p>您可以根据以上原因修改认证资料后重新提交。</p>
            <p>如有任何问题，请联系客服。</p>
            <p style="margin-top: 30px; font-size: 12px; color: #999;">
              此邮件由系统自动发送，请勿回复。
            </p>
          </div>
        `
      })
    } catch (error) {
      console.error('发送通知失败:', error)
      // 通知发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '拒绝用户认证成功',
      data: {
        id: updatedUser.id,
        verificationStatus: updatedUser.verificationStatus,
        reason
      }
    })
  } catch (error) {
    console.error("拒绝用户认证错误:", error)
    return NextResponse.json({
      success: false,
      message: '拒绝用户认证失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
