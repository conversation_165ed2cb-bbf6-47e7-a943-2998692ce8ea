/**
 * 权限导入API
 * 提供权限配置的导入功能
 */

import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { options } from "../../../auth/[...nextauth]/options"
import { PermissionExportService } from "@/lib/services/permission-export-service"
import { PermissionAuditService } from "@/lib/services/permission-audit-service"
import { writeFile } from "fs/promises"
import { join } from "path"
import { tmpdir } from "os"

/**
 * 导入权限配置
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限导入API:${requestId}] 导入权限配置`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[权限导入API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以导入权限配置",
        requestId
      }, { status: 403 })
    }

    // 获取表单数据
    const formData = await request.formData()
    const file = formData.get('file') as File
    const overwrite = formData.get('overwrite') === 'true'

    if (!file) {
      return NextResponse.json({
        success: false,
        message: "请上传文件",
        requestId
      }, { status: 400 })
    }

    // 检查文件类型
    const fileType = file.name.split('.').pop()?.toLowerCase()
    if (fileType !== 'csv' && fileType !== 'json') {
      return NextResponse.json({
        success: false,
        message: "只支持CSV和JSON格式的文件",
        requestId
      }, { status: 400 })
    }

    // 保存文件到临时目录
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const tempFilePath = join(tmpdir(), `permissions_import_${Date.now()}.${fileType}`)
    await writeFile(tempFilePath, buffer)

    // 导入权限配置
    const result = await PermissionExportService.importPermissionsFromFile(tempFilePath, {
      adminId: session.user.id,
      overwrite
    })

    return NextResponse.json({
      success: true,
      message: "导入权限配置成功",
      data: result,
      requestId
    })
  } catch (error) {
    console.error(`[权限导入API:${requestId}] 导入权限配置失败:`, error)
    return NextResponse.json({
      success: false,
      message: "导入权限配置失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
