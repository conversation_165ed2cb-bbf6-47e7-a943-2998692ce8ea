import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { hasResourcePermission } from "@/lib/abac/permission"
import { Prisma } from '@prisma/client'

const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

export async function POST(req: NextRequest) {
  try {
    // 获取token
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问',
      }, { status: 401 })
    }
    
    // 验证token
    const { payload } = await jose.jwtVerify(token, JWT_SECRET)
    const userId = payload.sub
    
    if (!userId) {
      throw new Error('无效的用户ID')
    }
    
    // 检查用户权限
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { role: true }
    })
    
    if (!user || user.role?.code !== 'admin') {
      return NextResponse.json({
        success: false,
        message: '无权限执行此操作',
      }, { status: 403 })
    }
    
    // 获取请求数据
    const data = await req.json()
    const { title, content, type = 'info', priority = 'medium' } = data
    
    if (!title || !content) {
      return NextResponse.json({
        success: false,
        message: '标题和内容不能为空',
      }, { status: 400 })
    }
    
    // 检查权限
    const hasCreatePermission = await hasResourcePermission(
      { sub: userId, role: { code: user.role?.code } },
      "notifications",
      "notifications:create"
    );
    
    if (!hasCreatePermission) {
      return NextResponse.json({
        success: false,
        message: '无权限执行此操作',
      }, { status: 403 })
    }
    
    // 创建通知
    const result = await prisma.$executeRaw`
      INSERT INTO notification (
        id,
        title,
        content,
        type,
        priority,
        "createdBy",
        "createdAt",
        "updatedAt"
      ) VALUES (
        ${Prisma.sql`gen_random_uuid()`},
        ${title},
        ${content},
        ${type},
        ${priority},
        ${userId},
        NOW(),
        NOW()
      ) RETURNING id, title, content, type, priority, "createdBy", "createdAt", "updatedAt"
    `
    
    // 触发通知创建事件
    return NextResponse.json({
      success: true,
      message: '创建通知成功',
      data: result
    })
    
  } catch (error) {
    console.error("创建通知错误:", error)
    return NextResponse.json({
      success: false,
      message: '创建通知失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 })
  }
} 