import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { options } from "../../auth/[...nextauth]/options"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 保存菜单设置并更新角色-菜单关联
 *
 * 此API端点用于保存菜单设置并更新角色-菜单关联，确保RBAC模型的正确实现
 */
export async function POST(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 保存菜单设置`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)

    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以保存菜单设置",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const { menuItems } = body

    if (!menuItems || !Array.isArray(menuItems)) {
      return NextResponse.json({
        success: false,
        message: "无效的菜单数据",
        requestId
      }, { status: 400 })
    }

    console.log(`[菜单API:${requestId}] 收到 ${menuItems.length} 个菜单项`)

    // 获取管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' }
    })

    if (!adminRole) {
      return NextResponse.json({
        success: false,
        message: "管理员角色不存在",
        requestId
      }, { status: 404 })
    }

    // 开始事务
    const result = await prisma.$transaction(async (tx) => {
      // 1. 更新菜单项
      for (const item of menuItems) {
        // 检查菜单是否存在
        const existingMenu = await tx.menu.findUnique({
          where: { id: item.id }
        })

        if (existingMenu) {
          // 更新现有菜单
          await tx.menu.update({
            where: { id: item.id },
            data: {
              name: item.title,
              path: item.path || item.href || "#",
              icon: item.icon,
              visible: item.isVisible !== undefined ? item.isVisible : true,
              order: item.order !== undefined ? item.order : 0
            }
          })

          // 处理子菜单
          if (item.children && Array.isArray(item.children)) {
            for (const child of item.children) {
              const existingChild = await tx.menu.findUnique({
                where: { id: child.id }
              })

              if (existingChild) {
                // 更新现有子菜单
                await tx.menu.update({
                  where: { id: child.id },
                  data: {
                    name: child.title,
                    path: child.path || child.href || "#",
                    icon: child.icon,
                    visible: child.isVisible !== undefined ? child.isVisible : true,
                    order: child.order !== undefined ? child.order : 0,
                    parentId: item.id
                  }
                })
              } else {
                // 创建新子菜单
                await tx.menu.create({
                  data: {
                    id: child.id,
                    code: child.code || \`menu_\${Math.random().toString(36).substring(2, 10)}\`,
                    name: child.title,
                    path: child.path || child.href || "#",
                    icon: child.icon,
                    visible: child.isVisible !== undefined ? child.isVisible : true,
                    order: child.order !== undefined ? child.order : 0,
                    parentId: item.id
                  }
                })
              }
            }
          }
        } else {
          // 创建新菜单
          await tx.menu.create({
            data: {
              id: item.id,
              code: item.code || \`menu_\${Math.random().toString(36).substring(2, 10)}\`,
              name: item.title,
              path: item.path || item.href || "#",
              icon: item.icon,
              visible: item.isVisible !== undefined ? item.isVisible : true,
              order: item.order !== undefined ? item.order : 0
            }
          })

          // 处理子菜单
          if (item.children && Array.isArray(item.children)) {
            for (const child of item.children) {
              await tx.menu.create({
                data: {
                  id: child.id,
                  code: child.code || \`menu_\${Math.random().toString(36).substring(2, 10)}\`,
                  name: child.title,
                  path: child.path || child.href || "#",
                  icon: child.icon,
                  visible: child.isVisible !== undefined ? child.isVisible : true,
                  order: child.order !== undefined ? child.order : 0,
                  parentId: item.id
                }
              })
            }
          }
        }
      }

      // 2. 更新管理员角色的菜单关联
      // 获取所有可见菜单
      const allMenus = await tx.menu.findMany({
        where: { visible: true }
      })

      // 清除现有关联
      await tx.$executeRaw\`DELETE FROM "_RoleMenus" WHERE "B" = \${adminRole.id}\`

      // 创建新关联
      for (const menu of allMenus) {
        await tx.$executeRaw\`INSERT INTO "_RoleMenus" ("A", "B") VALUES (\${menu.id}, \${adminRole.id})\`
      }

      // 在事务外处理 jCasbin 权限，因为 jCasbin 不支持事务
      // 在事务完成后执行

      return { success: true, menuCount: allMenus.length }
    })

    // 处理 jCasbin 权限
    const enforcer = await CasbinService.getEnforcer()

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany({
      where: { visible: true }
    })

    // 获取管理员角色的所有权限
    const permissions = await enforcer.getPermissionsForUser(adminRole.code)

    // 删除菜单相关的权限
    for (const permission of permissions) {
      if (permission[1] && permission[1].startsWith('menu:')) {
        await enforcer.removePolicy(adminRole.code, permission[1], permission[2])
      }
    }

    // 添加新的权限
    for (const menu of allMenus) {
      await enforcer.addPolicy(adminRole.code, `menu:${menu.code}`, 'view')
    }

    // 保存策略
    await enforcer.savePolicy()

    // 清除权限缓存
    CasbinService.clearCache()

    return NextResponse.json({
      success: true,
      message: \`菜单设置已保存，共 \${result.menuCount} 个菜单项\`,
      requestId
    })
  } catch (error) {
    console.error(\`[菜单API:\${requestId}] 保存菜单设置失败:\`, error)
    return NextResponse.json({
      success: false,
      message: "保存菜单设置失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
