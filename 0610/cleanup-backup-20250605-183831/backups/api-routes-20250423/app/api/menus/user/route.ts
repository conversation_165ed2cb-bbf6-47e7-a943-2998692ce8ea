import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { options } from "../../auth/[...nextauth]/options"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 获取当前用户有权限访问的菜单
 */
export async function GET(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单API:${requestId}] 获取用户菜单`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)

    // 如果没有会话，返回401
    if (!session?.user) {
      console.log(`[菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    const userId = session.user.id
    const roleCode = session.user.roleCode

    console.log(`[菜单API:${requestId}] 用户ID: ${userId}, 角色: ${roleCode}`)

    let allMenus = []

    // 如果是管理员角色，直接获取所有菜单
    if (roleCode === 'ADMIN') {
      console.log(`[菜单API:${requestId}] 管理员用户，获取所有菜单`)

      // 获取所有可见的菜单
      allMenus = await prisma.menu.findMany({
        where: {
          visible: true
        },
        orderBy: { order: 'asc' }
      })
    } else {
      // 其他角色获取角色关联的菜单
      const role = await prisma.role.findUnique({
        where: { code: roleCode },
        include: {
          menus: {
            where: { visible: true },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!role) {
        return NextResponse.json({
          success: false,
          message: "用户角色不存在",
          requestId
        }, { status: 404 })
      }

      // 获取菜单ID列表
      const menuIds = role.menus.map(menu => menu.id)

      // 获取所有菜单（包括子菜单）
      allMenus = await prisma.menu.findMany({
        where: {
          OR: [
            { id: { in: menuIds } },
            { parentId: { in: menuIds } }
          ],
          visible: true
        },
        orderBy: { order: 'asc' }
      })
    }

    // 使用jCasbin验证权限
    let authorizedMenus = []

    // 如果是管理员角色，直接授权所有菜单
    if (roleCode === 'ADMIN') {
      console.log(`[菜单API:${requestId}] 管理员用户，自动授权所有菜单`)
      authorizedMenus = allMenus
    } else {
      // 其他角色需要验证权限
      for (const menu of allMenus) {
        const hasPermission = await CasbinService.checkPermission(
          roleCode,
          `menu:${menu.code}`,
          'view'
        )

        if (hasPermission || menu.parentId) {
          authorizedMenus.push(menu)
        }
      }
    }

    // 构建菜单树
    const buildMenuTree = (items: any[], parentId: string | null = null) => {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          ...item,
          children: buildMenuTree(items, item.id)
        }))
    }

    // 构建树形结构
    const menuTree = buildMenuTree(authorizedMenus)

    return NextResponse.json({
      success: true,
      data: menuTree,
      requestId
    })
  } catch (error) {
    console.error(`[菜单API:${requestId}] 获取用户菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取用户菜单失败",
      requestId
    }, { status: 500 })
  }
}
