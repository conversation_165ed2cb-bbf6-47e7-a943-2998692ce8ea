/**
 * 客户充值记录API
 * 处理获取客户充值记录的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 获取客户充值记录
 *
 * @route GET /api/customers/[id]/recharge-records
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 解码并清理客户ID
    let customerId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    customerId = customerId.trim()
    console.log('充值记录API接收到的客户ID:', customerId)

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查管理员是否有权限
    if (!adminId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type') || undefined
    const startDate = searchParams.get('startDate') || undefined
    const endDate = searchParams.get('endDate') || undefined

    // 先尝试直接查询用户
    let user = null
    try {
      user = await prisma.user.findUnique({
        where: { id: customerId },
        select: { id: true }
      })
    } catch (err) {
      console.error('查询用户时出错:', err)
    }

    // 如果没找到用户，尝试使用username查询
    if (!user) {
      try {
        console.log('尝试使用username查询:', customerId)
        user = await prisma.user.findFirst({
          where: { username: customerId },
          select: { id: true }
        })

        if (user) {
          console.log('通过username找到用户:', user.id)
          customerId = user.id
        }
      } catch (err) {
        console.error('使用username查询用户时出错:', err)
      }
    }

    // 如果还是没找到用户，尝试使用邮箱查询
    if (!user && customerId.includes('@')) {
      try {
        console.log('尝试使用邮箱查询:', customerId)
        user = await prisma.user.findFirst({
          where: { email: customerId },
          select: { id: true }
        })

        if (user) {
          console.log('通过邮箱找到用户:', user.id)
          customerId = user.id
        }
      } catch (err) {
        console.error('使用邮箱查询用户时出错:', err)
      }
    }

    // 构建查询条件
    const where: any = {
      userId: customerId,
    }

    if (type && type !== 'all') {
      where.type = type
    }

    if (startDate && endDate) {
      where.createdAt = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      }
    } else if (startDate) {
      where.createdAt = {
        gte: new Date(startDate),
      }
    } else if (endDate) {
      where.createdAt = {
        lte: new Date(endDate),
      }
    }

    // 查询总记录数
    const total = await prisma.balanceTransaction.count({
      where,
    })

    // 查询交易记录
    const transactions = await prisma.balanceTransaction.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
      select: {
        id: true,
        amount: true,
        balanceAfter: true,
        creditLimitChange: true,
        creditLimitAfter: true,
        type: true,
        paymentMethod: true,
        remarks: true,
        createdAt: true,
        admin: {
          select: {
            id: true,
            name: true,
            username: true,
          },
        },
      },
    })

    // 返回交易记录
    return NextResponse.json({
      success: true,
      message: '获取充值记录成功',
      data: {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    })
  } catch (error) {
    console.error("获取客户充值记录错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取充值记录失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
