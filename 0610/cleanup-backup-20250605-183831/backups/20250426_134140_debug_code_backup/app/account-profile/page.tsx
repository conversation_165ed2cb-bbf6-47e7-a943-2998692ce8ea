"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"

// 允许的安全重定向路径白名单
const ALLOWED_REDIRECT_PATHS = ['/user', '/dashboard', '/profile']

// 安全的重定向目标
const SAFE_REDIRECT_TARGET = "/user?tab=profile"
const DEFAULT_REDIRECT = "/dashboard"
const LOGIN_PATH = "/login"

export default function AccountProfilePage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  // 使用 NextAuth 的 session
  const { data: session, status } = useSession()

  useEffect(() => {
    // 一次性随机令牌，用于防止重放攻击
    const redirectToken = Math.random().toString(36).substring(2, 15)
    
    const checkAuthAndRedirect = async () => {
      try {
        // 优先使用 NextAuth 的会话状态
        if (status === 'authenticated' && session) {
          // 已认证，执行安全重定向
          const targetPath = isValidRedirectPath(SAFE_REDIRECT_TARGET) 
            ? SAFE_REDIRECT_TARGET
            : DEFAULT_REDIRECT
            
          router.replace(`${targetPath}?rt=${redirectToken}`)
          return
        }
        
        if (status === 'unauthenticated') {
          // NextAuth 确认未认证，尝试传统 API
          tryTraditionalAuth()
          return
        }
        
        // NextAuth 状态仍在加载，等待
        if (status === 'loading') {
          return
        }
      } catch (error) {
        console.error("认证检查失败:", error)
        handleAuthError()
      }
    }
    
    const tryTraditionalAuth = async () => {
      try {
        // 检查传统 API 的认证状态
        const response = await fetch('/api/auth/me', {
          credentials: 'include',
          headers: {
            'Cache-Control': 'no-cache',
            'X-Redirect-Token': redirectToken
          }
        })
        const data = await response.json()

        if (data.success) {
          // 认证成功，安全重定向
          const targetPath = isValidRedirectPath(SAFE_REDIRECT_TARGET) 
            ? SAFE_REDIRECT_TARGET
            : DEFAULT_REDIRECT
            
          router.replace(`${targetPath}?rt=${redirectToken}`)
        } else {
          // 认证失败，安全重定向到登录页
          const encodedReturnUrl = encodeURIComponent(SAFE_REDIRECT_TARGET)
          router.replace(`${LOGIN_PATH}?returnUrl=${encodedReturnUrl}&rt=${redirectToken}`)
        }
      } catch (error) {
        console.error("传统认证检查失败:", error)
        handleAuthError()
      } finally {
        setIsLoading(false)
      }
    }
    
    const handleAuthError = () => {
      // 错误处理 - 安全地重定向到登录页
      const encodedReturnUrl = encodeURIComponent(SAFE_REDIRECT_TARGET)
      router.replace(`${LOGIN_PATH}?returnUrl=${encodedReturnUrl}&rt=${redirectToken}`)
      setIsLoading(false)
    }
    
    // 验证重定向路径是否在白名单中
    function isValidRedirectPath(path) {
      if (!path) return false
      return ALLOWED_REDIRECT_PATHS.some(allowedPath => 
        path === allowedPath || path.startsWith(`${allowedPath}/`) || path.startsWith(`${allowedPath}?`)
      )
    }
    
    // 检查认证并处理重定向
    checkAuthAndRedirect()
  }, [router, status, session])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">正在加载...</h1>
          <p>请稍候，正在验证您的身份</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">正在重定向...</h1>
        <p>正在将您重定向到个人资料页面</p>
      </div>
    </div>
  )
}