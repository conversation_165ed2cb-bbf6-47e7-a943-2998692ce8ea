"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"

export interface VerificationData {
  // 个人认证信息
  realName: string
  idCardNumber: string
  idCardFront: string
  idCardBack: string
  idCardHolding: string

  // 企业认证信息
  companyName: string
  legalPerson: string
  legalPersonIdCard: string
  socialCreditCode: string
  businessLicense: string
  otherDocuments: string[]
  remark: string
}

export interface VerificationChange {
  deadline: string
  reason: string
  originalType: string
  newType: string
}

export function useVerification() {
  // 认证资料相关状态
  const [verificationType, setVerificationType] = useState<"personal" | "enterprise" | "none">("personal")
  const [verificationStatus, setVerificationStatus] = useState<"none" | "pending" | "approved" | "rejected">("none")
  const [verificationChange, setVerificationChange] = useState<VerificationChange | null>(null)
  const [verificationData, setVerificationData] = useState<VerificationData>({
    // 个人认证信息
    realName: "",
    idCardNumber: "",
    idCardFront: "",
    idCardBack: "",
    idCardHolding: "",

    // 企业认证信息
    companyName: "",
    legalPerson: "",
    legalPersonIdCard: "",
    socialCreditCode: "",
    businessLicense: "",
    otherDocuments: [],
    remark: ""
  })

  const [uploadingFile, setUploadingFile] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [submittingVerification, setSubmittingVerification] = useState(false)

  // 获取认证资料
  const fetchVerificationData = async () => {
    try {
      const response = await fetch('/api/user/verification', {
        credentials: 'include' // 确保包含认证信息
      })
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // 设置认证状态和类型
          setVerificationStatus(data.data.status)

          // 如果有认证数据，设置认证类型和资料
          if (data.data.status !== 'none') {
            // 设置认证类型
            setVerificationType(data.data.type)

            // 设置认证资料
            setVerificationData({
              // 个人认证信息
              realName: data.data.data.realName || '',
              idCardNumber: data.data.data.idCardNumber || '',
              idCardFront: data.data.data.idCardFront || '',
              idCardBack: data.data.data.idCardBack || '',
              idCardHolding: data.data.data.idCardHolding || '',

              // 企业认证信息
              companyName: data.data.data.companyName || '',
              legalPerson: data.data.data.legalPerson || '',
              legalPersonIdCard: data.data.data.legalPersonIdCard || '',
              socialCreditCode: data.data.data.socialCreditCode || '',
              businessLicense: data.data.data.businessLicense || '',
              otherDocuments: data.data.data.otherDocuments || [],
              remark: data.data.data.remark || ''
            })
          } else {
            // 如果没有认证数据，默认选择个人认证
            setVerificationType('personal')
          }

          // 获取认证类型变更记录
          if (data.data.verificationChange) {
            setVerificationChange({
              deadline: data.data.verificationChange.deadline,
              reason: data.data.verificationChange.reason,
              originalType: data.data.verificationChange.originalType,
              newType: data.data.verificationChange.newType
            })
          } else {
            setVerificationChange(null)
          }

          return data.data
        }
      }
      return null
    } catch (error) {
      console.error('Error fetching verification data:', error)
      toast({
        variant: "destructive",
        title: "获取认证资料失败",
        description: "请稍后再试或联系管理员"
      })
      return null
    }
  }

  // 上传认证文件
  const uploadVerificationFile = async (file: File, type: string) => {
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: '文件类型不支持',
        description: '请上传JPG、JPEG或PNG格式的图片',
        variant: 'destructive',
      })
      return
    }

    // 验证文件大小
    if (file.size > 5 * 1024 * 1024) { // 5MB
      toast({
        title: '文件过大',
        description: '文件大小不能超过5MB',
        variant: 'destructive',
      })
      return
    }

    try {
      setUploadingFile(true)
      setUploadProgress(0)

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10
          return newProgress >= 90 ? 90 : newProgress
        })
      }, 300)

      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)

      const response = await fetch('/api/user/verification/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include' // 确保包含认证信息
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '文件上传失败')
      }

      const data = await response.json()
      if (data.success) {
        setUploadProgress(100)

        // 更新认证资料中的文件URL
        setVerificationData(prev => ({
          ...prev,
          [type]: data.data.url
        }))

        toast({
          title: '文件上传成功',
          description: '文件已成功上传',
          variant: 'success',
        })
      } else {
        throw new Error(data.message || '文件上传失败')
      }
    } catch (error: any) {
      console.error('文件上传错误:', error)
      toast({
        title: '文件上传失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setUploadingFile(false)
      setUploadProgress(0)
    }
  }

  // 上传多个文件
  const uploadMultipleFiles = async (files: FileList, type: string) => {
    try {
      const uploadedUrls: string[] = []

      for (let i = 0; i < files.length; i++) {
        setUploadingFile(true)
        setUploadProgress(0)

        // 验证文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
        if (!allowedTypes.includes(files[i].type)) {
          toast({
            title: '文件类型不支持',
            description: `${files[i].name}: 请上传JPG、JPEG、PNG或PDF格式的文件`,
            variant: 'destructive',
          })
          continue
        }

        // 验证文件大小
        if (files[i].size > 10 * 1024 * 1024) { // 10MB
          toast({
            title: '文件过大',
            description: `${files[i].name}: 文件大小不能超过10MB`,
            variant: 'destructive',
          })
          continue
        }

        // 模拟上传进度
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            const newProgress = prev + Math.random() * 10
            return newProgress >= 90 ? 90 : newProgress
          })
        }, 300)

        const formData = new FormData()
        formData.append('file', files[i])
        formData.append('type', type)

        const response = await fetch('/api/user/verification/upload', {
          method: 'POST',
          body: formData,
          credentials: 'include' // 确保包含认证信息
        })

        clearInterval(progressInterval)

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || '文件上传失败')
        }

        const data = await response.json()
        if (data.success) {
          setUploadProgress(100)
          uploadedUrls.push(data.data.url)
        } else {
          throw new Error(data.message || '文件上传失败')
        }
      }

      // 更新认证资料中的文件URL
      if (uploadedUrls.length > 0) {
        setVerificationData(prev => ({
          ...prev,
          otherDocuments: [...prev.otherDocuments, ...uploadedUrls]
        }))

        toast({
          title: '文件上传成功',
          description: `已成功上传${uploadedUrls.length}个文件`,
          variant: 'success',
        })
      }
    } catch (error: any) {
      console.error('文件上传错误:', error)
      toast({
        title: '文件上传失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      })
    } finally {
      setUploadingFile(false)
      setUploadProgress(0)
    }
  }

  // 提交认证资料
  const submitVerification = async () => {
    try {
      // 验证表单
      if (verificationType === "personal") {
        if (!verificationData.realName || !verificationData.idCardNumber) {
          toast({
            title: '信息不完整',
            description: '请填写真实姓名和身份证号码',
            variant: 'destructive',
          })
          return false
        }

        if (!verificationData.idCardFront || !verificationData.idCardBack || !verificationData.idCardHolding) {
          toast({
            title: '信息不完整',
            description: '请上传身份证正面、反面和手持身份证照片',
            variant: 'destructive',
          })
          return false
        }
      } else if (verificationType === "enterprise") {
        if (!verificationData.companyName || !verificationData.legalPerson ||
            !verificationData.legalPersonIdCard || !verificationData.socialCreditCode) {
          toast({
            title: '信息不完整',
            description: '请填写企业名称、法人姓名、法人身份证号和统一社会信用代码',
            variant: 'destructive',
          })
          return false
        }

        if (!verificationData.businessLicense) {
          toast({
            title: '信息不完整',
            description: '请上传营业执照照片',
            variant: 'destructive',
          })
          return false
        }
      }

      setSubmittingVerification(true)

      const response = await fetch('/api/user/verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // 确保包含认证信息
        body: JSON.stringify({
          type: verificationType,
          ...verificationData
        }),
      })

      const data = await response.json()
      if (data.success) {
        // 更新认证状态
        setVerificationStatus('pending')

        toast({
          title: '认证资料已提交',
          description: '您的认证资料已成功提交，请等待审核',
          variant: 'success',
        })
        return true
      } else {
        throw new Error(data.message || '提交认证资料失败')
      }
    } catch (error: any) {
      console.error('提交认证资料错误:', error)
      toast({
        title: '提交认证资料失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      })
      return false
    } finally {
      setSubmittingVerification(false)
    }
  }

  return {
    verificationType,
    setVerificationType,
    verificationStatus,
    setVerificationStatus,
    verificationChange,
    setVerificationChange,
    verificationData,
    setVerificationData,
    uploadingFile,
    uploadProgress,
    submittingVerification,
    fetchVerificationData,
    uploadVerificationFile,
    uploadMultipleFiles,
    submitVerification
  }
}
