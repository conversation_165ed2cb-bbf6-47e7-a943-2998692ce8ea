"use client"

import { useState } from "react"
import { toast } from "@/components/ui/use-toast"
import { DateRange } from "react-day-picker"

export interface BalanceTransaction {
  id: string
  amount: number
  balanceAfter: number
  creditLimitChange?: number
  creditLimitAfter?: number
  type: string
  paymentMethod: string
  remarks: string
  createdAt: string
  admin?: {
    id: string
    name: string
    username: string
  }
}

export interface BalanceTransactionsData {
  transactions: BalanceTransaction[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export function useBalanceTransactions() {
  const [balanceTransactions, setBalanceTransactions] = useState<BalanceTransactionsData>({
    transactions: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  })

  const [loadingTransactions, setLoadingTransactions] = useState(false)
  const [transactionTypeFilter, setTransactionTypeFilter] = useState<string>("all")
  const [transactionDateRange, setTransactionDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  })

  // 获取余额交易记录
  const fetchBalanceTransactions = async (page = 1, type = 'all', dateRange?: DateRange) => {
    try {
      setLoadingTransactions(true)

      // 构建查询参数
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', '10')

      if (type && type !== 'all') {
        params.append('type', type)
      }

      if (dateRange?.from) {
        params.append('startDate', dateRange.from.toISOString())
      }

      if (dateRange?.to) {
        params.append('endDate', dateRange.to.toISOString())
      }

      console.log('Fetching balance transactions with params:', params.toString())

      const response = await fetch(`/api/user/balance/transactions?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        cache: 'no-store'
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch balance transactions: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setBalanceTransactions(data.data)
        return data.data
      } else {
        throw new Error(data.message || '获取交易记录失败')
      }
    } catch (error) {
      console.error('Error fetching balance transactions:', error)
      toast({
        variant: "destructive",
        title: "获取交易记录失败",
        description: error instanceof Error ? error.message : "请稍后再试或联系管理员"
      })

      // 设置空数据
      setBalanceTransactions({
        transactions: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0
        }
      })
      return null
    } finally {
      setLoadingTransactions(false)
    }
  }

  // 处理交易记录筛选变化
  const handleTransactionFilterChange = () => {
    fetchBalanceTransactions(
      1,
      transactionTypeFilter,
      transactionDateRange
    )
  }

  // 处理交易记录分页变化
  const handleTransactionPageChange = (page: number) => {
    fetchBalanceTransactions(
      page,
      transactionTypeFilter,
      transactionDateRange
    )
  }

  // 导出余额记录
  const exportBalanceTransactions = async (format: 'csv' | 'excel') => {
    try {
      toast({
        title: `正在导出${format === 'csv' ? 'CSV' : 'Excel'}文件`,
        description: "请稍候...",
      })

      // 获取所有交易记录用于导出
      const allTransactionsData = await fetchBalanceTransactions(
        1,
        transactionTypeFilter,
        transactionDateRange
      )

      if (!allTransactionsData || !allTransactionsData.transactions || allTransactionsData.transactions.length === 0) {
        throw new Error('没有可导出的交易记录')
      }

      // 准备CSV数据
      const headers = ["交易时间", "交易类型", "交易金额", "交易后余额", "授信额度变动", "支付方式", "操作人", "备注"]

      const rows = allTransactionsData.transactions.map((transaction: BalanceTransaction) => {
        // 格式化交易类型
        const typeText =
          transaction.type === 'recharge' ? '充值' :
          transaction.type === 'deduct' ? '扣费' :
          transaction.type === 'refund' ? '退款' :
          transaction.type === 'credit_add' ? '授信额度增加' :
          transaction.type === 'credit_subtract' ? '授信额度减少' :
          transaction.type === 'credit_set' ? '授信额度调整' : '其他'

        // 格式化支付方式
        const paymentMethodText =
          transaction.paymentMethod === 'bank' ? '银行转账' :
          transaction.paymentMethod === 'alipay' ? '支付宝' :
          transaction.paymentMethod === 'wechat' ? '微信支付' : '其他'

        return [
          new Date(transaction.createdAt).toLocaleString('zh-CN'),
          typeText,
          `${transaction.amount >= 0 ? '+' : ''}¥${transaction.amount.toFixed(2)}`,
          `¥${transaction.balanceAfter.toFixed(2)}`,
          transaction.creditLimitChange ?
            `${transaction.creditLimitChange > 0 ? '+' : ''}¥${transaction.creditLimitChange.toFixed(2)}` :
            '-',
          paymentMethodText,
          transaction.admin ? transaction.admin.name || transaction.admin.username : '系统',
          `"${(transaction.remarks || '-').replace(/"/g, '""')}"`
        ]
      })

      // 创建CSV或Excel内容
      let fileContent = ''
      let mimeType = ''

      if (format === 'csv') {
        // CSV格式
        fileContent = [
          headers.join(','),
          ...rows.map((row: string[]) => row.join(','))
        ].join('\n')
        mimeType = 'text/csv;charset=utf-8;'
      } else {
        // Excel格式
        // 创建HTML表格来生成Excel兼容的内容
        fileContent = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">'
        fileContent += '<head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>余额记录</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head>'
        fileContent += '<body>'
        fileContent += '<table>'

        // 添加表头
        fileContent += '<tr>'
        headers.forEach(header => {
          fileContent += `<th>${header}</th>`
        })
        fileContent += '</tr>'

        // 添加数据行
        rows.forEach((row: string[]) => {
          fileContent += '<tr>'
          row.forEach((cell: string) => {
            fileContent += `<td>${cell}</td>`
          })
          fileContent += '</tr>'
        })

        fileContent += '</table></body></html>'
        mimeType = 'application/vnd.ms-excel;charset=utf-8'
      }

      // 创建Blob对象
      const blob = new Blob([fileContent], { type: mimeType })
      const url = URL.createObjectURL(blob)

      // 创建下载链接并触发下载
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `余额记录_${new Date().toISOString().slice(0, 10)}.${format === 'excel' ? 'xls' : format}`)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 释放URL对象
      URL.revokeObjectURL(url)

      toast({
        title: `${format === 'csv' ? 'CSV' : 'Excel'}文件导出成功`,
        description: "文件已保存到您的下载文件夹",
        variant: "success",
      })

    } catch (error) {
      console.error(`导出余额记录错误:`, error)
      toast({
        title: "导出失败",
        description: error instanceof Error ? error.message : "请稍后重试",
        variant: "destructive",
      })
    }
  }

  return {
    balanceTransactions,
    loadingTransactions,
    transactionTypeFilter,
    setTransactionTypeFilter,
    transactionDateRange,
    setTransactionDateRange,
    fetchBalanceTransactions,
    handleTransactionFilterChange,
    handleTransactionPageChange,
    exportBalanceTransactions
  }
}
