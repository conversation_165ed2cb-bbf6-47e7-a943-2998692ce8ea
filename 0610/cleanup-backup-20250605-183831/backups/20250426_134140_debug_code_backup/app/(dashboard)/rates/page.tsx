"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { DashboardShell } from "@/components/dashboard-shell"
import { But<PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { Pencil, Trash2, Plus } from "lucide-react"
import { format } from "date-fns"

// 业务类型选项
const businessTypes = [
  { value: "VIDEO_NOTIFICATION", label: "视频通知" },
  { value: "VIDEO_INTERACTION", label: "视频互动" },
  { value: "VOICE_INTERACTION", label: "语音互动" },
]

// 计费周期类型选项
const billingIncrementTypes = [
  { value: "PER_MINUTE", label: "按分钟计费 (60秒)" },
  { value: "PER_SIX_SECOND", label: "按6秒计费" },
]

// 费率类型接口
interface Rate {
  id: string
  amount: number
  period: number | null
  billingIncrement: string
  customerId: string | null
  businessType: string
  createdAt: string
  updatedAt: string
  customer?: {
    id: string
    name: string | null
    username: string
    email: string
  } | null
}

// 客户接口
interface Customer {
  id: string
  name: string | null
  username: string
  email: string
}

export default function RatesPage() {
  const router = useRouter()
  const [rates, setRates] = useState<Rate[]>([])
  const [customers, setCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [addDialogOpen, setAddDialogOpen] = useState(false)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [selectedRate, setSelectedRate] = useState<Rate | null>(null)

  // 新费率表单状态
  const [formData, setFormData] = useState({
    amount: "",
    period: "",
    billingIncrement: "PER_MINUTE",
    customerId: "all",
    businessType: "VIDEO_NOTIFICATION",
  })

  // 获取费率列表
  const fetchRates = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/rates", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setRates(data.data)
      } else {
        toast({
          title: "获取费率失败",
          description: data.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("获取费率错误:", error)
      toast({
        title: "获取费率失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 获取客户列表
  const fetchCustomers = async () => {
    try {
      // 使用正确的API端点获取用户列表
      const response = await fetch("/api/users?for=notification", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      })

      const data = await response.json()

      if (response.ok && data.success) {
        console.log("获取到的客户数据:", data);
        // 确保data.data是一个数组
        if (data.data && data.data.users && Array.isArray(data.data.users)) {
          setCustomers(data.data.users);
          console.log("设置客户数组:", data.data.users);
        } else {
          setCustomers([]);
          console.error("客户数据不是数组格式:", data.data);
        }
      } else {
        console.error("获取客户列表失败:", data.message);
        setCustomers([]);
      }
    } catch (error) {
      console.error("获取客户列表错误:", error)
    }
  }

  // 初始化加载
  useEffect(() => {
    fetchRates()
    fetchCustomers()
  }, [])

  // 处理表单输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    })
  }

  // 添加费率
  const handleAddRate = async () => {
    try {
      if (!formData.amount) {
        toast({
          title: "费率不能为空",
          variant: "destructive",
        })
        return
      }

      const response = await fetch("/api/rates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: formData.amount,
          period: formData.period || null,
          billingIncrement: formData.billingIncrement,
          customerId: formData.customerId === 'all' ? null : formData.customerId,
          businessType: formData.businessType,
        }),
        credentials: "include",
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "添加成功",
          description: "费率已成功添加",
          variant: "success",
          className: "bg-green-500 text-white border-green-600",
        })
        setAddDialogOpen(false)
        // 重置表单
        setFormData({
          amount: "",
          period: "",
          billingIncrement: "PER_MINUTE",
          customerId: "all",
          businessType: "VIDEO_NOTIFICATION",
        })
        // 刷新列表
        fetchRates()
      } else {
        toast({
          title: "添加失败",
          description: data.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("添加费率错误:", error)
      toast({
        title: "添加失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 编辑费率
  const handleEditRate = async () => {
    if (!selectedRate) return

    try {
      if (!formData.amount) {
        toast({
          title: "费率不能为空",
          variant: "destructive",
        })
        return
      }

      const response = await fetch(`/api/rates/${selectedRate.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount: formData.amount,
          period: formData.period || null,
          billingIncrement: formData.billingIncrement,
          customerId: formData.customerId === 'all' ? null : formData.customerId,
          businessType: formData.businessType,
        }),
        credentials: "include",
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "更新成功",
          description: "费率已成功更新",
          variant: "success",
          className: "bg-green-500 text-white border-green-600",
        })
        setEditDialogOpen(false)
        // 刷新列表
        fetchRates()
      } else {
        toast({
          title: "更新失败",
          description: data.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("更新费率错误:", error)
      toast({
        title: "更新失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 删除费率
  const handleDeleteRate = async () => {
    if (!selectedRate) return

    try {
      const response = await fetch(`/api/rates/${selectedRate.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "删除成功",
          description: "费率已成功删除",
          variant: "success",
          className: "bg-green-500 text-white border-green-600",
        })
        setDeleteDialogOpen(false)
        // 刷新列表
        fetchRates()
      } else {
        toast({
          title: "删除失败",
          description: data.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("删除费率错误:", error)
      toast({
        title: "删除失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    }
  }

  // 打开编辑对话框
  const openEditDialog = (rate: Rate) => {
    setSelectedRate(rate)
    setFormData({
      amount: rate.amount.toString(),
      period: rate.period ? rate.period.toString() : "",
      billingIncrement: rate.billingIncrement || "PER_MINUTE",
      customerId: rate.customerId || "all",
      businessType: rate.businessType,
    })
    setEditDialogOpen(true)
  }

  // 打开删除对话框
  const openDeleteDialog = (rate: Rate) => {
    setSelectedRate(rate)
    setDeleteDialogOpen(true)
  }

  // 获取业务类型显示名称
  const getBusinessTypeName = (type: string) => {
    const businessType = businessTypes.find((bt) => bt.value === type)
    return businessType ? businessType.label : type
  }

  return (
    <DashboardShell>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">费率管理</h1>
        <Button onClick={() => setAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          新增费率
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>费率(元)</TableHead>
              <TableHead>周期(秒)</TableHead>
              <TableHead>计费周期类型</TableHead>
              <TableHead>客户</TableHead>
              <TableHead>业务类型</TableHead>
              <TableHead>创建时间</TableHead>
              <TableHead>更新时间</TableHead>
              <TableHead className="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  加载中...
                </TableCell>
              </TableRow>
            ) : rates.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  暂无数据
                </TableCell>
              </TableRow>
            ) : (
              rates.map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell>{rate.amount}</TableCell>
                  <TableCell>{rate.period || "-"}</TableCell>
                  <TableCell>
                    {rate.billingIncrement === "PER_MINUTE" ? "按分钟计费 (60秒)" : "按6秒计费"}
                  </TableCell>
                  <TableCell>
                    {rate.customer ? rate.customer.name || rate.customer.username : "所有客户"}
                  </TableCell>
                  <TableCell>{getBusinessTypeName(rate.businessType)}</TableCell>
                  <TableCell>
                    {format(new Date(rate.createdAt), "yyyy-MM-dd HH:mm:ss")}
                  </TableCell>
                  <TableCell>
                    {format(new Date(rate.updatedAt), "yyyy-MM-dd HH:mm:ss")}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditDialog(rate)}
                    >
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">编辑</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openDeleteDialog(rate)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span className="sr-only">删除</span>
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* 新增费率对话框 */}
      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>新增费率</DialogTitle>
            <DialogDescription>
              添加新的费率配置，设置费率金额、周期和适用客户。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                费率(元) <span className="text-red-500">*</span>
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                className="col-span-3"
                placeholder="请输入费率金额"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="period" className="text-right">
                周期(秒)
              </Label>
              <Input
                id="period"
                type="number"
                min="0"
                value={formData.period}
                onChange={(e) => handleInputChange("period", e.target.value)}
                className="col-span-3"
                placeholder="请输入周期（可选）"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="billingIncrement" className="text-right">
                计费周期类型 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.billingIncrement}
                onValueChange={(value) => handleInputChange("billingIncrement", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择计费周期类型" />
                </SelectTrigger>
                <SelectContent>
                  {billingIncrementTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="customer" className="text-right">
                客户
              </Label>
              <Select
                value={formData.customerId}
                onValueChange={(value) => handleInputChange("customerId", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择客户（不选则适用所有客户）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有客户</SelectItem>
                  {Array.isArray(customers) && customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name || customer.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="businessType" className="text-right">
                业务类型 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.businessType}
                onValueChange={(value) => handleInputChange("businessType", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择业务类型" />
                </SelectTrigger>
                <SelectContent>
                  {businessTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setAddDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleAddRate}>确定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 编辑费率对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>编辑费率</DialogTitle>
            <DialogDescription>
              修改费率配置，更新费率金额、周期和适用客户。
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-amount" className="text-right">
                费率(元) <span className="text-red-500">*</span>
              </Label>
              <Input
                id="edit-amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                className="col-span-3"
                placeholder="请输入费率金额"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-period" className="text-right">
                周期(秒)
              </Label>
              <Input
                id="edit-period"
                type="number"
                min="0"
                value={formData.period}
                onChange={(e) => handleInputChange("period", e.target.value)}
                className="col-span-3"
                placeholder="请输入周期（可选）"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-billingIncrement" className="text-right">
                计费周期类型 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.billingIncrement}
                onValueChange={(value) => handleInputChange("billingIncrement", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择计费周期类型" />
                </SelectTrigger>
                <SelectContent>
                  {billingIncrementTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-customer" className="text-right">
                客户
              </Label>
              <Select
                value={formData.customerId}
                onValueChange={(value) => handleInputChange("customerId", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择客户（不选则适用所有客户）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有客户</SelectItem>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name || customer.username}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-businessType" className="text-right">
                业务类型 <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.businessType}
                onValueChange={(value) => handleInputChange("businessType", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择业务类型" />
                </SelectTrigger>
                <SelectContent>
                  {businessTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleEditRate}>确定</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 删除费率对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>删除费率</DialogTitle>
            <DialogDescription>
              确定要删除这个费率配置吗？此操作不可撤销。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteRate}
            >
              确认删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardShell>
  )
}
