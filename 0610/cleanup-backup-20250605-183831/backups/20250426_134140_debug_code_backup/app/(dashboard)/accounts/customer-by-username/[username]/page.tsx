import { redirect } from "next/navigation"
import { prisma } from "@/lib/prisma"

export default async function CustomerByUsernamePage({ params }: { params: { username: string } }) {
  // 解码并清理用户名
  const username = decodeURIComponent(params.username).trim()
  console.log('正在通过用户名查询客户:', username)

  try {
    // 直接从数据库查询用户ID
    const user = await prisma.user.findFirst({
      where: { username },
      select: { id: true }
    })

    if (!user) {
      console.log('未找到用户名为', username, '的用户')
      // 如果找不到用户，重定向到错误页面
      redirect('/accounts/customer?error=user-not-found')
    }

    // 找到用户，重定向到用户详情页
    console.log('找到用户ID:', user.id)
    redirect(`/accounts/customer/${user.id}`)
  } catch (error) {
    console.error('查询用户时出错:', error)
    // 发生错误，重定向到错误页面
    redirect('/accounts/customer?error=query-error')
  }
}
