"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"

interface ResetPasswordSuccessProps {
  newPassword: string
  onClose: () => void
}

export function ResetPasswordSuccess({ newPassword, onClose }: ResetPasswordSuccessProps) {
  const [processing, setProcessing] = useState(false)

  // 手动触发邮件处理器
  const handleProcessEmails = async () => {
    setProcessing(true)
    try {
      const response = await fetch('/api/email-processor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "邮件处理成功",
          description: `已处理 ${data.processed} 封邮件，成功: ${data.success}，失败: ${data.failed}`,
          variant: "success"
        })
      } else {
        toast({
          title: "邮件处理失败",
          description: data.message || "处理邮件时发生错误",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('处理邮件错误:', error)
      toast({
        title: "邮件处理失败",
        description: error instanceof Error ? error.message : "处理邮件时发生错误",
        variant: "destructive"
      })
    } finally {
      setProcessing(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="text-green-600">密码重置成功</CardTitle>
        <CardDescription>新密码已生成并添加到邮件队列</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-500 mb-1">临时密码:</p>
            <p className="font-mono text-lg font-bold">{newPassword}</p>
          </div>
          <p className="text-sm text-gray-600">
            此密码已通过邮件发送给用户。如果用户没有收到邮件，可以点击下方按钮手动触发邮件处理。
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onClose}>
          关闭
        </Button>
        <Button 
          variant="secondary" 
          onClick={handleProcessEmails}
          disabled={processing}
        >
          {processing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              处理中...
            </>
          ) : (
            "手动处理邮件"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
