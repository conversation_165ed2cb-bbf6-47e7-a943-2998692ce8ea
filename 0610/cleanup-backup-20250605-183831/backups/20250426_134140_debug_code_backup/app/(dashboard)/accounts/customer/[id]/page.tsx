import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Building, User, Mail, Phone, Wallet, Percent, CalendarIcon, CreditCard, Clock } from "lucide-react"
import ClientRechargeRecords from "./client-recharge-records"
import { CustomerDetailContent } from "./customer-detail-content"

import { prisma } from "@/lib/prisma"
import { CustomerNotFound } from "@/app/components/customer-not-found"

export default async function CustomerDetailPage({ params }: { params: { id: string } }) {
  // 解码并清理客户ID
  let customerId = params.id
  console.log('原始ID:', params.id)

  try {
    customerId = decodeURIComponent(params.id).trim()
    console.log('解码后的ID:', customerId)

    // 如果是ID包含特殊字符，尝试再次清理
    if (customerId.includes('%') || customerId.includes('+')) {
      try {
        customerId = decodeURIComponent(customerId)
        console.log('再次解码后的ID:', customerId)
      } catch (e) {
        console.error('再次解码ID时出错:', e)
      }
    }
  } catch (e) {
    console.error('解码ID时出错:', e)
    // 如果解码失败，使用原始ID
    customerId = params.id.trim()
  }

  // 判断是否是ID格式 - 增加更多可能的ID前缀
  // 如果不是ID格式，则很可能是用户名
  // 增加对常见 cuid 和 uuid 格式的支持
  const isUUID = customerId.startsWith('cm9') || customerId.startsWith('cl') || customerId.startsWith('ck') ||
                customerId.length > 20 ||
                /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(customerId) || // UUID格式
                /^c[a-z0-9]{20,25}$/i.test(customerId) // CUID格式
  console.log('是否是ID格式:', isUUID, '当前值:', customerId)

  // 如果不是ID格式，尝试直接从数据库查询用户ID
  if (!isUUID) {
    try {
      console.log('尝试从数据库查询用户:', customerId)

      // 先尝试使用用户名查询
      console.log('尝试使用用户名查询:', customerId)

      // 查询所有用户，并打印出来以便调试
      const allUsers = await prisma.user.findMany({
        select: { id: true, username: true },
        take: 10
      })
      console.log('数据库中的用户示例:', allUsers.map(u => u.username))

      // 打印所有用户名以便调试
      const allUsernames = await prisma.user.findMany({
        select: { id: true, username: true },
        take: 20
      })
      console.log('数据库中的用户名:', allUsernames.map(u => ({ id: u.id, username: u.username })))

      // 尝试精确匹配用户名
      let user = await prisma.user.findFirst({
        where: { username: customerId },
        select: { id: true, username: true }
      })

      // 如果精确匹配失败，尝试不区分大小写匹配
      if (!user) {
        user = await prisma.user.findFirst({
          where: { username: { equals: customerId, mode: 'insensitive' } },
          select: { id: true, username: true }
        })
      }

      // 如果还是失败，再尝试模糊匹配
      if (!user) {
        user = await prisma.user.findFirst({
          where: {
            OR: [
              { username: { contains: customerId, mode: 'insensitive' } },
              { username: { startsWith: customerId, mode: 'insensitive' } },
              { username: { endsWith: customerId, mode: 'insensitive' } }
            ]
          },
          select: { id: true, username: true }
        })
      }

      if (user) {
        console.log('找到用户:', user.id, user.username)
      } else {
        console.log('未找到用户名为', customerId, '的用户')
      }

      // 如果找到用户，使用用户ID
      if (user) {
        console.log('通过用户名找到用户:', user.id)
        customerId = user.id
      } else if (customerId.includes('@')) {
        // 如果是邮箱格式，尝试使用邮箱查询
        const emailUser = await prisma.user.findFirst({
          where: { email: customerId },
          select: { id: true }
        })

        if (emailUser) {
          console.log('通过邮箱找到用户:', emailUser.id)
          customerId = emailUser.id
        }
      }
    } catch (error) {
      console.error('查询用户时出错:', error)
    }
  }

  try {
    // 检查customerId是否为空
    if (!customerId || customerId.trim() === '') {
      throw new Error('用户ID不能为空')
    }

    // 使用最终确定的ID查询用户详情
    // 添加查询参数，指示这是用户名而不是ID
    // 确保正确编码URL参数
    const encodedId = encodeURIComponent(customerId)
    // 始终添加byUsername参数，让API尝试多种查询方式
    const apiUrl = `${process.env.NEXT_PUBLIC_API_URL || ''}/api/customers/${encodedId}?byUsername=true`

    console.log('最终使用的客户ID或用户名:', customerId, '是否是ID格式:', isUUID)
    console.log('使用的API URL:', apiUrl)

    // 在服务器组件中不能使用window对象
    // 默认使用basic标签
    let tab = 'basic'

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      cache: 'no-store'
    })

    if (!response.ok) {
      console.error('API响应错误:', response.status, response.statusText)

      // 尝试解析错误响应体
      let errorDetail = '';
      try {
        const errorData = await response.json();
        console.error('API错误详情:', errorData);
        if (errorData.message) {
          errorDetail = errorData.message;
        }
        if (errorData.debug) {
          console.error('API调试信息:', errorData.debug);
        }
      } catch (e) {
        console.error('解析错误响应时出错:', e);
      }

      throw new Error(`获取客户数据失败: ${response.status} ${response.statusText} ${errorDetail ? `- ${errorDetail}` : ''}`)
    }

    const data = await response.json()
    console.log('API响应数据:', data)

    // 如果有调试信息，显示出来
    if (data.debug) {
      console.log('调试信息:', data.debug)
    }

    if (!data.success || !data.data) {
      throw new Error(data.message || '客户数据不存在')
    }

    const customer = data.data
    return <CustomerDetailContent customer={customer} defaultTab={tab || 'basic'} />

  } catch (error) {
    // 处理错误情况
    console.error('获取客户详情错误:', error)

    // 尝试直接从数据库查询作为备用方案
    try {
      console.log('尝试直接从数据库查询用户:', customerId)

      // 先尝试使用ID查询
      let user = await prisma.user.findUnique({
        where: { id: customerId },
        include: {
          role: true,
          createdBy: {
            select: {
              id: true,
              name: true,
              username: true
            }
          },
          verification: true
        }
      })

      // 如果没找到，尝试使用用户名查询
      if (!user) {
        user = await prisma.user.findFirst({
          where: { username: customerId },
          include: {
            role: true,
            createdBy: {
              select: {
                id: true,
                name: true,
                username: true
              }
            },
            verification: true
          }
        })
      }

      // 如果找到用户，格式化并返回
      if (user) {
        console.log('直接从数据库找到用户:', user.id)

        // 格式化用户数据
        const customer = {
          id: user.id,
          name: user.name || user.username,
          loginName: user.username,
          email: user.email,
          phone: user.phone || '',
          accountType: user.role?.type || 'personal',
          status: user.status,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
          industry: user.industry || '',
          province: user.province || '',
          city: user.city || '',
          district: user.district || '',
          address: user.address || '',
          description: user.description || '',
          balance: user.balance || 0,
          creditLimit: user.creditLimit || 0,
          feeRate: '0%',
          personalVerification: user.verificationType === 'personal' ? user.verificationStatus : undefined,
          enterpriseVerification: user.verificationType === 'enterprise' ? user.verificationStatus : undefined,
        }

        return <CustomerDetailContent customer={customer} defaultTab={'basic'} />
      }
    } catch (dbError) {
      console.error('直接从数据库查询时出错:', dbError)
    }

    // 如果所有方法都失败，使用错误处理组件
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    return <CustomerNotFound customerId={customerId} errorMessage={errorMessage} />
  }
}
