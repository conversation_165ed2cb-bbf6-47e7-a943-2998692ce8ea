"use client"

import type React from "react"

// 定义日期范围类型
interface DateRange {
  from: Date | undefined
  to: Date | undefined
}

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CalendarIcon, Play, Pause, Info, Trash2, Upload, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "@/components/ui/use-toast"
import { useSession } from "next-auth/react"
import type { Task } from "@/lib/api/interfaces"
import { RichTextEditor } from "@/components/shared/rich-text-editor"
import { UserAvatar } from "@/components/shared/user-avatar"
import { NotificationButton } from "@/components/shared/notification-button"

// 如果需要扩展 Task 接口，可以这样做：
interface ExtendedTask extends Task {
  // 前端状态字段，用于UI显示
  uiStatus: string
  progress: number
}

// 引入路由和会话相关的导入
import { useRouter } from "next/navigation"

// 引入视频通话服务相关API
import { videoCallService, startTask, pauseTask, getBotList } from "@/lib/api/video-call-service"

export default function TasksPage() {
  // 用户角色状态
  const { data: session } = useSession()
  const user = session?.user
  const userType = user?.role?.code === "super" ? "admin" : "customer"

  // 加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 搜索表单状态
  const [taskName, setTaskName] = useState("")
  const [content, setContent] = useState("")
  const [type, setType] = useState("")
  const [startDateRange, setStartDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  })
  const [createDateRange, setCreateDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  })

  // 对话框状态
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [taskToDelete, setTaskToDelete] = useState<string | null>(null)
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)

  // 新建任务表单状态
  const [newTaskName, setNewTaskName] = useState("")
  const [newTaskContent, setNewTaskContent] = useState("")
  const [newTaskType, setNewTaskType] = useState("")
  const [newTaskStartTime, setNewTaskStartTime] = useState<Date | undefined>(undefined)
  const [newTaskResource, setNewTaskResource] = useState("")
  const [newTaskPhoneNumber, setNewTaskPhoneNumber] = useState("")
  const [newTaskSmsType, setNewTaskSmsType] = useState("")
  const [newTaskSmsTemplate, setNewTaskSmsTemplate] = useState("")
  const [showDateTimePicker, setShowDateTimePicker] = useState(false)
  const [selectedHour, setSelectedHour] = useState("00")
  const [selectedMinute, setSelectedMinute] = useState("00")
  const [selectedSecond, setSelectedSecond] = useState("00")
  const [activeTab, setActiveTab] = useState("manual")

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(5)
  const [tasks, setTasks] = useState<ExtendedTask[]>([])
  const [totalTasks, setTotalTasks] = useState(0)
  const [totalPages, setTotalPages] = useState(1)

  // Add a function to fetch Bot list for resources
  const [botList, setBotList] = useState<any[]>([])

  // 3. Add router initialization in the component
  // Add this near the other state declarations
  const router = useRouter()

  /**
   * 页面加载时获取任务列表
   */
  useEffect(() => {
    // 获取Bot列表和任务列表
    fetchBotList()
    fetchTasks()

    // 检查URL参数，如果有upload=true，自动打开上传对话框
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.get('upload') === 'true') {
      setShowImportDialog(true)

      // 清除URL参数，避免刷新页面时再次打开对话框
      router.replace('/tasks')
    }
  }, [currentPage, itemsPerPage, router])

  /**
   * 获取任务列表
   * 调用API获取任务数据
   */
  const fetchTasks = async () => {
    setIsLoading(true)
    try {
      // 构建查询参数
      const startTime = startDateRange.from ? new Date(startDateRange.from).getTime() : undefined
      const endTime = startDateRange.to ? new Date(startDateRange.to).getTime() : undefined

      // 调用真实API
      const response = await videoCallService.getTaskList({
        page: currentPage,
        pageSize: itemsPerPage,
        name: taskName || undefined,
        type: type === "all" ? undefined : type,
        startTime,
        endTime,
        userId: user?.id // 添加用户ID，确保只能查看自己的任务
      })

      if (response.success && response.data) {
        setTasks((response.data.list || []).map(task => ({
          ...task,
          uiStatus: task.status || 'pending',
          progress: task.progress || 0
        })))
        setTotalTasks(response.data.total || 0)
        setTotalPages(response.data.totalPages || 1)
      } else {
        toast({
          title: "获取任务列表失败",
          description: response.message || "请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("获取任务列表失败:", error)
      toast({
        title: "获取任务列表失败",
        description: "请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 获取演示任务数据
   * 生成模拟任务数据用于展示
   */
  const getDemoTasks = (): ExtendedTask[] => {
    return [
      {
        id: "task-001",
        name: "汽车保养提醒活动",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-15 09:30:45",
        startTime: "2024-10-16 10:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-16 18:35:22",
        creator: "张经理",
      },
      {
        id: "task-002",
        name: "轮胎更换促销活动",
        type: "5G视频互动",
        content: "轮胎更换",
        importTime: "2024-10-14 14:22:10",
        startTime: "2024-10-15 09:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-15 17:45:33",
        creator: "李主管",
      },
      {
        id: "task-003",
        name: "车险续保通知",
        type: "5G语音通话",
        content: "车险续保",
        importTime: "2024-10-16 11:15:30",
        startTime: "2024-10-17 09:30:00",
        progress: 75,
        status: "外呼中",
        uiStatus: "running",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-004",
        name: "汽车美容服务推广",
        type: "5G视频通知",
        content: "汽车美容",
        importTime: "2024-10-13 16:40:22",
        startTime: "2024-10-14 10:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-14 19:20:15",
        creator: "赵主管",
      },
      {
        id: "task-005",
        name: "车辆年检提醒",
        type: "5G视频互动",
        content: "车辆年检",
        importTime: "2024-10-17 08:55:40",
        startTime: "2024-10-18 09:00:00",
        progress: 0,
        uiStatus: "pending",
        status: "待启动",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-006",
        name: "汽车清洗优惠活动",
        type: "5G视频通知",
        content: "汽车清洗",
        importTime: "2024-10-12 10:30:15",
        startTime: "2024-10-13 09:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-13 18:15:40",
        creator: "李主管",
      },
      {
        id: "task-007",
        name: "维修大促活动通知",
        type: "5G视频互动",
        content: "维修大促",
        importTime: "2024-10-18 09:20:30",
        startTime: "2024-10-19 10:00:00",
        progress: 0,
        status: "待启动",
        uiStatus: "pending",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-008",
        name: "贴膜大促活动推广",
        type: "5G语音通话",
        content: "贴膜大促",
        importTime: "2024-10-11 15:45:20",
        startTime: "2024-10-12 09:30:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-12 17:50:25",
        creator: "赵主管",
      },
      {
        id: "task-009",
        name: "冬季保养套餐推广",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-19 11:10:35",
        startTime: "2024-10-20 09:00:00",
        progress: 0,
        status: "待启动",
        uiStatus: "pending",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-010",
        name: "雨刮器更换提醒",
        type: "5G视频互动",
        content: "维修大促",
        importTime: "2024-10-10 13:25:50",
        startTime: "2024-10-11 10:30:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-11 19:05:30",
        creator: "李主管",
      },
      {
        id: "task-011",
        name: "空调系统检查活动",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-20 08:40:15",
        startTime: "2024-10-21 09:30:00",
        progress: 0,
        status: "待启动",
        uiStatus: "pending",
        completionTime: null,
        creator: "王经理",
      },
      {
        id: "task-012",
        name: "刹车系统检测活动",
        type: "5G语音通话",
        content: "维修大促",
        importTime: "2024-10-09 14:50:25",
        startTime: "2024-10-10 09:00:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-10 18:30:45",
        creator: "赵主管",
      },
      {
        id: "task-013",
        name: "新车上市通知",
        type: "5G视频互动",
        content: "汽车美容",
        importTime: "2024-10-21 10:15:40",
        startTime: "2024-10-22 10:00:00",
        progress: 0,
        status: "待启动",
        uiStatus: "pending",
        completionTime: null,
        creator: "张经理",
      },
      {
        id: "task-014",
        name: "机油更换提醒",
        type: "5G视频通知",
        content: "汽车保养",
        importTime: "2024-10-08 11:35:55",
        startTime: "2024-10-09 09:30:00",
        progress: 100,
        status: "已完成",
        uiStatus: "completed",
        completionTime: "2024-10-09 17:45:20",
        creator: "李主管",
      },
      {
        id: "task-015",
        name: "轮胎平衡检测活动",
        type: "5G语音通话",
        content: "轮胎更换",
        importTime: "2024-10-22 09:25:30",
        startTime: "2024-10-23 09:00:00",
        progress: 0,
        status: "待启动",
        uiStatus: "pending",
        completionTime: null,
        creator: "王经理",
      },
    ]
  }

  /**
   * 处理任务删除
   * 调用API删除指定任务
   */
  const handleDeleteTask = async () => {
    if (!taskToDelete) return

    setIsLoading(true)
    try {
      // 调用删除任务API
      const response = await videoCallService.deleteTask(taskToDelete, user?.id)

      if (response.success) {
        toast({
          title: "删除成功",
          description: "任务已成功删除",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })

        // 刷新任务列表
        fetchTasks()
      } else {
        toast({
          title: "删除失败",
          description: response.message || "请稍后重试",
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("删除任务失败:", error)
      toast({
        title: "删除任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
      setTaskToDelete(null)
      setShowDeleteDialog(false)
    }
  }

  /**
   * 处理任务启动
   * 调用API启动指定任务
   */
  const handleStartTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 调用 API 启动任务，添加用户ID进行权限控制
      const response = await startTask(taskId, user?.id)

      if (response.success) {
        // 刷新任务列表，获取最新状态
        fetchTasks()

        // 显示成功提示
        toast({
          title: "启动成功",
          description: "任务已成功启动",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })
      } else {
        // 显示错误提示
        toast({
          title: "启动失败",
          description: response.message || "请稍后重试",
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("启动任务失败:", error)
      toast({
        title: "启动任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理任务暂停
   * 调用API暂停指定任务
   */
  const handlePauseTask = async (taskId: string) => {
    setIsLoading(true)
    try {
      // 调用 API 暂停任务，添加用户ID进行权限控制
      const response = await pauseTask(taskId, user?.id)

      if (response.success) {
        // 刷新任务列表，获取最新状态
        fetchTasks()

        // 显示成功提示
        toast({
          title: "暂停成功",
          description: "任务已成功暂停",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })
      } else {
        // 显示错误提示
        toast({
          title: "暂停失败",
          description: response.message || "请稍后重试",
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("暂停任务失败:", error)
      toast({
        title: "暂停任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理新建任务
   * 调用API创建新任务
   */
  const handleCreateTask = async () => {
    // 表单验证
    if (!newTaskName) {
      toast({
        title: "表单错误",
        description: "请输入任务名称",
        variant: "destructive",
      })
      return
    }

    if (!newTaskContent) {
      toast({
        title: "表单错误",
        description: "请选择外呼内容",
        variant: "destructive",
      })
      return
    }

    if (!newTaskType) {
      toast({
        title: "表单错误",
        description: "请选择外呼类型",
        variant: "destructive",
      })
      return
    }

    if (!newTaskStartTime) {
      toast({
        title: "表单错误",
        description: "请选择外呼开始时间",
        variant: "destructive",
      })
      return
    }

    if (!newTaskResource) {
      toast({
        title: "表单错误",
        description: "请选择外呼资源",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "manual" && !newTaskPhoneNumber) {
      toast({
        title: "表单错误",
        description: "请输入呼叫号码",
        variant: "destructive",
      })
      return
    }

    if (activeTab === "import" && !importFile) {
      toast({
        title: "表单错误",
        description: "请选择导入文件",
        variant: "destructive",
      })
      return
    }

    // 格式化日期和时间
    let formattedDateTime = ""
    if (newTaskStartTime) {
      const date = format(newTaskStartTime, "yyyy-MM-dd")
      formattedDateTime = `${date} ${selectedHour}:${selectedMinute}:${selectedSecond}`
    }

    setIsLoading(true)

    try {
      // 处理电话号码
      const phoneNumbers = activeTab === "manual"
        ? newTaskPhoneNumber.split(/[,，]/).map(p => p.trim()).filter(p => p)
        : [] // 如果是导入文件，需要解析文件内容获取号码

      // 调用真实API
      const response = await videoCallService.importTask({
        name: newTaskName,
        content: newTaskContent,
        callType: newTaskType,
        startTime: formattedDateTime,
        resource: newTaskResource,
        phoneNumbers: phoneNumbers,
        smsType: newTaskSmsType,
        smsTemplate: newTaskSmsTemplate,
        userId: user?.id // 添加用户ID，确保只能创建自己的任务
      })

      if (response.success) {
        toast({
          title: "创建成功",
          description: "任务已成功创建",
          variant: "success",
          className: "bg-green-600 text-white font-medium border-none",
        })

        // 重置表单并关闭对话框
        resetNewTaskForm()
        setShowImportDialog(false)

        // 刷新任务列表
        fetchTasks()
      } else {
        toast({
          title: "创建失败",
          description: response.message || "请稍后重试",
          variant: "destructive",
          className: "bg-red-600 text-white font-medium border-none",
        })
      }
    } catch (error) {
      console.error("创建任务失败:", error)
      toast({
        title: "创建任务失败",
        description: "请稍后重试",
        variant: "destructive",
        className: "bg-red-600 text-white font-medium border-none",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const fetchBotList = async () => {
    try {
      const response = await getBotList()
      if (response.success) {
        setBotList(response.data)
      } else {
        console.error("获取Bot列表失败:", response.message)
      }
    } catch (error) {
      console.error("获取Bot列表错误:", error)
    }
  }

  /**
   * 重置新建任务表单
   * 清空所有表单字段
   */
  const resetNewTaskForm = () => {
    setNewTaskName("")
    setNewTaskContent("")
    setNewTaskType("")
    setNewTaskStartTime(undefined)
    setNewTaskResource("")
    setNewTaskPhoneNumber("")
    setNewTaskSmsType("")
    setNewTaskSmsTemplate("")
    setSelectedHour("00")
    setSelectedMinute("00")
    setSelectedSecond("00")
    setActiveTab("manual")
    setImportFile(null)
  }

  /**
   * 处理文件选择
   * 当用户选择导入文件时触发
   */
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0])
    }
  }

  /**
   * 处理搜索
   * 调用API根据搜索条件获取任务数据
   */
  const handleSearch = () => {
    // 重置当前页码并获取任务
    setCurrentPage(1)
    fetchTasks()
  }

  /**
   * 生成时间选择器选项
   * 生成小时、分钟、秒钟的选择器选项
   */
  const generateTimeOptions = (max: number) => {
    const options = []
    for (let i = 0; i < max; i++) {
      const value = i.toString().padStart(2, "0")
      options.push(
        <option key={value} value={value}>
          {value}
        </option>,
      )
    }
    return options
  }

  // 添加一个辅助函数来获取任务类型的样式类名
  const getTaskTypeClassName = (type: string) => {
    switch (type) {
      case "5G视频通知":
        return "status-active"
      case "5G视频互动":
        return "status-processing"
      default:
        return "status-pending"
    }
  }

  // 根据用户角色调整页面标题和描述
  const pageTitle = userType === "admin" ? "任务上传" : "我的任务"
  const pageDescription = userType === "admin" ? "管理外呼任务" : "管理您的外呼任务"

  return (
    <div>
      <DashboardShell>
        <div className="flex items-center justify-between mb-4">
          <DashboardHeader heading={pageTitle} text={pageDescription}>
            <Button onClick={() => setShowImportDialog(true)}>导入任务</Button>
          </DashboardHeader>
          <div className="flex items-center space-x-4">
            <NotificationButton />
            <UserAvatar
              userName={user?.data?.name || "用户"}
              userType={userType as "admin" | "customer"}
              userInitials={user?.data?.name?.[0] || "U"}
              onToggleUserType={() => {}}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
              <div className="space-y-2">
                <Label htmlFor="task-name">外呼任务名称</Label>
                <Input
                  id="task-name"
                  placeholder="输入任务名称"
                  value={taskName}
                  onChange={(e) => setTaskName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content">视频外呼内容</Label>
                <Select value={content} onValueChange={setContent}>
                  <SelectTrigger id="content">
                    <SelectValue placeholder="选择内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部内容</SelectItem>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">外呼类型</Label>
                <Select value={type} onValueChange={setType}>
                  <SelectTrigger id="type">
                    <SelectValue placeholder="选择类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                    <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                    <SelectItem value="5G语音通话">5G语音通话</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>任务开始时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDateRange.from ? (
                        startDateRange.to ? (
                          <>
                            {format(startDateRange.from, "yyyy-MM-dd")} - {format(startDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(startDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={startDateRange}
                      onSelect={(range) => {
                        if (range) {
                          setStartDateRange({
                            from: range.from,
                            to: range.to || undefined
                          })
                        } else {
                          setStartDateRange({ from: undefined, to: undefined })
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label>任务创建时间</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !createDateRange.from && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {createDateRange.from ? (
                        createDateRange.to ? (
                          <>
                            {format(createDateRange.from, "yyyy-MM-dd")} - {format(createDateRange.to, "yyyy-MM-dd")}
                          </>
                        ) : (
                          format(createDateRange.from, "yyyy-MM-dd")
                        )
                      ) : (
                        <span>选择日期范围</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="range"
                      selected={createDateRange}
                      onSelect={(range) => {
                        if (range) {
                          setCreateDateRange({
                            from: range.from,
                            to: range.to || undefined
                          })
                        } else {
                          setCreateDateRange({ from: undefined, to: undefined })
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-2">
              <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSearch} disabled={isLoading}>
                {isLoading ? "查询中..." : "查询"}
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={() => {
                  resetNewTaskForm()
                  setShowImportDialog(true)
                }}
                disabled={isLoading}
              >
                <Upload className="mr-2 h-4 w-4" />
                任务导入
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 任务列表表格 */}
        <div className="rounded-md border mt-6">
          <div className="overflow-x-auto">
            <Table className="table-enhanced relative">
              <TableHeader>
                <TableRow>
                  <TableHead>外呼任务名称</TableHead>
                  <TableHead>外呼类型</TableHead>
                  <TableHead>视频外呼内容</TableHead>
                  <TableHead>任务导入时间</TableHead>
                  <TableHead>任务开始时间</TableHead>
                  <TableHead>任务进度</TableHead>
                  <TableHead>任务完成时间</TableHead>
                  <TableHead>创建人</TableHead>
                  <TableHead className="sticky right-0 bg-white shadow-l z-10">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      <div className="flex justify-center items-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                        加载中...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : tasks.length > 0 ? (
                  tasks.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell className="font-medium">{task.name}</TableCell>
                      <TableCell>
                        <span className={getTaskTypeClassName(task.type)}>
                          {task.type}
                        </span>
                      </TableCell>
                      <TableCell>{task.content}</TableCell>
                      <TableCell>{task.importTime}</TableCell>
                      <TableCell>{task.startTime}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Progress
                            value={task.progress}
                            className={`h-2 w-[100px] ${
                              task.progress === 100
                                ? "bg-green-600"
                                : task.progress > 50
                                  ? "bg-blue-600"
                                  : task.progress > 0
                                    ? "bg-yellow-600"
                                    : "bg-gray-300"
                            }`}
                          />
                          <span
                            className={
                              task.progress === 100
                                ? "text-green-600 font-medium"
                                : task.progress > 50
                                  ? "text-blue-600 font-medium"
                                  : task.progress > 0
                                    ? "text-yellow-600 font-medium"
                                    : "text-gray-500"
                            }
                          >
                            {task.progress}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {task.completionTime ? (
                          task.completionTime
                        ) : (
                          <span
                            className={
                              task.uiStatus === "completed"
                                ? "status-active"
                                : task.uiStatus === "running"
                                  ? "status-processing"
                                  : task.uiStatus === "paused"
                                    ? "status-warning"
                                    : "status-pending"
                            }
                          >
                            {task.uiStatus === "pending"
                              ? "待启动"
                              : task.uiStatus === "running"
                                ? "外呼中"
                                : task.uiStatus === "paused"
                                  ? "已暂停"
                                  : "已完成"}
                          </span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <UserAvatar
                            userName={task.creator}
                            userType="admin"
                            userInitials={task.creator?.[0] || "U"}
                            onToggleUserType={() => {}}
                          />
                          <span>{task.creator}</span>
                        </div>
                      </TableCell>
                      <TableCell className="sticky right-0 bg-white shadow-l z-10">
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-green-600"
                            disabled={task.uiStatus === "completed" || task.uiStatus === "running" || isLoading}
                            onClick={() => handleStartTask(task.id)}
                          >
                            <Play className="h-4 w-4" />
                            <span className="sr-only">启动</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-yellow-600"
                            disabled={task.uiStatus !== "running" || isLoading}
                            onClick={() => handlePauseTask(task.id)}
                          >
                            <Pause className="h-4 w-4" />
                            <span className="sr-only">暂停</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-blue-600"
                            onClick={() => router.push(`/calls/${task.id}`)}
                          >
                            <Info className="h-4 w-4" />
                            <span className="sr-only">详情</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 text-red-600"
                            disabled={isLoading}
                            onClick={() => {
                              setTaskToDelete(task.id)
                              setShowDeleteDialog(true)
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">删除</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      没有找到任务
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* 分页控件 */}
        {totalTasks > 0 && (
          <div className="mt-4 flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => !isLoading && setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <PaginationItem key={page}>
                    <PaginationLink
                      onClick={() => !isLoading && setCurrentPage(page)}
                      isActive={currentPage === page}
                      className={isLoading ? "cursor-pointer opacity-50 pointer-events-none" : "cursor-pointer"}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                ))}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => !isLoading && setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages || isLoading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </DashboardShell>

      {/* 删除任务确认对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确认删除</DialogTitle>
            <DialogDescription>您确定要删除此任务吗？此操作无法撤销。</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)} disabled={isLoading}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteTask} disabled={isLoading}>
              {isLoading ? "删除中..." : "删除"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 新建5G视频外呼任务对话框 */}
      <Dialog
        open={showImportDialog}
        onOpenChange={(open) => {
          if (!isLoading) {
            setShowImportDialog(open)
            if (!open) resetNewTaskForm()
          }
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>创建新任务</DialogTitle>
            <DialogDescription>请填写任务详细信息</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="taskName" className="text-right">任务名称</Label>
              <Input
                id="taskName"
                value={newTaskName}
                onChange={(e) => setNewTaskName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="taskContent" className="text-right">外呼内容</Label>
              <div className="col-span-3">
                <Select value={newTaskContent} onValueChange={setNewTaskContent}>
                  <SelectTrigger id="taskContent">
                    <SelectValue placeholder="请选择外呼内容" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="汽车清洗">汽车清洗</SelectItem>
                    <SelectItem value="维修大促">维修大促</SelectItem>
                    <SelectItem value="贴膜大促">贴膜大促</SelectItem>
                    <SelectItem value="汽车保养">汽车保养</SelectItem>
                    <SelectItem value="轮胎更换">轮胎更换</SelectItem>
                    <SelectItem value="车险续保">车险续保</SelectItem>
                    <SelectItem value="汽车美容">汽车美容</SelectItem>
                    <SelectItem value="车辆年检">车辆年检</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-type">外呼类型</Label>
              <Select value={newTaskType} onValueChange={setNewTaskType}>
                <SelectTrigger id="new-task-type">
                  <SelectValue placeholder="请选择外呼类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5G视频通知">5G视频通知</SelectItem>
                  <SelectItem value="5G视频互动">5G视频互动</SelectItem>
                  <SelectItem value="5G语音互动">5G语音互动</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-start-time">外呼开始时间</Label>
              <div className="flex space-x-2">
                <Popover open={showDateTimePicker} onOpenChange={setShowDateTimePicker}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !newTaskStartTime && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newTaskStartTime ? (
                        format(newTaskStartTime, "yyyy-MM-dd") +
                        ` ${selectedHour}:${selectedMinute}:${selectedSecond}`
                      ) : (
                        <span>选择日期和时间</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <div className="p-2">
                      <Calendar
                        mode="single"
                        selected={newTaskStartTime}
                        onSelect={(date) => setNewTaskStartTime(date)}
                        initialFocus
                      />
                      <div className="flex items-center justify-between px-3 py-2 border-t">
                        <Label>时间:</Label>
                        <div className="flex space-x-1">
                          <select
                            value={selectedHour}
                            onChange={(e) => setSelectedHour(e.target.value)}
                            className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                          >
                            {generateTimeOptions(24)}
                          </select>
                          <span className="py-1">:</span>
                          <select
                            value={selectedMinute}
                            onChange={(e) => setSelectedMinute(e.target.value)}
                            className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                          >
                            {generateTimeOptions(60)}
                          </select>
                          <span className="py-1">:</span>
                          <select
                            value={selectedSecond}
                            onChange={(e) => setSelectedSecond(e.target.value)}
                            className="w-16 rounded-md border border-input bg-background px-3 py-1 text-sm"
                          >
                            {generateTimeOptions(60)}
                          </select>
                        </div>
                      </div>
                      <div className="flex justify-end p-2">
                        <Button
                          size="sm"
                          onClick={() => setShowDateTimePicker(false)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          确定
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-resource">视频外呼资源</Label>
              <Select value={newTaskResource} onValueChange={setNewTaskResource}>
                <SelectTrigger id="new-task-resource">
                  <SelectValue placeholder="请选择外呼资源" />
                </SelectTrigger>
                <SelectContent>
                  {botList.length > 0 ? (
                    botList.map((bot) => (
                      <SelectItem key={bot._id} value={bot._id}>
                        {bot.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="loading">加载中...</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-phone">呼叫号码</Label>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="manual">手动输入</TabsTrigger>
                  <TabsTrigger value="import">导入表格</TabsTrigger>
                </TabsList>
                <TabsContent value="manual" className="mt-2">
                  <Input
                    id="new-task-phone"
                    placeholder="请输入呼叫号码"
                    value={newTaskPhoneNumber}
                    onChange={(e) => setNewTaskPhoneNumber(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">多个号码请用中文逗号（，）分隔</p>
                </TabsContent>
                <TabsContent value="import" className="mt-2">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Input
                        id="phone-file"
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileChange}
                        className="flex-1"
                      />
                    </div>
                    {importFile && <p className="text-sm text-muted-foreground">已选择文件: {importFile.name}</p>}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-sms-type">短信方式</Label>
              <Select value={newTaskSmsType} onValueChange={setNewTaskSmsType}>
                <SelectTrigger id="new-task-sms-type">
                  <SelectValue placeholder="请选择短信方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="文本短信">文本短信</SelectItem>
                  <SelectItem value="视频短信">视频短信</SelectItem>
                  <SelectItem value="无">无</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="new-task-sms-template">短信模板</Label>
              <Select
                value={newTaskSmsTemplate}
                onValueChange={setNewTaskSmsTemplate}
                disabled={newTaskSmsType === "无"}
              >
                <SelectTrigger id="new-task-sms-template">
                  <SelectValue placeholder="请选择短信模板" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="模板1">模板1：我们门店的地址是……</SelectItem>
                  <SelectItem value="模板2">模板2：我们门店的地址是……</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

