"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { Plus, RefreshCw } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

// 表单验证模式
const formSchema = z.object({
  username: z.string().min(2, "用户名至少2个字符").max(50, "用户名最多50个字符"),
  email: z.string().email("请输入有效的邮箱地址"),
  password: z.string().min(6, "密码至少6个字符"),
  name: z.string().optional(),
  roleCode: z.string(),
  status: z.enum(["active", "inactive"]),
})

interface User {
  id: string
  username: string
  email: string
  name?: string
  roleCode: string
  status: string
  createdAt: string
  updatedAt: string
  verificationStatus?: string
  verificationType?: string
  role?: {
    name: string
    code: string
  }
}

export default function UsersPage() {
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [showDialog, setShowDialog] = useState(false)
  const [roles, setRoles] = useState<{ id: string; name: string; code: string }[]>([])

  // 表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
      email: "",
      password: "",
      name: "",
      roleCode: "",
      status: "active",
    },
  })

  // 加载用户列表
  const loadUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/users")
      const data = await response.json()

      if (data.success) {
        setUsers(data.data)
      } else {
        toast.error("加载用户列表失败")
      }
    } catch (error) {
      console.error("加载用户列表失败:", error)
      toast.error("加载用户列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 加载角色列表
  const loadRoles = async () => {
    try {
      const response = await fetch("/api/roles")
      const data = await response.json()

      if (data.success) {
        setRoles(data.data)
      } else {
        toast.error("加载角色列表失败")
      }
    } catch (error) {
      console.error("加载角色列表失败:", error)
      toast.error("加载角色列表失败")
    }
  }

  // 创建用户
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const response = await fetch("/api/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("创建用户成功")
        setShowDialog(false)
        form.reset()
        loadUsers()
      } else {
        toast.error(data.message || "创建用户失败")
      }
    } catch (error) {
      console.error("创建用户失败:", error)
      toast.error("创建用户失败")
    }
  }

  // 删除用户
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/users/${id}`, {
        method: "DELETE",
      })

      const data = await response.json()

      if (data.success) {
        toast.success("删除用户成功")
        loadUsers()
      } else {
        toast.error(data.message || "删除用户失败")
      }
    } catch (error) {
      console.error("删除用户失败:", error)
      toast.error("删除用户失败")
    }
  }

  // 初始加载
  useEffect(() => {
    loadUsers()
    loadRoles()
  }, [])

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">用户管理</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={loadUsers}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
            <DialogTrigger asChild>
              <Button onClick={() => {
                form.reset({
                  username: "",
                  email: "",
                  password: "",
                  name: "",
                  roleCode: roles.length > 0 ? roles[0].code : "",
                  status: "active",
                })
              }}>
                <Plus className="mr-2 h-4 w-4" />
                新建用户
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>新建用户</DialogTitle>
                <DialogDescription>
                  创建新的用户，设置用户名、邮箱、密码和角色。
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>用户名</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入用户名" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>邮箱</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入邮箱" type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>密码</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入密码" type="password" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>姓名</FormLabel>
                        <FormControl>
                          <Input placeholder="请输入姓名（选填）" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="roleCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>角色</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择角色" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roles.map((role) => (
                              <SelectItem key={role.code} value={role.code}>
                                {role.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>状态</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="请选择状态" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">启用</SelectItem>
                            <SelectItem value="inactive">禁用</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button
                      type="submit"
                      disabled={form.formState.isSubmitting}
                    >
                      {form.formState.isSubmitting ? "提交中..." : "创建用户"}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr>
              <th className="p-4 text-left font-medium">用户名</th>
              <th className="p-4 text-left font-medium">邮箱</th>
              <th className="p-4 text-left font-medium">姓名</th>
              <th className="p-4 text-left font-medium">角色</th>
              <th className="p-4 text-left font-medium">状态</th>
              <th className="p-4 text-left font-medium">认证状态</th>
              <th className="p-4 text-left font-medium">创建时间</th>
              <th className="p-4 text-left font-medium">更新时间</th>
              <th className="p-4 text-left font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={9} className="p-4 text-center">
                  加载中...
                </td>
              </tr>
            ) : users.length === 0 ? (
              <tr>
                <td colSpan={9} className="p-4 text-center">
                  暂无数据
                </td>
              </tr>
            ) : (
              users.map((user) => (
                <tr key={user.id}>
                  <td className="p-4">{user.username}</td>
                  <td className="p-4">{user.email}</td>
                  <td className="p-4">{user.name || '-'}</td>
                  <td className="p-4">{user.role?.name || user.roleCode}</td>
                  <td className="p-4">
                    {user.status === "active" ? "启用" : "禁用"}
                  </td>
                  <td className="p-4">
                    {user.verificationStatus === "approved" && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                        {user.verificationType === "personal" ? "个人" : "企业"}认证已通过
                      </span>
                    )}
                    {user.verificationStatus === "pending" && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                        {user.verificationType === "personal" ? "个人" : "企业"}认证审核中
                      </span>
                    )}
                    {user.verificationStatus === "rejected" && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                        {user.verificationType === "personal" ? "个人" : "企业"}认证已拒绝
                      </span>
                    )}
                    {(!user.verificationStatus || user.verificationStatus === "none") && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">
                        未认证
                      </span>
                    )}
                  </td>
                  <td className="p-4">{user.createdAt ? new Date(user.createdAt).toLocaleString('zh-CN') : '-'}</td>
                  <td className="p-4">{user.updatedAt ? new Date(user.updatedAt).toLocaleString('zh-CN') : '-'}</td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/users/${user.id}`)}
                      >
                        编辑
                      </Button>
                      {user.verificationStatus === "pending" && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-500 hover:text-green-700"
                            onClick={() => router.push(`/accounts/customer/${user.id}?tab=verification`)}
                          >
                            审核认证
                          </Button>
                        </>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700"
                        onClick={() => {
                          if (confirm(`确定要删除用户 ${user.username} 吗？`)) {
                            handleDelete(user.id)
                          }
                        }}
                      >
                        删除
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}
