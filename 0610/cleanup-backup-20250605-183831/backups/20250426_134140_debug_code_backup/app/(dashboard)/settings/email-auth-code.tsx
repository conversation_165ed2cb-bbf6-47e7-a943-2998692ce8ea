import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Edit } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface EmailAuthCodeProps {
  emailHost: string
  emailPort: number
  emailUser: string
  setIsSaving: (value: boolean) => void
  onSuccess: (data: any) => void
}

export function EmailAuthCode({ emailHost, emailPort, emailUser, setIsSaving, onSuccess }: EmailAuthCodeProps) {
  const handleSecureUpdate = async () => {
    try {
      // 验证邮箱设置
      if (!emailHost || !emailPort || !emailUser) {
        toast({
          title: "验证失败",
          description: "请先完成邮件服务器设置",
          variant: "destructive",
        })
        return
      }

      // 发送验证码
      const sendResponse = await fetch("/api/settings/email/send-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      const sendData = await sendResponse.json()

      if (!sendData.success) {
        throw new Error(sendData.message || "发送验证码失败")
      }

      toast({
        title: "发送成功",
        description: "验证码已发送到您的邮箱",
      })

      // 输入验证码
      const code = prompt("请输入验证码")
      if (!code) return

      // 输入新的授权码
      const authCode = prompt("请输入新的邮箱授权码")
      if (!authCode) return

      // 验证验证码并更新授权码
      setIsSaving(true)
      const verifyResponse = await fetch("/api/settings/email/verify-and-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          code,
          authCode,
        }),
      })

      const verifyData = await verifyResponse.json()

      if (verifyData.success) {
        toast({
          title: "更新成功",
          description: "邮箱授权码已安全更新",
        })

        // 更新邮件设置
        onSuccess(verifyData.data)
      } else {
        throw new Error(verifyData.message || "验证失败")
      }
    } catch (error) {
      console.error("更新邮箱授权码失败:", error)
      toast({
        title: "更新失败",
        description: error instanceof Error ? error.message : "无法更新邮箱授权码",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <Button
      type="button"
      variant="outline"
      onClick={handleSecureUpdate}
      className="whitespace-nowrap"
    >
      <Edit className="mr-2 h-4 w-4" />
      安全修改
    </Button>
  )
}
