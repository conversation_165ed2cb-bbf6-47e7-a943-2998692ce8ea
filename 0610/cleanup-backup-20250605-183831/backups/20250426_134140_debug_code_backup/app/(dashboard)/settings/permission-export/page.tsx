"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { Download, Upload, RefreshCw, Check } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"

interface Role {
  id: string
  code: string
  name: string
  type: string
}

export default function PermissionExportPage() {
  // 导出选项
  const [roles, setRoles] = useState<Role[]>([])
  const [selectedRole, setSelectedRole] = useState<string>("")
  const [exportFormat, setExportFormat] = useState<string>("json")
  const [includeMenus, setIncludeMenus] = useState<boolean>(true)
  const [includeOperations, setIncludeOperations] = useState<boolean>(true)
  const [includeResources, setIncludeResources] = useState<boolean>(true)
  const [loading, setLoading] = useState<boolean>(false)

  // 导入选项
  const [file, setFile] = useState<File | null>(null)
  const [overwrite, setOverwrite] = useState<boolean>(false)
  const [importing, setImporting] = useState<boolean>(false)
  const [importResult, setImportResult] = useState<any>(null)
  const [importProgress, setImportProgress] = useState<number>(0)

  // 加载角色列表
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const response = await fetch("/api/roles")
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setRoles(data.data)
          }
        }
      } catch (error) {
        console.error("获取角色列表失败:", error)
      }
    }

    fetchRoles()
  }, [])

  // 处理导出
  const handleExport = async () => {
    try {
      setLoading(true)

      // 构建查询参数
      const params = new URLSearchParams()
      if (selectedRole && selectedRole !== "all") params.append("roleCode", selectedRole)
      params.append("format", exportFormat)
      params.append("includeMenus", includeMenus.toString())
      params.append("includeOperations", includeOperations.toString())
      params.append("includeResources", includeResources.toString())

      // 发起导出请求
      const response = await fetch(`/api/admin/permissions/export?${params.toString()}`)

      if (response.ok) {
        // 获取文件名
        const contentDisposition = response.headers.get("Content-Disposition")
        let filename = "permissions.json"
        if (contentDisposition) {
          const match = contentDisposition.match(/filename="(.+)"/)
          if (match && match[1]) {
            filename = match[1]
          }
        }

        // 下载文件
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        URL.revokeObjectURL(url)
        document.body.removeChild(a)

        toast.success("导出权限配置成功")
      } else {
        const error = await response.json()
        toast.error(error.message || "导出权限配置失败")
      }
    } catch (error) {
      console.error("导出权限配置失败:", error)
      toast.error("导出权限配置失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0]
      const fileType = selectedFile.name.split('.').pop()?.toLowerCase()

      if (fileType !== 'csv' && fileType !== 'json') {
        toast.error("只支持CSV和JSON格式的文件")
        return
      }

      setFile(selectedFile)
      setImportResult(null)
    }
  }

  // 处理导入
  const handleImport = async () => {
    if (!file) {
      toast.error("请选择文件")
      return
    }

    try {
      setImporting(true)
      setImportProgress(10)

      // 创建表单数据
      const formData = new FormData()
      formData.append("file", file)
      formData.append("overwrite", overwrite.toString())

      // 发起导入请求
      setImportProgress(30)
      const response = await fetch("/api/admin/permissions/import", {
        method: "POST",
        body: formData
      })

      setImportProgress(70)

      if (response.ok) {
        const result = await response.json()
        setImportResult(result.data)
        toast.success("导入权限配置成功")
      } else {
        const error = await response.json()
        toast.error(error.message || "导入权限配置失败")
      }

      setImportProgress(100)
    } catch (error) {
      console.error("导入权限配置失败:", error)
      toast.error("导入权限配置失败")
    } finally {
      setImporting(false)
    }
  }

  return (
    <div className="space-y-4">
      <Tabs defaultValue="export">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="export">导出权限</TabsTrigger>
          <TabsTrigger value="import">导入权限</TabsTrigger>
        </TabsList>

        {/* 导出权限选项卡 */}
        <TabsContent value="export">
          <Card>
            <CardHeader>
              <CardTitle>导出权限配置</CardTitle>
              <CardDescription>
                导出系统权限配置，支持JSON和CSV格式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role">角色</Label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger id="role">
                      <SelectValue placeholder="选择角色（全部）" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部角色</SelectItem>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.code}>
                          {role.name} ({role.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="format">导出格式</Label>
                  <Select value={exportFormat} onValueChange={setExportFormat}>
                    <SelectTrigger id="format">
                      <SelectValue placeholder="选择导出格式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="json">JSON</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>包含权限类型</Label>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeMenus"
                      checked={includeMenus}
                      onCheckedChange={(checked) => setIncludeMenus(checked === true)}
                    />
                    <Label htmlFor="includeMenus">菜单权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeOperations"
                      checked={includeOperations}
                      onCheckedChange={(checked) => setIncludeOperations(checked === true)}
                    />
                    <Label htmlFor="includeOperations">操作权限</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeResources"
                      checked={includeResources}
                      onCheckedChange={(checked) => setIncludeResources(checked === true)}
                    />
                    <Label htmlFor="includeResources">资源权限</Label>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleExport} disabled={loading}>
                {loading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    导出中...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    导出权限
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* 导入权限选项卡 */}
        <TabsContent value="import">
          <Card>
            <CardHeader>
              <CardTitle>导入权限配置</CardTitle>
              <CardDescription>
                从文件导入权限配置，支持JSON和CSV格式
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTitle>注意</AlertTitle>
                <AlertDescription>
                  导入权限配置将会修改系统权限设置，请确保导入文件的正确性。建议在导入前先导出备份。
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="file">选择文件</Label>
                  <Input
                    id="file"
                    type="file"
                    accept=".json,.csv"
                    onChange={handleFileChange}
                    disabled={importing}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="overwrite"
                    checked={overwrite}
                    onCheckedChange={(checked) => setOverwrite(checked === true)}
                    disabled={importing}
                  />
                  <Label htmlFor="overwrite">覆盖现有权限（谨慎选择）</Label>
                </div>

                {importing && (
                  <div className="space-y-2">
                    <Label>导入进度</Label>
                    <Progress value={importProgress} />
                  </div>
                )}

                {importResult && (
                  <div className="space-y-2 p-4 border rounded-md bg-muted">
                    <div className="font-medium">导入结果</div>
                    <div className="text-sm">
                      <div>总计: {importResult.total} 条权限</div>
                      <div className="text-green-600">成功: {importResult.imported} 条</div>
                      <div className="text-yellow-600">跳过: {importResult.skipped} 条</div>
                      <div className="text-red-600">失败: {importResult.failed} 条</div>
                    </div>
                    {importResult.errors && importResult.errors.length > 0 && (
                      <div className="mt-2">
                        <div className="font-medium text-red-600">错误信息:</div>
                        <ul className="text-xs text-red-600 list-disc list-inside">
                          {importResult.errors.slice(0, 5).map((error: string, index: number) => (
                            <li key={index}>{error}</li>
                          ))}
                          {importResult.errors.length > 5 && (
                            <li>...还有 {importResult.errors.length - 5} 个错误</li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleImport} disabled={!file || importing}>
                {importing ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    导入中...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    导入权限
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
