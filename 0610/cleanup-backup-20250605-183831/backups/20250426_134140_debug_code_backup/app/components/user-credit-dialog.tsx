"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { toast } from "@/components/ui/use-toast"

interface UserCreditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  currentCreditLimit?: number
  onSuccess: (newCreditLimit: number) => void
}

export function UserCreditDialog({
  open,
  onOpenChange,
  userId,
  userName,
  currentCreditLimit = 0,
  onSuccess,
}: UserCreditDialogProps) {
  const [creditLimit, setCreditLimit] = useState("")
  const [remarks, setRemarks] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [operationType, setOperationType] = useState("set") // set, add 或 reset

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 如果不是清零操作，需要验证金额
    let creditAmount = 0
    if (operationType !== "reset") {
      creditAmount = parseFloat(creditLimit)
      if (isNaN(creditAmount) || creditAmount < 0) {
        toast({
          title: "金额无效",
          description: "请输入有效的授信额度",
          variant: "destructive",
        })
        return
      }
    }

    setIsSubmitting(true)
    try {
      // 准备请求数据
      let requestData = {}

      if (operationType === "reset") {
        // 清零操作
        requestData = {
          creditLimit: 0,
          operationType: "set", // 使用set操作类型，但值为0
          remarks: remarks.trim() || "系统授信额度清零"
        }
        console.log(`授信额度清零操作: 用户ID=${userId}, 备注=${remarks.trim() || "系统授信额度清零"}`)
      } else {
        // 设置或增加操作
        requestData = {
          creditLimit: creditAmount,
          operationType: operationType,
          remarks: remarks.trim() || "系统授信额度调整"
        }
        console.log(`授信额度操作: 用户ID=${userId}, 金额=${creditAmount}, 类型=${operationType}, 备注=${remarks.trim() || "系统授信额度调整"}`)
      }

      // 使用正确的API路径
      const url = `/api/customers/${userId}/limit/credit`
      console.log('发送请求到:', url, '数据:', requestData)
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        credentials: 'include',
      })

      // 改进错误处理，先获取响应文本
      const responseText = await response.text()
      console.log('原始响应文本:', responseText)

      // 尝试将文本解析为JSON
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('解析JSON响应失败:', parseError)
        throw new Error('服务器返回了无效的响应格式')
      }

      if (response.ok && data.success) {
        toast({
          title: "授信调整成功",
          description: operationType === "reset"
            ? `已成功将用户 ${userName} 的授信额度清零`
            : (operationType === "set"
              ? `已成功为用户 ${userName} 设置授信额度为 ¥${creditAmount.toLocaleString()}`
              : `已成功为用户 ${userName} ${operationType === "add" ? "增加" : "减少"}授信额度 ¥${creditAmount.toLocaleString()}`),
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })

        // 重置表单
        setCreditLimit("")
        setRemarks("")

        // 关闭对话框
        onOpenChange(false)

        // 获取实际的新授信额度
        let actualNewCreditLimit = 0
        if (data.data && data.data.newCreditLimit !== undefined) {
          actualNewCreditLimit = data.data.newCreditLimit
        } else if (data.data && data.data.creditLimit !== undefined) {
          actualNewCreditLimit = data.data.creditLimit
        }

        console.log('授信调整成功，新授信额度:', actualNewCreditLimit)

        // 通知父组件
        onSuccess(actualNewCreditLimit)
      } else {
        toast({
          title: "授信调整失败",
          description: data.message || "无法调整授信额度，请稍后重试",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('授信调整错误:', error)
      toast({
        title: "授信调整失败",
        description: "调整授信额度时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>调整授信额度</DialogTitle>
          <DialogDescription>
            为用户 <span className="font-semibold">{userName}</span> 调整授信额度。
            当前授信额度：<span className="font-semibold">¥{currentCreditLimit.toLocaleString()}</span>
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="operationType" className="text-right">
                操作类型
              </Label>
              <div className="col-span-3 flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="set"
                    name="operationType"
                    value="set"
                    checked={operationType === "set"}
                    onChange={() => setOperationType("set")}
                    className="h-4 w-4 text-blue-600"
                  />
                  <Label htmlFor="set" className="cursor-pointer">设置额度</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="add"
                    name="operationType"
                    value="add"
                    checked={operationType === "add"}
                    onChange={() => setOperationType("add")}
                    className="h-4 w-4 text-green-600"
                  />
                  <Label htmlFor="add" className="cursor-pointer">增加额度</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="reset"
                    name="operationType"
                    value="reset"
                    checked={operationType === "reset"}
                    onChange={() => setOperationType("reset")}
                    className="h-4 w-4 text-red-600"
                  />
                  <Label htmlFor="reset" className="cursor-pointer">清零额度</Label>
                </div>
              </div>
            </div>
            {operationType !== "reset" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="creditLimit" className="text-right">
                  {operationType === "set" ? "授信额度" : "增加额度"}
                </Label>
                <div className="col-span-3 relative">
                  <span className="absolute left-3 top-2.5">¥</span>
                  <Input
                    id="creditLimit"
                    type="number"
                    min="0"
                    step="0.01"
                    value={creditLimit}
                    onChange={(e) => setCreditLimit(e.target.value)}
                    className="pl-8"
                    placeholder="0.00"
                    required={operationType !== "reset"}
                  />
                </div>
              </div>
            )}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="remarks" className="text-right">
                备注说明
              </Label>
              <Textarea
                id="remarks"
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                className="col-span-3"
                placeholder="可选填写授信调整原因或其他备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || (operationType !== "reset" && (!creditLimit || parseFloat(creditLimit) <= 0))}
              className={operationType === "reset" ? "bg-red-500 hover:bg-red-600 text-white" : "bg-blue-500 hover:bg-blue-600 text-white"}
            >
              {isSubmitting ? "处理中..." : operationType === "reset" ? "确认清零" : "确认调整"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
