import { useState } from "react"
import Image from "next/image"
import { toast } from "sonner"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"

// 预设头像列表 - 使用iconfont.cn的头像资源
const presetAvatars = [
  // 原有头像
  "https://img.alicdn.com/imgextra/i1/O1CN01KwRDrI1JbQgMkN3Tz_!!6000000001045-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i2/O1CN01UHcZYY1j2rEKWwGYQ_!!6000000004495-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i3/O1CN01vzXYL71JbQgLdRCLN_!!6000000001045-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i4/O1CN01Qg1Zz91woEIIcZFQx_!!6000000006369-2-tps-80-80.png",

  // 新增头像 - 来自iconfont.cn
  "https://img.alicdn.com/imgextra/i4/O1CN01Ug9jZJ1aOjJj3FLYk_!!6000000003324-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i2/O1CN01ZBTIrA1tEgAblpZAd_!!6000000005866-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i1/O1CN01KKL9Xo1zcbFJV3SJU_!!6000000006726-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i2/O1CN01JNYpnj1uRDWsvBSsj_!!6000000006033-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i3/O1CN01Mpnpkj1yD0X8Kem35_!!6000000006546-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i1/O1CN01S8q8SQ1n5MNUvRPqt_!!6000000005040-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i4/O1CN01EI9Jow1kTpZSvWQVi_!!6000000004682-2-tps-200-200.png",
  "https://img.alicdn.com/imgextra/i2/O1CN01xIBOIP1lnRyM9V4Nt_!!6000000004865-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i4/O1CN01WLGC8T1JbQgJJKJF1_!!6000000001045-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i2/O1CN01xPeZMK1CPuJBNcf60_!!6000000000074-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i3/O1CN01VxJ2LN1oCMlvRZsXP_!!6000000005181-2-tps-80-80.png",
  "https://img.alicdn.com/imgextra/i1/O1CN01KtTW5J1uGd3ePL0Cg_!!6000000006012-2-tps-80-80.png",
]

interface PresetAvatarsProps {
  onSelect: (url: string) => void
}

export function PresetAvatars({ onSelect }: PresetAvatarsProps) {
  const [selectedAvatar, setSelectedAvatar] = useState<string>("")

  const handleSelect = async () => {
    if (!selectedAvatar) {
      toast.error("请选择头像")
      return
    }

    try {
      const response = await fetch("/api/user/avatar/preset", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ avatarUrl: selectedAvatar }),
      })

      const data = await response.json()

      if (data.success) {
        onSelect(data.data.image)
        toast.success("头像设置成功")

        // 自动保存用户资料
        try {
          // 获取当前用户资料
          const profileResponse = await fetch('/api/user/profile', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })

          const profileData = await profileResponse.json()

          if (profileData.success) {
            // 更新用户资料，保存新头像
            await fetch('/api/user/profile', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...profileData.data,
                image: data.data.image
              }),
            })

            // 刷新页面以更新所有头像显示
            window.location.reload()
          }
        } catch (saveError) {
          console.error("保存用户资料失败:", saveError)
        }
      } else {
        toast.error(data.message || "设置失败")
      }
    } catch (error) {
      console.error("设置头像失败:", error)
      toast.error("设置失败")
    }
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">选择预设头像</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>选择预设头像</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[300px] p-4">
          <div className="grid grid-cols-4 gap-4">
            {presetAvatars.map((avatar) => (
              <div
                key={avatar}
                className={`relative aspect-square cursor-pointer rounded-lg border-2 p-1 transition-all hover:border-primary ${
                  selectedAvatar === avatar
                    ? "border-primary"
                    : "border-transparent"
                }`}
                onClick={() => setSelectedAvatar(avatar)}
              >
                <Image
                  src={avatar}
                  alt="Avatar"
                  className="rounded-lg"
                  fill
                  sizes="100px"
                />
              </div>
            ))}
          </div>
        </ScrollArea>
        <div className="flex justify-end gap-4">
          <DialogTrigger asChild>
            <Button variant="outline">取消</Button>
          </DialogTrigger>
          <Button onClick={handleSelect}>确定</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}