"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, Dialog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"

interface UserRechargeDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  currentBalance?: number
  onSuccess: (newBalance: number) => void
}

export function UserRechargeDialog({
  open,
  onOpenChange,
  userId,
  userName,
  currentBalance = 0,
  onSuccess,
}: UserRechargeDialogProps) {
  const [amount, setAmount] = useState("")
  const [remarks, setRemarks] = useState("")
  const [operationType, setOperationType] = useState("recharge") // recharge, deduct 或 reset
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 如果不是清零操作，需要验证金额
    let amountValue = 0
    if (operationType !== "reset") {
      amountValue = parseFloat(amount)
      if (isNaN(amountValue) || amountValue <= 0) {
        toast({
          title: "金额无效",
          description: `请输入有效的${operationType === "recharge" ? "充值" : "扣费"}金额`,
          variant: "destructive",
        })
        return
      }
    }

    setIsSubmitting(true)
    try {
      // 准备请求数据
      let requestData = {}

      if (operationType === "reset") {
        // 清零操作
        requestData = {
          amount: -currentBalance, // 负值，扣除当前所有余额
          paymentMethod: "system",
          remarks: remarks.trim() || "系统账户余额清零"
        }
        console.log(`账户余额清零操作: 用户ID=${userId}, 当前余额=${currentBalance}, 备注=${remarks.trim() || "系统账户余额清零"}`)
      } else {
        // 充值或扣费操作
        requestData = {
          amount: operationType === "recharge" ? amountValue : -amountValue,
          paymentMethod: "system",
          remarks: remarks.trim() || ""
        }
        console.log(`账户${operationType === "recharge" ? "充值" : "扣费"}操作: 用户ID=${userId}, 金额=${amountValue}, 备注=${remarks.trim() || ""}`)
      }

      const url = `/api/customers/${userId}/recharge`
      console.log('发送请求到:', url, '数据:', requestData)
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
        credentials: 'include',
      })

      // 改进错误处理，先获取响应文本
      const responseText = await response.text()
      console.log('原始响应文本:', responseText)

      // 尝试将文本解析为JSON
      let data
      try {
        data = JSON.parse(responseText)
      } catch (parseError) {
        console.error('解析JSON响应失败:', parseError)
        throw new Error('服务器返回了无效的响应格式')
      }

      if (response.ok && data.success) {
        toast({
          title: operationType === "reset" ? "余额清零成功" : (operationType === "recharge" ? "充值成功" : "扣费成功"),
          description: operationType === "reset"
            ? `已成功将用户 ${userName} 的账户余额清零`
            : (operationType === "recharge"
              ? `已成功为用户 ${userName} 充值 ¥${amountValue.toLocaleString()}`
              : `已成功从用户 ${userName} 扣除 ¥${amountValue.toLocaleString()}`),
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })

        // 重置表单
        setAmount("")
        setRemarks("")
        setOperationType("recharge")

        // 关闭对话框
        onOpenChange(false)

        // 获取实际的新余额
        let actualNewBalance = 0
        if (data.data && data.data.newBalance !== undefined) {
          actualNewBalance = data.data.newBalance
        } else if (data.data && data.data.balance !== undefined) {
          actualNewBalance = data.data.balance
        }

        console.log('操作成功，新余额:', actualNewBalance)

        // 通知父组件
        onSuccess(actualNewBalance)
      } else {
        toast({
          title: operationType === "reset" ? "余额清零失败" : (operationType === "recharge" ? "充值失败" : "扣费失败"),
          description: data.message || (operationType === "reset" ? "无法完成余额清零，请稍后重试" : (operationType === "recharge" ? "无法完成充值，请稍后重试" : "无法完成扣费，请稍后重试")),
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error(operationType === "reset" ? '余额清零错误:' : (operationType === "recharge" ? '充值错误:' : '扣费错误:'), error)
      toast({
        title: operationType === "reset" ? "余额清零失败" : (operationType === "recharge" ? "充值失败" : "扣费失败"),
        description: operationType === "reset" ? "清零余额时发生错误，请稍后重试" : (operationType === "recharge" ? "充值时发生错误，请稍后重试" : "扣费时发生错误，请稍后重试"),
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {operationType === "reset" ? "账户余额清零" : (operationType === "recharge" ? "账户充值" : "账户扣费")}
          </DialogTitle>
          <DialogDescription>
            {operationType === "reset"
              ? <>将用户 <span className="font-semibold">{userName}</span> 的账户余额清零。当前余额：<span className="font-semibold">¥{currentBalance.toLocaleString()}</span></>
              : (operationType === "recharge"
                ? <>为用户 <span className="font-semibold">{userName}</span> 的账户充值。当前余额：<span className="font-semibold">¥{currentBalance.toLocaleString()}</span></>
                : <>从用户 <span className="font-semibold">{userName}</span> 的账户扣费。当前余额：<span className="font-semibold">¥{currentBalance.toLocaleString()}</span></>)
            }
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="operationType" className="text-right">
                操作类型
              </Label>
              <div className="col-span-3 flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="recharge"
                    name="operationType"
                    value="recharge"
                    checked={operationType === "recharge"}
                    onChange={() => setOperationType("recharge")}
                    className="h-4 w-4 text-blue-600"
                  />
                  <Label htmlFor="recharge" className="cursor-pointer">充值</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="deduct"
                    name="operationType"
                    value="deduct"
                    checked={operationType === "deduct"}
                    onChange={() => setOperationType("deduct")}
                    className="h-4 w-4 text-red-600"
                  />
                  <Label htmlFor="deduct" className="cursor-pointer">扣费</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="reset"
                    name="operationType"
                    value="reset"
                    checked={operationType === "reset"}
                    onChange={() => setOperationType("reset")}
                    className="h-4 w-4 text-red-600"
                  />
                  <Label htmlFor="reset" className="cursor-pointer">清零余额</Label>
                </div>
              </div>
            </div>
            {operationType !== "reset" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="amount" className="text-right">
                  {operationType === "recharge" ? "充值金额" : "扣费金额"}
                </Label>
                <div className="col-span-3 relative">
                  <span className="absolute left-3 top-2.5">¥</span>
                  <Input
                    id="amount"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="pl-8"
                    placeholder="0.00"
                    required={operationType !== "reset"}
                  />
                </div>
              </div>
            )}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="remarks" className="text-right">
                备注说明
              </Label>
              <textarea
                id="remarks"
                value={remarks}
                onChange={(e) => setRemarks(e.target.value)}
                className="col-span-3 border rounded-md p-2 h-20"
                placeholder="可选填写备注信息"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
              取消
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || (operationType !== "reset" && (!amount || parseFloat(amount) <= 0))}
              className={operationType === "recharge" ? "bg-green-500 hover:bg-green-600 text-white" : "bg-red-500 hover:bg-red-600 text-white"}
            >
              {isSubmitting ? "处理中..." : operationType === "reset" ? "确认清零" : (operationType === "recharge" ? "确认充值" : "确认扣费")}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
