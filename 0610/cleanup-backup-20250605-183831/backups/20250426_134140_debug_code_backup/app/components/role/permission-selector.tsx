"use client"

import { useState, useEffect } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { toast } from "sonner"

interface Permission {
  id: string
  name: string
  code: string
  type: string
  description?: string
}

interface PermissionSelectorProps {
  selectedPermissions: string[]
  onChange: (permissions: string[]) => void
}

export function PermissionSelector({ selectedPermissions, onChange }: PermissionSelectorProps) {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [loading, setLoading] = useState(true)
  const [permissionsByType, setPermissionsByType] = useState<Record<string, Permission[]>>({})
  const [permissionTypes, setPermissionTypes] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState<string>("all")

  // 加载权限列表
  const loadPermissions = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/permissions")
      const data = await response.json()

      if (data.success) {
        setPermissions(data.data)
        
        // 按类型分组权限
        const groupedPermissions: Record<string, Permission[]> = {}
        const types = new Set<string>()
        
        data.data.forEach((permission: Permission) => {
          if (!groupedPermissions[permission.type]) {
            groupedPermissions[permission.type] = []
          }
          groupedPermissions[permission.type].push(permission)
          types.add(permission.type)
        })
        
        setPermissionsByType(groupedPermissions)
        setPermissionTypes(Array.from(types))
      } else {
        toast.error("加载权限列表失败")
      }
    } catch (error) {
      console.error("加载权限列表失败:", error)
      toast.error("加载权限列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 处理权限选择变化
  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedPermissions, permissionId])
    } else {
      onChange(selectedPermissions.filter(id => id !== permissionId))
    }
  }

  // 选择所有权限
  const selectAllPermissions = () => {
    onChange(permissions.map(p => p.id))
  }

  // 取消选择所有权限
  const deselectAllPermissions = () => {
    onChange([])
  }

  // 选择特定类型的所有权限
  const selectAllPermissionsByType = (type: string) => {
    const permissionIds = permissionsByType[type]?.map(p => p.id) || []
    const newSelectedPermissions = [...selectedPermissions]
    
    permissionIds.forEach(id => {
      if (!newSelectedPermissions.includes(id)) {
        newSelectedPermissions.push(id)
      }
    })
    
    onChange(newSelectedPermissions)
  }

  // 取消选择特定类型的所有权限
  const deselectAllPermissionsByType = (type: string) => {
    const permissionIds = permissionsByType[type]?.map(p => p.id) || []
    onChange(selectedPermissions.filter(id => !permissionIds.includes(id)))
  }

  // 初始加载
  useEffect(() => {
    loadPermissions()
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-x-2">
          <Badge variant="outline">已选择 {selectedPermissions.length} 项</Badge>
          <Badge variant="outline">共 {permissions.length} 项</Badge>
        </div>
        <div className="space-x-2">
          <button 
            type="button"
            className="text-xs text-blue-500 hover:underline"
            onClick={selectAllPermissions}
          >
            全选
          </button>
          <button 
            type="button"
            className="text-xs text-blue-500 hover:underline"
            onClick={deselectAllPermissions}
          >
            取消全选
          </button>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4 flex flex-wrap">
          <TabsTrigger value="all">全部</TabsTrigger>
          {permissionTypes.map(type => (
            <TabsTrigger key={type} value={type}>
              {type === "function" ? "功能权限" : 
               type === "data" ? "数据权限" : 
               type === "menu" ? "菜单权限" : type}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="all">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">所有权限</CardTitle>
              <CardDescription>
                管理所有可用权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {permissions.map(permission => (
                  <div key={permission.id} className="flex items-start space-x-2">
                    <Checkbox
                      id={`permission-${permission.id}`}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) => 
                        handlePermissionChange(permission.id, checked === true)
                      }
                    />
                    <div className="space-y-1">
                      <Label 
                        htmlFor={`permission-${permission.id}`}
                        className="font-medium"
                      >
                        {permission.name}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {permission.code}
                      </p>
                      {permission.description && (
                        <p className="text-xs text-muted-foreground">
                          {permission.description}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {permissionTypes.map(type => (
          <TabsContent key={type} value={type}>
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">
                    {type === "function" ? "功能权限" : 
                     type === "data" ? "数据权限" : 
                     type === "menu" ? "菜单权限" : type}
                  </CardTitle>
                  <div className="space-x-2">
                    <button 
                      type="button"
                      className="text-xs text-blue-500 hover:underline"
                      onClick={() => selectAllPermissionsByType(type)}
                    >
                      全选
                    </button>
                    <button 
                      type="button"
                      className="text-xs text-blue-500 hover:underline"
                      onClick={() => deselectAllPermissionsByType(type)}
                    >
                      取消全选
                    </button>
                  </div>
                </div>
                <CardDescription>
                  管理{type === "function" ? "功能" : 
                        type === "data" ? "数据" : 
                        type === "menu" ? "菜单" : type}相关权限
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {permissionsByType[type]?.map(permission => (
                    <div key={permission.id} className="flex items-start space-x-2">
                      <Checkbox
                        id={`permission-${permission.id}`}
                        checked={selectedPermissions.includes(permission.id)}
                        onCheckedChange={(checked) => 
                          handlePermissionChange(permission.id, checked === true)
                        }
                      />
                      <div className="space-y-1">
                        <Label 
                          htmlFor={`permission-${permission.id}`}
                          className="font-medium"
                        >
                          {permission.name}
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          {permission.code}
                        </p>
                        {permission.description && (
                          <p className="text-xs text-muted-foreground">
                            {permission.description}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
