"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Copy } from "lucide-react"

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(2, "名称至少2个字符").max(50, "名称最多50个字符"),
  code: z.string().min(2, "编码至少2个字符").max(50, "编码最多50个字符"),
  description: z.string().optional(),
})

interface RoleDuplicateDialogProps {
  role: {
    id: string
    name: string
    code: string
    type: string
    description?: string
    permissions?: string[]
    menuItems?: string[]
  }
}

export function RoleDuplicateDialog({ role }: RoleDuplicateDialogProps) {
  const router = useRouter()
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: `${role.name} 副本`,
      code: `${role.code}_COPY`,
      description: role.description || "",
    },
  })

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setLoading(true)
      
      const response = await fetch("/api/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...values,
          type: role.type,
          permissions: role.permissions || [],
          menuIds: role.menuItems || [],
          autoGenerateCode: false,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success("角色复制成功")
        setOpen(false)
        router.refresh()
      } else {
        toast.error(data.message || "角色复制失败")
      }
    } catch (error) {
      console.error("角色复制失败:", error)
      toast.error("角色复制失败")
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Copy className="mr-2 h-4 w-4" />
          复制
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>复制角色</DialogTitle>
          <DialogDescription>
            创建一个基于现有角色的副本，包含相同的权限和菜单设置。
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>角色名称</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入角色名称" {...field} />
                  </FormControl>
                  <FormDescription>
                    新角色的名称，默认为原角色名称加"副本"
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>角色编码</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入角色编码" {...field} />
                  </FormControl>
                  <FormDescription>
                    新角色的唯一编码，默认为原角色编码加"_COPY"
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>描述</FormLabel>
                  <FormControl>
                    <Input placeholder="请输入角色描述" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="submit" disabled={loading}>
                {loading ? "处理中..." : "创建副本"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
