import { toast } from 'react-hot-toast';
import { mutate } from 'swr';

interface NotificationButtonProps {
  id?: string;
}

export default function NotificationButton({ id }: NotificationButtonProps) {
  const handleMarkAsRead = async () => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: 'PUT',
      });
      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        mutate('/api/notifications/unread-count');
        mutate('/api/notifications');
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('标记通知为已读失败:', error);
      toast.error('标记已读失败');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'PUT',
      });
      const data = await response.json();
      
      if (data.success) {
        toast.success(data.message);
        mutate('/api/notifications/unread-count');
        mutate('/api/notifications');
      } else {
        toast.error(data.message);
      }
    } catch (error) {
      console.error('标记所有通知为已读失败:', error);
      toast.error('标记已读失败');
    }
  };
} 