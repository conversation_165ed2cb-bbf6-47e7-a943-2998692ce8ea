"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  MoreHorizontal,
  Eye,
  CreditCard,
  Check,
  X,
  Wallet,
  Power,
  PowerOff,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react"
import { useRouter } from "next/navigation"
import { CustomerType } from "@/app/types/customer"
import { getAccountTypeBadge, getUserVerificationBadge, getRoleBadge, getStatusBadge, getBusinessTypeName } from "@/app/lib/customer-utils"

interface CustomerTableProps {
  customers: CustomerType[]
  loading?: boolean
  onApprove: (id: string) => void
  onReject: (id: string) => void
  onDisableUser: (customer: CustomerType) => void
  onEnableUser: (customer: CustomerType) => void
  onRecharge: (customer: CustomerType) => void
  onCredit: (customer: CustomerType) => void
  // 分页相关属性
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
}

export function CustomerTable({
  customers,
  loading = false,
  onApprove,
  onReject,
  onDisableUser,
  onEnableUser,
  onRecharge,
  onCredit,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
}: CustomerTableProps) {
  const router = useRouter()

  return (
    <Card className="bg-white bg-opacity-90 shadow-md border border-blue-100 mx-auto overflow-hidden rounded-t-none" style={{ width: '90%' }}>
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
        <CardTitle className="text-blue-700">客户列表</CardTitle>
      </CardHeader>
      <CardContent className="p-0 overflow-x-auto" style={{ position: 'relative' }}>
        <Table className="w-full min-w-[1000px] relative border-collapse">
          <style jsx global>{`
            .sticky-col {
              position: sticky;
              background-color: white;
              z-index: 1;
            }
            .sticky-col-time1 {
              right: 120px;
            }
            .sticky-col-time2 {
              right: 240px;
            }
            .sticky-col-status {
              right: 60px;
            }
            .sticky-col-action {
              right: 0;
              box-shadow: -2px 0 5px rgba(0,0,0,0.05);
            }
            .sticky-col-time1::after,
            .sticky-col-time2::after,
            .sticky-col-status::after {
              content: '';
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              width: 1px;
              background-color: #e2e8f0;
            }
            tr:hover .sticky-col {
              background-color: #ebf5ff; /* 与hover:bg-blue-50相匹配 */
            }
            thead .sticky-col {
              background-color: #ebf8ff; /* 与bg-blue-50相匹配 */
              z-index: 2;
            }
            /* 增强冻结列的视觉效果 */
            .sticky-col {
              box-shadow: -2px 0 8px rgba(0,0,0,0.1);
            }
            /* 确保冻结列有足够的空间 */
            .sticky-col-time1, .sticky-col-time2, .sticky-col-status, .sticky-col-action {
              padding-left: 8px;
              padding-right: 8px;
            }
            /* 增强表头冻结列的样式 */
            thead .sticky-col {
              position: sticky;
              top: 0;
              background-color: #ebf8ff;
              font-weight: 600;
              border-bottom: 1px solid #e2e8f0;
            }
          `}</style>
          <TableHeader className="bg-blue-50">
            <TableRow>
              <TableHead className="whitespace-nowrap px-2 py-2">客户名称</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">邮箱</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">电话</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">客户类型</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">认证状态</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">角色</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">余额</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">授信额度</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">费率</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">业务类型</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2">创建者</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-time2">创建时间</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-time1">更新时间</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-status">状态</TableHead>
              <TableHead className="whitespace-nowrap px-2 py-2 text-right sticky-col sticky-col-action">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {customers && customers.length > 0 ? (
              customers.map((customer) => customer && (
                <TableRow
                  key={customer.id}
                  className={`hover:bg-blue-50 ${(customer.status === "pending" || customer.personalVerification === "pending" || customer.enterpriseVerification === "pending") ? 'bg-yellow-50 border-l-4 border-l-yellow-400' : ''}`}
                >
                  <TableCell className="font-medium whitespace-nowrap px-2 py-2">{(customer.name || customer.username || '-')}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{customer.email || '-'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{customer.phone || '-'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{getAccountTypeBadge(customer.accountType || '')}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{getUserVerificationBadge(customer.personalVerification, customer.enterpriseVerification)}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{getRoleBadge(customer.role || '')}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">¥{(customer.balance || 0).toLocaleString()}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">¥{customer.creditLimit?.toLocaleString() || '0'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">
                    {customer.rate !== undefined && customer.rate !== null ? `¥${customer.rate}` : '-'}
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">
                    {getBusinessTypeName(customer.businessType) || '-'}
                  </TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2">{customer.createdBy?.name || '-'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-time2">{customer.createdAt ? new Date(customer.createdAt).toLocaleString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'}) : '-'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-time1">{customer.updatedAt ? new Date(customer.updatedAt).toLocaleString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'}) : '-'}</TableCell>
                  <TableCell className="whitespace-nowrap px-2 py-2 sticky-col sticky-col-status">{getStatusBadge(customer.status || '')}</TableCell>
                  <TableCell className="text-right whitespace-nowrap px-2 py-2 sticky-col sticky-col-action">
                    <div className="flex justify-end items-center space-x-2">
                      {(customer.status === "pending" || customer.personalVerification === "pending" || customer.enterpriseVerification === "pending") ? (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-8 bg-yellow-500 hover:bg-yellow-600 text-white border-0 animate-pulse"
                            onClick={() => onApprove(customer.id)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            待审核资料
                          </Button>
                        </>
                      ) : (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">打开菜单</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => {
                              // 统一使用用户ID访问客户详情页
                              console.log('点击查看客户详情:', customer.id)
                              // 添加查询参数，确保使用ID而不是用户名
                              router.push(`/accounts/customer/${encodeURIComponent(customer.id)}`)
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              查看详情
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onRecharge(customer)}>
                              <CreditCard className="mr-2 h-4 w-4" />
                              充值账户
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onCredit(customer)}>
                              <Wallet className="mr-2 h-4 w-4" />
                              调整授信额度
                            </DropdownMenuItem>
                            {(customer.status === "pending" || customer.personalVerification === "pending" || customer.enterpriseVerification === "pending") && (
                              <>
                                <DropdownMenuItem onClick={() => onApprove(customer.id)}>
                                  <Eye className="mr-2 h-4 w-4 text-yellow-500" />
                                  查看认证资料
                                </DropdownMenuItem>
                              </>
                            )}
                            {customer.status === "active" && customer.role !== "ADMIN" && (
                              <DropdownMenuItem onClick={() => onDisableUser(customer)}>
                                <PowerOff className="mr-2 h-4 w-4 text-red-500" />
                                禁用账户
                              </DropdownMenuItem>
                            )}
                            {customer.status === "inactive" && (
                              <DropdownMenuItem onClick={() => onEnableUser(customer)}>
                                <Power className="mr-2 h-4 w-4 text-green-500" />
                                启用账户
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={15} className="h-24 text-center py-8">
                  没有找到匹配的客户
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
      {/* 始终显示分页控件，即使只有1页 */}
      <CardFooter className="flex items-center justify-center py-4 border-t border-blue-100">
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => onPageChange?.(1)}
              disabled={currentPage === 1 || loading}
              className="h-8 w-8"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => onPageChange?.(currentPage - 1)}
              disabled={currentPage === 1 || loading}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm text-muted-foreground">
              第 {currentPage} 页 / 共 {totalPages} 页
            </span>
            <Button
              variant="outline"
              size="icon"
              onClick={() => onPageChange?.(currentPage + 1)}
              disabled={currentPage === totalPages || loading}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => onPageChange?.(totalPages)}
              disabled={currentPage === totalPages || loading}
              className="h-8 w-8"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
    </Card>
  )
}
