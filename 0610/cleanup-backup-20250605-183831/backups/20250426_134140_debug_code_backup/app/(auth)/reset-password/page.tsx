"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { KeyIcon, MailIcon, Loader2, AlertCircle } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { PasswordInput } from "@/components/password-input"
import { usePasswordRules } from "@/hooks/use-password-rules"
import { checkPasswordStrengthClient, PasswordStrengthResult } from "@/lib/password-rules"

/**
 * 密码重置页面组件
 * 实现密码找回功能，包含以下步骤：
 * 1. 输入邮箱
 * 2. 获取验证码
 * 3. 输入新密码
 * 4. 确认新密码
 */
export default function ResetPasswordPage() {
  const router = useRouter()

  // 表单状态管理
  const [email, setEmail] = useState("")
  const [verificationCode, setVerificationCode] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)

  // 验证码发送状态
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [countdown, setCountdown] = useState(0)

  // 对话框状态
  const [showDialog, setShowDialog] = useState(false)
  const [dialogContent, setDialogContent] = useState({
    title: "",
    description: "",
    action: null as (() => void) | null
  })

  // 获取密码规则
  const { rules: passwordRules, isLoading: isLoadingRules } = usePasswordRules()

  // 密码强度检查状态
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrengthResult>({
    score: 0,
    hasLowerCase: false,
    hasUpperCase: false,
    hasNumber: false,
    hasSpecialChar: false,
    isLongEnough: false,
    isValid: false
  })

  // 表单错误状态
  const [errors, setErrors] = useState({
    email: "",
    verificationCode: "",
    newPassword: "",
    confirmPassword: "",
  })

  // 当密码变化时，检查密码强度
  useEffect(() => {
    if (newPassword) {
      setPasswordStrength(checkPasswordStrengthClient(newPassword, passwordRules))
    } else {
      setPasswordStrength({
        score: 0,
        hasLowerCase: false,
        hasUpperCase: false,
        hasNumber: false,
        hasSpecialChar: false,
        isLongEnough: false,
        isValid: false
      })
    }
  }, [newPassword, passwordRules])

  /**
   * 发送验证码
   * 1. 验证邮箱格式
   * 2. 发送验证码请求
   * 3. 开始倒计时
   */
  const handleSendCode = async () => {
    // 重置错误状态
    setErrors(prev => ({ ...prev, email: "" }))

    // 验证邮箱格式
    if (!email) {
      setErrors(prev => ({ ...prev, email: "请输入邮箱" }))
      return
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setErrors(prev => ({ ...prev, email: "请输入有效的邮箱地址" }))
      return
    }

    try {
      setIsSendingCode(true)
      const response = await fetch("/api/auth/send-reset-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 开始60秒倒计时
        setCountdown(60)
        const timer = setInterval(() => {
          setCountdown(prev => {
            if (prev <= 1) {
              clearInterval(timer)
              return 0
            }
            return prev - 1
          })
        }, 1000)

        toast.success("验证码已发送到您的邮箱")
      } else {
        // 根据不同的错误状态显示不同的提示信息
        if (response.status === 404) {
          setDialogContent({
            title: "邮箱未注册 ❌",
            description: "该邮箱尚未注册，您需要先注册账号才能使用密码重置功能。",
            action: () => {
              setShowDialog(false)
              router.replace("/login?tab=register")
            }
          })
          setShowDialog(true)
        } else if (response.status === 429) {
          setDialogContent({
            title: "请求过于频繁",
            description: data.message,
            action: null
          })
          setShowDialog(true)
        } else {
          setDialogContent({
            title: "发送失败",
            description: data.message || "验证码发送失败，请稍后重试",
            action: null
          })
          setShowDialog(true)
        }
      }
    } catch (error) {
      console.error("发送验证码错误:", error)
      setDialogContent({
        title: "网络错误",
        description: "请检查您的网络连接后重试",
        action: null
      })
      setShowDialog(true)
    } finally {
      setIsSendingCode(false)
    }
  }

  /**
   * 表单验证
   * 检查所有必填字段和密码匹配
   */
  const validateForm = () => {
    const newErrors = {
      email: "",
      verificationCode: "",
      newPassword: "",
      confirmPassword: "",
    }

    if (!email) {
      newErrors.email = "请输入邮箱"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = "请输入有效的邮箱地址"
    }

    if (!verificationCode) {
      newErrors.verificationCode = "请输入验证码"
    }

    if (!newPassword) {
      newErrors.newPassword = "请输入新密码"
    } else if (!passwordStrength.isValid) {
      newErrors.newPassword = "密码不符合要求"
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = "请确认新密码"
    } else if (confirmPassword !== newPassword) {
      newErrors.confirmPassword = "两次输入的密码不一致"
    }

    setErrors(newErrors)
    return !Object.values(newErrors).some(error => error !== "")
  }

  /**
   * 提交重置密码表单
   * 检查所有必填字段和密码匹配
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          verificationCode,
          newPassword,
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setDialogContent({
          title: "✅ 重置成功",
          description: "密码已重置成功，请使用新密码登录。",
          action: () => {
            router.push("/login")
          }
        })
        setShowDialog(true)
      } else {
        setDialogContent({
          title: "❌ 重置失败",
          description: data.message || "密码重置失败，请稍后重试",
          action: null
        })
        setShowDialog(true)
      }
    } catch (error) {
      console.error("重置密码错误:", error)
      setDialogContent({
        title: "❌ 网络错误",
        description: "请检查您的网络连接后重试",
        action: null
      })
      setShowDialog(true)
    }
  }

  return (
    <>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md space-y-8 p-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
              重置密码
            </h2>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              请填写以下信息来重置您的密码
            </p>
          </div>

          <form onSubmit={handleSubmit} className="mt-8 space-y-6">
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-gray-700 dark:text-gray-300">
                邮箱
              </Label>
              <div className="relative">
                <MailIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    setErrors(prev => ({ ...prev, email: "" }))
                  }}
                  className="pl-10 w-full"
                  placeholder="请输入邮箱"
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-500 mt-1 flex items-center">
                  <span className="mr-1">⚠</span>
                  {errors.email}
                </p>
              )}
            </div>

            {/* 验证码输入 */}
            <div className="space-y-2">
              <Label htmlFor="verificationCode" className="text-gray-700 dark:text-gray-300">
                验证码
              </Label>
              <div className="flex space-x-2">
                <div className="flex-1 relative">
                  <KeyIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <Input
                    id="verificationCode"
                    type="text"
                    value={verificationCode}
                    onChange={(e) => {
                      setVerificationCode(e.target.value)
                      setErrors(prev => ({ ...prev, verificationCode: "" }))
                    }}
                    className="pl-10 w-full"
                    placeholder="请输入验证码"
                  />
                </div>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleSendCode}
                  disabled={countdown > 0 || isSendingCode}
                  className={`min-w-[120px] flex items-center justify-center transition-all ${
                    countdown > 0 || isSendingCode
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-blue-600 hover:text-white'
                  }`}
                >
                  {isSendingCode ? (
                    <div className="flex items-center justify-center">
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      发送中
                    </div>
                  ) : countdown > 0 ? (
                    `${countdown}秒后重试`
                  ) : (
                    "获取验证码"
                  )}
                </Button>
              </div>
              {errors.verificationCode && (
                <p className="text-sm text-red-500 mt-1 flex items-center">
                  <span className="mr-1">⚠</span>
                  {errors.verificationCode}
                </p>
              )}
            </div>

            {/* 新密码输入 */}
            <PasswordInput
              id="newPassword"
              label="新密码"
              value={newPassword}
              onChange={setNewPassword}
              placeholder="请输入新密码"
              error={errors.newPassword}
              disabled={false}
              showStrengthIndicator={true}
              rules={passwordRules}
            />

            {/* 确认新密码输入 */}
            <PasswordInput
              id="confirmPassword"
              label="确认新密码"
              value={confirmPassword}
              onChange={setConfirmPassword}
              placeholder="请确认新密码"
              error={errors.confirmPassword}
              disabled={false}
              showStrengthIndicator={false}
            />

            {/* 提交按钮 */}
            <div>
              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                重置密码
              </Button>
            </div>

            {/* 返回登录链接 */}
            <div className="text-center">
              <button
                type="button"
                onClick={() => router.replace("/login?tab=login")}
                className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                返回登录
              </button>
            </div>
          </form>
        </Card>
      </div>

      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {dialogContent.title.includes("✅") ? (
                <div className="h-5 w-5 text-green-500">✓</div>
              ) : (
                <AlertCircle className="h-5 w-5 text-red-500" />
              )}
              {dialogContent.title}
            </DialogTitle>
            <DialogDescription>
              {dialogContent.description}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setShowDialog(false)}
            >
              关闭
            </Button>
            {dialogContent.action && (
              <Button
                onClick={() => {
                  setShowDialog(false)
                  dialogContent.action?.()
                }}
              >
                {dialogContent.title.includes("未注册") ? "去注册" : "去登录"}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}