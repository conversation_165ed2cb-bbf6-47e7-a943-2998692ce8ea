import { prisma } from "@/app/lib/prisma"
import { Metadata } from "next"

/**
 * 从数据库获取系统设置并生成元数据
 * 用于动态设置网站标题、描述等元数据
 */
export async function getMetadata(): Promise<Metadata> {
  try {
    // 从数据库获取系统设置
    const settings = await prisma.systemSettings.findFirst()

    // 如果没有设置记录，返回默认元数据
    if (!settings) {
      return {
        title: "外呼管理系统",
        description: "高效的外呼任务管理平台",
        applicationName: "外呼管理系统",
        keywords: ["外呼", "管理系统", "任务管理"],
        other: {
          "apple-mobile-web-app-title": "外呼系统",
        },
      }
    }

    // 处理关键词，如果是字符串则转换为数组
    const keywords = settings.keywords
      ? typeof settings.keywords === 'string'
        ? settings.keywords.split(',').map(k => k.trim())
        : settings.keywords
      : ["外呼", "管理系统", "任务管理"]

    // 返回基于系统设置的元数据
    return {
      title: settings.siteName,
      description: settings.description || "高效的外呼任务管理平台",
      applicationName: settings.applicationName || settings.siteName,
      keywords: keywords,
      other: {
        "apple-mobile-web-app-title": settings.appleMobileWebAppTitle || settings.siteName,
      },
    }
  } catch (error) {
    console.error("获取元数据失败:", error)
    // 出错时返回默认元数据
    return {
      title: "外呼管理系统",
      description: "高效的外呼任务管理平台",
      applicationName: "外呼管理系统",
      keywords: ["外呼", "管理系统", "任务管理"],
      other: {
        "apple-mobile-web-app-title": "外呼系统",
      },
    }
  }
}
