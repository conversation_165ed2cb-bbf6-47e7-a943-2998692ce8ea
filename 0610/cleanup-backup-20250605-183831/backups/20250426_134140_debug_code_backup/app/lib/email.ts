import nodemailer from 'nodemailer'

/**
 * 邮件发送配置接口
 */
interface EmailConfig {
  to: string        // 收件人邮箱
  subject: string   // 邮件主题
  html: string      // 邮件HTML内容
}

/**
 * 创建邮件发送器
 * 使用 nodemailer 创建一个 SMTP 传输器
 * 
 * 配置说明：
 * - host: SMTP服务器地址（默认使用QQ邮箱的SMTP服务器）
 * - port: SMTP服务器端口（QQ邮箱使用465端口）
 * - secure: 是否使用SSL/TLS（465端口需要设置为true）
 * - auth: 认证信息
 *   - user: 发件人邮箱
 *   - pass: 邮箱授权码（不是邮箱密码）
 */
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.qq.com',
  port: Number(process.env.EMAIL_PORT) || 465,
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,     // 发件人邮箱
    pass: process.env.EMAIL_PASS,     // 邮箱授权码
  },
})

/**
 * 发送邮件的工具函数
 * 
 * @param config - 邮件配置对象，包含收件人、主题和HTML内容
 * @returns Promise<void>
 * 
 * 使用示例:
 * ```typescript
 * await sendEmail({
 *   to: '<EMAIL>',
 *   subject: '验证码',
 *   html: '<p>您的验证码是：123456</p>'
 * })
 * ```
 * 
 * 错误处理：
 * - 如果发送失败，会抛出异常
 * - 异常信息会包含具体的错误原因
 */
export async function sendEmail(config: EmailConfig): Promise<void> {
  try {
    // 验证环境变量是否配置
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
      throw new Error('邮件服务未配置，请检查环境变量')
    }

    // 发送邮件
    await transporter.sendMail({
      from: `"系统通知" <${process.env.EMAIL_USER}>`,
      ...config,
    })

    // 打印发送日志（可选）
    console.log('邮件发送成功:', {
      to: config.to,
      subject: config.subject
    })
  } catch (error) {
    // 错误日志记录
    console.error('发送邮件失败:', error)
    
    // 抛出友好的错误信息
    if (error instanceof Error) {
      throw new Error(`邮件发送失败: ${error.message}`)
    } else {
      throw new Error('邮件发送失败，请稍后重试')
    }
  }
} 