import { prisma } from "@/lib/prisma";
import { sendDirectEmail } from "./direct-email";
import { sendEmail } from "./email";

/**
 * 创建系统通知并发送邮件
 *
 * @param userId 用户ID
 * @param title 通知标题
 * @param content 通知内容
 * @param emailSubject 邮件主题
 * @param emailHtml 邮件HTML内容
 * @param priority 优先级
 * @returns 创建结果
 */
export async function createSystemNotificationWithEmail(
  userId: string,
  title: string,
  content: string,
  emailSubject: string,
  emailHtml: string,
  priority: string = 'normal'
) {
  try {
    // 1. 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      console.error(`用户不存在: ${userId}`);
      return { success: false, error: '用户不存在' };
    }

    // 检查通知类型是否存在
    const notificationType = await prisma.notificationType.findFirst({
      where: {
        OR: [
          { id: 1 },
          { code: 'SYSTEM' }
        ]
      }
    });

    if (!notificationType) {
      console.log('通知类型不存在，创建默认类型');
      await prisma.notificationType.create({
        data: {
          id: 1,
          code: 'SYSTEM',
          name: '系统通知',
          description: '系统相关的通知'
        }
      });
    }

    // 2. 创建通知
    const notification = await prisma.notification.create({
      data: {
        title,
        content,
        priority,
        sendToAll: false,
        recipients: [userId],
        status: 'published',
        typeId: 1, // 系统通知类型ID
        publishedAt: new Date(),
        allowBatchMarkRead: true,
        readCount: 0,
        readRate: 0,
        totalRecipients: 1,
        createdBy: 'system'
      }
    });

    console.log('通知创建成功:', notification);

    // 3. 创建用户通知关联
    const userNotification = await prisma.userNotification.create({
      data: {
        userId,
        notificationId: notification.id,
        read: false
      }
    });

    console.log('用户通知关联创建成功:', userNotification);

    // 4. 发送邮件（如果用户有邮箱）
    let emailResult = { success: false };
    if (user.email) {
      try {
        // 先标记邮件已加入队列，不阻塞主流程
        await prisma.userNotification.update({
          where: { id: userNotification.id },
          data: {
            emailSent: true,
            emailSentAt: new Date()
          }
        });

        // 异步发送邮件，不阻塞主流程
        // 使用setTimeout来异步处理邮件发送
        setTimeout(async () => {
          try {
            console.log('开始异步发送邮件到:', user.email, '主题:', emailSubject);

            // 先尝试将邮件保存到队列，确保即使直接发送失败也能后续处理
            try {
              await prisma.emailQueue.create({
                data: {
                  recipient: user.email,
                  subject: emailSubject,
                  content: emailHtml,
                  status: 'pending'
                }
              });
              console.log('邮件已添加到队列作为备份:', user.email);
            } catch (queueError) {
              console.error('将邮件添加到队列失败:', queueError);
            }

            // 然后尝试直接发送
            // 使用与通知系统相同的邮件发送方法
            try {
              await sendEmail({
                to: user.email,
                subject: emailSubject,
                html: emailHtml
              });
              console.log('使用sendEmail发送邮件成功:', user.email, emailSubject);
            } catch (emailSendError) {
              console.error('使用sendEmail发送邮件失败，尝试使用sendDirectEmail:', emailSendError);
              // 如果sendEmail失败，尝试使用sendDirectEmail
              const result = await sendDirectEmail(
                user.email,
                emailSubject,
                emailHtml
              );
              console.log('使用sendDirectEmail邮件发送成功:', result);
            }

            // 更新邮件队列状态为已发送
            try {
              await prisma.emailQueue.updateMany({
                where: {
                  recipient: user.email,
                  subject: emailSubject,
                  status: 'pending'
                },
                data: {
                  status: 'sent',
                  sentAt: new Date()
                }
              });
            } catch (updateError) {
              console.error('更新邮件队列状态失败:', updateError);
            }
          } catch (emailError) {
            console.error('直接发送邮件失败:', emailError);
            // 邮件已经加入队列，将由定时任务处理
          }
        }, 0);

        // 不等待邮件发送结果，直接返回成功
        emailResult = { success: true, async: true };
      } catch (error) {
        console.error('处理邮件发送失败:', error);
        emailResult = {
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        };
      }
    }

    return {
      success: true,
      notification,
      emailResult
    };
  } catch (error) {
    console.error('创建通知和发送邮件失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 创建账户禁用通知和邮件
 *
 * @param userId 用户ID
 * @param reason 禁用原因
 * @returns 创建结果
 */
export async function createAccountDisabledNotification(userId: string, reason: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    const title = '账户已禁用';
    const content = `您的账户已被管理员禁用。\n\n禁用原因：${reason}`;

    const emailSubject = '您的账户已被禁用';
    const emailHtml = `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; color: #333;">
        <h2 style="color: #e53935; text-align: center; margin-bottom: 20px;">账户禁用通知</h2>
        <div style="background-color: #f5f5f5; border-left: 4px solid #e53935; padding: 15px; margin-bottom: 20px;">
          <p style="margin: 0; font-size: 16px;">尊敬的 ${user.name || user.username}：</p>
          <p style="margin: 10px 0; font-size: 16px;">您的账户已被管理员禁用。</p>
          <p style="margin: 10px 0; font-size: 16px;"><strong>禁用原因：</strong>${reason}</p>
        </div>
        <p style="font-size: 14px;">如有疑问，请联系系统管理员。</p>
        <p style="font-size: 14px; margin-top: 30px;">此致，</p>
        <p style="font-size: 14px;">系统管理团队</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 12px;">
          此邮件由系统自动发送，请勿回复。
        </div>
      </div>
    `;

    return await createSystemNotificationWithEmail(
      userId,
      title,
      content,
      emailSubject,
      emailHtml,
      'high'
    );
  } catch (error) {
    console.error('创建账户禁用通知失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 创建账户启用通知和邮件
 *
 * @param userId 用户ID
 * @returns 创建结果
 */
export async function createAccountEnabledNotification(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    const title = '账户已启用';
    const content = '您的账户已被管理员启用，现在您可以正常登录和使用系统了。';

    const emailSubject = '您的账户已被启用';
    const emailHtml = `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; color: #333;">
        <h2 style="color: #4caf50; text-align: center; margin-bottom: 20px;">账户启用通知</h2>
        <div style="background-color: #f5f5f5; border-left: 4px solid #4caf50; padding: 15px; margin-bottom: 20px;">
          <p style="margin: 0; font-size: 16px;">尊敬的 ${user.name || user.username}：</p>
          <p style="margin: 10px 0; font-size: 16px;">您的账户已被管理员启用，现在您可以正常登录和使用系统了。</p>
        </div>
        <p style="font-size: 14px;">如有疑问，请联系系统管理员。</p>
        <p style="font-size: 14px; margin-top: 30px;">此致，</p>
        <p style="font-size: 14px;">系统管理团队</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 12px;">
          此邮件由系统自动发送，请勿回复。
        </div>
      </div>
    `;

    return await createSystemNotificationWithEmail(
      userId,
      title,
      content,
      emailSubject,
      emailHtml,
      'normal'
    );
  } catch (error) {
    console.error('创建账户启用通知失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 创建账户锁定通知和邮件
 * 当用户多次登录失败被锁定时发送通知
 *
 * @param userId 用户ID
 * @param reason 锁定原因
 * @returns 创建结果
 */
export async function createAccountLockedNotification(userId: string, reason: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return { success: false, error: '用户不存在' };
    }

    const title = '账户已被锁定';
    const content = `您的账户已被系统临时锁定。\n\n锁定原因：${reason}`;

    const emailSubject = '安全提醒：您的账户已被锁定';
    const emailHtml = `
      <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; color: #333;">
        <h2 style="color: #ff9800; text-align: center; margin-bottom: 20px;">账户锁定通知</h2>
        <div style="background-color: #f5f5f5; border-left: 4px solid #ff9800; padding: 15px; margin-bottom: 20px;">
          <p style="margin: 0; font-size: 16px;">尊敬的 ${user.name || user.username}：</p>
          <p style="margin: 10px 0; font-size: 16px;">您的账户已被系统临时锁定。</p>
          <p style="margin: 10px 0; font-size: 16px;"><strong>锁定原因：</strong>${reason}</p>
        </div>
        <div style="background-color: #fff8e1; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
          <p style="margin: 0; font-size: 14px;">请等待锁定时间结束后再尝试登录。如果您忘记了密码，可以使用“忘记密码”功能重置密码。</p>
        </div>
        <p style="font-size: 14px;">如果这不是您的操作，请立即联系系统管理员。</p>
        <p style="font-size: 14px; margin-top: 30px;">此致，</p>
        <p style="font-size: 14px;">系统安全团队</p>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #999; font-size: 12px;">
          此邮件由系统自动发送，请勿回复。
        </div>
      </div>
    `;

    return await createSystemNotificationWithEmail(
      userId,
      title,
      content,
      emailSubject,
      emailHtml,
      'high'
    );
  } catch (error) {
    console.error('创建账户锁定通知失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}
