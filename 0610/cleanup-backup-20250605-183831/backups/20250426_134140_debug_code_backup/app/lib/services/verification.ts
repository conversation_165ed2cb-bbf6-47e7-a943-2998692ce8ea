/**
 * 验证码服务
 * 提供验证码生成、存储和验证功能
 */

import { redis } from "@/lib/redis"

/**
 * 生成6位数字验证码
 *
 * @returns 6位数字验证码
 */
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

/**
 * 将验证码存储到Redis
 *
 * @param email 邮箱
 * @param code 验证码
 * @param type 验证码类型，默认为register
 * @param expiryInSeconds 过期时间（秒），默认300秒（5分钟）
 */
export async function storeVerificationCode(
  email: string,
  code: string,
  type: "register" | "reset" | "email_change" = "register",
  expiryInSeconds: number = 300
): Promise<void> {
  const key = `verification:${type}:${email}`
  try {
    await redis.set(key, code)
    await redis.expire(key, expiryInSeconds)
    console.log(`验证码已存储到Redis: ${key}, 过期时间: ${expiryInSeconds}秒`)
    return Promise.resolve()
  } catch (error) {
    console.error(`存储验证码到Redis失败: ${error}`)
    return Promise.reject("存储验证码失败")
  }
}

/**
 * 校验验证码
 *
 * @param email 邮箱
 * @param code 验证码
 * @param type 验证码类型，默认为register
 * @returns 是否验证成功
 */
export async function verifyVerificationCode(
  email: string,
  code: string,
  type: "register" | "reset" | "email_change" = "register"
): Promise<boolean> {
  const key = `verification:${type}:${email}`
  try {
    const storedCode = await redis.get(key)
    console.log(`验证码校验: 邮箱=${email}, 输入验证码=${code}, 存储验证码=${storedCode}`)

    if (!storedCode) {
      console.log(`验证码不存在或已过期: ${key}`)
      return false
    }

    const isValid = code === storedCode

    // 如果验证成功，删除验证码
    if (isValid) {
      await redis.del(key)
      console.log(`验证成功，已删除验证码: ${key}`)
    } else {
      console.log(`验证失败: 输入验证码=${code}, 存储验证码=${storedCode}`)
    }

    return isValid
  } catch (error) {
    console.error(`验证码校验失败: ${error}`)
    return false
  }
}

/**
 * 从数据库验证验证码
 *
 * @param email 邮箱
 * @param code 验证码
 * @param type 验证码类型
 * @returns 验证结果
 */
export async function verifyVerificationCodeFromDB(
  email: string,
  code: string,
  type: string
): Promise<boolean> {
  try {
    // 从验证码表查找未过期的验证码
    const { prisma } = await import("@/lib/prisma")
    const verificationCode = await prisma.verificationCode.findFirst({
      where: {
        email,
        code,
        type,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    // 如果找到验证码且未过期，返回true
    if (verificationCode) {
      // 使用后删除验证码
      await prisma.verificationCode.delete({
        where: { id: verificationCode.id }
      })
      return true
    }

    return false
  } catch (error) {
    console.error("数据库验证码校验失败:", error)
    return false
  }
}