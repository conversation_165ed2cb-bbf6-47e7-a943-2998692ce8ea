"use client"

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react"

// 用户类型定义
export interface User {
  id: string
  username: string
  role: "admin" | "user"
  email?: string
  name?: string
}

// 认证上下文类型
export interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  login: (username: string, password: string) => Promise<void>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
  checkPermission: (permission: string) => Promise<boolean>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 初始化时检查认证状态
  useEffect(() => {
    // 添加小延迟，确保cookie已经设置好
    const timer = setTimeout(() => {
      checkAuth()
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // 检查认证状态
  const checkAuth = async () => {
    try {
      setLoading(true)
      console.log("开始检查认证状态...")
      const response = await fetch("/api/auth/me", {
        credentials: 'include', // 确保包含cookie
        headers: {
          "Cache-Control": "no-cache", // 避免缓存问题
        }
      })

      console.log("认证检查响应状态:", response.status)

      if (!response.ok) {
        throw new Error(`认证失败: ${response.status}`)
      }

      const data = await response.json()

      if (data.success && data.data) {
        setUser(data.data)
      } else {
        setUser(null)
      }

      setError(null)
    } catch (error) {
      console.error("认证检查失败:", error)
      setUser(null)
      setError("认证检查失败")
    } finally {
      setLoading(false)
    }
  }

  // 登录
  const login = async (username: string, password: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      })

      const data = await response.json()

      if (!response.ok || !data.success) {
        throw new Error(data.message || "登录失败")
      }

      if (data.data) {
        setUser(data.data)
      } else {
        throw new Error("未获取到用户信息")
      }
    } catch (error) {
      console.error("登录失败:", error)
      setError(error instanceof Error ? error.message : "登录失败")
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 登出
  const logout = async () => {
    try {
      setLoading(true)

      const response = await fetch("/api/auth/logout", {
        method: "POST",
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || "登出失败")
      }

      setUser(null)
      setError(null)
    } catch (error) {
      console.error("登出失败:", error)
      setError(error instanceof Error ? error.message : "登出失败")
    } finally {
      setLoading(false)
    }
  }

  // 检查权限
  const checkPermission = async (permission: string): Promise<boolean> => {
    try {
      const response = await fetch("/api/auth/check-permission", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ permission }),
      })

      if (!response.ok) {
        return false
      }

      const data = await response.json()
      return data.success && data.hasPermission
    } catch (error) {
      console.error("权限检查失败:", error)
      return false
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        error,
        login,
        logout,
        checkAuth,
        checkPermission,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

// 自定义钩子，用于访问认证上下文
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error("useAuth 必须在 AuthProvider 内使用")
  }

  return context
}