/**
 * 根布局组件
 * 为整个应用提供基础布局结构和全局配置
 *
 * 特性：
 * - 配置网站元数据（标题、描述等）
 * - 集成主题切换功能
 * - 提供认证上下文
 * - 支持 Toast 通知
 * - 使用 Inter 字体
 *
 * @example
 * ```tsx
 * // 页面会自动被包裹在这个布局中
 * <RootLayout>
 *   <YourPage />
 * </RootLayout>
 * ```
 */

import "@/app/globals.css"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
// import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"
import Script from "next/script"
import { RoutePrefetcher } from "@/components/route-prefetcher"
import { SidebarProvider } from "@/components/ui/sidebar"
import { getMetadata } from "@/lib/get-metadata"
import { SystemInitializer } from "@/app/components/system-initializer"
import { Providers } from "./providers"
import { getServerSession } from "next-auth/next"
import { options } from "./api/auth/[...nextauth]/options"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  preload: true,
  adjustFontFallback: true,
  fallback: ['system-ui', 'arial'],
})

// 动态生成元数据
export async function generateMetadata(): Promise<Metadata> {
  // 获取动态元数据
  const dynamicMetadata = await getMetadata()

  // 添加静态元数据
  return {
    ...dynamicMetadata,
    generator: "v0.dev",
    authors: [{ name: "Your Company" }],
    manifest: "/manifest.json",
    other: {
      ...dynamicMetadata.other,
      "apple-mobile-web-app-capable": "yes",
      "apple-mobile-web-app-status-bar-style": "default",
      "format-detection": "telephone=no",
      "mobile-web-app-capable": "yes",
    }
  }
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" }
  ],
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: "cover",
}

interface RootLayoutProps {
  /** 子组件，将被包裹在布局中 */
  children: React.ReactNode
}

export default async function RootLayout({ children }: RootLayoutProps) {
  // 获取服务器端会话
  const session = await getServerSession(options);

  return (
    <html
      lang="zh-CN"
      suppressHydrationWarning
    >
      <head>
        <link
          rel="preload"
          href="/dashboard"
          as="document"
        />
        <link
          rel="preload"
          href="/tasks"
          as="document"
        />
        <link
          rel="preconnect"
          href={process.env.NEXT_PUBLIC_API_URL}
          crossOrigin="anonymous"
        />
        <link
          rel="apple-touch-icon"
          href="/icons/icon-192x192.png"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="外呼系统" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <Script
          id="register-sw"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js').then(
                    function(registration) {
                      console.log('ServiceWorker registration successful');
                    },
                    function(err) {
                      console.log('ServiceWorker registration failed: ', err);
                    }
                  );
                });
              }
            `,
          }}
        />
      </head>
      <body className={`${inter.className} overflow-x-hidden`}>
        <Providers session={session}>
          <SidebarProvider>
            <TooltipProvider>
              <RoutePrefetcher />
              <SystemInitializer />
              {children}
              <Toaster />
            </TooltipProvider>
          </SidebarProvider>
        </Providers>
      </body>
    </html>
  )
}

import './globals.css'