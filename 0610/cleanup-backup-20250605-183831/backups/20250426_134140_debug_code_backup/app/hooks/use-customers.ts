"use client"

import { useState, useEffect, useMemo } from "react"
import { toast } from "@/components/ui/use-toast"
import { CustomerType } from "@/app/types/customer"

export function useCustomers() {
  const [customers, setCustomers] = useState<CustomerType[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [accountTypeFilter, setAccountTypeFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [loading, setLoading] = useState(true)
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(5) // 减小每页显示数量，使分页更容易触发

  // 刷新客户列表的函数
  const refreshCustomers = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  // 页面加载时从管理员API获取用户列表，遵循ABAC权限模型
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/admin/users', {
          credentials: 'include', // 包含认证Cookie
        })
        const data = await response.json()

        if (response.ok && data.success) {
          // 使用 API 返回的用户数据
          setCustomers(data.data.users || [])
        } else {
          console.error('获取用户列表失败:', data.message)
          // 如果 API 调用失败，显示空数组
          setCustomers([])

          // 显示错误提示
          toast({
            title: "获取用户列表失败",
            description: data.message || "请检查您的权限或网络连接",
            variant: "destructive",
          })
        }
      } catch (error) {
        console.error('获取用户列表错误:', error)
        // 如果发生错误，显示空数组
        setCustomers([])

        // 显示错误提示
        toast({
          title: "获取用户列表失败",
          description: "请检查您的网络连接并刷新页面",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [refreshTrigger]) // 添加refreshTrigger依赖，当其变化时重新获取数据

  // 筛选客户
  const filteredCustomers = useMemo(() => {
    return customers.filter((customer) => {
      // 文字搜索
      const textMatch =
        searchTerm === "" ||
        customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.phone?.includes(searchTerm)

      // 账户类型筛选
      const typeMatch = accountTypeFilter === "all" || customer.accountType === accountTypeFilter

      // 状态筛选
      const statusMatch = statusFilter === "all" || customer.status === statusFilter

      return textMatch && typeMatch && statusMatch
    })
  }, [customers, searchTerm, accountTypeFilter, statusFilter])

  // 计算总页数
  const totalPages = Math.ceil(filteredCustomers.length / pageSize)

  // 获取当前页的数据
  const paginatedCustomers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    return filteredCustomers.slice(startIndex, startIndex + pageSize)
  }, [filteredCustomers, currentPage, pageSize])

  // 导出数据
  const exportData = (format: string) => {
    // 实现导出逻辑
    toast({
      title: "导出成功",
      description: `数据已成功导出为 ${format.toUpperCase()} 格式`,
    })
  }

  // 重置筛选条件
  const resetFilters = () => {
    setSearchTerm("")
    setAccountTypeFilter("all")
    setStatusFilter("all")
    setCurrentPage(1) // 重置到第一页
  }

  // 页面变化处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  return {
    customers,
    setCustomers,
    filteredCustomers,
    paginatedCustomers, // 分页后的数据
    searchTerm,
    setSearchTerm,
    accountTypeFilter,
    setAccountTypeFilter,
    statusFilter,
    setStatusFilter,
    loading,
    exportData,
    resetFilters,
    refreshCustomers, // 暴露刷新函数
    // 分页相关
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    totalPages,
    handlePageChange,
  }
}
