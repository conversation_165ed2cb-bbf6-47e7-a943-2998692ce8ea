import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "菜单重排序API");
    if (response) {
      return NextResponse.json(
        { success: false, error: "未授权访问或没有管理员权限" },
        { status: 401 }
      )
    }

    // 管理员默认有更新菜单的权限

    const { updates } = await request.json()

    // 使用事务批量更新菜单顺序
    await prisma.$transaction(
      updates.map(({ id, order }: { id: string; order: number }) =>
        prisma.menu.update({
          where: { id },
          data: { order },
        })
      )
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Reorder menus error:", error)
    return NextResponse.json(
      { success: false, error: "更新菜单排序失败" },
      { status: 500 }
    )
  }
}