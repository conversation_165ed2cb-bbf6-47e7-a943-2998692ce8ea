/**
 * 调试 API 路由
 * 用于检查用户的登录状态和认证信息
 */

import { cookies } from "next/headers"
import { NextRequest } from "next/server"
import { AuthService } from "@/lib/auth-service"
import { verify } from "jsonwebtoken"

// JWT 配置
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"
const TOKEN_NAME = "token"

export async function GET(request: NextRequest) {
  try {
    // 1. 获取所有 cookie
    const cookieStore = cookies()
    const allCookies = cookieStore.getAll()
    const cookieNames = allCookies.map(c => c.name)
    
    // 2. 检查认证 cookie
    const authCookie = cookieStore.get(TOKEN_NAME)
    
    // 3. 尝试获取当前用户
    const user = await AuthService.getCurrentUser()
    
    // 4. 如果有 token，尝试解析
    let tokenData = null
    if (authCookie?.value) {
      try {
        tokenData = verify(authCookie.value, JWT_SECRET)
      } catch (error) {
        console.error("Token 解析失败:", error)
      }
    }
    
    return Response.json({
      success: true,
      cookieNames,
      hasAuthCookie: !!authCookie,
      isLoggedIn: !!user,
      user: user ? {
        id: user.id,
        username: user.username,
        email: user.email,
        roleCode: user.roleCode,
        permissions: user.permissions
      } : null,
      tokenData: tokenData ? {
        sub: (tokenData as any).sub,
        userId: (tokenData as any).userId,
        username: (tokenData as any).username,
        roleCode: (tokenData as any).roleCode
      } : null
    })
  } catch (error) {
    console.error("调试接口错误:", error)
    return Response.json(
      { success: false, error: "获取调试信息失败" },
      { status: 500 }
    )
  }
} 