import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AuthMiddleware } from '@/lib/middleware/auth-middleware';

// 定义通知类型
type NotificationWithUserStatus = {
  id: string;
  title: string;
  userNotifications: Array<{
    id: string;
    read: boolean;
  }>;
};

/**
 * 标记所有通知为已读
 *
 * @route PUT /api/notifications/read-all
 * @access private - 需要认证和权限
 */
export async function PUT(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "标记所有通知为已读API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    const userId = user.id;

    // 检查用户是否为管理员
    const userRole = await prisma.user.findUnique({
      where: { id: user.id },
      select: { roleCode: true }
    });

    const isAdmin = userRole?.roleCode === 'ADMIN';
    console.log('当前用户角色:', userRole?.roleCode, '是否为管理员:', isAdmin);

    // 获取所有未读的通知
    let unreadNotificationsQuery;

    // 无论是管理员还是普通用户，都只能标记全局通知和发送给自己的通知为已读
    console.log('获取全局通知和发送给自己的未读通知');
    unreadNotificationsQuery = prisma.$queryRaw`
      SELECT n.id
      FROM notification n
      LEFT JOIN "user_notification" un
        ON n.id = un."notificationId"
        AND un."userId" = ${user.id}
        AND un.read = true
      WHERE
        n.status = 'published' AND
        (n."sendToAll" = true OR (n."sendToAll" = false AND n.recipients::text ILIKE ${'%' + user.id + '%'})) AND
        un.id IS NULL
    `;

    const unreadNotifications = await unreadNotificationsQuery as { id: string }[];

    console.log(`找到 ${unreadNotifications.length} 条未读通知要标记为已读`);

    // 批量创建已读记录
    if (unreadNotifications.length > 0) {
      const results = await prisma.$transaction(
        unreadNotifications.map(notification =>
          prisma.userNotification.upsert({
            where: {
              userId_notificationId: {
                userId: user.id,
                notificationId: notification.id
              }
            },
            create: {
              id: crypto.randomUUID(),
              userId: user.id,
              notificationId: notification.id,
              read: true,
              readAt: new Date()
            },
            update: {
              read: true,
              readAt: new Date()
            }
          })
        )
      );

      console.log(`成功标记 ${results.length} 条通知为已读`);

      // 触发客户端刷新
      return NextResponse.json({
        success: true,
        message: '所有通知已标记为已读',
        data: {
          count: results.length
        }
      }, { status: 200 });
    } else {
      console.log('没有找到需要标记的未读通知');

      return NextResponse.json({
        success: true,
        message: '没有需要标记的未读通知',
        data: {
          count: 0
        }
      }, { status: 200 });
    }
  } catch (error) {
    console.error('标记所有通知为已读错误:', error);
    return NextResponse.json({
      success: false,
      message: '标记已读失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}