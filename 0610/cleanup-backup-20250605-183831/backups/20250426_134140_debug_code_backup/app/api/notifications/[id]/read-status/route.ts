import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getToken } from '@/app/lib/token';
import { checkPermission } from '@/app/lib/auth';

/**
 * 通知已读状态API
 *
 * 此API用于获取通知的已读状态信息，包括：
 * 1. 管理员可以查看所有用户对通知的已读状态
 * 2. 普通用户只能查看自己的已读状态
 * 3. 支持分页查询，默认每页10条数据
 * 4. 支持按已读状态筛选
 * 5. 返回通知的阅读统计信息
 */

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  // 获取查询参数
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1');
  const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
  const readFilter = url.searchParams.get('read'); // 'true', 'false', null(所有)
  const sortBy = url.searchParams.get('sortBy') || 'username'; // 'username', 'name', 'readAt'
  const sortOrder = url.searchParams.get('sortOrder') || 'asc'; // 'asc', 'desc'

  // 验证分页参数
  const validPage = page > 0 ? page : 1;
  const validPageSize = pageSize > 0 && pageSize <= 100 ? pageSize : 10;
  const skip = (validPage - 1) * validPageSize;

  // 构建排序参数
  const orderBy: any = {};
  if (sortBy === 'readAt') {
    orderBy.readAt = sortOrder;
  } else if (sortBy === 'name') {
    orderBy.user = { name: sortOrder };
  } else {
    orderBy.user = { username: sortOrder };
  }
  try {
    const token = await getToken();
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未登录或登录已过期'
      }, { status: 401 });
    }

    // 检查通知是否存在
    const notification = await prisma.notification.findUnique({
      where: {
        id: params.id
      },
      select: {
        id: true,
        sendToAll: true,
        recipients: true
      }
    });

    if (!notification) {
      return NextResponse.json({
        success: false,
        message: '通知不存在'
      }, { status: 404 });
    }

    // 检查用户是否有管理权限
    // 简化处理，默认为普通用户
    const hasManagePermission = false; // 默认不具有管理权限

    // 如果用户没有管理权限，只返回自己的已读状态
    if (!hasManagePermission) {
      const userNotification = await prisma.userNotification.findFirst({
        where: {
          userId: token.id,
          notificationId: params.id
        },
        select: {
          read: true,
          readAt: true
        }
      });

      return NextResponse.json({
        success: true,
        data: {
          isAdmin: false,
          userStatus: userNotification ? {
            userId: token.id,
            read: userNotification.read,
            readAt: userNotification.readAt
          } : null
        }
      });
    }

    // 如果是管理员，获取所有用户的已读状态
    let targetUserIds: string[] = [];

    if (notification.sendToAll) {
      // 如果是全局通知，获取所有用户
      const allUsers = await prisma.user.findMany({
        select: { id: true, username: true, name: true }
      });
      targetUserIds = allUsers.map(user => user.id);
    } else if (notification.recipients) {
      // 如果是指定用户通知，解析recipients字段
      try {
        if (typeof notification.recipients === 'string') {
          targetUserIds = JSON.parse(notification.recipients);
        } else if (Array.isArray(notification.recipients)) {
          targetUserIds = notification.recipients as string[];
        }
      } catch (e) {
        console.error('解析recipients错误:', e);
      }
    }

    // 获取通知的阅读统计信息
    const notificationStats = await prisma.notification.findUnique({
      where: { id: params.id },
      select: {
        readCount: true,
        totalRecipients: true,
        readRate: true,
        lastReadAt: true
      }
    });

    // 构建查询条件
    const whereCondition: any = {
      notificationId: params.id,
      userId: { in: targetUserIds }
    };

    // 根据已读状态过滤
    if (readFilter === 'true') {
      whereCondition.read = true;
    } else if (readFilter === 'false') {
      whereCondition.read = false;
    }

    // 获取总记录数
    const totalCount = await prisma.userNotification.count({
      where: whereCondition
    });

    // 获取分页数据
    const userNotifications = await prisma.userNotification.findMany({
      where: whereCondition,
      select: {
        userId: true,
        read: true,
        readAt: true,
        viewCount: true,
        lastViewedAt: true,
        starred: true,
        user: {
          select: {
            username: true,
            name: true,
            image: true,
            roleCode: true
          }
        }
      },
      orderBy,
      skip,
      take: validPageSize
    });

    // 获取所有目标用户信息
    const users = await prisma.user.findMany({
      where: {
        id: { in: targetUserIds }
      },
      select: {
        id: true,
        username: true,
        name: true
      }
    });

    // 合并用户信息和已读状态
    const readStatusList = users.map(user => {
      const userNotification = userNotifications.find(un => un.userId === user.id);
      return {
        userId: user.id,
        username: user.username,
        name: user.name,
        read: userNotification ? userNotification.read : false,
        readAt: userNotification ? userNotification.readAt : null
      };
    });

    // 计算已读和未读数量
    const currentPageReadCount = readStatusList.filter(status => status.read).length;
    const currentPageUnreadCount = readStatusList.length - currentPageReadCount;

    // 计算总页数
    const totalPages = Math.ceil(totalCount / validPageSize);

    return NextResponse.json({
      success: true,
      data: {
        isAdmin: true,
        sendToAll: notification.sendToAll,
        stats: notificationStats || {
          readCount: 0,
          totalRecipients: 0,
          readRate: 0,
          lastReadAt: null
        },
        pagination: {
          page: validPage,
          pageSize: validPageSize,
          totalCount,
          totalPages
        },
        currentPageStats: {
          readCount: currentPageReadCount,
          unreadCount: currentPageUnreadCount,
          total: readStatusList.length
        },
        readStatusList
      }
    });
  } catch (error) {
    console.error('获取通知已读状态错误:', error);
    return NextResponse.json({
      success: false,
      message: '获取通知已读状态失败'
    }, { status: 500 });
  }
}
