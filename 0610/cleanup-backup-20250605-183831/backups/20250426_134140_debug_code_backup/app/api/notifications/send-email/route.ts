import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { sendEmail } from "@/lib/email";
import { checkPermission } from "@/lib/casbin/enforcer";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";

/**
 * 发送通知电子邮件
 *
 * @route POST /api/notifications/send-email
 * @access 需要 notifications:send 权限
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "发送通知邮件API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    // 验证用户权限
    const hasPermission = await checkPermission(request, 'notifications:send');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权发送通知邮件'
      }, { status: 403 });
    }

    // 解析请求体
    const body = await request.json();
    const { notificationId, userId } = body;

    if (!notificationId || !userId) {
      return NextResponse.json({
        success: false,
        message: '通知ID和用户ID不能为空'
      }, { status: 400 });
    }

    // 获取通知信息
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
      include: {
        type: true
      }
    });

    if (!notification) {
      return NextResponse.json({
        success: false,
        message: '通知不存在'
      }, { status: 404 });
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        notificationSettings: true
      }
    });

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 });
    }

    // 检查用户是否启用了电子邮件通知
    if (!user.notificationSettings?.emailEnabled) {
      return NextResponse.json({
        success: false,
        message: '用户未启用电子邮件通知'
      }, { status: 400 });
    }

    // 检查通知类型是否在用户设置的接收类型中
    const typeCode = notification.type?.code || '';
    if (typeCode && user.notificationSettings?.types &&
        !user.notificationSettings.types.includes(typeCode)) {
      return NextResponse.json({
        success: false,
        message: '用户未订阅此类型的通知'
      }, { status: 400 });
    }

    // 将优先级转换为中文
    const priorityInChinese = {
      'low': '低',
      'normal': '中',
      'high': '高',
      'urgent': '紧急'
    };
    const priorityText = priorityInChinese[notification.priority as keyof typeof priorityInChinese] || notification.priority;

    // 发送电子邮件
    await sendEmail({
      to: user.email,
      subject: `${notification.type?.name || '系统'} 通知: ${notification.title}`,
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">${notification.title}</h2>
          <div style="background: #f5f5f5; padding: 15px; margin: 20px 0;">
            <div style="font-size: 16px; color: #333; line-height: 1.6;">
              ${notification.content}
            </div>
          </div>
          <p style="color: #666; font-size: 14px;">
            优先级: ${priorityText}
          </p>
          <p style="color: #666; font-size: 14px;">
            发布时间: ${notification.publishedAt ? new Date(notification.publishedAt).toLocaleString() : '未发布'}
          </p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            此邮件由系统自动发送，请勿回复。如需关闭邮件通知，请在系统设置中修改通知偏好。
          </p>
        </div>
      `
    });

    // 更新用户通知关联，记录邮件已发送
    await prisma.userNotification.updateMany({
      where: {
        userId: userId,
        notificationId: notificationId
      },
      data: {
        emailSent: true,
        emailSentAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: '通知邮件发送成功'
    });
  } catch (error) {
    console.error('发送通知邮件失败:', error);
    return NextResponse.json({
      success: false,
      message: '发送通知邮件失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
