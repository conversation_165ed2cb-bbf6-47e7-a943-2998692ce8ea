import { type NextRequest, NextResponse } from "next/server"
import { mockTasks } from "@/lib/api/mock"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取任务详情API");
    if (response) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const taskId = params.id
    const task = mockTasks.find((t) => t.id === taskId)

    if (!task) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "任务不存在",
          data: null,
        },
        { status: 404 }
      )
    }

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取任务详情成功",
        data: task,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("获取任务详情错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "更新任务API");
    if (response) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const taskId = params.id
    const body = await request.json()
    const { name, content, status, progress, completionTime } = body

    const taskIndex = mockTasks.findIndex((t) => t.id === taskId)
    if (taskIndex === -1) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "任务不存在",
          data: null,
        },
        { status: 404 }
      )
    }

    // 更新任务信息
    mockTasks[taskIndex] = {
      ...mockTasks[taskIndex],
      name: name || mockTasks[taskIndex].name,
      content: content || mockTasks[taskIndex].content,
      status: status || mockTasks[taskIndex].status,
      progress: progress !== undefined ? progress : mockTasks[taskIndex].progress,
      completionTime: completionTime || mockTasks[taskIndex].completionTime,
    }

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "更新任务成功",
        data: mockTasks[taskIndex],
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("更新任务错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "删除任务API");
    if (response) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未授权访问",
          data: null,
        },
        { status: 401 }
      )
    }

    const taskId = params.id
    const taskIndex = mockTasks.findIndex((t) => t.id === taskId)

    if (taskIndex === -1) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "任务不存在",
          data: null,
        },
        { status: 404 }
      )
    }

    // 从数组中删除任务
    mockTasks.splice(taskIndex, 1)

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "删除任务成功",
        data: null,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("删除任务错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

