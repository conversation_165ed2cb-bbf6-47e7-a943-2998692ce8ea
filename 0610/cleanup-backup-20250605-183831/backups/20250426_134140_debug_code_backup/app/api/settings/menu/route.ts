import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 批量保存菜单设置
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[菜单设置API:${requestId}] 保存菜单设置`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "菜单设置API");
    if (response) {
      console.log(`[菜单设置API:${requestId}] 未授权访问`)
      return response;
    }

    // 获取请求体
    const body = await request.json()
    const { menuItems } = body

    if (!menuItems || !Array.isArray(menuItems)) {
      return NextResponse.json({
        success: false,
        message: "无效的菜单数据",
        requestId
      }, { status: 400 })
    }

    console.log(`[菜单设置API:${requestId}] 收到 ${menuItems.length} 个菜单项`)

    // 处理菜单项
    const processMenuItems = async (items: any[], parentId: string | null = null) => {
      for (const item of items) {
        // 检查菜单是否存在
        const existingMenu = await prisma.menu.findUnique({
          where: { id: item.id },
        }).catch(() => null)

        if (existingMenu) {
          // 更新现有菜单
          await prisma.menu.update({
            where: { id: item.id },
            data: {
              name: item.name || item.title,
              path: item.path,
              icon: item.icon,
              visible: item.isVisible !== undefined ? item.isVisible : true,
              order: item.order || 0,
              parentId: parentId
            }
          })
        } else {
          // 创建新菜单
          const code = item.code || `menu-${Date.now()}-${Math.floor(Math.random() * 1000)}`

          const newMenu = await prisma.menu.create({
            data: {
              id: item.id,
              name: item.name || item.title,
              path: item.path,
              icon: item.icon,
              code: code,
              visible: item.isVisible !== undefined ? item.isVisible : true,
              order: item.order || 0,
              parentId: parentId
            }
          })

          // 将菜单添加到管理员角色
          const adminRole = await prisma.role.findFirst({
            where: { code: 'ADMIN' }
          })

          if (adminRole) {
            await prisma.role.update({
              where: { id: adminRole.id },
              data: {
                menus: {
                  connect: { id: newMenu.id }
                }
              }
            })

            // 使用jCasbin添加权限
            await CasbinService.addPermissionForRole('ADMIN', `menu:${code}`, 'view')
          }
        }

        // 处理子菜单
        if (item.children && Array.isArray(item.children) && item.children.length > 0) {
          await processMenuItems(item.children, item.id)
        }
      }
    }

    // 处理所有菜单项
    await processMenuItems(menuItems)

    return NextResponse.json({
      success: true,
      message: '菜单设置保存成功',
      requestId
    })
  } catch (error) {
    console.error(`[菜单设置API:${requestId}] 保存菜单设置失败:`, error)
    return NextResponse.json({
      success: false,
      message: "保存菜单设置失败",
      requestId
    }, { status: 500 })
  }
}
