import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { prisma } from "@/lib/prisma";
import { verifyVerificationCode } from "@/lib/services/verification";
import { v4 as uuidv4 } from "uuid";

/**
 * 验证邮箱验证码并返回授权码
 * POST /api/settings/email/verify-code
 *
 * 用于验证邮箱验证码并返回当前授权码
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "验证邮箱验证码");
    if (authResult.response) {
      return authResult.response;
    }

    const user = authResult.user;
    if (!user?.email) {
      return NextResponse.json({
        success: false,
        message: "无法获取管理员邮箱地址"
      }, { status: 400 });
    }

    // 获取请求数据
    const data = await request.json();

    // 验证必要字段
    if (!data.code) {
      return NextResponse.json({
        success: false,
        message: "验证码不能为空"
      }, { status: 400 });
    }

    // 验证验证码
    const isValid = await verifyVerificationCode(user.email, data.code, "email_auth_code");
    if (!isValid) {
      return NextResponse.json({
        success: false,
        message: "验证码无效或已过期"
      }, { status: 400 });
    }

    // 获取当前邮件设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings || !settings.emailSettings) {
      return NextResponse.json({
        success: false,
        message: "系统设置不存在"
      }, { status: 404 });
    }

    // 解析邮件设置
    const emailSettings = settings.emailSettings as any;

    // 生成一个临时验证ID，用于后续更新授权码
    const verificationId = uuidv4();

    // 存储验证ID，有效期5分钟
    await prisma.verificationCode.create({
      data: {
        code: verificationId,
        type: "auth_code_update",
        email: user.email,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5分钟后过期
      }
    });

    return NextResponse.json({
      success: true,
      message: "验证成功",
      data: {
        authCode: emailSettings.auth?.pass || "",
        verificationId
      }
    });
  } catch (error) {
    console.error("验证邮箱验证码失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "验证邮箱验证码失败"
    }, { status: 500 });
  }
}
