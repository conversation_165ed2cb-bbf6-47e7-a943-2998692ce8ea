import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { prisma } from "@/lib/prisma";
import { generateVerificationCode, storeVerificationCode } from "@/lib/services/verification";
import { emailService } from "@/lib/services/email";

/**
 * 发送邮箱验证码
 * POST /api/settings/email/auth-code
 *
 * 用于发送邮箱验证码，以便后续验证邮箱
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "发送邮箱验证码");
    if (authResult.response) {
      return authResult.response;
    }

    const user = authResult.user;
    if (!user?.email) {
      return NextResponse.json({
        success: false,
        message: "无法获取管理员邮箱地址"
      }, { status: 400 });
    }

    // 生成验证码
    const code = generateVerificationCode();

    // 存储验证码，类型为email_auth_code，有效期10分钟
    await storeVerificationCode(user.email, code, "email_auth_code", 600);

    // 发送验证码邮件
    try {
      await emailService.sendVerificationEmail(user.email, code, "email_settings");
    } catch (error) {
      console.error("发送验证码邮件失败:", error);
      return NextResponse.json({
        success: false,
        message: "发送验证码邮件失败，请稍后重试"
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "验证码已发送，请查收邮件"
    });
  } catch (error) {
    console.error("发送邮箱验证码失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "发送邮箱验证码失败"
    }, { status: 500 });
  }
}

/**
 * 更新邮箱授权码
 * PUT /api/settings/email/auth-code
 *
 * 用于更新邮箱授权码，需要提供验证ID
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, "更新邮箱授权码");
    if (authResult.response) {
      return authResult.response;
    }

    // 获取请求数据
    const data = await request.json();

    // 验证必要字段
    if (!data.verificationId || !data.authCode) {
      return NextResponse.json({
        success: false,
        message: "验证ID和授权码不能为空"
      }, { status: 400 });
    }

    // 验证验证ID
    const verification = await prisma.verificationCode.findFirst({
      where: {
        code: data.verificationId,
        type: "auth_code_update",
        email: authResult.user.email,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (!verification) {
      return NextResponse.json({
        success: false,
        message: "验证ID无效或已过期，请重新验证邮箱"
      }, { status: 400 });
    }

    // 获取当前邮件设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      return NextResponse.json({
        success: false,
        message: "系统设置不存在"
      }, { status: 404 });
    }

    // 解析并更新邮件设置
    const emailSettings = settings.emailSettings as any || {};

    // 更新授权码
    emailSettings.auth = {
      ...(emailSettings.auth || {}),
      pass: data.authCode
    };

    // 保存更新后的设置
    await prisma.systemSettings.update({
      where: { id: settings.id },
      data: {
        emailSettings: emailSettings as any,
        updatedAt: new Date()
      }
    });

    // 删除已使用的验证ID
    await prisma.verificationCode.delete({
      where: { id: verification.id }
    });

    return NextResponse.json({
      success: true,
      message: "授权码已更新",
      data: {
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error("更新邮箱授权码失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "更新邮箱授权码失败"
    }, { status: 500 });
  }
}
