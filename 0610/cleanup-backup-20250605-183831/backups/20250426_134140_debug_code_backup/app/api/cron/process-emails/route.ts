/**
 * 邮件队列处理定时任务
 * 用于定期处理邮件队列中的邮件
 */

import { NextRequest, NextResponse } from "next/server"
import CronService from "@/lib/services/cron-service"

/**
 * 处理邮件队列
 *
 * @route GET /api/cron/process-emails
 * @access 公开访问，但需要验证密钥
 */
export async function GET(request: NextRequest) {
  try {
    // 验证请求密钥
    const apiKey = request.headers.get('x-api-key');
    const cronSecret = process.env.CRON_SECRET || 'default-cron-secret';

    if (apiKey !== cronSecret) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    // 使用定时任务服务处理邮件队列
    const result = await CronService.processEmails();

    return NextResponse.json(result);
  } catch (error) {
    console.error("处理邮件队列错误:", error);
    return NextResponse.json({
      success: false,
      message: '处理邮件队列失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
