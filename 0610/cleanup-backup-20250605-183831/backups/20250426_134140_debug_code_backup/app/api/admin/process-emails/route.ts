/**
 * 管理员手动处理邮件队列API
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { mailSender } from "@/lib/services/mail-sender"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 处理邮件队列
 * 
 * @route POST /api/admin/process-emails
 * @access 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "手动处理邮件队列API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 });
    }

    // 获取待处理的邮件
    const pendingEmails = await prisma.emailQueue.findMany({
      where: {
        status: 'pending',
        retries: {
          lt: 3 // 重试次数小于3次
        }
      },
      take: 10, // 一次处理10封邮件
      orderBy: {
        createdAt: 'asc' // 优先处理早期创建的邮件
      }
    });

    if (pendingEmails.length === 0) {
      return NextResponse.json({
        success: true,
        message: '没有待处理的邮件',
        processed: 0
      });
    }

    // 处理邮件
    let successCount = 0;
    let failCount = 0;

    for (const email of pendingEmails) {
      try {
        // 调用真实的邮件发送服务
        console.log(`开始发送邮件: ${email.id}, 收件人: ${email.to}, 主题: ${email.subject}`);

        // 实际发送邮件
        const sendResult = await mailSender.sendMail({
          to: email.to,
          subject: email.subject,
          html: email.html,
          text: email.text || ''
        });

        if (sendResult) {
          // 发送成功
          await prisma.emailQueue.update({
            where: { id: email.id },
            data: {
              status: 'sent',
              sentAt: new Date()
            }
          });

          console.log(`邮件发送成功: ${email.id}`);
          successCount++;
        } else {
          // 发送失败
          throw new Error('邮件发送失败');
        }
      } catch (error) {
        console.error(`处理邮件 ${email.id} 失败:`, error);
        
        // 更新重试次数
        await prisma.emailQueue.update({
          where: { id: email.id },
          data: {
            status: 'failed',
            retries: email.retries + 1,
            error: error instanceof Error ? error.message : '未知错误'
          }
        });
        
        failCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: '邮件处理完成',
      processed: pendingEmails.length,
      success: successCount,
      failed: failCount
    });
  } catch (error) {
    console.error("处理邮件队列错误:", error);
    return NextResponse.json({
      success: false,
      message: '处理邮件队列失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
