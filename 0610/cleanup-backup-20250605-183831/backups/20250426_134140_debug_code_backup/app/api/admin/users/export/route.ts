/**
 * 用户导出API
 * 提供用户列表的导出功能
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"
import { checkPermission } from "@/lib/casbin/enforcer"
import { SystemLogService } from "@/lib/services/system-log-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 导出用户列表
 *
 * @route GET /api/admin/users/export
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "导出用户列表API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问或没有管理员权限'
      }, { status: 401 })
    }

    const adminId = admin.id;

    // 检查权限
    const hasPermission = await checkPermission(request, 'users', 'export')
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限导出用户列表'
      }, { status: 403 })
    }

    // 获取导出格式
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'csv'

    // 查询客户列表（非管理员用户）
    const customers = await prisma.user.findMany({
      where: {
        roleCode: {
          not: 'ADMIN'
        }
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        roleCode: true,
        status: true,
        balance: true,
        creditLimit: true,
        verificationStatus: true,
        verificationType: true,
        createdAt: true,
        updatedAt: true,
        createdBy: {
          select: {
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 记录导出操作
    await SystemLogService.log({
      userId: admin.id,
      action: "export",
      module: "users",
      details: {
        format,
        count: customers.length
      }
    })

    // 格式化数据
    const formattedData = customers.map(customer => ({
      ID: customer.id,
      用户名: customer.username,
      邮箱: customer.email,
      姓名: customer.name || '',
      电话: customer.phone || '',
      角色: customer.role?.name || customer.roleCode,
      账户类型: customer.role?.type === 'system' ? '管理员' : (customer.role?.type === 'enterprise' ? '企业' : '个人'),
      状态: customer.status === 'active' ? '正常' : '禁用',
      余额: customer.balance || 0,
      授信额度: customer.creditLimit || 0,
      认证状态: customer.verificationStatus ? (
        customer.verificationStatus === 'approved' ? '已认证' :
        customer.verificationStatus === 'pending' ? '待审核' :
        customer.verificationStatus === 'rejected' ? '已拒绝' : '未认证'
      ) : '未认证',
      认证类型: customer.verificationType === 'personal' ? '个人认证' :
               customer.verificationType === 'enterprise' ? '企业认证' : '未认证',
      创建者: customer.createdBy ? (customer.createdBy.name || customer.createdBy.username) : '',
      创建时间: format(new Date(customer.createdAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN }),
      更新时间: format(new Date(customer.updatedAt), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
    }))

    // 根据格式返回不同类型的数据
    const timestamp = format(new Date(), 'yyyyMMdd_HHmmss')
    const filename = `用户列表_${timestamp}`
    const headers = new Headers()

    if (format === 'csv') {
      // 生成CSV
      const header = Object.keys(formattedData[0]).join(',') + '\n'
      const rows = formattedData.map(row =>
        Object.values(row).map(value =>
          typeof value === 'string' && value.includes(',') ? `"${value}"` : value
        ).join(',')
      ).join('\n')
      const csv = header + rows

      headers.set('Content-Type', 'text/csv; charset=utf-8')
      headers.set('Content-Disposition', `attachment; filename="${filename}.csv"`)
      return new NextResponse(csv, { headers })
    } else if (format === 'excel') {
      // 生成简单的Excel兼容CSV
      const header = Object.keys(formattedData[0]).join('\t') + '\n'
      const rows = formattedData.map(row =>
        Object.values(row).join('\t')
      ).join('\n')
      const excel = header + rows

      headers.set('Content-Type', 'application/vnd.ms-excel; charset=utf-8')
      headers.set('Content-Disposition', `attachment; filename="${filename}.xls"`)
      return new NextResponse(excel, { headers })
    } else {
      // 默认返回JSON
      headers.set('Content-Type', 'application/json; charset=utf-8')
      headers.set('Content-Disposition', `attachment; filename="${filename}.json"`)
      return new NextResponse(JSON.stringify(formattedData, null, 2), { headers })
    }
  } catch (error) {
    console.error("导出用户列表错误:", error)
    return NextResponse.json({
      success: false,
      message: '导出用户列表失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
