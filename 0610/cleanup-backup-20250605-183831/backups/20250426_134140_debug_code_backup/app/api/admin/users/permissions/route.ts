import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { AuthMiddleware } from '@/lib/middleware/auth-middleware'
import { checkPermission } from '@/lib/casbin/enforcer'

/**
 * 获取用户权限列表
 * @route GET /api/admin/users/permissions
 */
export async function GET(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "获取用户权限列表API");
    if (response) {
      return NextResponse.json(
        { success: false, message: '未授权访问或没有管理员权限', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理权限
    const hasAccess = await checkPermission(req, 'user:manage')
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有操作权限', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    // 获取所有权限
    const permissions = await prisma.operation.findMany({
      orderBy: { code: 'asc' },
    })

    return NextResponse.json(
      { success: true, data: permissions },
      { status: 200, headers }
    )
  } catch (error) {
    console.error('获取权限列表失败:', error)
    return NextResponse.json(
      { success: false, message: '获取权限列表失败', error },
      { status: 500, headers }
    )
  }
}

/**
 * 更新用户权限
 * @route POST /api/admin/users/permissions
 */
export async function POST(req: NextRequest) {
  const headers = new Headers()
  headers.append('Content-Type', 'application/json')

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "更新用户权限API");
    if (response) {
      return NextResponse.json(
        { success: false, message: '未授权访问或没有管理员权限', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 检查是否有管理权限
    const hasAccess = await checkPermission(req, 'user:manage')
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, message: '没有操作权限', error: 'Forbidden' },
        { status: 403, headers }
      )
    }

    const body = await req.json()
    const { userId, permissionIds } = body

    if (!userId || !Array.isArray(permissionIds)) {
      return NextResponse.json(
        { success: false, message: '参数错误', error: 'InvalidParams' },
        { status: 400, headers }
      )
    }

    // 更新用户权限
    // 直接更新用户的permissions字段
    await prisma.user.update({
      where: { id: userId },
      data: {
        permissions: permissionIds
      }
    })

    return NextResponse.json(
      { success: true, message: '权限更新成功' },
      { status: 200, headers }
    )
  } catch (error) {
    console.error('更新用户权限失败:', error)
    return NextResponse.json(
      { success: false, message: '更新用户权限失败', error },
      { status: 500, headers }
    )
  }
}