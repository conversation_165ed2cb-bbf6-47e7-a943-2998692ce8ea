import { NextRequest, NextResponse } from "next/server";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 确保用户角色有适当的菜单权限
 * 
 * @route POST /api/admin/ensure-user-role
 * @access 仅管理员可访问
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(req, "确保用户角色API");
    if (response) {
      return NextResponse.json({
        success: false,
        message: '未授权访问',
      }, { status: 401 });
    }

    console.log('开始确保用户角色有适当的菜单权限...');

    // 检查 USER 角色是否存在
    let userRole = await prisma.role.findUnique({
      where: { code: 'USER' },
      include: {
        menus: true
      }
    });

    // 如果不存在，创建 USER 角色
    if (!userRole) {
      console.log('创建 USER 角色...');
      userRole = await prisma.role.create({
        data: {
          code: 'USER',
          name: '普通用户',
          type: 'customer',
          description: '系统默认用户角色',
          permissions: ['notifications:list', 'notifications:read', 'user:profile', 'user:password', 'TASK_READ', 'TASK_CREATE']
        },
        include: {
          menus: true
        }
      });
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany();
    console.log(`找到 ${allMenus.length} 个菜单`);

    // 用户应该看到的菜单代码
    const userMenuCodes = [
      'dashboard',
      'tasks',
      'notifications',
      'user_center',
      'profile'
    ];

    // 找到用户应该看到的菜单
    const userMenus = allMenus.filter(menu => 
      userMenuCodes.includes(menu.code) || 
      menu.path.includes('/user/') || 
      menu.path.includes('/tasks/') ||
      menu.path.includes('/notifications')
    );

    console.log(`用户应该看到 ${userMenus.length} 个菜单`);

    // 检查用户角色是否已经有这些菜单
    const existingMenuIds = userRole.menus.map(menu => menu.id);
    const menusToAdd = userMenus.filter(menu => !existingMenuIds.includes(menu.id));

    if (menusToAdd.length > 0) {
      console.log(`为 USER 角色添加 ${menusToAdd.length} 个菜单...`);
      
      // 为用户角色添加菜单
      await prisma.role.update({
        where: { code: 'USER' },
        data: {
          menus: {
            connect: menusToAdd.map(menu => ({ id: menu.id }))
          }
        }
      });

      console.log('菜单添加成功');
    } else {
      console.log('USER 角色已经有所有需要的菜单');
    }

    // 确保所有用户都有 USER 角色
    const usersWithoutRole = await prisma.user.findMany({
      where: {
        roleCode: {
          not: 'USER'
        },
        NOT: {
          roleCode: 'ADMIN'
        }
      }
    });

    if (usersWithoutRole.length > 0) {
      console.log(`发现 ${usersWithoutRole.length} 个用户没有 USER 角色，更新中...`);
      
      for (const user of usersWithoutRole) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            roleCode: 'USER'
          }
        });
      }

      console.log('用户角色更新成功');
    } else {
      console.log('所有非管理员用户都已经有 USER 角色');
    }

    // 确保 USER 角色有 notifications:list 权限
    if (!userRole.permissions.includes('notifications:list')) {
      console.log('为 USER 角色添加 notifications:list 权限...');
      
      await prisma.role.update({
        where: { code: 'USER' },
        data: {
          permissions: {
            push: 'notifications:list'
          }
        }
      });

      console.log('权限添加成功');
    }

    return NextResponse.json({
      success: true,
      message: '用户角色和菜单权限已更新',
      data: {
        role: userRole.code,
        menuCount: userMenus.length,
        usersUpdated: usersWithoutRole.length
      }
    });
  } catch (error) {
    console.error('确保用户角色API错误:', error);
    return NextResponse.json({
      success: false,
      message: '处理请求时发生错误: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}
