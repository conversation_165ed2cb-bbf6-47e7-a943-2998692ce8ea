import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { verifyVerificationCode } from "@/lib/services/verification"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 验证邮箱修改响应接口
 */
interface VerifyEmailChangeResponse {
  code: number
  success: boolean
  message: string
  data?: any
}

/**
 * 验证邮箱修改请求验证 schema
 */
const verifyEmailChangeSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  code: z.string().min(6, '验证码格式不正确').max(6, '验证码格式不正确'),
})

/**
 * 处理验证邮箱修改请求
 * POST /api/user/verify-email-change
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 验证邮箱修改响应
 */
export async function POST(request: NextRequest): Promise<NextResponse<VerifyEmailChangeResponse>> {
  try {
    console.log('收到验证邮箱修改请求')
    console.log('请求头:', request.headers)
    console.log('请求方法:', request.method)
    console.log('请求URL:', request.url)

    // 使用权限检查中间件检查用户是否已登录
    console.log('检查用户认证状态...')
    const { response, user } = await AuthMiddleware.requireAuth(request, "验证邮箱修改API");
    if (response) {
      console.log('未授权访问')
      return NextResponse.json({
        code: 401,
        success: false,
        message: '未登录或会话已过期'
      }, { status: 401 })
    }

    console.log('当前用户:', user.id, user.username)

    const body = await request.json()
    console.log('请求体:', body)

    // 验证请求参数
    const result = verifyEmailChangeSchema.safeParse(body)
    if (!result.success) {
      console.log('参数验证失败:', result.error.errors)
      return NextResponse.json({
        code: 400,
        success: false,
        message: result.error.errors[0].message
      }, { status: 400 })
    }

    const { email, code } = result.data
    console.log('参数验证通过:', { email, code })

    // 检查邮箱是否已被其他用户注册
    console.log('检查邮箱是否已被其他用户注册...')
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
        id: { not: user.id }
      }
    })

    if (existingUser) {
      console.log('邮箱已被其他用户注册:', existingUser.id)
      return NextResponse.json({
        code: 400,
        success: false,
        message: '该邮箱已被其他用户注册'
      }, { status: 400 })
    }

    // 验证验证码
    console.log('验证验证码...')
    const isValid = await verifyVerificationCode(email, code, "email_change")
    if (!isValid) {
      console.log('验证码无效')
      return NextResponse.json({
        code: 400,
        success: false,
        message: '验证码无效或已过期'
      }, { status: 400 })
    }

    // 更新用户邮箱
    console.log('更新用户邮箱...')
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        email,
        emailVerified: new Date()
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        image: true,
        emailVerified: true,
        roleCode: true,
        role: {
          select: {
            name: true,
            code: true,
            type: true
          }
        }
      }
    })

    console.log('用户邮箱更新成功:', updatedUser)
    return NextResponse.json({
      code: 200,
      success: true,
      message: '邮箱修改成功',
      data: updatedUser
    })
  } catch (error) {
    console.error('验证邮箱修改失败:', error)
    return NextResponse.json({
      code: 500,
      success: false,
      message: '验证邮箱修改失败，请稍后重试'
    }, { status: 500 })
  }
}
