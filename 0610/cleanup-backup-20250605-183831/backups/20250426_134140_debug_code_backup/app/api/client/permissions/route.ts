/**
 * 客户端权限API
 * 提供给客户端组件使用的权限相关API
 */

import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/lib/prisma'
import { AuthMiddleware } from '@/lib/middleware/auth-middleware'

/**
 * 获取所有权限
 */
export async function GET(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response } = await AuthMiddleware.requireAuth(req, "获取客户端权限API");
    if (response) {
      return NextResponse.json(
        { success: false, message: "未授权，请登录后再试", error: "Unauthorized" },
        { status: 401 }
      )
    }

    // 从数据库获取所有权限定义
    const permissions = {
      // 管理员用户权限
      ADMIN_USER_VIEW: '查看管理员用户',
      ADMIN_USER_CREATE: '创建管理员用户',
      ADMIN_USER_UPDATE: '更新管理员用户',
      ADMIN_USER_DELETE: '删除管理员用户',
      ADMIN_USER_RESET_PASSWORD: '重置管理员密码',

      // 客户管理权限
      CUSTOMER_VIEW: '查看客户',
      CUSTOMER_CREATE: '创建客户',
      CUSTOMER_UPDATE: '更新客户',
      CUSTOMER_DELETE: '删除客户',
      CUSTOMER_BALANCE: '管理客户余额',
      CUSTOMER_CREDIT: '管理客户额度',

      // 角色管理权限
      ROLE_VIEW: '查看角色',
      ROLE_CREATE: '创建角色',
      ROLE_UPDATE: '更新角色',
      ROLE_DELETE: '删除角色',
      ROLE_ASSIGN: '分配角色权限',

      // 菜单管理权限
      MENU_VIEW: '查看菜单',
      MENU_CREATE: '创建菜单',
      MENU_UPDATE: '更新菜单',
      MENU_DELETE: '删除菜单',

      // 系统设置权限
      SYSTEM_SETTINGS: '管理系统设置',
      SYSTEM_BACKUP: '系统备份',
      SYSTEM_RESTORE: '系统恢复',

      // 系统日志权限
      LOG_VIEW: '查看系统日志',
      LOG_EXPORT: '导出系统日志'
    }

    // 获取所有角色
    const roles = await prisma.role.findMany()

    // 返回所有权限
    return NextResponse.json({
      success: true,
      permissions: Object.entries(permissions).map(([key, value]) => ({
        key,
        value
      })),
      roles
    })
  } catch (error) {
    console.error('获取权限列表失败:', error)
    return NextResponse.json(
      { success: false, message: "获取权限列表失败", error: String(error) },
      { status: 500 }
    )
  }
}
