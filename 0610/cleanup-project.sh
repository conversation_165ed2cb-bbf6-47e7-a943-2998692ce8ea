#!/bin/bash

# 项目代码清理和重组脚本
# 采用保守方式，确保不影响现有功能

set -e  # 遇到错误立即退出

echo "🚀 开始项目代码清理和重组..."
echo "📍 当前目录: $(pwd)"

# 创建备份目录
BACKUP_DIR="cleanup-backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
echo "📦 创建备份目录: $BACKUP_DIR"

# 1. 备份重要文件（保守策略）
echo "\n📋 第一步: 备份重要文件"
cp -r backups "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  backups 目录不存在，跳过"
cp -r database_backups "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  database_backups 目录不存在，跳过"
cp backup.sql "$BACKUP_DIR/" 2>/dev/null || echo "⚠️  backup.sql 文件不存在，跳过"

# 2. 清理明显的无用文件（仅删除确定安全的文件）
echo "\n🧹 第二步: 清理明显无用的文件"

# 清理 .bak 文件（先备份）
find . -name "*.bak" -type f | while read file; do
    if [ -f "$file" ]; then
        echo "备份并删除: $file"
        cp "$file" "$BACKUP_DIR/"
        rm "$file"
    fi
done

# 清理空的测试目录
if [ -d "backend/test" ] && [ -z "$(ls -A backend/test)" ]; then
    echo "删除空的测试目录: backend/test"
    rmdir backend/test
fi

# 3. 创建新的目录结构（不移动现有文件，只创建目录）
echo "\n📁 第三步: 创建优化的目录结构"

# 创建新的组织目录
mkdir -p "src/components/business"
mkdir -p "src/components/common"
mkdir -p "src/components/forms"
mkdir -p "src/hooks/business"
mkdir -p "src/hooks/common"
mkdir -p "src/services/api"
mkdir -p "src/services/auth"
mkdir -p "src/services/notification"
mkdir -p "src/utils/helpers"
mkdir -p "src/utils/validation"
mkdir -p "src/types/api"
mkdir -p "src/types/business"
mkdir -p "docs/api"
mkdir -p "docs/deployment"
mkdir -p "docs/development"
mkdir -p "scripts/cleanup"
mkdir -p "scripts/deployment"
mkdir -p "scripts/development"

echo "✅ 新目录结构已创建"

# 4. 生成文件清理报告（不实际删除）
echo "\n📊 第四步: 生成清理建议报告"

REPORT_FILE="cleanup-report-$(date +%Y%m%d-%H%M%S).md"

cat > "$REPORT_FILE" << 'EOF'
# 项目清理建议报告

## 🎯 清理目标
- 提高代码可维护性
- 减少项目体积
- 优化目录结构

## 📋 建议清理的文件和目录

### 1. 备份文件（可安全删除）
EOF

# 查找备份相关文件
echo "\n### 备份文件列表:" >> "$REPORT_FILE"
find . -path "./node_modules" -prune -o -name "*backup*" -type f -print >> "$REPORT_FILE" 2>/dev/null || true
find . -path "./node_modules" -prune -o -name "*.backup" -type f -print >> "$REPORT_FILE" 2>/dev/null || true

cat >> "$REPORT_FILE" << 'EOF'

### 2. 调试代码（需要人工审查）

#### Console.log 语句位置:
EOF

# 查找 console.log（排除 node_modules）
grep -r "console\.log" . --exclude-dir=node_modules --exclude-dir="$BACKUP_DIR" | head -20 >> "$REPORT_FILE" 2>/dev/null || true

cat >> "$REPORT_FILE" << 'EOF'

#### TODO 和 FIXME 标记:
EOF

# 查找 TODO 和 FIXME
grep -r "TODO\|FIXME" . --exclude-dir=node_modules --exclude-dir="$BACKUP_DIR" | head -10 >> "$REPORT_FILE" 2>/dev/null || true

cat >> "$REPORT_FILE" << 'EOF'

## 🔄 建议的文件重组

### 组件文件重组建议:
- 将业务相关组件移动到 `src/components/business/`
- 将通用组件移动到 `src/components/common/`
- 将表单组件移动到 `src/components/forms/`

### Hooks 重组建议:
- 将业务逻辑 hooks 移动到 `src/hooks/business/`
- 将通用 hooks 移动到 `src/hooks/common/`

### 服务文件重组建议:
- 将 API 相关服务移动到 `src/services/api/`
- 将认证服务移动到 `src/services/auth/`
- 将通知服务移动到 `src/services/notification/`

## ⚠️ 注意事项
1. 移动文件前请确保更新所有导入路径
2. 建议分批进行，每次只重组一个模块
3. 每次重组后进行完整测试
4. 保持 Git 提交记录清晰

## 🛠️ 下一步操作建议
1. 审查此报告
2. 手动删除确认无用的备份文件
3. 逐步清理调试代码
4. 分模块进行文件重组
5. 更新文档和导入路径
EOF

echo "📄 清理报告已生成: $REPORT_FILE"

# 5. 创建文件移动脚本模板（不执行）
echo "\n📝 第五步: 创建文件移动脚本模板"

cat > "move-files-template.sh" << 'EOF'
#!/bin/bash
# 文件移动脚本模板
# 请根据实际需要修改并谨慎执行

set -e

echo "⚠️  这是一个模板脚本，请根据实际情况修改后使用"
echo "建议每次只移动一个模块的文件"

# 示例：移动组件文件
# echo "移动业务组件..."
# mkdir -p src/components/business
# mv app/components/customer src/components/business/
# mv app/components/admin src/components/business/

# 示例：移动 hooks
# echo "移动业务 hooks..."
# mkdir -p src/hooks/business  
# mv app/hooks/use-customer-actions.ts src/hooks/business/
# mv app/hooks/use-customers.ts src/hooks/business/

# 示例：移动服务文件
# echo "移动服务文件..."
# mkdir -p src/services/auth
# mv app/lib/auth-service.ts src/services/auth/
# mv app/lib/permission-service.ts src/services/auth/

echo "✅ 文件移动完成（模板）"
echo "⚠️  请记得更新所有相关的导入路径！"
EOF

chmod +x "move-files-template.sh"
echo "📄 文件移动脚本模板已创建: move-files-template.sh"

# 6. 创建导入路径更新脚本
echo "\n🔗 第六步: 创建导入路径更新脚本"

cat > "update-imports.sh" << 'EOF'
#!/bin/bash
# 导入路径更新脚本
# 在移动文件后使用此脚本更新导入路径

set -e

echo "🔄 更新导入路径..."

# 示例：更新组件导入路径
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/components/customer|@/components/business/customer|g'
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/components/admin|@/components/business/admin|g'

# 示例：更新 hooks 导入路径  
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/hooks/use-customer|@/hooks/business/use-customer|g'

# 示例：更新服务导入路径
# find . -name "*.tsx" -o -name "*.ts" | xargs sed -i '' 's|@/lib/auth-service|@/services/auth/auth-service|g'

echo "✅ 导入路径更新完成（模板）"
echo "⚠️  请根据实际移动的文件修改此脚本！"
EOF

chmod +x "update-imports.sh"
echo "📄 导入路径更新脚本已创建: update-imports.sh"

# 7. 创建清理验证脚本
echo "\n✅ 第七步: 创建清理验证脚本"

cat > "verify-cleanup.sh" << 'EOF'
#!/bin/bash
# 清理验证脚本
# 验证清理和重组后项目的完整性

set -e

echo "🔍 验证项目完整性..."

# 检查构建
echo "📦 检查项目构建..."
npm run build || echo "❌ 构建失败，请检查导入路径"

# 检查 TypeScript
echo "🔧 检查 TypeScript..."
npx tsc --noEmit || echo "❌ TypeScript 检查失败"

# 检查 ESLint
echo "📋 检查 ESLint..."
npm run lint || echo "⚠️  ESLint 检查有警告"

# 统计清理效果
echo "\n📊 清理效果统计:"
echo "当前项目文件数: $(find . -type f -not -path './node_modules/*' | wc -l)"
echo "当前项目大小: $(du -sh . --exclude=node_modules 2>/dev/null || du -sh .)"
echo "剩余 console.log: $(grep -r "console\.log" . --exclude-dir=node_modules | wc -l)"
echo "剩余 TODO/FIXME: $(grep -r "TODO\|FIXME" . --exclude-dir=node_modules | wc -l)"

echo "✅ 验证完成"
EOF

chmod +x "verify-cleanup.sh"
echo "📄 清理验证脚本已创建: verify-cleanup.sh"

# 完成总结
echo "\n🎉 项目清理脚本执行完成！"
echo "\n📋 已完成的操作:"
echo "  ✅ 创建了备份目录: $BACKUP_DIR"
echo "  ✅ 清理了 .bak 文件"
echo "  ✅ 删除了空的测试目录"
echo "  ✅ 创建了优化的目录结构"
echo "  ✅ 生成了清理报告: $REPORT_FILE"
echo "  ✅ 创建了文件移动脚本模板: move-files-template.sh"
echo "  ✅ 创建了导入路径更新脚本: update-imports.sh"
echo "  ✅ 创建了清理验证脚本: verify-cleanup.sh"

echo "\n📖 下一步建议:"
echo "  1. 查看清理报告: cat $REPORT_FILE"
echo "  2. 根据报告手动清理调试代码"
echo "  3. 修改并执行文件移动脚本: ./move-files-template.sh"
echo "  4. 更新导入路径: ./update-imports.sh"
echo "  5. 验证清理效果: ./verify-cleanup.sh"

echo "\n⚠️  重要提醒:"
echo "  - 所有操作都已备份到 $BACKUP_DIR"
echo "  - 建议在 Git 中提交当前状态后再进行文件移动"
echo "  - 每次移动文件后都要进行测试"
echo "  - 保持小步快跑，逐步优化"

# 在脚本末尾添加回滚脚本生成
cat > "rollback-cleanup.sh" << EOF
#!/bin/bash
# 清理回滚脚本
# 如果清理后出现问题，使用此脚本回滚

set -e

echo "🔄 开始回滚清理操作..."

if [ -d "$BACKUP_DIR" ]; then
    echo "从备份恢复文件..."
    
    # 恢复 .bak 文件
    find "$BACKUP_DIR" -name "*.bak" | while read backup_file; do
        original_file=\$(echo "\$backup_file" | sed "s|$BACKUP_DIR/||")
        if [ ! -f "\$original_file" ]; then
            echo "恢复: \$original_file"
            cp "\$backup_file" "\$original_file"
        fi
    done
    
    echo "✅ 回滚完成"
else
    echo "❌ 备份目录不存在: $BACKUP_DIR"
    exit 1
fi
EOF

chmod +x "rollback-cleanup.sh"
echo "📄 回滚脚本已创建: rollback-cleanup.sh"