import logger from '@/lib/utils/logger';

const fs = require('fs');

const filePath = 'app/(dashboard)/settings/page.tsx';
const content = fs.readFileSync(filePath, 'utf8');
const lines = content.split('\n');

let braceCount = 0;
let parenCount = 0;
let squareCount = 0;

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    
    if (char === '{') braceCount++;
    else if (char === '}') braceCount--;
    else if (char === '(') parenCount++;
    else if (char === ')') parenCount--;
    else if (char === '[') squareCount++;
    else if (char === ']') squareCount--;
    
    if (braceCount < 0) {
      logger.log(`Extra closing brace at line ${i + 1}, column ${j + 1}`);
      braceCount = 0;
    }
    if (parenCount < 0) {
      logger.log(`Extra closing parenthesis at line ${i + 1}, column ${j + 1}`);
      parenCount = 0;
    }
    if (squareCount < 0) {
      logger.log(`Extra closing square bracket at line ${i + 1}, column ${j + 1}`);
      squareCount = 0;
    }
  }
}

logger.log(`Final counts: braces=${braceCount}, parentheses=${parenCount}, square brackets=${squareCount}`);

if (braceCount > 0) {
  logger.log(`Missing ${braceCount} closing braces`);
}
if (parenCount > 0) {
  logger.log(`Missing ${parenCount} closing parentheses`);
}
if (squareCount > 0) {
  logger.log(`Missing ${squareCount} closing square brackets`);
}
