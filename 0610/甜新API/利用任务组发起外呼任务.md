Title: 利用任务组发起外呼任务--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797847

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

提前设置外呼组，比如视频Bot+音频Bot组合，得到taskGroupId，利用此taskGroupId发起外呼。

**\*注意：该接口目前建议单次最多传输300个手机号的数据量。**

##### 请求URL

*   `/openapi/callout/callList`

##### 接口协议

| 协议类型 | 协议方法 |
| --- | --- |
| https | post |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| content-type | 是 | string | “application/json; charset=utf-8” - 固定值 |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名 | 必选 | 类型 | 说明 |
| --- | --- | --- | --- |
| taskGroupId | 是 | string | 任务组代号，5位数字组成的字符串 |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 账号名 |
| encryptType | 否 | int | 加密方式 1.AES 2.MD5 (默认使用AES) |
| userInfoList | 是 | array | 名单列表 |
| userInfoList.name | 否 | string | 姓名 |
| userInfoList.phone | 是 | string | 手机号或者加密手机号 |
| userInfoList.otherInfo | 否 | array | 备注信息 |
| userInfoList.otherInfo.title | 否 | string | 标题 |
| userInfoList.otherInfo.value | 否 | string | 内容 |

##### 请求示例

复制```
{
    "taskGroupId": "300021",
    "orgCode": "spcd_org",
    "loginName": "ty-louis",
    "userInfoList": [
        {
            "name": "louis",
            "phone": "17612162706",
            "otherInfo": [
                {
                    "title": "aaa",
                    "value": "222"
                }
            ]
        },
        {
            "name": "louis2",
            "phone": "13585924653",
            "otherInfo": [
                {
                    "title": "aaa",
                    "value": "222"
                }
            ]
        }
    ],
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "errInfo": "",
    "result": {
        "batchId": "656eba9403141aa747d5981c"
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败 |
| result | object | 结果 |
| result.batchId | string | 导入id |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 1002 | 登录名错误 |
| 1003 | 没有有效的手机号 |
| 10 | 系统内部错误 |
