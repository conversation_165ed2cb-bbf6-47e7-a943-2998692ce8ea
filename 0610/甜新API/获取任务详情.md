Title: 获取任务详情--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797900

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口类型](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E7%B1%BB%E5%9E%8B)
*   [headers头部](https://doc.vcrm.vip:8800/web/#headers%E5%A4%B4%E9%83%A8)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

获取任务详情

##### 请求URL

*   `/openapi/callout/task/:taskId`

##### 接口类型

| 协议类型 | 协议方法 |
| --- | --- |
| https | get |

| 参数名 | 是否必填 | 类型 | 说明 |
| --- | --- | --- | --- |
| access-token | 是 | string | 见开发前须知中的token/签名生成方式 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| loginName | 是 | string | 发送任务的账号名 |

##### 返回示例

复制```
{
    "errCode": 0,
    "result": {
        "taskId": "6721aeff2c89f300198b8e1a",
        "importNumber": 1,
        "deliverNumber": 1,
        "calledCount": 0,
        "creator": "dev-yll",
        "createdAt": "2024-10-30 11:58:55",
        "settingStartTime": "2024-10-30 11:58:47",
        "startedAt": null,
        "endedAt": null,
        "callType": 2,
        "videoBotName": "G视频外呼0809",
        "videoBotId": "66b5d6cc243b540020bd439f",
        "audioBotName": "4节点-测试语音bot",
        "audioBotId": "66c9a37e00072700215ed358",
        "trunkName": "SIT1-业务组-视频-真实-重庆移动-声动",
        "concurrency": 1,
        "trunkId": "65eab9d57d4fa0259bb2d294",
        "flashMessageTemplateName": "Song of Song",
        "flashMessageTemplateId": "663c6ef2f0f0f60020bba2f8",
        "messageTemplateName": "测试勿动-跳转客户页面",
        "messageTemplateId": "640e95594a4bf8559b2b7e41",
        "videoMessageTemplateName": "617视频短信无变量001",
        "videoMessageTemplateId": "666ff24d4bfaaf00198233ce",
        "blackListGroups": [
            "672198e6d24839afe048f275"
        ],
        "s3Uri": null
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败，具体参见下表 |
| result | object | 结果 |
| result.taskId | string | 任务ID |
| result.importNumber | int | 导入数量 |
| result.deliverNumber | int | 有效数量 |
| result.calledCount | int | 已呼数量 |
| result.creator | string | 创建人 |
| result.createdAt | string | 创建时间 |
| result.settingStartTime | string | 设定开始时间 yyyy-MM-dd HH:mm:ss |
| result.startedAt | string | 发送时间 yyyy-MM-dd HH:mm:ss |
| result.endedAt | string | 完成时间 yyyy-MM-dd HH:mm:ss |
| result.callType | int | 外呼类型 1.视频通知 2.视频互动 3.语音互动 |
| result.videoBotName | string | 外呼内容名称 |
| result.videoBotId | string | 外呼内容ID |
| result.audioBotName | string | 语音分流内容名称 |
| result.audioBotId | string | 语音分流内容ID |
| result.trunkName | string | 任务线路名称 |
| result.trunkId | string | 线路ID |
| result.concurrency | int | 任务并发 |
| result.flashMessageTemplateName | string | 闪信模板 |
| result.flashMessageTemplateId | string | 闪信模板ID |
| result.messageTemplateName | string | 文本短信模板 |
| result.messageTemplateId | string | 文本短信模板ID |
| result.videoMessageTemplateName | string | 视频短信模板 |
| result.videoMessageTemplateId | string | 视频短信模板ID |
| result.blackListGroups | Array<String\> | 客户黑名单分组id |
| result.s3Uri | string | S3地址 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1000 | 参数错误 |
| 1001 | 签名错误 |
| 1002 | 登录名错误 |
| 10 | 系统内部错误 |
