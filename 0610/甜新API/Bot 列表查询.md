Title: Bot 列表查询--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797903

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

可以读取对应账号下个人bot id、个人bot名称。

##### 请求URL

*   `/api/bots/external`

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

##### 参数

| 参数名称 | 是否必填 | 类型 | 描述 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| sign | 是 | string | 加密签名 |
| loginName | 是 | string | 发送任务的账号名 |

##### 请求示例

复制```
{
    "orgCode": "spcd_org",
    "sign": "8db1f84648c43e7d0e3f3927baf6108770f5aa7ff68c6c104de04916e44ae2e9",
    "loginName": "test"
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "result": [
        {
            "_id": "664c2fe6da85ce15be1e981d",
            "name": "测试bot_audio"
        },
        {
            "_id": "664d9ed687117900209dbeca",
            "name": "测试语音bot"
        },
        {
            "_id": "66581b7825b05600202e7123",
            "name": "场景测试bot"
        }
    ]
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | number | 0.成功，其它失败，具体参见下表 |
| result | array | 结果 |
| result\[n\].\_id | string | BotId |
| result\[n\].name | string | Bot 名称 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| 1001 | 签名错误 |
| 1002 | 登录名错误 |
| 10 | 系统内部错误 |
