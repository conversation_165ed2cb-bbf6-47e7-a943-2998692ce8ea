Title: NodeJS-AesUtils--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797737

Markdown Content:
以下是简单的示例代码，方便客户快速接入，具体调整请看需求安排哈

复制```
const CryptoJS = require("crypto-js");

// 运算模式 CBC（密码块链） 
// 填充模式 PKCS7
// 密钥长度 128bit
// 密钥，偏移量类型 Text
// 字符编码 UTF-8
// 输出十六进制形式的密文

const keyText = 'xxxxxxxxxxxx'; // 16字节
const ivText = 'xxxxxxxxxxxx'; // 16字节
class AesUtils {


    cbcEncrypt(plaintext) {
        //// 将文本形式的密钥和初始化向量转换为 WordArray
        const key1 = CryptoJS.enc.Utf8.parse(keyText);
        const iv1 = CryptoJS.enc.Utf8.parse(ivText);
        const encrypted = CryptoJS.AES.encrypt(plaintext, key1, { iv: iv1 }).ciphertext;
        return encrypted.toString();
    }


    cbcdecrypt(encryptStr) {
        // 解密密文
        const encrypted1 = CryptoJS.enc.Hex.parse(encryptStr)
        const key1 = CryptoJS.enc.Utf8.parse(keyText);
        const iv1 = CryptoJS.enc.Utf8.parse(ivText);
        const decrypted = CryptoJS.AES.decrypt({ ciphertext: encrypted1 }, key1, { iv: iv1 });
        // 将解密后的明文转换为 UTF-8 字符串
        const plaintext1 = decrypted.toString(CryptoJS.enc.Utf8);
        return plaintext1;
    }


}

const testUtils = new AesUtils();
// 输出加密后的密文
var encryptStr = testUtils.cbcEncrypt('xxxxxxxxxxxx', keyText, ivText)
console.log("加密后的密文:", encryptStr);

let encryptStr1 = testUtils.cbcdecrypt(encryptStr)
console.log("解密后的明文:", encryptStr1);

module.exports = AesUtils;
```
