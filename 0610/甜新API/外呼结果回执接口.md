Title: 外呼结果回执接口--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797743

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [解密后示例](https://doc.vcrm.vip:8800/web/#%E8%A7%A3%E5%AF%86%E5%90%8E%E7%A4%BA%E4%BE%8B)
*   [解密后参数说明](https://doc.vcrm.vip:8800/web/#%E8%A7%A3%E5%AF%86%E5%90%8E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [返回参数](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0)
*   [备注](https://doc.vcrm.vip:8800/web/#%E5%A4%87%E6%B3%A8)
    

##### 简要描述

在每一通外呼完成后，将外呼结果回调到您的接口，以便完成后续的业务行为  
您需要实现回调接口，并提供回调地址  
回调返回数据为密文，需要进行解密  
目前无重试机制  
注意：响应格式麻烦按照文档上的要求返回哦

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

##### 参数

| 参数名 | 必选 | 类型 | 说明 |
| --- | --- | --- | --- |
| data | 是 | String | 加密数据 |

##### 请求示例

复制```
//请求体内容
 {
   "data": "8b985f7ea8430f85c7eaf70d3f27a140bfbbf01dd46192812de33dec31e52c132f11bc37a5bf692f93e6a4628d6c8bc9454073ed90bc5f450f74dba945aad12ffa98872f5d5d9037803d3b080af030cc2e161bc2fe22c77640ad282101b09c450e1520fa109e53220683bac2a04d262b31d1759b14d82d99518a78d4038b5863ed8f5a1944dd0c5ca5a6d448b7ebcb4473b36fd4ea08d0d678da911000c74da1afe763e7c86825eec2e062d7b0520cc30692ead6b06cebf1b88b3d399db8612e57cbd4f8d7f5e1a861df92a30d935eea32210bb24927fdcffc2215dd00abf02a0c70ba1baf3a87becac05b2ecb260c5f08670db38ff51595495ef8520b1a963c162e01cec721a05762537b27d8d4042833aed4b863bbc96635d72cf26b08f6c9d0070cf90bbb9c60e3d20433d5bd648e299f84956a509e27f5b93a4e76371dbfce13c855c6b17bfd9f58bc133fa3614988722f015e545db7c0db1614eb2f235ad1acf3025992a1c6bc235a3c809eb63563cbd0204177e99211cc2946fe29f1a7f04214700789a3f3faeff14d03ed4954108cdae12582c76d19eb3deb285831983f867c9c9dfce0f4e85dfe1c032f8b2d9b083053db213bf7cde41442cb2b3a39123b1a5e017f85e1f57c047271e5f1528d6457c652444bc12da4282179823194" //通过aes加密后的数据
 }
```

##### 解密后示例

复制```
//通过AES算法解密
{
    "taskGroupId": "30001",
    "mediaDeliverId": "64f17c03157a8daec8767d0c",
    "name": "测试任务",
    "type": 1,
    "templateId": "64f146f4bf373c0019a7d4c8",
    "callId": "64f17c03157a8daec8767d0e",
    "phone": "13585924653",
    "isConnected": 1,
    "holdingTime": 12,
    "userName": "客户名称",
    "connectType": 1,
    "intention": "A",
    "callStartTime": "2023-09-01 10:20:31",
    "callOnTime": "2023-09-01 10:20:50",
    "callEndTime": "2023-09-01 10:21:10",
    "waitingTime": 19,
    "comment": [{"title":"code","value":"UR89J1"},{"title":"URL","value":"invitecode=123"}],
    "callRelation":"本人", 
    "callinCompleteReason":["愿意借款","没兴趣"], 
    "ringingDuration":9 ,
    "totalInteractNum":10,
    "effectiveInteractNum":3, 
    "hungupNode":"开场白挂断", 
    "callNumber":"0212123123", 
    "fullAudioUrl":"https://xxx.xxx.mp3", 
    "hangupBy":"1", 
    "callDetailText":[{"AI":"内容","CUST":"内容"}], 
    "completionRate": "86.7" ,
    "conversationLabels" : [
        "标签意图001",
        "标签意图002",
        "标签意图003",
        "标签意图004"
    ],
    "intentLevelAndLabels" : [
        {
            "intention" : "B",
            "labels" : ["标签意图003","标签意图004"]
        },
        {
            "intention" : "H",
            "labels" : ["标签意图001","标签意图002"]
        }
    ],
    "isFriendAdded": 1

}
```

##### 解密后参数说明

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| taskGroupId | string | 任务组id,通过taskGroupId发起外呼的任务专用 |
| mediaDeliverId | string | 任务id |
| name | string | 任务名称 |
| type | int | 模板类型 1.互动 2.视频模板 3.视频内容 |
| templateId | string | 内容模板id |
| callId | string | 呼叫id |
| phone | string | 手机号码 |
| isConnected | int | 接通状态 1.未接通 2.已接通 |
| holdingTime | float | 接通时长(秒) |
| userName | string | 客户名称 |
| connectType | int | 接通类型 1.视频接通 2.语音接通 |
| intention | string | 本通呼叫中用户的意愿，枚举：A、B、C、D、E、F、G、H、Z |
| callOnTime | string | 呼叫接通时间 yyyy-MM-dd HH:mm:ss |
| callEndTime | string | 呼叫结束时间 yyyy-MM-dd HH:mm:ss |
| waitingTime | float | 呼叫等待时间(秒) |
| comment | array | 备注 |
| comment.title | string | 备注title |
| comment.value | string | 备注内容 |
| callStartTime | string | 呼叫开始时间 yyyy-MM-dd HH:mm:ss |
| reason | string | **_原因结果 未呼叫：_**黑名单、高频呼叫、敏感地区 ； 呼叫结束：语音接听、视频接听、被叫未振铃、被叫拒接、主叫挂断、通话异常（xxxx） |
| callRelation | string | 与本人关系（”本人”,”三方”） |
| callinCompleteReason | array | 实际营销情况（例如\[“愿意借款”,”没兴趣”\]） |
| ringingDuration | int | 用户振铃时长(秒) |
| totalInteractNum | int | 总交互轮次 |
| effectiveInteractNum | int | 有效交互轮次 |
| hungupNode | string | 挂断节点（如：”开场白挂断”） |
| callNumber | string | 主叫号码 |
| fullAudioUrl | string | 录音地址（未接通为空字符串，默认存储一个月） |
| hangupBy | string | 挂断方（”1” 代表被叫挂断，”2” 代表主叫挂断） |
| callDetailText | array | 交付文本（\[{“AI”:”XX”,”CUST”:”XXX”}\] |
| completionRate | string | 完播率，带1位小数的百分数字符串，诸如”89.1” |
| isSMSSent | int | 是否发送挂机短信, 0否，1是 |
| conversationLabels | array | 该通话汇总的对话标签列表, 如：\[“XX”,”BB”\] |
| intentLevelAndLabels | array | 意图标签和对话标签列表 |
| intentLevelAndLabels\[n\].intention | string | 意图标签 |
| intentLevelAndLabels\[n\].labels | array | 对话标签列表 |
| isFriendAdded | int | 是否发起加微信好友, 0否，1是 |

##### 返回参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| errCode | int | 0.成功，其它失败 |
| errInfo | string | 失败信息 |

##### 备注

*   更多返回错误代码请看首页的错误代码描述
