Title: 代码示例-发起外呼任务--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797736

Markdown Content:
复制```
//示例请求数据
//{
//    orgCode: "xxxxxxxxxx",
//   "sign": "" + sign + "",
//    "loginName": "xxxxxxxxxx",
//    "name": "测试专用",
//   "mediaType": "videoBot",
//   "videoBotId": "xxxxxxxxxx",
//    "deliverList": [
//        {
//            "phone": "xxxxxxxxxx",
//            "otherInfo": [
//                {
//                    "title": "备注",
//                    "value": "1233"
//               }
//           ]
//       }
//    ],
//    "isRelateSendMessage": false,
//    "isVoiceDivide": false,
//    "audioBotId": ""
//}

const request = require('request');
const AesUtils = require('./aesutils.js');

const aesUtils = new AesUtils();

let time = Math.floor(Date.now() / 1000);
let orgCode = "xxxxxxxxxx";

const sign = aesUtils.cbcEncrypt(`${orgCode}${time}`)
console.log("sign" + sign);
const post_data = {
    "orgCode": "xxxxxxxxxx",
    "sign": "" + sign + "",
    "loginName": "xxxxxxxxxx",
    "name": "测试专用",
    "mediaType": "videoBot",
    "videoBotId": "xxxxxxxxxx",
    "deliverList": [
        {
            "phone": "xxxxxxxxxx",
            "otherInfo": [
                {
                    "title": "备注",
                    "value": "1233"
                }
            ]
        }
    ],
    "isRelateSendMessage": false,
    "isVoiceDivide": false,
    "audioBotId": ""
};

const url = 'https://services.vcrm.vip:8000/api/mediaDeliverPlatform/external/create';

function requestServer() {
    const options = {
        url: url,
        method: 'POST',
        json: true,
        headers: {
            'Content-Type': 'application/json'
        },
        body: post_data
    };
    request(options, (error, response, body) => {
        if(error){
            console.error('error:' + JSON.stringify(error));
        }

        console.log('response:' + JSON.stringify(response));
        console.log('body:' + JSON.stringify(body));
    })

}
console.log('请求返回:' + requestServer());
```
