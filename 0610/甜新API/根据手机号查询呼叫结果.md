Title: 根据手机号查询呼叫结果--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797745

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [请求URL](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82URL)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回示例](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E7%A4%BA%E4%BE%8B)
*   [返回参数说明](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E)
*   [错误码描述](https://doc.vcrm.vip:8800/web/#%E9%94%99%E8%AF%AF%E7%A0%81%E6%8F%8F%E8%BF%B0)
    

##### 简要描述

针对回调异常等情况，可以通过该接口主动查询补偿相应的外呼结果

##### 请求URL

*   `/api/mediaDeliverPlatform/external/queryCallId`

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

##### 参数

| 参数名 | 必选 | 类型 | 说明 |
| --- | --- | --- | --- |
| orgCode | 是 | string | 机构代码，固定值，由对接人员提供 |
| sign | 是 | string | 加密签名 |
| phone | 是 | string | 查询呼叫记录的手机号 |
| beginTime | 否 | number | 查询任务创建起始时间戳，单位毫秒 |
| endTime | 否 | number | 查询任务创建截止时间戳，单位毫秒 |

##### 请求示例

复制```
{
  "orgCode": "spcd_org", 
  "sign": "xxx",
  "phone": "13631470285",
  "beginTime": 1663831958000,
  "endTime": 1695367958000
}
```

##### 返回示例

复制```
{
    "errCode": 0,
    "result": {
        "callList": [
            {
                "name": "14739_thb视频外呼任务01_4",
                "type": 3,
                "callId": "650bffa2a7d44c00198c961f",
                "taskId": "650bffa2a7d44c00198c961e",
                "templateId": "64ffc4d2a9281400190aa8c5",
                "phone": "13631470285",
                "intention": "A",
                "callOnTime": "2023-09-21 16:32:52",
                "status": 0,
                "connectType": "video",
                "waitingTime": 5.78,
                "holdingTime": 10.946,
                "callStartTime": "2023-09-21 16:32:46",
                "callEndTime": "2023-09-21 16:33:03",
                "comment": [
                    {
                        "title": "params",
                        "value": "14739:4:3314091a-4e3a-4ec7-8be1-a33ef72691cd"
                    }
                ]
            }
    }
}
```

##### 返回参数说明

| 参数名称 | 类型 | 描述 |
| --- | --- | --- |
| errCode | number | 0.成功，其它失败 |
| result | object | 结果 |
| result.callList | array | 呼叫信息数组 |
| result.callList.name | string | 任务名称 |
| result.callList.type | number | 模板类型 1.互动 2.视频模板 3.视频内容 |
| result.callList.templateId | string | 模板id |
| result.callList.phone | string | 手机号 |
| result.callList.userName | string | 客户名称 |
| result.callList.intention | string | 本通呼叫中用户的意愿，枚举：A、B、C、D、E、F、G、H、Z。  
未接通为Z，接通时由运营的配置决定（未配置时则接通默认为A） |
| result.callList.taskId | string | 任务id |
| result.callList.callId | string | 呼叫唯一id |
| result.callList.callOnTime | string | 呼叫接通时间，格式YYYY-MM-DD HH:mm:ss |
| result.callList.status | number | 接通状态， 0 接通，1未接通 |
| result.callList.connectType | number | 接通类型 1.视频接通 2.语音接通 |
| result.callList.waitingTime | number | 呼叫等待时间， 单位：秒 |
| result.callList.holdingTime | number | 通话时长，单位：秒 |
| result.callList.callStartTime | string | 外呼时间， YYYY-MM-DD HH:mm:ss |
| result.callList.callEndTime | string | 挂断时间， YYYY-MM-DD HH:mm:ss |
| result.callList.comment | array | 备注 |
| result.callList.comment.title | string | 备注title |
| result.callList.comment.value | string | 备注内容 |
| result.callList.mediaDeliverId | string | 任务id |
| result.callList.isConnected | number | 接通状态 1.未接通 2.已接通 |
| result.callList.connectTypeCode | number | 接通类型 1.视频接通 2.语音接通 |
| result.callList.reason | string | 原因结果 未呼叫：号码检测中、黑名单、高频呼叫、敏感地区、任务取消 呼叫中：排队中、呼叫中 呼叫结束：语音接听、视频接听、被叫未振铃、被叫拒接、主叫挂断、通话异常（xxxx） |
| result.callList.createdAt | string | 创建时间, YYYY-MM-DD HH:mm:ss |
| result.callList.callRelation | string | 与本人关系（”本人”,”三方”） |
| result.callList.callinCompleteReason | array | 实际营销情况（例如\[“愿意借款”,”没兴趣”\]） |
| result.callList.ringingDuration | number | 用户振铃时长(秒) |
| result.callList.totalInteractNum | number | 总交互轮次 |
| result.callList.effectiveInteractNum | number | 有效交互轮次 |
| result.callList.hungupNode | string | 挂断节点（如：”开场白挂断”） |
| result.callList.callNumber | string | 主叫号码 |
| result.callList.fullAudioUrl | string | 录音地址（未接通为空字符串，默认存储一个月） |
| result.callList.hangupBy | string | 挂断方（”1” 代表被叫挂断，”2” 代表主叫挂断） |
| result.callList.callDetailText | array | 交付文本（\[{“AI”:”XX”,”CUST”:”XXX”}\] |
| result.callList.completionRate | string | 完播率，带1位小数的百分数字符串，诸如”89.1” |
| result.callList.isSMSSent | number | 是否发送挂机短信, 0否，1是 |

##### 错误码描述

| 错误码 | 描述 |
| --- | --- |
| \-1 | 参数错误 |
| \-2 | 找不到此机构 |
| 10 | 其它异常错误 |
