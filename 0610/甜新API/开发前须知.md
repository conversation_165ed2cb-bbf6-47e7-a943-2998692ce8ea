Title: 开发前须知--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797730

Markdown Content:
*   [1.请求地址](https://doc.vcrm.vip:8800/web/#1.%E8%AF%B7%E6%B1%82%E5%9C%B0%E5%9D%80)
*   [2.鉴权相关参数(由对接人员提供)：](https://doc.vcrm.vip:8800/web/#2.%E9%89%B4%E6%9D%83%E7%9B%B8%E5%85%B3%E5%8F%82%E6%95%B0(%E7%94%B1%E5%AF%B9%E6%8E%A5%E4%BA%BA%E5%91%98%E6%8F%90%E4%BE%9B)%EF%BC%9A)
*   [3.token/签名生成](https://doc.vcrm.vip:8800/web/#3.token/%E7%AD%BE%E5%90%8D%E7%94%9F%E6%88%90)
*   [4.加解密demo（Java版本）](https://doc.vcrm.vip:8800/web/#4.%E5%8A%A0%E8%A7%A3%E5%AF%86demo%EF%BC%88Java%E7%89%88%E6%9C%AC%EF%BC%89)
*   [具体加密代码见以下目录:](https://doc.vcrm.vip:8800/web/#%E5%85%B7%E4%BD%93%E5%8A%A0%E5%AF%86%E4%BB%A3%E7%A0%81%E8%A7%81%E4%BB%A5%E4%B8%8B%E7%9B%AE%E5%BD%95:)
    

##### 1.请求地址

[https://services.vcrm.vip:8000](https://services.vcrm.vip:8000/)

##### 2.鉴权相关参数(由对接人员提供)：

| 参数名称 | 参数值示例（只是示例，不是实际使用值） | 描述 |
| --- | --- | --- |
| orgCode | spcd\_org | 机构码 |
| aesKey | 2f91918af948d1e0 | aesKey:用于数据加解密 |
| aesIv | 252735dd9387c861 | aesIv:用于数据加解密 |
| loginName | test | 账号名 |

##### 3.token/签名生成

将orgCode的内容和当前时间戳（单位秒）拼接起来后，对该字符串进行aes-128-cbc方式加密后即得到加密签名。例如时间戳为1669880287，加密前的字符串即为 spcd\_org1669880287，加密后得到的签名为 8db1f84648c43e7d0e3f3927baf6108770f5aa7ff68c6c104de04916e44ae2e9。  
有效期为5分钟。

##### 4.加解密demo（Java版本）

复制```
package com.example.demo;

public class Test {
    /**
     * 测试环境AES KEY：2f91918af948d1e0
     * 测试环境AES IV：252735dd9387c861
     * 外呼任务手机号支持加密： 加密前13500000003， 对该字符串进行aes-128-cbc方式加密后4bf8ae0ba3e8c0f17d885b8c5b0af602，加密所用key和iv与外呼加密签名key/iv一致。
     * @param args
     * @throws Exception
     */

    public static void main(String[] args) throws Exception {
        String result = AesUtil.encrypt("13500000003");
        System.out.println(result);
    }

}
```

#### 具体加密代码见以下目录:

[Java-Aesutils](https://doc.vcrm.vip:8800/web/#/662402387/122797429 "java")  
[Python-Aesutils](https://doc.vcrm.vip:8800/web/#/662402387/122797511 "python")  
[NodeJS-Aesutils](https://doc.vcrm.vip:8800/web/#/662402387/122797513 "NodeJS")  
[Go-Aesutils](https://doc.vcrm.vip:8800/web/#/662402387/122797514 "Go")
