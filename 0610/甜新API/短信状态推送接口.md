Title: 短信状态推送接口--ShowDoc

URL Source: https://doc.vcrm.vip:8800/web/#/662402432/122797898

Markdown Content:
*   [简要描述](https://doc.vcrm.vip:8800/web/#%E7%AE%80%E8%A6%81%E6%8F%8F%E8%BF%B0)
*   [接口协议](https://doc.vcrm.vip:8800/web/#%E6%8E%A5%E5%8F%A3%E5%8D%8F%E8%AE%AE)
*   [参数](https://doc.vcrm.vip:8800/web/#%E5%8F%82%E6%95%B0)
*   [解密后参数](https://doc.vcrm.vip:8800/web/#%E8%A7%A3%E5%AF%86%E5%90%8E%E5%8F%82%E6%95%B0)
*   [请求示例](https://doc.vcrm.vip:8800/web/#%E8%AF%B7%E6%B1%82%E7%A4%BA%E4%BE%8B)
*   [返回参数](https://doc.vcrm.vip:8800/web/#%E8%BF%94%E5%9B%9E%E5%8F%82%E6%95%B0)
    

##### 简要描述

用于通知客户短信状态，回执发送时机有2：发送失败或者短信平台产生接收回执时  
注意：响应格式麻烦按照文档上的要求返回哦

##### 接口协议

| 协议类型 | 协议方法 | 头部 |
| --- | --- | --- |
| https | post | application/json; charset=utf-8 |

##### 参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| data | string | 所有参数通过 AES 加密后字符串 |

##### 解密后参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| msgId | string | 短信记录id |
| taskId | string | 任务id |
| callId | string | 通话id，这条短信对应的通话 |
| templateId | string | 内容模板id |
| signature | string | 短信签名（仅文本短信含有，对应该模板的签名） |
| content | string | 短信内容（仅文本短信含有，对应该模板的内容） |
| commitResult | int | 短信提交状态code，该字段代表外呼平台将短信推给运营商的结果。0：提交失败，1：提交成功 |
| sendTime | string | 短信发送时间，格式比如2024-05-17 15:58:48 |
| sendResult | int | 短信发送状态code，该字段代表运营商用户下发短信的结果。0：接收成功，1：接收失败 |
| recvTime | string | 短信接收时间 |
| callOnTime | string | 呼叫接通时间 yyyy-MM-dd HH:mm:ss |
| callEndTime | string | 呼叫结束时间 yyyy-MM-dd HH:mm:ss |
| phone | string | 手机号 |

##### 请求示例

复制```
// 请求体内容
{data: "49191de74566b58445c009735b601dece6550953c2e14c041ed63879b413d43819f3c2ae199bcbf354f449fb3dd29689ae283d11494df890cea8367d46e232724985fdfea5544e0684edef873df393cf30e143a3ea1a708a6bbe3fea7a77fac3031fabf8d369a130cce0f1a91d658f811420b70c307f6589152352654497c351559fd7208f588b588f5021aaf16177ce36c2d848adeee04076667abd8e2667974f559a4234f2d69da4a529c612bf47d73bb398cd4fd878177f82eebc5801f2075ca5e149971ea5d5e0be3826779cbc862522b60d4e32bc681f3cd7006b5d75fd16d9137e205271d234963e5ff345667a73e1e5ea5c1d6601786f464d17cf6f9b65458c4cfb702f4833b6550b33c4df4adc19c3095b516d3f1b892f876b483ed7b87eca6ee05a72216d1e4ed0e5f38b6ad4d7dd8e4969809f851c41421a5985b3ab77a4080ae1b1b17b5f1527bf7844ca9b0ecc0f01ac9bfe8b14094265cefc7745a8911d0c0a52b20125843d58cb5dbbdd7e73cd5d15b750981ecedd70b80d4f"}

//请求体 data 通过 AES 解密后参数示例
{
    "msgId": "darenSms319922828017621680660",
    "taskId": "667a35cb320e660019dc5404",
    "callId": "667a35cb320e660019dc5406",
    "templateId": "0000",
    "signature": "抹茶手霜",
    "content": "美美哒",
    "commitResult": 1,
    "sendTime": "2024-07-09T07:56:25.930Z",
    "sendResult": 0,
    "recvTime": "2024-08-14T11:00:52.941Z",
    "callOnTime": "2024-06-26T08:00:34.303Z",
    "callEndTime": "2024-06-26T08:00:44.303Z",
    "phone": "19000000001",
}
```

##### 返回参数

正常响应接口状态码 200 即可。
