/**
 * PM2 配置文件
 * 用于生产环境部署和进程管理
 */

module.exports = {
  apps: [
    {
      name: "5gweb",
      script: "node_modules/next/dist/bin/next",
      args: "start",
      instances: "max", // 使用所有可用CPU核心
      exec_mode: "cluster", // 集群模式
      watch: false, // 不监视文件变化
      env: {
        NODE_ENV: "production",
        PORT: 3000
      },
      // 资源限制
      max_memory_restart: "1G", // 内存超过1G时自动重启
      // 日志配置
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      combine_logs: true,
      error_file: "logs/error.log",
      out_file: "logs/out.log",
      // 重启策略
      exp_backoff_restart_delay: 100, // 重启延迟，避免频繁重启
      // 优雅关闭
      kill_timeout: 5000, // 给进程5秒时间优雅关闭
      // 监控
      merge_logs: true,
      // 启动配置
      wait_ready: true, // 等待进程发送ready信号
      listen_timeout: 50000, // 等待进程监听端口的超时时间
      // 健康检查
      max_restarts: 10, // 最大重启次数
      restart_delay: 4000, // 重启延迟
      autorestart: true, // 自动重启
    }
  ],
  // 部署配置
  deploy: {
    production: {
      user: "deploy",
      host: ["your-production-server"],
      ref: "origin/main",
      repo: "**************:your-username/your-repo.git",
      path: "/var/www/production",
      "post-deploy": "npm ci && npm run build && pm2 reload ecosystem.config.js --env production",
      env: {
        NODE_ENV: "production"
      }
    },
    staging: {
      user: "deploy",
      host: ["your-staging-server"],
      ref: "origin/develop",
      repo: "**************:your-username/your-repo.git",
      path: "/var/www/staging",
      "post-deploy": "npm ci && npm run build && pm2 reload ecosystem.config.js --env staging",
      env: {
        NODE_ENV: "staging"
      }
    }
  }
};
