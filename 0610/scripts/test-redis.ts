import Redis from 'ioredis'

const redis = new Redis({
  host: 'localhost',
  port: 6379,
  password: process.env.REDIS_PASSWORD
})

async function testRedis() {
  try {
    // 测试写入
    await redis.set('test_key', 'test_value')
    console.log('✅ 写入测试成功')

    // 测试读取
    const value = await redis.get('test_key')
    console.log('✅ 读取测试成功:', value)

    // 测试删除
    await redis.del('test_key')
    console.log('✅ 删除测试成功')

    // 测试过期时间
    await redis.set('temp_key', 'temp_value', 'EX', 5)
    console.log('✅ 设置过期时间成功')

    // 等待 6 秒后检查是否过期
    setTimeout(async () => {
      const expiredValue = await redis.get('temp_key')
      console.log('✅ 过期测试成功:', expiredValue === null)
    }, 6000)

  } catch (error) {
    console.error('❌ Redis 测试失败:', error)
  }
}

testRedis() 