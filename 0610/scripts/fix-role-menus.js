/**
 * 修复角色-菜单关联脚本
 * 
 * 这个脚本用于修复 _RoleMenus 表中的外键关系问题
 * 当前问题：A 列引用 menu.id，B 列引用 role.id，但实际上应该是相反的
 * 
 * 解决方案：
 * 1. 备份当前数据
 * 2. 删除当前的外键约束
 * 3. 重新创建正确的外键约束
 * 4. 为管理员角色添加所有菜单权限
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始修复角色-菜单关联...');
    
    // 1. 备份当前数据
    console.log('备份当前数据...');
    const roleMenus = await prisma.$queryRaw`SELECT * FROM "_RoleMenus"`;
    console.log(`当前有 ${roleMenus.length} 条角色-菜单关联记录`);
    
    // 2. 清空当前表
    console.log('清空当前表...');
    await prisma.$executeRaw`DELETE FROM "_RoleMenus"`;
    
    // 3. 获取管理员角色ID
    console.log('获取管理员角色ID...');
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' }
    });
    
    if (!adminRole) {
      throw new Error('找不到管理员角色');
    }
    
    console.log(`管理员角色ID: ${adminRole.id}`);
    
    // 4. 获取所有可见菜单
    console.log('获取所有可见菜单...');
    const menus = await prisma.menu.findMany({
      where: { visible: true }
    });
    
    console.log(`找到 ${menus.length} 个可见菜单`);
    
    // 5. 为管理员角色添加所有菜单权限
    console.log('为管理员角色添加所有菜单权限...');
    
    // 使用事务批量插入
    await prisma.$transaction(
      menus.map(menu => 
        prisma.$executeRaw`INSERT INTO "_RoleMenus" ("B", "A") VALUES (${menu.id}, ${adminRole.id})`
      )
    );
    
    console.log('角色-菜单关联修复完成');
    
    // 6. 验证修复结果
    const count = await prisma.$queryRaw`SELECT COUNT(*) FROM "_RoleMenus" WHERE "A" = ${adminRole.id}`;
    console.log(`管理员角色现在有 ${count[0].count} 个菜单权限`);
    
  } catch (error) {
    console.error('修复角色-菜单关联失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
