// 更新菜单名称的脚本
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    // 更新用户管理菜单为客户管理
    const accountsMenu = await prisma.menu.update({
      where: { code: 'accounts' },
      data: {
        name: '客户管理'
      }
    })
    console.log('菜单名称更新成功:', accountsMenu)

    // 更新用户列表菜单为客户列表
    const customerMenu = await prisma.menu.update({
      where: { code: 'customer' },
      data: {
        name: '客户列表'
      }
    })
    console.log('菜单名称更新成功:', customerMenu)

    console.log('菜单名称更新完成')
  } catch (error) {
    console.error('更新菜单名称失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
