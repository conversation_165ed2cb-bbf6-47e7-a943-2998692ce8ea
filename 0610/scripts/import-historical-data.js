/**
 * 一次性导入历史数据脚本
 * 从远程接口获取所有历史任务和外呼详情数据，并导入到本地数据库中
 *
 * 使用方法：
 * node scripts/import-historical-data.js
 */

// 导入必要的模块
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const path = require('path');
const fs = require('fs');

// 创建日志函数
const log = (message) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);

  // 同时写入日志文件
  fs.appendFileSync(
    path.join(__dirname, 'import-log.txt'),
    `[${timestamp}] ${message}\n`
  );
};

// 导入视频外呼服务
const videoCallService = {
  // 获取历史任务数据
  async getHistoricalTasks() {
    try {
      log('从远程接口获取历史任务数据...');

      // 这里是模拟数据，实际应该调用远程接口
      // 在实际实现中，您需要替换为真实的API调用
      const tasks = [
        {
          id: 'task-001',
          name: '5G视频通知任务1',
          type: '5G视频通知',
          content: '通知内容1',
          status: '已完成',
          progress: 100,
          startTime: '2023-01-01T08:00:00Z',
          endTime: '2023-01-01T10:00:00Z',
          createdBy: '系统导入',
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-01T07:00:00Z',
          updatedAt: '2023-01-01T10:00:00Z'
        },
        {
          id: 'task-002',
          name: '5G视频互动任务1',
          type: '5G视频互动',
          content: '互动内容1',
          status: '已完成',
          progress: 100,
          startTime: '2023-01-02T08:00:00Z',
          endTime: '2023-01-02T10:00:00Z',
          createdBy: '系统导入',
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-02T07:00:00Z',
          updatedAt: '2023-01-02T10:00:00Z'
        },
        {
          id: 'task-003',
          name: '5G语音通话任务1',
          type: '5G语音通话',
          content: '通话内容1',
          status: '已完成',
          progress: 100,
          startTime: '2023-01-03T08:00:00Z',
          endTime: '2023-01-03T10:00:00Z',
          createdBy: '系统导入',
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-03T07:00:00Z',
          updatedAt: '2023-01-03T10:00:00Z'
        }
      ];

      log(`获取到 ${tasks.length} 条历史任务数据`);
      return tasks;
    } catch (error) {
      log(`获取历史任务数据失败: ${error.message}`);
      return [];
    }
  },

  // 获取历史外呼详情数据
  async getHistoricalCallDetails() {
    try {
      log('从远程接口获取历史外呼详情数据...');

      // 这里是模拟数据，实际应该调用远程接口
      // 在实际实现中，您需要替换为真实的API调用
      const callDetails = [
        {
          id: 'call-001',
          taskId: 'task-001',
          taskName: '5G视频通知任务1',
          type: '5G视频通知',
          content: '通知内容1',
          customerName: '张三',
          phoneNumber: '13800000001',
          connectionType: '视频接通',
          startTime: '2023-01-01T08:10:00Z',
          endTime: '2023-01-01T08:15:00Z',
          duration: '5分钟',
          ringTime: '10秒',
          intention: 'A',
          recordingUrl: 'https://example.com/recordings/001.mp4',
          completionRate: 0.95,
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-01T08:10:00Z',
          updatedAt: '2023-01-01T08:15:00Z'
        },
        {
          id: 'call-002',
          taskId: 'task-001',
          taskName: '5G视频通知任务1',
          type: '5G视频通知',
          content: '通知内容1',
          customerName: '李四',
          phoneNumber: '13800000002',
          connectionType: '语音接通',
          startTime: '2023-01-01T08:20:00Z',
          endTime: '2023-01-01T08:25:00Z',
          duration: '5分钟',
          ringTime: '8秒',
          intention: 'B',
          recordingUrl: 'https://example.com/recordings/002.mp4',
          completionRate: 0.85,
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-01T08:20:00Z',
          updatedAt: '2023-01-01T08:25:00Z'
        },
        {
          id: 'call-003',
          taskId: 'task-002',
          taskName: '5G视频互动任务1',
          type: '5G视频互动',
          content: '互动内容1',
          customerName: '王五',
          phoneNumber: '13800000003',
          connectionType: '视频接通',
          startTime: '2023-01-02T08:10:00Z',
          endTime: '2023-01-02T08:20:00Z',
          duration: '10分钟',
          ringTime: '5秒',
          intention: 'A',
          recordingUrl: 'https://example.com/recordings/003.mp4',
          completionRate: 0.98,
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-02T08:10:00Z',
          updatedAt: '2023-01-02T08:20:00Z'
        },
        {
          id: 'call-004',
          taskId: 'task-003',
          taskName: '5G语音通话任务1',
          type: '5G语音通话',
          content: '通话内容1',
          customerName: '赵六',
          phoneNumber: '13800000004',
          connectionType: '语音接通',
          startTime: '2023-01-03T08:10:00Z',
          endTime: '2023-01-03T08:18:00Z',
          duration: '8分钟',
          ringTime: '3秒',
          intention: 'C',
          recordingUrl: 'https://example.com/recordings/004.mp4',
          completionRate: 0.75,
          userId: 'cm9kqdehc00015415v1qz264y', // admin2 用户ID
          createdAt: '2023-01-03T08:10:00Z',
          updatedAt: '2023-01-03T08:18:00Z'
        }
      ];

      log(`获取到 ${callDetails.length} 条历史外呼详情数据`);
      return callDetails;
    } catch (error) {
      log(`获取历史外呼详情数据失败: ${error.message}`);
      return [];
    }
  }
};

// 导入历史任务数据
async function importHistoricalTasks() {
  try {
    log('开始导入历史任务数据...');

    // 1. 获取历史任务数据
    const historicalTasks = await videoCallService.getHistoricalTasks();

    if (historicalTasks.length === 0) {
      log('没有找到历史任务数据');
      return { success: true, importedCount: 0, totalCount: 0 };
    }

    // 2. 检查哪些任务尚未在本地数据库中
    const existingTaskIds = new Set((await prisma.task.findMany({
      where: { externalId: { not: null } },
      select: { externalId: true }
    })).map(task => task.externalId));

    const newTasks = historicalTasks.filter(task => !existingTaskIds.has(task.id));
    log(`需要导入 ${newTasks.length} 条新历史任务数据`);

    // 3. 导入新任务数据
    let importedCount = 0;
    if (newTasks.length > 0) {
      // 使用截图中的用户ID
      const defaultUserId = 'cm9kqdehc00015415v1qz264y'; // admin2 用户ID

      // 批量导入任务数据
      const tasksToImport = newTasks.map(task => ({
        name: task.name,
        type: task.type,
        content: task.content,
        status: task.status,
        progress: task.progress || 0,
        importTime: new Date(),
        startTime: new Date(task.startTime),
        completionTime: task.endTime ? new Date(task.endTime) : null,
        creator: task.createdBy || '系统导入',
        userId: task.userId || defaultUserId, // 如果没有用户ID，则使用默认管理员ID
        externalId: task.id,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt || task.createdAt)
      }));

      // 逐个导入任务数据
      for (const taskData of tasksToImport) {
        try {
          await prisma.task.create({
            data: taskData
          });
          importedCount++;
          log(`已导入 ${importedCount}/${tasksToImport.length} 条任务数据`);
        } catch (error) {
          log(`导入任务失败: ${error.message}`);
        }
      }
    }

    return { success: true, importedCount, totalCount: historicalTasks.length };
  } catch (error) {
    log(`导入历史任务数据失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 导入历史外呼详情数据
async function importHistoricalCallDetails() {
  try {
    log('开始导入历史外呼详情数据...');

    // 1. 获取历史外呼详情数据
    const historicalCallDetails = await videoCallService.getHistoricalCallDetails();

    if (historicalCallDetails.length === 0) {
      log('没有找到历史外呼详情数据');
      return { success: true, importedCount: 0, totalCount: 0 };
    }

    // 2. 检查哪些外呼详情尚未在本地数据库中
    const existingCallDetailIds = new Set((await prisma.call_detail.findMany({
      where: { externalCallId: { not: null } },
      select: { externalCallId: true }
    })).map(detail => detail.externalCallId));

    const newCallDetails = historicalCallDetails.filter(detail => !existingCallDetailIds.has(detail.id));
    log(`需要导入 ${newCallDetails.length} 条新历史外呼详情数据`);

    // 3. 导入新外呼详情数据
    let importedCount = 0;
    if (newCallDetails.length > 0) {
      // 获取所有任务的映射关系（externalId -> id）
      const taskMap = new Map((await prisma.task.findMany({
        where: { externalId: { not: null } },
        select: { id: true, externalId: true }
      })).map(task => [task.externalId, task.id]));

      // 使用截图中的用户ID
      const defaultUserId = 'cm9kqdehc00015415v1qz264y'; // admin2 用户ID

      // 批量导入外呼详情数据
      const callDetailsToImport = newCallDetails
        .filter(detail => taskMap.has(detail.taskId)) // 只导入有对应任务的外呼详情
        .map(detail => ({
          taskId: taskMap.get(detail.taskId), // 使用本地任务ID
          taskName: detail.taskName,
          type: detail.type,
          content: detail.content,
          customerName: detail.customerName,
          phoneNumber: detail.phoneNumber,
          connectionType: detail.connectionType,
          startTime: detail.startTime ? new Date(detail.startTime) : null,
          endTime: detail.endTime ? new Date(detail.endTime) : null,
          duration: detail.duration,
          ringTime: detail.ringTime,
          intention: detail.intention,
          externalCallId: detail.id,
          recordingUrl: detail.recordingUrl,
          completionRate: detail.completionRate,
          userId: detail.userId || defaultUserId, // 如果没有用户ID，则使用默认管理员ID
          createdAt: new Date(detail.createdAt),
          updatedAt: new Date(detail.updatedAt || detail.createdAt)
        }));

      // 逐个导入外呼详情数据
      for (const detailData of callDetailsToImport) {
        try {
          await prisma.call_detail.create({
            data: detailData
          });
          importedCount++;
          log(`已导入 ${importedCount}/${callDetailsToImport.length} 条外呼详情数据`);
        } catch (error) {
          log(`导入外呼详情失败: ${error.message}`);
        }
      }
    }

    return { success: true, importedCount, totalCount: historicalCallDetails.length };
  } catch (error) {
    log(`导入历史外呼详情数据失败: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// 主函数
async function main() {
  try {
    log('开始一次性导入所有历史数据...');

    const startTime = Date.now();

    // 1. 导入历史任务数据
    const tasksResult = await importHistoricalTasks();

    // 2. 导入历史外呼详情数据
    const callDetailsResult = await importHistoricalCallDetails();

    // 3. 计算导入耗时
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);

    // 4. 输出导入结果
    log('历史数据导入完成!');
    log(`导入任务数据: ${tasksResult.importedCount}/${tasksResult.totalCount} 条`);
    log(`导入外呼详情数据: ${callDetailsResult.importedCount}/${callDetailsResult.totalCount} 条`);
    log(`总耗时: ${duration} 秒`);

    // 5. 关闭数据库连接
    await prisma.$disconnect();
  } catch (error) {
    log(`导入历史数据失败: ${error.message}`);

    // 关闭数据库连接
    await prisma.$disconnect();
    process.exit(1);
  }
}

// 执行主函数
main();
