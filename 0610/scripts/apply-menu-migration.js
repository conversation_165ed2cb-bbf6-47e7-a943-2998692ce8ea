// 应用菜单结构更新迁移脚本
const { PrismaClient } = require('@prisma/client');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始应用菜单结构更新...');
    
    // 1. 备份当前菜单数据
    const menus = await prisma.menu.findMany();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(__dirname, '..', `menu-backup-${timestamp}.json`);
    
    fs.writeFileSync(backupPath, JSON.stringify(menus, null, 2));
    console.log(`菜单数据已备份到: ${backupPath}`);
    
    // 2. 执行迁移SQL脚本
    const migrationPath = path.join(__dirname, '..', 'prisma', 'migrations', '20250501000002_update_menu_structure', 'migration.sql');
    const sqlContent = fs.readFileSync(migrationPath, 'utf8');
    
    // 使用Prisma执行原始SQL
    console.log('执行菜单结构更新SQL...');
    const statements = sqlContent.split(';').filter(stmt => stmt.trim());
    
    for (const stmt of statements) {
      if (stmt.trim()) {
        await prisma.$executeRawUnsafe(`${stmt};`);
      }
    }
    
    console.log('菜单结构更新完成!');
    
    // 3. 验证更新结果
    const updatedMenus = await prisma.menu.findMany({
      where: {
        OR: [
          { code: 'system' },
          { code: 'customer-management' },
          { code: 'customers' },
          { code: 'rates' },
          { code: 'menu-settings' }
        ]
      }
    });
    
    console.log('更新后的菜单项:');
    updatedMenus.forEach(menu => {
      console.log(`- ${menu.name} (${menu.code}): 路径=${menu.path}, 父菜单ID=${menu.parentId || 'null'}`);
    });
    
  } catch (error) {
    console.error('菜单结构更新失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log('脚本执行完成'))
  .catch(e => {
    console.error('脚本执行失败:', e);
    process.exit(1);
  });
