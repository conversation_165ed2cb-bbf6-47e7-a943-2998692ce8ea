/**
 * 导入未来任务数据脚本
 * 根据远程截图中的日期创建未来任务数据
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('开始导入未来任务数据...');

    // 1. 获取有效的用户ID
    const user = await prisma.user.findFirst({
      where: {
        username: 'admin'
      }
    });

    if (!user) {
      throw new Error('找不到有效的用户');
    }

    console.log(`使用用户ID: ${user.id}`);

    // 2. 创建未来任务数据和对应的通话记录数量
    const futureTasks = [
      {
        task: {
          id: 'task-20250321',
          name: '0321001',
          type: '5G视频通知',
          content: '通知内容',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-21T14:26:34'),
          startTime: new Date('2025-03-21T18:36:27'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250321',
          createdAt: new Date('2025-03-21T14:26:34'),
          updatedAt: new Date('2025-03-21T14:26:34')
        },
        callCount: 8
      },
      {
        task: {
          id: 'task-20250317',
          name: '内部测试',
          type: '5G视频通知',
          content: '测试内容',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-17T14:53:38'),
          startTime: new Date('2025-03-17T14:56:06'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250317',
          createdAt: new Date('2025-03-17T14:53:38'),
          updatedAt: new Date('2025-03-17T14:53:38')
        },
        callCount: 5
      },
      {
        task: {
          id: 'task-20250401',
          name: 'DEMO演示-安踏会员',
          type: '5G视频通知',
          content: '会员通知',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-14T14:24:15'),
          startTime: new Date('2025-04-01T11:29:45'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250401',
          createdAt: new Date('2025-03-14T14:24:15'),
          updatedAt: new Date('2025-03-14T14:24:15')
        },
        callCount: 6
      },
      {
        task: {
          id: 'task-20250310',
          name: 'DEMO演示-汽车',
          type: '5G视频通知',
          content: '汽车促销',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-07T22:28:06'),
          startTime: new Date('2025-03-10T10:12:36'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250310',
          createdAt: new Date('2025-03-07T22:28:06'),
          updatedAt: new Date('2025-03-07T22:28:06')
        },
        callCount: 8
      },
      {
        task: {
          id: 'task-20250306-1',
          name: 'DEMO演示-信贷',
          type: '5G视频通知',
          content: '信贷产品',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-05T18:29:45'),
          startTime: new Date('2025-03-06T18:09:33'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250306-1',
          createdAt: new Date('2025-03-05T18:29:45'),
          updatedAt: new Date('2025-03-05T18:29:45')
        },
        callCount: 21
      },
      {
        task: {
          id: 'task-20250306-2',
          name: 'DEMO演示-服装',
          type: '5G视频通知',
          content: '服装促销',
          status: '已启动-空闲',
          progress: 100,
          importTime: new Date('2025-03-05T18:29:04'),
          startTime: new Date('2025-03-06T18:36:51'),
          completionTime: null,
          creator: '系统导入',
          userId: user.id,
          externalId: 'ext-20250306-2',
          createdAt: new Date('2025-03-05T18:29:04'),
          updatedAt: new Date('2025-03-05T18:29:04')
        },
        callCount: 7
      }
    ];

    // 3. 导入任务数据
    let importedTaskCount = 0;
    let importedCallCount = 0;

    for (const taskData of futureTasks) {
      try {
        // 导入任务
        const task = await prisma.task.upsert({
          where: { id: taskData.task.id },
          update: {},
          create: taskData.task
        });

        console.log(`导入任务: ${task.id} - ${task.name}`);
        importedTaskCount++;

        // 为每个任务创建通话记录
        const callCount = taskData.callCount || 5;
        for (let i = 0; i < callCount; i++) {
          const callId = `call-${task.id}-${i + 1}`;

          // 生成随机电话号码
          const phoneNumber = `1${Math.floor(Math.random() * 9) + 3}${Math.floor(Math.random() * 10000000).toString().padStart(8, '0')}`;

          // 随机选择接通类型
          const connectionType = Math.random() > 0.5 ? '视频接通' : '语音接通';

          // 随机选择意向等级
          const intentions = ['A', 'B', 'C'];
          const intention = intentions[Math.floor(Math.random() * intentions.length)];

          // 随机生成通话时长（1-10分钟）
          const durationMinutes = Math.floor(Math.random() * 10) + 1;

          // 随机生成响铃时间（1-15秒）
          const ringSeconds = Math.floor(Math.random() * 15) + 1;

          // 计算开始和结束时间
          const startTime = new Date(task.startTime);
          startTime.setMinutes(startTime.getMinutes() + i * 15); // 每15分钟一个通话

          const endTime = new Date(startTime);
          endTime.setMinutes(endTime.getMinutes() + durationMinutes);

          // 导入通话记录
          const callDetail = await prisma.call_detail.upsert({
            where: { id: callId },
            update: {},
            create: {
              id: callId,
              taskId: task.id,
              taskName: task.name,
              type: task.type,
              content: task.content,
              customerName: `客户${i + 1}`,
              phoneNumber,
              connectionType,
              startTime,
              endTime,
              duration: `${durationMinutes}分钟`,
              ringTime: `${ringSeconds}秒`,
              intention,
              externalCallId: `ext-${callId}`,
              recordingUrl: '',
              completionRate: Math.random(),
              userId: user.id,
              createdAt: task.createdAt,
              updatedAt: task.updatedAt
            }
          });

          console.log(`导入通话记录: ${callDetail.id} - ${callDetail.phoneNumber}`);
          importedCallCount++;
        }
      } catch (error) {
        console.error(`导入任务 ${taskData.id} 失败:`, error);
      }
    }

    console.log(`成功导入 ${importedTaskCount} 条任务数据和 ${importedCallCount} 条通话记录数据`);
    console.log('未来任务数据导入完成!');
  } catch (error) {
    console.error('导入未来任务数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
