/**
 * 上线前检查脚本
 * 
 * 此脚本用于在部署前检查项目中的潜在问题
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 颜色代码
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

// 打印带颜色的消息
function log(message, color = 'white') {
  console.log(colors[color] + message + colors.reset);
}

// 打印标题
function logTitle(title) {
  console.log('\n' + colors.cyan + '='.repeat(80) + colors.reset);
  console.log(colors.cyan + ' ' + title + colors.reset);
  console.log(colors.cyan + '='.repeat(80) + colors.reset);
}

// 打印成功消息
function logSuccess(message) {
  console.log(colors.green + '✓ ' + message + colors.reset);
}

// 打印警告消息
function logWarning(message) {
  console.log(colors.yellow + '⚠ ' + message + colors.reset);
}

// 打印错误消息
function logError(message) {
  console.log(colors.red + '✗ ' + message + colors.reset);
}

// 打印信息消息
function logInfo(message) {
  console.log(colors.blue + 'ℹ ' + message + colors.reset);
}

// 检查控制台日志
function checkConsoleLogs() {
  logTitle('检查控制台日志');
  
  try {
    const result = execSync('grep -r "console.log" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v "scripts" | wc -l').toString().trim();
    const count = parseInt(result);
    
    if (count > 0) {
      logWarning(`发现 ${count} 个 console.log 语句，建议在生产环境中移除或替换为 logger`);
      
      // 显示部分示例
      const examples = execSync('grep -r "console.log" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v "scripts" | head -5').toString().trim();
      log('示例：\n' + examples, 'yellow');
      
      log('可以使用 node scripts/replace-console-logs.js 替换 console.log 语句', 'blue');
    } else {
      logSuccess('未发现 console.log 语句');
    }
  } catch (error) {
    logError('检查 console.log 失败: ' + error.message);
  }
}

// 检查TODO标记
function checkTodos() {
  logTitle('检查TODO标记');
  
  try {
    const result = execSync('grep -r "TODO" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v "scripts" | wc -l').toString().trim();
    const count = parseInt(result);
    
    if (count > 0) {
      logWarning(`发现 ${count} 个 TODO 标记，建议在生产环境中处理`);
      
      // 显示所有TODO
      const todos = execSync('grep -r "TODO" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v "scripts"').toString().trim();
      log('TODO列表：\n' + todos, 'yellow');
    } else {
      logSuccess('未发现 TODO 标记');
    }
  } catch (error) {
    logError('检查 TODO 失败: ' + error.message);
  }
}

// 检查环境变量
function checkEnvVars() {
  logTitle('检查环境变量');
  
  try {
    // 检查.env文件是否存在
    if (!fs.existsSync('.env')) {
      logWarning('.env 文件不存在');
    } else {
      logSuccess('.env 文件存在');
    }
    
    // 检查.env.local文件是否存在
    if (!fs.existsSync('.env.local')) {
      logWarning('.env.local 文件不存在');
    } else {
      logSuccess('.env.local 文件存在');
    }
    
    // 检查.env.production文件是否存在
    if (!fs.existsSync('.env.production')) {
      logWarning('.env.production 文件不存在，建议创建生产环境配置文件');
      
      // 检查模板是否存在
      if (fs.existsSync('.env.production.template')) {
        logInfo('发现 .env.production.template 文件，可以基于此创建 .env.production');
      }
    } else {
      logSuccess('.env.production 文件存在');
    }
    
    // 检查必要的环境变量
    const requiredVars = [
      'DATABASE_URL',
      'NEXTAUTH_SECRET',
      'NEXTAUTH_URL',
      'JWT_SECRET'
    ];
    
    let envContent = '';
    try {
      envContent = fs.readFileSync('.env', 'utf8');
    } catch (error) {
      // 忽略错误
    }
    
    let envLocalContent = '';
    try {
      envLocalContent = fs.readFileSync('.env.local', 'utf8');
    } catch (error) {
      // 忽略错误
    }
    
    let envProdContent = '';
    try {
      envProdContent = fs.readFileSync('.env.production', 'utf8');
    } catch (error) {
      // 忽略错误
    }
    
    const allEnvContent = envContent + '\n' + envLocalContent + '\n' + envProdContent;
    
    for (const varName of requiredVars) {
      if (!allEnvContent.includes(varName + '=')) {
        logWarning(`缺少必要的环境变量: ${varName}`);
      }
    }
  } catch (error) {
    logError('检查环境变量失败: ' + error.message);
  }
}

// 检查依赖版本
function checkDependencies() {
  logTitle('检查依赖版本');
  
  try {
    // 检查过时的依赖
    log('检查过时的依赖...', 'blue');
    try {
      const outdated = execSync('npm outdated --depth=0').toString().trim();
      if (outdated) {
        logWarning('发现过时的依赖:');
        log(outdated, 'yellow');
      } else {
        logSuccess('所有依赖都是最新的');
      }
    } catch (error) {
      // npm outdated 在有过时依赖时会返回非零退出码
      if (error.stdout) {
        logWarning('发现过时的依赖:');
        log(error.stdout.toString(), 'yellow');
      }
    }
    
    // 检查安全漏洞
    log('\n检查安全漏洞...', 'blue');
    try {
      const audit = execSync('npm audit --production').toString().trim();
      if (audit.includes('found 0 vulnerabilities')) {
        logSuccess('未发现安全漏洞');
      } else {
        logWarning('发现安全漏洞:');
        log(audit, 'yellow');
      }
    } catch (error) {
      // npm audit 在有漏洞时会返回非零退出码
      if (error.stdout) {
        logWarning('发现安全漏洞:');
        log(error.stdout.toString(), 'yellow');
      }
    }
  } catch (error) {
    logError('检查依赖版本失败: ' + error.message);
  }
}

// 检查TypeScript类型
function checkTypeScript() {
  logTitle('检查TypeScript类型');
  
  try {
    log('运行TypeScript类型检查...', 'blue');
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      logSuccess('TypeScript类型检查通过');
    } catch (error) {
      logWarning('TypeScript类型检查失败:');
      if (error.stdout) {
        const output = error.stdout.toString();
        // 只显示错误摘要
        const errorCount = (output.match(/Found \d+ errors? in \d+ files?/g) || [''])[0];
        log(errorCount, 'yellow');
        log('请运行 npx tsc --noEmit 查看详细错误', 'blue');
      }
    }
  } catch (error) {
    logError('检查TypeScript类型失败: ' + error.message);
  }
}

// 检查构建
function checkBuild() {
  logTitle('检查构建');
  
  try {
    log('尝试构建项目...', 'blue');
    log('注意：这可能需要几分钟时间', 'blue');
    log('跳过实际构建，如需测试构建请手动运行 npm run build', 'yellow');
    
    // 实际部署前应该取消下面的注释，运行真正的构建测试
    /*
    try {
      execSync('npm run build', { stdio: 'pipe' });
      logSuccess('项目构建成功');
    } catch (error) {
      logError('项目构建失败:');
      if (error.stdout) {
        log(error.stdout.toString(), 'red');
      }
      if (error.stderr) {
        log(error.stderr.toString(), 'red');
      }
    }
    */
  } catch (error) {
    logError('检查构建失败: ' + error.message);
  }
}

// 检查敏感信息
function checkSensitiveInfo() {
  logTitle('检查敏感信息');
  
  try {
    const patterns = [
      { name: '密码', pattern: 'password.*=.*[\'"]' },
      { name: '密钥', pattern: '(secret|key).*=.*[\'"]' },
      { name: 'API密钥', pattern: 'api.*key.*=.*[\'"]' },
      { name: '令牌', pattern: 'token.*=.*[\'"]' }
    ];
    
    for (const { name, pattern } of patterns) {
      log(`检查${name}...`, 'blue');
      try {
        const result = execSync(`grep -r "${pattern}" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v ".env" | grep -v "scripts" | wc -l`).toString().trim();
        const count = parseInt(result);
        
        if (count > 0) {
          logWarning(`发现 ${count} 个可能的${name}硬编码`);
          
          // 显示部分示例
          const examples = execSync(`grep -r "${pattern}" --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" . | grep -v "node_modules" | grep -v "backups" | grep -v ".env" | grep -v "scripts" | head -3`).toString().trim();
          log('示例：\n' + examples, 'yellow');
        } else {
          logSuccess(`未发现${name}硬编码`);
        }
      } catch (error) {
        // 忽略错误
      }
    }
  } catch (error) {
    logError('检查敏感信息失败: ' + error.message);
  }
}

// 检查文档
function checkDocumentation() {
  logTitle('检查文档');
  
  try {
    // 检查README.md
    if (fs.existsSync('README.md')) {
      logSuccess('README.md 文件存在');
    } else {
      logWarning('README.md 文件不存在，建议创建项目说明文档');
    }
    
    // 检查docs目录
    if (fs.existsSync('docs')) {
      logSuccess('docs 目录存在');
      
      // 检查关键文档
      const requiredDocs = [
        { name: '部署指南', file: '部署指南.md' },
        { name: '回滚计划', file: '回滚计划.md' },
        { name: '上线检查清单', file: '上线检查清单.md' }
      ];
      
      for (const doc of requiredDocs) {
        if (fs.existsSync(path.join('docs', doc.file))) {
          logSuccess(`${doc.name}文档存在`);
        } else {
          logWarning(`${doc.name}文档不存在，建议创建`);
        }
      }
    } else {
      logWarning('docs 目录不存在，建议创建项目文档目录');
    }
  } catch (error) {
    logError('检查文档失败: ' + error.message);
  }
}

// 主函数
function main() {
  logTitle('上线前检查');
  log('开始执行上线前检查...', 'blue');
  
  // 执行各项检查
  checkConsoleLogs();
  checkTodos();
  checkEnvVars();
  checkDependencies();
  checkTypeScript();
  checkBuild();
  checkSensitiveInfo();
  checkDocumentation();
  
  logTitle('检查完成');
  log('请根据上述检查结果修复问题，确保项目可以安全上线', 'blue');
}

main();
