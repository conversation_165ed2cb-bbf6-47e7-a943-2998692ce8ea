// 初始化菜单数据的脚本
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    console.log('开始初始化菜单数据...')

    // 创建系统管理菜单
    const systemMenu = await prisma.menu.upsert({
      where: { code: 'system' },
      update: {},
      create: {
        code: 'system',
        name: '系统管理',
        path: '/system',
        icon: 'Settings',
        order: 100,
        visible: true
      }
    })
    console.log('系统管理菜单创建成功:', systemMenu.id)

    // 创建用户管理菜单
    const usersMenu = await prisma.menu.upsert({
      where: { code: 'users' },
      update: {
        parentId: systemMenu.id
      },
      create: {
        code: 'users',
        name: '用户管理',
        path: '/system/users',
        icon: 'Users',
        parentId: systemMenu.id,
        order: 110,
        visible: true
      }
    })
    console.log('用户管理菜单创建成功:', usersMenu.id)

    // 创建角色管理菜单
    const rolesMenu = await prisma.menu.upsert({
      where: { code: 'roles' },
      update: {
        parentId: systemMenu.id
      },
      create: {
        code: 'roles',
        name: '角色管理',
        path: '/system/roles',
        icon: 'Shield',
        parentId: systemMenu.id,
        order: 120,
        visible: true
      }
    })
    console.log('角色管理菜单创建成功:', rolesMenu.id)

    // 创建系统设置菜单
    const settingsMenu = await prisma.menu.upsert({
      where: { code: 'settings' },
      update: {},
      create: {
        code: 'settings',
        name: '系统设置',
        path: '/settings',
        icon: 'Settings',
        order: 200,
        visible: true
      }
    })
    console.log('系统设置菜单创建成功:', settingsMenu.id)

    // 创建角色设置菜单
    const roleSettingsMenu = await prisma.menu.upsert({
      where: { code: 'roles-settings' },
      update: {
        parentId: settingsMenu.id
      },
      create: {
        code: 'roles-settings',
        name: '角色设置',
        path: '/settings/roles',
        icon: 'Shield',
        parentId: settingsMenu.id,
        order: 210,
        visible: true
      }
    })
    console.log('角色设置菜单创建成功:', roleSettingsMenu.id)

    // 获取管理员角色
    const adminRole = await prisma.role.findFirst({
      where: { code: 'ADMIN' }
    })

    if (adminRole) {
      // 将所有菜单关联到管理员角色
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          menus: {
            connect: [
              { id: systemMenu.id },
              { id: usersMenu.id },
              { id: rolesMenu.id },
              { id: settingsMenu.id },
              { id: roleSettingsMenu.id }
            ]
          }
        }
      })
      console.log('已将所有菜单关联到管理员角色')
    } else {
      console.log('未找到管理员角色，无法关联菜单')
    }

    console.log('菜单数据初始化完成！')
  } catch (error) {
    console.error('初始化菜单数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
