/**
 * 历史数据导入脚本
 * 用于在系统启动时导入历史任务和外呼详情数据
 *
 * 使用方法：
 * 1. 在开发环境中：npm run import-historical-data
 * 2. 在生产环境中：NODE_ENV=production npm run import-historical-data
 */

// 使用 CommonJS 风格导入
const { dataImportService } = require("../lib/services/data-import-service");

async function main() {
  console.log("开始导入历史数据...");

  try {
    const startTime = Date.now();

    // 导入所有历史数据
    const result = await dataImportService.importAllHistoricalData();

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);

    if (result.success) {
      console.log("历史数据导入成功!");
      console.log(`导入任务数据: ${result.tasks.importedCount}/${result.tasks.totalCount} 条`);
      console.log(`导入外呼详情数据: ${result.callDetails.importedCount}/${result.callDetails.totalCount} 条`);
      console.log(`总耗时: ${duration} 秒`);
    } else {
      console.error("历史数据导入失败:", result.message);
    }
  } catch (error) {
    console.error("导入历史数据时发生错误:", error);
  } finally {
    // 退出进程
    process.exit(0);
  }
}

// 执行主函数
main();
