/**
 * 从远程API导入数据脚本
 * 使用 Prisma 导入远程API的任务和外呼详情数据
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const fetch = require('node-fetch');
const crypto = require('crypto');

// API配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";
const AES_KEY = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_KEY || "f4d9145303db415a";
const AES_IV = process.env.NEXT_PUBLIC_VIDEO_CALL_AES_IV || "6778fd576ed3dfff";

// 生成签名
function generateSignature(orgCode) {
  const timestamp = Math.floor(Date.now() / 1000);
  const plaintext = `${orgCode}${timestamp}`;

  // 将文本形式的密钥和初始化向量转换为 Buffer
  const key = Buffer.from(AES_KEY, 'utf8');
  const iv = Buffer.from(AES_IV, 'utf8');

  // 加密
  const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
  let encrypted = cipher.update(plaintext, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return encrypted;
}

// 获取请求头
function getHeaders() {
  const signature = generateSignature(ORG_CODE);
  return {
    "Content-Type": "application/json; charset=utf-8",
    "access-token": signature,
  };
}

// 将API任务状态映射到项目状态
function mapTaskStatus(apiStatus) {
  switch (apiStatus) {
    case "initialized":
      return "未开始";
    case "started":
      return "进行中";
    case "paused":
      return "已暂停";
    case "finished":
      return "已完成";
    case "stopped":
      return "已停止";
    default:
      return "未知";
  }
}

// 根据callType获取任务类型
function getTaskTypeFromCallType(callType) {
  switch (callType) {
    case 1:
      return "5G视频通知";
    case 2:
      return "5G视频互动";
    case 3:
      return "5G语音互动";
    default:
      return "5G视频通知";
  }
}

// 获取任务列表
async function getTaskList() {
  try {
    // 获取当前时间戳（毫秒）
    const endTime = Date.now();
    // 获取一周前的时间戳（毫秒）
    const startTime = endTime - (7 * 24 * 60 * 60 * 1000);

    // 构建查询参数
    const queryParams = new URLSearchParams({
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
      startCreateAt: startTime.toString(),
      endCreateAt: endTime.toString(),
      pageIndex: "1",
      pageSize: "100"
    }).toString();

    const url = `${API_BASE_URL}/openapi/callout/taskList?${queryParams}`;
    console.log("请求URL:", url);
    console.log("请求头:", getHeaders());

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
    });

    console.log("响应状态:", response.status);
    const data = await response.json();
    console.log("响应数据:", JSON.stringify(data, null, 2));

    if (data.errCode === 0) {
      console.log(`成功获取 ${data.result.list?.length || 0} 条任务数据`);
      return data.result.list || [];
    } else {
      console.error(`获取任务列表失败: ${data.errInfo || "未知错误"}`);
      return [];
    }
  } catch (error) {
    console.error("获取任务列表错误:", error);
    return [];
  }
}

// 获取任务详情
async function getTaskDetail(taskId) {
  try {
    // 构建查询参数
    const queryParams = new URLSearchParams({
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
    }).toString();

    // 发送请求
    const response = await fetch(`${API_BASE_URL}/openapi/callout/task/${taskId}?${queryParams}`, {
      method: "GET",
      headers: getHeaders(),
    });

    const data = await response.json();

    if (data.errCode === 0) {
      return data.result;
    } else {
      console.error(`获取任务详情失败: ${data.errInfo || "未知错误"}`);
      return null;
    }
  } catch (error) {
    console.error("获取任务详情错误:", error);
    return null;
  }
}

// 获取任务的通话记录
async function getTaskCallRecords(taskId) {
  try {
    // 构建请求参数
    const queryParams = new URLSearchParams({
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
      mediaDeliverId: taskId,
      pageNum: "1",
      pageSize: "100" // 获取前100条记录
    }).toString();

    // 发送请求
    const response = await fetch(`${API_BASE_URL}/openapi/task/callRecords?${queryParams}`, {
      method: "GET",
      headers: getHeaders(),
    });

    const data = await response.json();

    if (data.errCode === 0) {
      return data.result?.list || [];
    } else {
      console.error(`获取通话记录失败: ${data.errInfo || "未知错误"}`);
      return [];
    }
  } catch (error) {
    console.error("获取通话记录错误:", error);
    return [];
  }
}

// 主函数
async function main() {
  try {
    console.log('开始从远程API导入数据...');

    // 1. 获取有效的用户ID
    const user = await prisma.user.findFirst({
      where: {
        username: 'admin'
      }
    });

    if (!user) {
      throw new Error('找不到有效的用户');
    }

    console.log(`使用用户ID: ${user.id}`);

    // 2. 获取远程任务列表
    const remoteTasks = await getTaskList();
    console.log(`获取到 ${remoteTasks.length} 条远程任务数据`);

    // 3. 导入任务数据
    let importedTaskCount = 0;
    let importedCallCount = 0;

    for (const remoteTask of remoteTasks) {
      try {
        // 获取任务详情
        const taskDetail = await getTaskDetail(remoteTask.taskId);

        if (taskDetail) {
          // 导入任务
          const task = await prisma.task.upsert({
            where: { id: remoteTask.taskId },
            update: {},
            create: {
              id: remoteTask.taskId,
              name: remoteTask.name,
              type: getTaskTypeFromCallType(taskDetail.callType || 1),
              content: remoteTask.name, // 使用任务名称作为内容
              status: mapTaskStatus(remoteTask.status),
              progress: remoteTask.status === "finished" ? 100 : 0,
              importTime: new Date(remoteTask.createdAt),
              startTime: taskDetail.settingStartTime ? new Date(taskDetail.settingStartTime) : new Date(remoteTask.createdAt),
              completionTime: taskDetail.endedAt ? new Date(taskDetail.endedAt) : null,
              creator: remoteTask.creator || "系统导入",
              userId: user.id,
              externalId: remoteTask.taskId,
              createdAt: new Date(remoteTask.createdAt),
              updatedAt: new Date(remoteTask.createdAt)
            }
          });

          console.log(`导入任务: ${task.id} - ${task.name}`);
          importedTaskCount++;

          // 获取任务的通话记录
          const callRecords = await getTaskCallRecords(remoteTask.taskId);

          if (callRecords.length > 0) {
            for (const record of callRecords) {
              try {
                // 生成唯一ID
                const callId = record.callId || `call-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

                // 导入通话记录
                const callDetail = await prisma.call_detail.upsert({
                  where: { id: callId },
                  update: {},
                  create: {
                    id: callId,
                    taskId: task.id,
                    taskName: task.name,
                    type: task.type,
                    content: task.content,
                    customerName: record.customerName || "客户",
                    phoneNumber: record.phone || record.customerPhone || "未知",
                    connectionType: record.callType === 1 ? "视频接通" : "语音接通",
                    startTime: record.startTime ? new Date(record.startTime) : record.callStartTime ? new Date(record.callStartTime) : new Date(task.startTime),
                    endTime: record.endTime ? new Date(record.endTime) : record.callEndTime ? new Date(record.callEndTime) : task.completionTime ? new Date(task.completionTime) : null,
                    duration: record.duration || "未知",
                    ringTime: record.ringTime || "未知",
                    intention: record.intentLevel || "C",
                    externalCallId: record.callId,
                    recordingUrl: record.recordUrl || "",
                    completionRate: 1.0,
                    userId: user.id,
                    createdAt: new Date(),
                    updatedAt: new Date()
                  }
                });

                console.log(`导入通话记录: ${callDetail.id} - ${callDetail.phoneNumber}`);
                importedCallCount++;
              } catch (callError) {
                console.error(`导入通话记录失败:`, callError);
              }
            }
          } else {
            // 如果没有通话记录，创建一个示例记录
            const callDetail = await prisma.call_detail.upsert({
              where: { id: `call-${task.id}-sample` },
              update: {},
              create: {
                id: `call-${task.id}-sample`,
                taskId: task.id,
                taskName: task.name,
                type: task.type,
                content: task.content,
                customerName: "示例客户",
                phoneNumber: "13800000000",
                connectionType: "视频接通",
                startTime: task.startTime,
                endTime: task.completionTime || task.startTime,
                duration: "5分钟",
                ringTime: "10秒",
                intention: "A",
                externalCallId: `ext-${task.id}`,
                recordingUrl: "",
                completionRate: 1.0,
                userId: user.id,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            });

            console.log(`导入示例通话记录: ${callDetail.id}`);
            importedCallCount++;
          }
        }
      } catch (taskError) {
        console.error(`导入任务 ${remoteTask.taskId} 失败:`, taskError);
      }
    }

    console.log(`成功导入 ${importedTaskCount} 条任务数据和 ${importedCallCount} 条通话记录数据`);
    console.log('远程数据导入完成!');
  } catch (error) {
    console.error('导入远程数据失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
