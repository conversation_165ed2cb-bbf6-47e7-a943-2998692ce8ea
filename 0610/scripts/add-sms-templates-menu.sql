-- 获取设置菜单的ID
DO $$
DECLARE
    settings_menu_id TEXT;
    admin_role_id TEXT;
    sms_templates_menu_id TEXT;
BEGIN
    -- 获取设置菜单的ID
    SELECT id INTO settings_menu_id FROM "menu" WHERE code = 'settings';

    -- 获取管理员角色ID
    SELECT id INTO admin_role_id FROM "role" WHERE code = 'ADMIN';

    -- 检查短信模板菜单是否已存在
    SELECT id INTO sms_templates_menu_id FROM "menu" WHERE code = 'sms_templates';

    IF sms_templates_menu_id IS NULL THEN
        -- 插入短信模板菜单
        INSERT INTO "menu" (id, code, name, path, icon, "parentId", "order", visible, "createdAt", "updatedAt")
        VALUES (
            gen_random_uuid(), -- 生成UUID
            'sms_templates', -- 菜单代码
            '短信模板', -- 菜单名称
            '/settings/sms-templates', -- 路由路径
            'MessageSquare', -- 图标
            settings_menu_id, -- 父菜单ID
            75, -- 排序顺序
            true, -- 是否可见
            NOW(), -- 创建时间
            NOW() -- 更新时间
        )
        RETURNING id INTO sms_templates_menu_id;

        RAISE NOTICE '短信模板菜单已添加，ID: %', sms_templates_menu_id;
    ELSE
        RAISE NOTICE '短信模板菜单已存在，ID: %', sms_templates_menu_id;
    END IF;

    -- 确保管理员角色有权限访问此菜单
    IF NOT EXISTS (
        SELECT 1 FROM "_RoleMenus"
        WHERE "A" = sms_templates_menu_id AND "B" = admin_role_id
    ) THEN
        BEGIN
            INSERT INTO "_RoleMenus" ("A", "B")
            VALUES (sms_templates_menu_id, admin_role_id);

            RAISE NOTICE '管理员角色权限已更新';
        EXCEPTION
            WHEN others THEN
                RAISE NOTICE '更新角色权限失败: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE '管理员角色已有短信模板菜单权限';
    END IF;

EXCEPTION
    WHEN others THEN
        RAISE NOTICE '错误: %', SQLERRM;
END $$;
