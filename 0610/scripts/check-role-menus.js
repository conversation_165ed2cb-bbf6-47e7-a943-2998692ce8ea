const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    // 查找管理员角色
    const role = await prisma.role.findFirst({
      where: { code: 'ADMIN' },
      include: { menus: true }
    });

    console.log('角色信息:');
    console.log(JSON.stringify({
      id: role.id,
      code: role.code,
      name: role.name,
      type: role.type,
      permissions: role.permissions,
      menuCount: role.menus.length
    }, null, 2));

    if (role.menus.length > 0) {
      console.log(`\n关联的菜单 (${role.menus.length}):`);
      role.menus.forEach(menu => {
        console.log(`- ${menu.name} (${menu.path})`);
      });
    } else {
      console.log('\n没有关联的菜单');
    }

    // 查找所有菜单
    const allMenus = await prisma.menu.findMany();
    console.log(`\n所有菜单 (${allMenus.length}):`);
    allMenus.forEach(menu => {
      console.log(`- ${menu.name} (${menu.path})`);
    });

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
