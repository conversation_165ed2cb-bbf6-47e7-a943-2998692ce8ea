// 初始化菜单数据的脚本
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function main() {
  try {
    // 创建仪表盘菜单
    const dashboardMenu = await prisma.menu.upsert({
      where: { code: 'dashboard' },
      update: {},
      create: {
        code: 'dashboard',
        name: '仪表盘',
        path: '/dashboard',
        icon: 'LayoutDashboard',
        order: 10,
        visible: true
      }
    })
    console.log('仪表盘菜单创建成功:', dashboardMenu)

    // 创建用户管理菜单
    const accountsMenu = await prisma.menu.upsert({
      where: { code: 'accounts' },
      update: {},
      create: {
        code: 'accounts',
        name: '用户管理',
        path: '/accounts',
        icon: 'Users',
        order: 20,
        visible: true
      }
    })
    console.log('用户管理菜单创建成功:', accountsMenu)

    // 创建客户管理子菜单
    const customerMenu = await prisma.menu.upsert({
      where: { code: 'customer' },
      update: {},
      create: {
        code: 'customer',
        name: '用户列表',
        path: '/accounts/customer',
        icon: 'User',
        parentId: accountsMenu.id,
        order: 21,
        visible: true
      }
    })
    console.log('用户列表菜单创建成功:', customerMenu)

    // 创建费率管理菜单
    const ratesMenu = await prisma.menu.upsert({
      where: { code: 'rates' },
      update: {},
      create: {
        code: 'rates',
        name: '费率管理',
        path: '/rates',
        icon: 'CreditCard',
        order: 50,
        visible: true
      }
    })
    console.log('费率管理菜单创建成功:', ratesMenu)

    // 创建通知管理菜单
    const notificationsMenu = await prisma.menu.upsert({
      where: { code: 'notifications' },
      update: {},
      create: {
        code: 'notifications',
        name: '通知管理',
        path: '/notifications',
        icon: 'Bell',
        order: 60,
        visible: true
      }
    })
    console.log('通知管理菜单创建成功:', notificationsMenu)

    // 创建系统设置菜单
    const settingsMenu = await prisma.menu.upsert({
      where: { code: 'settings' },
      update: {},
      create: {
        code: 'settings',
        name: '系统设置',
        path: '/settings',
        icon: 'Settings',
        order: 100,
        visible: true
      }
    })
    console.log('系统设置菜单创建成功:', settingsMenu)

    // 将菜单分配给管理员角色
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' }
    })

    if (adminRole) {
      await prisma.role.update({
        where: { id: adminRole.id },
        data: {
          menus: {
            connect: [
              { id: dashboardMenu.id },
              { id: accountsMenu.id },
              { id: customerMenu.id },
              { id: ratesMenu.id },
              { id: notificationsMenu.id },
              { id: settingsMenu.id }
            ]
          }
        }
      })
      console.log('菜单已分配给管理员角色')
    }

    // 将部分菜单分配给普通用户角色
    const userRole = await prisma.role.findUnique({
      where: { code: 'USER' }
    })

    if (userRole) {
      await prisma.role.update({
        where: { id: userRole.id },
        data: {
          menus: {
            connect: [
              { id: dashboardMenu.id }
            ]
          }
        }
      })
      console.log('菜单已分配给普通用户角色')
    }

    // 将部分菜单分配给企业用户角色
    const enterpriseRole = await prisma.role.findUnique({
      where: { code: 'ENTERPRISE' }
    })

    if (enterpriseRole) {
      await prisma.role.update({
        where: { id: enterpriseRole.id },
        data: {
          menus: {
            connect: [
              { id: dashboardMenu.id }
            ]
          }
        }
      })
      console.log('菜单已分配给企业用户角色')
    }

    console.log('菜单数据初始化完成')
  } catch (error) {
    console.error('初始化菜单数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()
