import { generateVerificationCode, storeVerificationCode, verifyCode } from '../lib/services/verification';
import { sendVerificationEmail } from '../lib/services/email';
import { prisma } from '../lib/prisma';
import { hash } from 'bcryptjs';
import { Prisma, UserRole } from '@prisma/client';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * 测试用户注册流程
 * 包括：
 * 1. 发送验证码
 * 2. 验证验证码
 * 3. 创建用户账户
 */
async function testRegister() {
  try {
    const email = '<EMAIL>';
    const username = 'testuser';
    const password = 'testpassword123';

    // 1. 生成并发送验证码
    const code = generateVerificationCode();
    await storeVerificationCode(email, code);
    await sendVerificationEmail(email, code);
    console.log('✅ 验证码已发送到邮箱');

    // 2. 等待用户输入验证码
    const inputCode = await new Promise<string>((resolve) => {
      rl.question('请输入收到的验证码: ', (answer) => {
        resolve(answer);
      });
    });

    // 3. 验证验证码
    const isValid = await verifyCode(email, inputCode);
    if (!isValid) {
      throw new Error('验证码无效');
    }
    console.log('✅ 验证码验证成功');

    // 4. 检查用户名和邮箱是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email }
        ]
      }
    });

    if (existingUser) {
      throw new Error('用户名或邮箱已存在');
    }

    // 5. 创建用户
    const hashedPassword = await hash(password, 10);
    const userData: Prisma.UserCreateInput = {
      username,
      email,
      password: hashedPassword,
      role: UserRole.USER,
      permissions: []
    };

    const user = await prisma.user.create({
      data: userData
    });

    console.log('✅ 用户创建成功:', {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    });

  } catch (error) {
    console.error('❌ 注册测试失败:', error);
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

// 运行测试
testRegister(); 