/**
 * 手动触发认证类型变更检查定时任务
 * 用于测试和开发环境
 */

const fetch = require('node-fetch');
require('dotenv').config();

const CRON_SECRET = process.env.CRON_SECRET || 'your-cron-secret';
const API_URL = 'http://localhost:3000/api/cron/verification-check';

async function runVerificationCheck() {
  try {
    console.log('正在触发认证类型变更检查定时任务...');
    
    const response = await fetch(API_URL, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${CRON_SECRET}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('定时任务执行成功!');
      console.log('结果:', JSON.stringify(data, null, 2));
    } else {
      console.error('定时任务执行失败:', data.message || '未知错误');
    }
  } catch (error) {
    console.error('执行定时任务时出错:', error);
  }
}

runVerificationCheck();
