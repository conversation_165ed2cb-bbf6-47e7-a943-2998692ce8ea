/**
 * 替换console.log脚本
 * 
 * 此脚本用于将项目中的console.log语句替换为logger工具函数
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 要排除的目录
const EXCLUDED_DIRS = [
  'node_modules',
  '.next',
  'out',
  'public',
  'backups',
  'scripts'
];

// 要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// 替换规则
const REPLACEMENTS = [
  // 基本console.log替换
  {
    pattern: /console\.log\((.*?)\);/g,
    replacement: 'logger.log($1);'
  },
  // 带有API字样的日志
  {
    pattern: /console\.log\(\s*\[API.*?\](.*?)\);/g,
    replacement: 'logger.api($1);'
  },
  // 带有错误字样的日志
  {
    pattern: /console\.log\(\s*['"`].*?错误.*?['"`](.*?)\);/g,
    replacement: 'logger.error($1);'
  },
  // 带有失败字样的日志
  {
    pattern: /console\.log\(\s*['"`].*?失败.*?['"`](.*?)\);/g,
    replacement: 'logger.error($1);'
  },
  // 带有警告字样的日志
  {
    pattern: /console\.log\(\s*['"`].*?警告.*?['"`](.*?)\);/g,
    replacement: 'logger.warn($1);'
  },
  // 带有成功字样的日志
  {
    pattern: /console\.log\(\s*['"`].*?成功.*?['"`](.*?)\);/g,
    replacement: 'logger.log($1);'
  },
  // console.error替换
  {
    pattern: /console\.error\((.*?)\);/g,
    replacement: 'logger.error($1);'
  },
  // console.warn替换
  {
    pattern: /console\.warn\((.*?)\);/g,
    replacement: 'logger.warn($1);'
  }
];

// 导入检查
const IMPORT_STATEMENT = "import logger from '@/lib/utils/logger';";

/**
 * 检查文件是否已经导入了logger
 * @param {string} content 文件内容
 * @returns {boolean} 是否已导入
 */
function hasLoggerImport(content) {
  return content.includes("import logger from '@/lib/utils/logger'") || 
         content.includes('import logger from "@/lib/utils/logger"');
}

/**
 * 添加logger导入语句
 * @param {string} content 文件内容
 * @returns {string} 更新后的内容
 */
function addLoggerImport(content) {
  // 如果已经有导入语句，在最后一个导入后添加
  const importRegex = /import .* from .*;\n/g;
  let lastImportMatch;
  let lastImportIndex = -1;
  
  while ((match = importRegex.exec(content)) !== null) {
    lastImportMatch = match;
    lastImportIndex = match.index + match[0].length;
  }
  
  if (lastImportIndex !== -1) {
    return content.slice(0, lastImportIndex) + IMPORT_STATEMENT + '\n' + content.slice(lastImportIndex);
  }
  
  // 如果没有导入语句，在文件开头添加
  return IMPORT_STATEMENT + '\n\n' + content;
}

/**
 * 处理单个文件
 * @param {string} filePath 文件路径
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let needsLoggerImport = false;
    
    // 应用替换规则
    for (const rule of REPLACEMENTS) {
      if (rule.pattern.test(content)) {
        content = content.replace(rule.pattern, rule.replacement);
        needsLoggerImport = true;
      }
    }
    
    // 如果有替换且没有导入logger，添加导入
    if (needsLoggerImport && !hasLoggerImport(content)) {
      content = addLoggerImport(content);
    }
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

/**
 * 递归处理目录
 * @param {string} dir 目录路径
 */
function processDirectory(dir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      if (!EXCLUDED_DIRS.includes(entry.name)) {
        processDirectory(fullPath);
      }
    } else if (entry.isFile() && FILE_EXTENSIONS.includes(path.extname(entry.name))) {
      processFile(fullPath);
    }
  }
}

// 主函数
function main() {
  const rootDir = process.cwd();
  console.log(`Starting to replace console.log statements in ${rootDir}`);
  processDirectory(rootDir);
  console.log('Replacement completed');
}

main();
