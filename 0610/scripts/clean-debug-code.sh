#!/bin/bash

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.."

# 创建备份目录
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)_debug_code_backup"
mkdir -p "$BACKUP_DIR"

echo "创建备份目录: $BACKUP_DIR"

# 查找并备份包含调试代码的文件
echo "正在查找并备份包含调试代码的文件..."

# 查找包含 console.log 的文件
echo "查找包含 console.log 的文件..."
grep -r "console.log" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq > /tmp/console_log_files.txt

# 查找包含 console.error 的文件
echo "查找包含 console.error 的文件..."
grep -r "console.error" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq > /tmp/console_error_files.txt

# 查找包含 alert 的文件
echo "查找包含 alert 的文件..."
grep -r "alert(" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq > /tmp/alert_files.txt

# 合并所有文件列表并去重
cat /tmp/console_log_files.txt /tmp/console_error_files.txt /tmp/alert_files.txt | sort | uniq > /tmp/debug_code_files.txt

# 备份文件
echo "备份文件..."
cat /tmp/debug_code_files.txt | while read file; do
  if [ -f "$file" ]; then
    backup_path="$BACKUP_DIR/${file#./}"
    mkdir -p "$(dirname "$backup_path")"
    cp "$file" "$backup_path"
    echo "已备份: $file"
  fi
done

# 清理临时文件
rm /tmp/console_log_files.txt /tmp/console_error_files.txt /tmp/alert_files.txt /tmp/debug_code_files.txt

echo "备份完成。备份文件保存在: $BACKUP_DIR"
echo ""
echo "调试代码统计信息:"
echo "----------------------------------------"
echo "包含 console.log 的文件数量: $(grep -r "console.log" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq | wc -l)"
echo "包含 console.error 的文件数量: $(grep -r "console.error" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq | wc -l)"
echo "包含 alert 的文件数量: $(grep -r "alert(" --include="*.ts" --include="*.tsx" ./app | cut -d: -f1 | sort | uniq | wc -l)"
echo "----------------------------------------"
echo ""
echo "注意: 此脚本只创建了备份，没有修改任何文件。"
echo "如需清理调试代码，请参考 docs/问题修复/代码冗余和调试代码清理.md 中的建议。"
echo ""
echo "特别关注以下文件:"
echo "1. 包含模拟数据的文件: app/lib/api/mock.ts"
echo "2. 使用模拟数据的 API 路由: app/api/tasks/route.ts 等"
