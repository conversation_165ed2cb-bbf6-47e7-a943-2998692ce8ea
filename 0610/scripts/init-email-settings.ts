/**
 * 初始化邮件设置脚本
 *
 * 该脚本从.env文件读取邮件配置，并将其插入到数据库中
 * 运行方式: npx ts-node scripts/init-email-settings.ts
 */

const { PrismaClient } = require('@prisma/client');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 创建Prisma客户端
const prisma = new PrismaClient()

async function main() {
  try {
    console.log('开始初始化邮件设置...')

    // 从环境变量中获取邮件配置
    const emailHost = process.env.EMAIL_HOST || 'smtp.qq.com'
    const emailPort = parseInt(process.env.EMAIL_PORT || '465')
    const emailUser = process.env.EMAIL_USER || ''
    const emailPass = process.env.EMAIL_PASS || ''

    // 构建邮件设置对象
    const emailSettings = {
      host: emailHost,
      port: emailPort,
      secure: true,
      auth: {
        user: emailUser,
        pass: emailPass
      },
      from: `"系统通知" <${emailUser}>`,
      enableAutoRetry: true,
      retryInterval: 30,
      maxRetries: 3
    }

    console.log('邮件设置信息:', {
      host: emailSettings.host,
      port: emailSettings.port,
      secure: emailSettings.secure,
      from: emailSettings.from,
      hasAuthUser: !!emailSettings.auth.user,
      hasAuthPass: !!emailSettings.auth.pass
    })

    // 检查系统设置是否存在
    const existingSettings = await prisma.systemSettings.findFirst()

    if (existingSettings) {
      // 更新现有设置
      console.log(`找到现有系统设置记录，ID: ${existingSettings.id}，正在更新...`)

      await prisma.systemSettings.update({
        where: { id: existingSettings.id },
        data: {
          emailSettings: emailSettings as any
        }
      })

      console.log('邮件设置已更新')
    } else {
      // 创建新设置
      console.log('未找到系统设置记录，正在创建...')

      await prisma.systemSettings.create({
        data: {
          siteName: "外呼管理系统",
          logo: "/logo.png",
          footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
          description: "高效的外呼任务管理平台",
          keywords: "外呼,管理系统,任务管理",
          applicationName: "外呼管理系统",
          appleMobileWebAppTitle: "外呼系统",
          theme: {
            primaryColor: "#0284c7",
            mode: "system"
          },
          features: {
            enableRegistration: true,
            enablePasswordReset: true,
            enableNotifications: true
          },
          security: {
            passwordMinLength: 8,
            requireSpecialChar: true,
            requireNumber: true,
            requireUppercase: true,
            loginAttempts: 5,
            sessionTimeout: 30
          },
          loginPage: {
            backgroundImage: "/placeholder.svg?height=1080&width=1920",
            backgroundEffect: "all",
            title: "外呼管理系统",
            subtitle: "提升工作效率的得力助手",
            features: [
              { icon: "user", text: "专业的客户管理" },
              { icon: "lock", text: "安全的数据保护" },
              { icon: "mail", text: "高效的沟通工具" }
            ]
          },
          emailSettings: emailSettings as any
        }
      })

      console.log('系统设置已创建，包含邮件设置')
    }

    console.log('邮件设置初始化完成')
  } catch (error) {
    console.error('初始化邮件设置时出错:', error)
  } finally {
    // 关闭Prisma连接
    await prisma.$disconnect()
  }
}

// 执行主函数
main()
  .then(() => console.log('脚本执行完成'))
  .catch(e => {
    console.error('脚本执行失败:', e)
    process.exit(1)
  })
