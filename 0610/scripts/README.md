# 脚本工具说明

本目录包含一些有用的脚本工具，用于辅助开发和维护项目。

## 调试代码备份工具

### 文件: `clean-debug-code.sh`

这个脚本用于查找和备份项目中包含调试代码的文件，包括 `console.log`、`console.error` 和 `alert` 语句。

#### 使用方法

```bash
# 确保脚本有执行权限
chmod +x scripts/clean-debug-code.sh

# 运行脚本
./scripts/clean-debug-code.sh
```

#### 功能说明

1. 脚本会在项目根目录下创建一个备份目录，格式为 `./backups/YYYYMMDD_HHMMSS_debug_code_backup`。
2. 查找项目中包含 `console.log`、`console.error` 和 `alert` 语句的文件。
3. 将这些文件备份到备份目录中，保持原有的目录结构。
4. 提供调试代码的统计信息，包括包含各类调试代码的文件数量。
5. 脚本只创建备份，不修改任何文件，以确保不影响现有功能。

#### 注意事项

- 此脚本只创建备份，不修改任何文件。
- 如需清理调试代码，请参考 `docs/问题修复/代码冗余和调试代码清理.md` 中的建议。
- 特别关注 `app/lib/api/mock.ts` 和使用模拟数据的 API 路由文件。

## ESLint 配置

项目根目录下的 `.eslintrc.json` 文件配置了 ESLint 规则，帮助开发人员在开发过程中避免使用调试代码。

### 使用方法

```bash
# 运行 ESLint 检查
npm run lint
```

### 配置说明

- `no-console`: 警告级别，禁止使用 `console.log` 等语句，但允许 `console.warn` 和 `console.error`。
- `no-alert`: 警告级别，禁止使用 `alert()`、`confirm()` 和 `prompt()` 函数。
- `no-debugger`: 警告级别，禁止使用 `debugger` 语句。

### 注意事项

- 规则设置为警告级别，而不是错误级别，以避免阻止构建或部署。
- 在 `*.dev.js`、`*.dev.ts` 和 `*.dev.tsx` 文件中，`no-console` 规则被禁用，允许使用 `console.log` 等语句。
