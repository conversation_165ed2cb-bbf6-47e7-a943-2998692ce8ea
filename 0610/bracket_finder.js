import logger from '@/lib/utils/logger';

const fs = require('fs');

const filePath = 'app/(dashboard)/settings/page.tsx';
const content = fs.readFileSync(filePath, 'utf8');
const lines = content.split('\n');

let braceStack = [];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  
  for (let j = 0; j < line.length; j++) {
    const char = line[j];
    
    if (char === '{') {
      braceStack.push({ line: i + 1, column: j + 1 });
    } else if (char === '}') {
      if (braceStack.length > 0) {
        braceStack.pop();
      } else {
        logger.log(`Extra closing brace at line ${i + 1}, column ${j + 1}`);
      }
    }
  }
}

if (braceStack.length > 0) {
  logger.log(`Missing ${braceStack.length} closing braces`);
  logger.log('Opening braces without matching closing braces:');
  braceStack.forEach(pos => {
    logger.log(`Line ${pos.line}, Column ${pos.column}`);
  });
}
