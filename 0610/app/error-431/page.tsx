'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export default function Error431Page() {
  const router = useRouter()

  const handleRefresh = () => {
    // 清除一些可能导致请求头过大的 cookie
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/")
    })
    // 刷新页面
    router.refresh()
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className="mx-auto max-w-2xl px-4 text-center">
        <h1 className="mb-4 text-4xl font-bold">请求头太大</h1>
        <p className="mb-8 text-gray-600 dark:text-gray-400">
          抱歉，您的请求头数据超出了服务器限制。这通常是由于 cookie 或其他请求头数据过大导致的。
        </p>
        <div className="flex justify-center gap-4">
          <Button onClick={handleRefresh}>
            清除数据并刷新
          </Button>
          <Button variant="outline" onClick={() => router.push("/")}>
            返回首页
          </Button>
        </div>
      </div>
    </div>
  )
}
