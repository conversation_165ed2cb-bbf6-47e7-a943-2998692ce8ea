"use client"

import React, { useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { signIn } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { UserIcon } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { LoginErrors, LoginPageSettings } from "./types"
import { Footer } from "./Footer"
import { PasswordInput } from "@/components/password-input"
import { useAsyncError } from "@/hooks/use-async-error"
import { ErrorService } from "@/lib/error-service"

interface LoginFormProps {
  onTabChange: (tab: "login" | "register") => void;
  setShowDialog: (show: boolean) => void;
  setDialogContent: (content: { title: string; description: string; action: (() => void) | null }) => void;
  settings: LoginPageSettings;
}

export function LoginForm({ onTabChange, setShowDialog, setDialogContent, settings }: LoginFormProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isLoading, handleAsync } = useAsyncError()

  // 登录表单状态
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  // 不再需要显示密码状态，已由PasswordInput组件处理
  const [rememberMe, setRememberMe] = useState(false)
  const [loginError, setLoginError] = useState("")

  // 错误状态
  const [errors, setErrors] = useState<LoginErrors>({
    username: "",
    password: ""
  })

  // 处理登录表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoginError("")

    // 重置错误
    setErrors({
      username: "",
      password: ""
    })

    // 表单验证
    let hasError = false
    if (!username) {
      setErrors(prev => ({ ...prev, username: "请输入用户名" }))
      hasError = true
    }

    if (!password) {
      setErrors(prev => ({ ...prev, password: "请输入密码" }))
      hasError = true
    }

    if (hasError) {
      return
    }

    await handleAsync(async () => {
      // 在登录前只清除本地存储，不清除cookie以避免影响NextAuth会话
      try {
        // 清除本地存储
        localStorage.clear();
        sessionStorage.clear();

        // 清除浏览器缓存
        if (window.caches) {
          caches.keys().then(names => {
            names.forEach(name => {
              caches.delete(name);
            });
          });
        }
      } catch (e) {
        ErrorService.logError(e, { context: '清除存储和缓存' });
      }

      // 直接使用 NextAuth.js 登录并重定向
      toast({
        title: "登录中",
        description: "正在验证您的凭证...",
      })

      // 获取重定向URL
      const returnUrl = searchParams.get('returnUrl') || '/dashboard'

      // 使用重定向模式，让NextAuth处理重定向
      const result = await signIn('credentials', {
        username,
        password,
        redirect: false,
        callbackUrl: returnUrl
      })

      if (result && !result.error) {
        // 登录成功
        toast({
          title: "登录成功",
          description: "欢迎回来！正在跳转到系统...",
        })

        // 使用路由跳转到目标页面
        setTimeout(() => {
          if (result.url) {
            // 使用结果中的URL进行跳转
            window.location.href = result.url;
          } else {
            // 使用returnUrl或默认跳转到dashboard
            const targetUrl = returnUrl || '/dashboard';
            router.push(targetUrl);
          }
        }, 1000)
      } else {
        // 登录失败
        let errorMessage = "用户名或密码错误";

        // 处理各种错误类型
        if (result?.error === "CredentialsSignin") {
          errorMessage = "用户名或密码错误";
        } else if (result?.error) {
          errorMessage = result.error;
        }

        setLoginError(errorMessage);
        throw new Error(errorMessage);
      }
    }, {
      context: { username, action: '用户登录' }
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="username" className="text-gray-700 dark:text-gray-300">
          用户名
        </Label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <UserIcon className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            id="username"
            type="text"
            placeholder="请输入用户名"
            className={`pl-10 ${errors.username ? 'border-red-500 focus:ring-red-500' : ''}`}
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            disabled={isLoading}
          />
        </div>
        {errors.username && (
          <p className="text-sm text-red-500">{errors.username}</p>
        )}
      </div>

      <PasswordInput
        id="password"
        label="密码"
        value={password}
        onChange={setPassword}
        placeholder="请输入密码"
        error={errors.password}
        disabled={isLoading}
        showStrengthIndicator={false}
      />

      {loginError && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-500 p-3 rounded-md text-sm flex items-start">
          <div className="mr-2 mt-0.5">⚠️</div>
          <div>{loginError}</div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="remember"
            checked={rememberMe}
            onCheckedChange={(checked) => setRememberMe(checked === true)}
          />
          <Label
            htmlFor="remember"
            className="text-sm text-gray-600 dark:text-gray-400 cursor-pointer"
          >
            记住我
          </Label>
        </div>
        <Link
          href="/reset-password"
          className="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
        >
          忘记密码？
        </Link>
      </div>

      <Button
        type="submit"
        className="w-full bg-blue-600 hover:bg-blue-700"
        disabled={isLoading}
      >
        {isLoading ? "登录中..." : "登录"}
      </Button>

      <div className="text-center mt-4">
        <span className="text-gray-600 dark:text-gray-400 text-sm">
          还没有账号？
        </span>{" "}
        <Button
          type="button"
          variant="link"
          className="text-sm text-blue-600 dark:text-blue-400 p-0 h-auto"
          onClick={() => onTabChange("register")}
        >
          立即注册
        </Button>
      </div>

      {/* 页脚信息 */}
      <div className="mt-8">
        <Footer settings={settings} />
      </div>
    </form>
  )
}
