"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "@/components/ui/use-toast"
import { ArrowLeft, Check } from "lucide-react"
import Link from "next/link"

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // 这里应该有实际的API调用来处理密码重置
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setIsSubmitted(true)
      toast({
        title: "重置链接已发送",
        description: "请检查您的邮箱，按照邮件中的指示重置密码。",
      })
    } catch (error) {
      console.error("发送重置链接失败", error)
      toast({
        title: "发送失败",
        description: "无法发送重置链接，请稍后重试。",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-gradient-to-b from-blue-50 to-blue-100 dark:from-gray-900 dark:to-gray-800">
      <Card className="w-full max-w-md shadow-lg bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border border-white/20">
        <CardHeader>
          <div className="flex items-center mb-2">
            <Link href="/login" className="text-sm text-primary hover:underline flex items-center">
              <ArrowLeft className="mr-1 h-4 w-4" />
              返回登录
            </Link>
          </div>
          <CardTitle className="text-2xl text-center">忘记密码</CardTitle>
          <CardDescription className="text-center">
            {!isSubmitted ? "请输入您的邮箱地址，我们将发送密码重置链接给您" : "重置链接已发送，请检查您的邮箱"}
          </CardDescription>
        </CardHeader>

        {!isSubmitted ? (
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入您的邮箱地址"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "发送中..." : "发送重置链接"}
              </Button>
            </CardFooter>
          </form>
        ) : (
          <CardContent className="space-y-4">
            <div className="flex flex-col items-center justify-center py-6">
              <div className="rounded-full bg-green-100 p-3 mb-4">
                <Check className="h-6 w-6 text-green-600" />
              </div>
              <p className="text-center text-sm text-muted-foreground">
                我们已向 <span className="font-medium text-foreground">{email}</span> 发送了一封包含密码重置链接的邮件。
                请检查您的邮箱并点击邮件中的链接来重置密码。
              </p>
              <p className="text-center text-sm text-muted-foreground mt-4">
                如果您没有收到邮件，请检查垃圾邮件文件夹，或者
                <Button variant="link" className="p-0 h-auto" onClick={() => setIsSubmitted(false)}>
                  重新发送
                </Button>
              </p>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}

