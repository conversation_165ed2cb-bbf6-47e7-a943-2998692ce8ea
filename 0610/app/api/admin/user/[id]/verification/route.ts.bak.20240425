import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"



/**
 * 获取用户认证资料
 *
 * @route GET /api/admin/user/[id]/verification
 * @access 需要管理员权限
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id
    console.log(`[认证资料查看API] 开始处理请求，用户ID: ${userId}`)

    // 获取请求头信息进行调试
    const headers = Object.fromEntries(request.headers.entries())
    console.log(`[认证资料查看API] 请求头:`, JSON.stringify(headers, null, 2))

    // 检查cookie
    const cookieHeader = request.headers.get('cookie')
    console.log(`[认证资料查看API] Cookie:`, cookieHeader)

    // 使用统一认证服务获取用户
    console.log(`[认证资料查看API] 开始获取当前用户`)
    const admin = await UnifiedAuthService.getCurrentUser(request, "认证资料查看API")
    console.log(`[认证资料查看API] 获取当前用户结果:`, admin ? `成功, ID: ${admin.id}` : '失败')

    if (!admin) {
      console.error('未找到有效的认证信息')
      return NextResponse.json({
        success: false,
        message: '未授权访问，请确保您已登录并具有管理员权限'
      }, { status: 401 })
    }

    // 使用统一认证服务检查管理员权限
    if (!UnifiedAuthService.isAdmin(admin, "认证资料查看API")) {
      console.error('用户没有管理员权限:', admin.username, admin.roleCode)
      return NextResponse.json({
        success: false,
        message: '没有权限执行此操作，需要管理员权限'
      }, { status: 403 })
    }

    // 获取用户认证资料
    console.log(`[认证资料查看API] 开始查询用户认证资料: ${userId}`)
    let verification;
    try {
      verification = await prisma.userVerification.findUnique({
        where: { userId }
      })

      console.log(`[认证资料查看API] 查询结果:`, verification ? `成功, 类型: ${verification.type}, 状态: ${verification.status}` : '未找到认证资料')

      if (!verification) {
        console.log(`用户 ${userId} 没有提交认证资料`)
        return NextResponse.json({
          success: false,
          message: '未找到认证资料，用户可能尚未提交',
          data: null
        }, { status: 404 })
      }
    } catch (dbError) {
      console.error(`[认证资料查看API] 查询认证资料错误:`, dbError)
      return NextResponse.json({
        success: false,
        message: '查询认证资料时发生错误: ' + (dbError instanceof Error ? dbError.message : '未知错误')
      }, { status: 500 })
    }

    console.log(`[认证资料查看API] 成功获取用户 ${userId} 的认证资料，类型: ${verification.type}, 状态: ${verification.status}`)

    // 准备响应数据
    const responseData = {
      success: true,
      message: '获取认证资料成功',
      data: {
        status: verification.status,
        type: verification.type,
        reviewedAt: verification.reviewedAt,
        remark: verification.remark,
        data: {
          // 个人认证信息
          realName: verification.realName,
          idCardNumber: verification.idCardNumber,
          idCardFront: verification.idCardFront,
          idCardBack: verification.idCardBack,
          idCardHolding: verification.idCardHolding,

          // 企业认证信息
          companyName: verification.companyName,
          legalPerson: verification.legalPerson,
          legalPersonIdCard: verification.legalPersonIdCard,
          socialCreditCode: verification.socialCreditCode,
          businessLicense: verification.businessLicense,
          otherDocuments: verification.otherDocuments,
        }
      }
    };

    console.log(`[认证资料查看API] 返回数据:`, {
      success: responseData.success,
      message: responseData.message,
      dataType: responseData.data.type,
      dataStatus: responseData.data.status
    });

    // 返回认证资料
    return NextResponse.json(responseData)
  } catch (error) {
    console.error("获取用户认证资料错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户认证资料失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
