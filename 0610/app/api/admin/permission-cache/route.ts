import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取权限缓存统计信息
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[权限缓存API:${requestId}] 获取权限缓存统计信息`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "获取权限缓存统计信息API");
    if (response) {
      logger.error(`[权限缓存API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以查看权限缓存统计信息",
        requestId
      }, { status: 403 })
    }

    // 获取缓存统计信息
    const stats = CasbinService.getCacheStats()

    return NextResponse.json({
      success: true,
      data: stats,
      requestId
    })
  } catch (error) {
    console.error(`[权限缓存API:${requestId}] 获取权限缓存统计信息失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取权限缓存统计信息失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 清除权限缓存
 */
export async function DELETE(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[权限缓存API:${requestId}] 清除权限缓存`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "清除权限缓存API");    if (response) {
      console.log(`[权限缓存API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以清除权限缓存",
        requestId
      }, { status: 403 })
    }

    // 清除缓存
    CasbinService.clearCache()

    return NextResponse.json({
      success: true,
      message: "权限缓存已清除",
      requestId
    })
  } catch (error) {
    console.error(`[权限缓存API:${requestId}] 清除权限缓存失败:`, error)
    return NextResponse.json({
      success: false,
      message: "清除权限缓存失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
