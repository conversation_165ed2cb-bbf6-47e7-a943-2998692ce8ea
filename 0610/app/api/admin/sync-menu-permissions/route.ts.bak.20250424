import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { getServerSession } from "next-auth/next"
import { options } from "../../auth/[...nextauth]/options"

/**
 * 同步菜单权限的共享逻辑
 */
async function syncMenuPermissions(requestId: string, roleCode?: string) {
  try {
    // 获取所有菜单
    const menus = await prisma.menu.findMany()
    console.log(`[同步菜单权限API:${requestId}] 找到 ${menus.length} 个菜单`)

    // 如果指定了角色代码，只同步该角色的权限
    if (roleCode) {
      // 获取角色
      const role = await prisma.role.findUnique({
        where: { code: roleCode },
        include: {
          menus: true,
          operations: true
        }
      })

      if (!role) {
        throw new Error(`角色 ${roleCode} 不存在`)
      }

      console.log(`[同步菜单权限API:${requestId}] 同步角色 ${role.name} (${role.code}) 的菜单权限`)

      // 获取角色当前的所有权限
      const currentPermissions = await CasbinService.getRolePermissions(role.code)

      // 删除所有菜单和操作权限
      for (const permission of currentPermissions) {
        if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
          await CasbinService.removePermissionForRole(role.code, permission[1], permission[2])
        }
      }

      // 添加菜单权限
      for (const menu of role.menus) {
        await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
      }

      // 添加操作权限
      for (const operation of role.operations) {
        await CasbinService.addPermissionForRole(role.code, `operation:${operation.code}`, 'execute')
      }

      // 如果是管理员角色，添加通配符权限
      if (role.code === 'ADMIN') {
        await CasbinService.addPermissionForRole('ADMIN', '*', '*')
      }

      return {
        success: true,
        message: `角色 ${role.name} (${role.code}) 的权限同步成功`,
        data: {
          roleCode: role.code,
          menuCount: role.menus.length,
          operationCount: role.operations.length
        }
      }
    } else {
      // 同步所有角色的权限
      const roles = await prisma.role.findMany({
        include: {
          menus: true,
          operations: true
        }
      })
      console.log(`[同步菜单权限API:${requestId}] 找到 ${roles.length} 个角色`)

      // 同步每个角色的菜单权限
      for (const role of roles) {
        console.log(`[同步菜单权限API:${requestId}] 同步角色 ${role.name} (${role.code}) 的菜单权限`)

        // 获取角色当前的所有权限
        const currentPermissions = await CasbinService.getRolePermissions(role.code)

        // 删除所有菜单和操作权限
        for (const permission of currentPermissions) {
          if (permission[1].startsWith('menu:') || permission[1].startsWith('operation:')) {
            await CasbinService.removePermissionForRole(role.code, permission[1], permission[2])
          }
        }

        // 添加菜单权限
        for (const menu of role.menus) {
          await CasbinService.addPermissionForRole(role.code, `menu:${menu.code}`, 'view')
        }

        // 添加操作权限
        for (const operation of role.operations) {
          await CasbinService.addPermissionForRole(role.code, `operation:${operation.code}`, 'execute')
        }

        // 如果是管理员角色，添加通配符权限
        if (role.code === 'ADMIN') {
          await CasbinService.addPermissionForRole('ADMIN', '*', '*')
        }
      }

      return {
        success: true,
        message: "所有角色的权限同步成功",
        data: {
          roleCount: roles.length,
          menuCount: menus.length
        }
      }
    }
  } catch (error) {
    console.error(`[同步菜单权限API:${requestId}] 同步菜单权限失败:`, error)
    throw error
  }
}

/**
 * 同步菜单权限 (GET)
 */
export async function GET(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[同步菜单权限API:${requestId}] 开始同步菜单权限 (GET)`)

  try {
    const result = await syncMenuPermissions(requestId)
    return NextResponse.json({
      ...result,
      requestId
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "同步菜单权限失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 同步菜单权限 (POST)
 */
export async function POST(request: Request) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[同步菜单权限API:${requestId}] 开始同步菜单权限 (POST)`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)

    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[同步菜单权限API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以同步菜单权限",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json().catch(() => ({}))
    const roleCode = body.roleCode

    const result = await syncMenuPermissions(requestId, roleCode)
    return NextResponse.json({
      ...result,
      requestId
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "同步菜单权限失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
