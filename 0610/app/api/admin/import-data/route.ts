import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { videoCallService } from "@/lib/api/video-call-service";
import { prisma } from "@/lib/prisma";
import logger from "@/lib/utils/logger";

/**
 * 导入历史数据API
 * 从远程接口获取历史任务和外呼详情数据，并导入到本地数据库
 */
export async function POST(req: NextRequest) {
  try {
    // 1. 获取当前用户会话
    const session = await getServerSession(authOptions);
    
    // 2. 检查用户权限
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 }
      );
    }
    
    // 3. 检查用户角色
    const userRole = session.user.role;
    if (userRole !== "super" && userRole !== "admin") {
      return NextResponse.json(
        { success: false, message: "权限不足，仅管理员可以导入历史数据" },
        { status: 403 }
      );
    }
    
    // 4. 导入历史任务数据
    const startTime = Date.now();
    logger.log("开始导入历史任务数据...");
    
    // 4.1 获取历史任务数据
    const historicalTasks = await videoCallService.getHistoricalTasks();
    logger.log(`获取到 ${historicalTasks.length} 条历史任务数据`);
    
    // 4.2 检查哪些任务尚未在本地数据库中
    const existingTaskIds = new Set((await prisma.task.findMany({
      where: { externalId: { not: null } },
      select: { externalId: true }
    })).map(task => task.externalId));
    
    const newTasks = historicalTasks.filter(task => !existingTaskIds.has(task.id));
    logger.log(`需要导入 ${newTasks.length} 条新历史任务数据`);
    
    // 4.3 导入新任务数据
    let importedTasksCount = 0;
    if (newTasks.length > 0) {
      // 批量导入任务数据
      const tasksToImport = newTasks.map(task => ({
        name: task.name,
        type: task.type,
        content: task.content,
        status: task.status,
        progress: task.progress || 0,
        importTime: new Date(),
        startTime: new Date(task.startTime),
        completionTime: task.endTime ? new Date(task.endTime) : null,
        creator: task.createdBy || "系统导入",
        userId: task.userId || session.user.id, // 如果没有用户ID，则使用当前管理员ID
        externalId: task.id,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt || task.createdAt)
      }));
      
      // 分批导入，避免一次导入过多数据
      const batchSize = 100;
      for (let i = 0; i < tasksToImport.length; i += batchSize) {
        const batch = tasksToImport.slice(i, i + batchSize);
        await prisma.task.createMany({
          data: batch,
          skipDuplicates: true
        });
        importedTasksCount += batch.length;
        logger.log(`已导入 ${importedTasksCount}/${tasksToImport.length} 条任务数据`);
      }
    }
    
    // 5. 导入历史外呼详情数据
    logger.log("开始导入历史外呼详情数据...");
    
    // 5.1 获取历史外呼详情数据
    const historicalCallDetails = await videoCallService.getHistoricalCallDetails();
    logger.log(`获取到 ${historicalCallDetails.length} 条历史外呼详情数据`);
    
    // 5.2 检查哪些外呼详情尚未在本地数据库中
    const existingCallDetailIds = new Set((await prisma.call_detail.findMany({
      where: { externalCallId: { not: null } },
      select: { externalCallId: true }
    })).map(detail => detail.externalCallId));
    
    const newCallDetails = historicalCallDetails.filter(detail => !existingCallDetailIds.has(detail.id));
    logger.log(`需要导入 ${newCallDetails.length} 条新历史外呼详情数据`);
    
    // 5.3 导入新外呼详情数据
    let importedCallDetailsCount = 0;
    if (newCallDetails.length > 0) {
      // 获取所有任务的映射关系（externalId -> id）
      const taskMap = new Map((await prisma.task.findMany({
        where: { externalId: { not: null } },
        select: { id: true, externalId: true }
      })).map(task => [task.externalId, task.id]));
      
      // 批量导入外呼详情数据
      const callDetailsToImport = newCallDetails
        .filter(detail => taskMap.has(detail.taskId)) // 只导入有对应任务的外呼详情
        .map(detail => ({
          taskId: taskMap.get(detail.taskId)!, // 使用本地任务ID
          taskName: detail.taskName,
          type: detail.type,
          content: detail.content,
          customerName: detail.customerName,
          phoneNumber: detail.phoneNumber,
          connectionType: detail.connectionType,
          startTime: detail.startTime ? new Date(detail.startTime) : null,
          endTime: detail.endTime ? new Date(detail.endTime) : null,
          duration: detail.duration,
          ringTime: detail.ringTime,
          intention: detail.intention,
          externalCallId: detail.id,
          recordingUrl: detail.recordingUrl,
          completionRate: detail.completionRate,
          userId: detail.userId || session.user.id, // 如果没有用户ID，则使用当前管理员ID
          createdAt: new Date(detail.createdAt),
          updatedAt: new Date(detail.updatedAt || detail.createdAt)
        }));
      
      // 分批导入，避免一次导入过多数据
      const batchSize = 100;
      for (let i = 0; i < callDetailsToImport.length; i += batchSize) {
        const batch = callDetailsToImport.slice(i, i + batchSize);
        await prisma.call_detail.createMany({
          data: batch,
          skipDuplicates: true
        });
        importedCallDetailsCount += batch.length;
        logger.log(`已导入 ${importedCallDetailsCount}/${callDetailsToImport.length} 条外呼详情数据`);
      }
    }
    
    // 6. 计算导入耗时
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    // 7. 返回导入结果
    return NextResponse.json({
      success: true,
      message: "历史数据导入成功",
      data: {
        importedTasksCount,
        totalTasksCount: historicalTasks.length,
        importedCallDetailsCount,
        totalCallDetailsCount: historicalCallDetails.length,
        duration: `${duration}秒`
      }
    });
  } catch (error) {
    logger.error("导入历史数据失败:", error);
    return NextResponse.json(
      { success: false, message: `导入历史数据失败: ${error.message}` },
      { status: 500 }
    );
  }
}
