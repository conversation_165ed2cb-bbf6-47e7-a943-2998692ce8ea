import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { dataImportService } from "@/lib/services/data-import-service";
import logger from '@/lib/utils/logger';

/**
 * 导入历史数据API
 * 仅管理员可以访问
 */
export async function POST(req: NextRequest) {
  try {
    // 1. 获取当前用户会话
    const session = await getServerSession(authOptions);
    
    // 2. 检查用户权限
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 }
      );
    }
    
    // 3. 检查用户角色
    const userRole = session.user.role;
    if (userRole !== "super" && userRole !== "admin") {
      return NextResponse.json(
        { success: false, message: "权限不足，仅管理员可以导入历史数据" },
        { status: 403 }
      );
    }
    
    // 4. 导入历史数据
    const startTime = Date.now();
    const result = await dataImportService.importAllHistoricalData();
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    // 5. 返回结果
    return NextResponse.json({
      success: result.success,
      message: result.message,
      importedTasksCount: result.tasks.importedCount,
      importedCallDetailsCount: result.callDetails.importedCount,
      totalTasksCount: result.tasks.totalCount,
      totalCallDetailsCount: result.callDetails.totalCount,
      duration: `${duration}秒`
    });
  } catch (error) {
    logger.error("导入历史数据失败:", error);
    return NextResponse.json(
      { success: false, message: `导入历史数据失败: ${error.message}` },
      { status: 500 }
    );
  }
}
