/**
 * CSRF保护中间件
 * 用于保护API请求免受CSRF攻击
 * 
 * 工作原理：
 * 1. 检查请求头中是否包含正确的Origin或Referer
 * 2. 对于非GET请求，验证请求是否来自同一站点
 * 
 * @module CSRFMiddleware
 */

import { NextRequest, NextResponse } from "next/server"

/**
 * CSRF保护中间件
 * @param request 请求对象
 * @param handler 处理函数
 */
export async function csrfProtection(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  // 跳过GET请求的CSRF检查
  if (request.method === "GET") {
    return handler(request)
  }

  // 获取请求头
  const origin = request.headers.get("origin")
  const referer = request.headers.get("referer")
  const host = request.headers.get("host")

  // 如果没有Origin和Referer头，拒绝请求
  if (!origin && !referer) {
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: "CSRF保护：缺少Origin或Referer头"
      }),
      {
        status: 403,
        headers: {
          "Content-Type": "application/json"
        }
      }
    )
  }

  // 检查Origin是否匹配
  if (origin) {
    try {
      const originHost = new URL(origin).host
      if (originHost !== host) {
        return new NextResponse(
          JSON.stringify({
            success: false,
            error: "CSRF保护：Origin不匹配"
          }),
          {
            status: 403,
            headers: {
              "Content-Type": "application/json"
            }
          }
        )
      }
    } catch (error) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: "CSRF保护：无效的Origin"
        }),
        {
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      )
    }
  }

  // 检查Referer是否匹配
  if (referer && !origin) {
    try {
      const refererHost = new URL(referer).host
      if (refererHost !== host) {
        return new NextResponse(
          JSON.stringify({
            success: false,
            error: "CSRF保护：Referer不匹配"
          }),
          {
            status: 403,
            headers: {
              "Content-Type": "application/json"
            }
          }
        )
      }
    } catch (error) {
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: "CSRF保护：无效的Referer"
        }),
        {
          status: 403,
          headers: {
            "Content-Type": "application/json"
          }
        }
      )
    }
  }

  // 通过CSRF检查，继续处理请求
  return handler(request)
}
