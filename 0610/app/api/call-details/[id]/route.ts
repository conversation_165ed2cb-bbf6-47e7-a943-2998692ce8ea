import { type NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"
import { formatApiResponseDates } from "@/lib/utils/api-formatter"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取通话记录详情API");
    if (response) {
      return response;
    }

    const callId = params.id;

    // 检查用户角色，非管理员只能查看自己的通话记录
    const isAdmin = user.role === "super" || user.role === "admin";
    const where: any = { id: callId };
    if (!isAdmin) {
      where.userId = user.id;
    }

    // 查询通话记录详情
    const callDetail = await db.call_detail.findFirst({
      where,
      include: {
        task: {
          select: {
            name: true,
            type: true,
            content: true,
            status: true,
          },
        },
      },
    });

    if (!callDetail) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "通话记录不存在或无权访问",
          data: null,
        },
        { status: 404 }
      );
    }

    // 格式化日期时间字段
    const formattedCallDetail = formatApiResponseDates(callDetail);

    // 返回通话记录详情
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取通话记录详情成功",
        data: formattedCallDetail,
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("获取通话记录详情错误:", error);

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    );
  }
}
