import { type NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"
import { formatApiResponseDates } from "@/lib/utils/api-formatter"

export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "通话记录API");
    if (response) {
      return response;
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "10")
    const search = searchParams.get("search") || ""
    const taskId = searchParams.get("taskId") || ""
    const type = searchParams.get("type") || ""
    const startDate = searchParams.get("startDate") || ""
    const endDate = searchParams.get("endDate") || ""

    // 构建查询条件
    const where: any = {}

    // 检查用户角色，非管理员只能查看自己的通话记录
    const isAdmin = user.role === "super" || user.role === "admin";
    if (!isAdmin) {
      where.userId = user.id;
    }

    // 如果是历史通话记录查询，不需要额外的过滤条件
    if (type === "historical") {
      // 历史通话记录查询不需要额外的过滤条件
      logger.log("查询历史通话记录数据");
    }

    if (taskId) {
      where.taskId = taskId
    }

    if (search) {
      where.OR = [
        { customerName: { contains: search } },
        { phoneNumber: { contains: search } }
      ]
    }

    if (startDate) {
      where.startTime = {
        ...where.startTime,
        gte: new Date(startDate)
      }
    }

    if (endDate) {
      where.endTime = {
        ...where.endTime,
        lte: new Date(endDate)
      }
    }

    // 查询总数
    const total = await db.call_detail.count({ where })
    const totalPages = Math.ceil(total / pageSize)

    // 查询分页数据
    const callDetails = await db.call_detail.findMany({
      where,
      skip: (page - 1) * pageSize,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
      include: {
        task: {
          select: {
            name: true,
            type: true,
            content: true
          }
        }
      }
    })

    // 格式化日期时间字段
    const formattedCallDetails = formatApiResponseDates(callDetails);

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取通话记录列表成功",
        data: {
          list: formattedCallDetails,
          total,
          page,
          pageSize,
          totalPages,
        },
      },
      { status: 200 },
    )
  } catch (error) {
    logger.error("获取通话记录列表错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}
