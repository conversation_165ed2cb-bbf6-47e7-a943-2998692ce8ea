/**
 * 通话计费处理模块
 * 用于处理通话记录的计费逻辑
 */

import { prisma } from "@/lib/prisma"
import { BillingService } from "@/lib/services/billing-service"
import { BalanceAlertService } from "@/lib/services/balance-alert-service"

/**
 * 处理通话计费
 * 计算通话费用并扣除用户余额
 *
 * @param callRecord 通话记录
 * @param task 关联的任务
 * @returns 计费结果
 */
export async function processCallBilling(callRecord: any, task: any) {
  try {
    // 只对已接通的通话进行计费
    if (!callRecord.isConnected) {
      console.log(`通话未接通，不计费: ${callRecord.externalCallId}`)
      return {
        success: true,
        message: "通话未接通，不计费",
        data: null
      }
    }

    // 获取用户ID
    const userId = task.userId

    // 查询用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      console.error(`找不到用户: ${userId}`)
      return {
        success: false,
        message: "找不到用户",
        data: null
      }
    }

    // 获取通话时长（秒）
    const duration = callRecord.duration || 0

    // 获取通话类型
    const connectionType = callRecord.connectionType // VIDEO 或 AUDIO

    // 获取业务类型
    const businessType = connectionType === "VIDEO" ? "VIDEO_INTERACTION" : "VOICE_INTERACTION"

    // 查询用户对应业务类型的费率
    const rate = await prisma.rate.findFirst({
      where: {
        customerId: userId,
        businessType: businessType
      }
    })

    // 如果没有找到用户特定费率，使用默认费率
    const defaultRate = await prisma.rate.findFirst({
      where: {
        customerId: null,
        businessType: businessType
      }
    })

    // 使用找到的费率或默认值
    const userRate = rate || defaultRate

    if (!userRate) {
      console.error(`找不到费率配置: userId=${userId}, businessType=${businessType}`)
      return {
        success: false,
        message: "找不到费率配置",
        data: null
      }
    }

    // 计算费用
    const cost = BillingService.calculateCallCost(
      duration,
      userRate.amount,
      userRate.billingIncrement || "PER_MINUTE"
    )

    // 如果费用为0，不进行余额扣减
    if (cost <= 0) {
      console.log(`通话费用为0，不扣减余额: ${callRecord.externalCallId}`)
      return {
        success: true,
        message: "通话费用为0，不扣减余额",
        data: { cost }
      }
    }

    // 计算新余额
    const newBalance = user.balance - cost

    // 更新用户余额
    await prisma.user.update({
      where: { id: userId },
      data: {
        balance: newBalance
      }
    })

    // 记录余额变动日志
    await prisma.balanceTransaction.create({
      data: {
        userId: userId,
        amount: -cost, // 负数表示扣费
        balanceAfter: newBalance,
        type: "deduct",
        paymentMethod: "system",
        remarks: `通话费用: ${task.name || "外呼任务"} - ${callRecord.phone}`,
      }
    })

    // 检查余额预警
    await BalanceAlertService.checkAndSendBalanceAlert(userId, newBalance)

    console.log(`通话计费成功: userId=${userId}, cost=${cost}, newBalance=${newBalance}`)

    return {
      success: true,
      message: "通话计费成功",
      data: {
        userId,
        cost,
        newBalance,
        duration,
        businessType,
        rateAmount: userRate.amount,
        billingIncrement: userRate.billingIncrement
      }
    }
  } catch (error) {
    console.error("处理通话计费失败:", error)
    return {
      success: false,
      message: "处理通话计费失败: " + (error instanceof Error ? error.message : "未知错误"),
      data: null
    }
  }
}
