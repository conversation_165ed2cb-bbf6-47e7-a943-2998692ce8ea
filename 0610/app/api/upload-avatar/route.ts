// Since we're removing the avatar upload functionality, we can simplify this API route
// to return an error message indicating the feature is disabled

import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { writeFile } from "fs/promises"
import { join } from "path"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "上传头像API");
    if (response) {
      return NextResponse.json(
        {
          code: 401,
          success: false,
          message: "未登录",
          data: null,
        },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get("file") as File

    // 验证文件
    if (!file) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "未提供文件",
          data: null,
        },
        { status: 400 }
      )
    }

    // 验证文件类型
    const allowedTypes = ["image/jpeg", "image/png", "image/gif"]
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "不支持的文件类型",
          data: null,
        },
        { status: 400 }
      )
    }

    // 验证文件大小（最大 5MB）
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "文件大小超过限制",
          data: null,
        },
        { status: 400 }
      )
    }

    // 生成文件名
    const timestamp = Date.now()
    const extension = file.name.split(".").pop()
    const filename = `${user.id}_${timestamp}.${extension}`

    // 保存文件
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    const path = join(process.cwd(), "public/uploads", filename)
    await writeFile(path, buffer)

    // 更新用户头像
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        image: `/uploads/${filename}`,
      },
      select: {
        id: true,
        username: true,
        image: true,
      },
    })

    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "上传头像成功",
        data: updatedUser,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("上传头像错误:", error)
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    )
  }
}

