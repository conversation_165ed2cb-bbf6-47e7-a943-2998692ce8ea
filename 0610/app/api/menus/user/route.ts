import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { withAuth } from "@/lib/middleware/with-auth"
import logger from "@/lib/utils/logger"
import crypto from "crypto"
import { rateLimit } from "@/lib/utils/rate-limit"

/**
 * 生成字符串的哈希值
 * @param data 要哈希的数据
 * @returns 哈希值（十六进制字符串）
 */
async function generateHash(data: string): Promise<string> {
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * 获取当前用户有权限访问的菜单
 *
 * 使用tryAuth方法，允许未登录用户访问，但会根据用户角色返回不同的菜单：
 * - 管理员用户：返回所有可见菜单
 * - 普通用户：返回用户角色关联的菜单
 * - 未登录用户：返回预定义的公开菜单
 *
 * 安全考虑：
 * - 未登录用户只能看到明确定义为公开的菜单
 * - 所有菜单访问都经过权限验证
 * - 日志记录了访问请求，便于审计
 * - 响应头设置禁止缓存，防止敏感信息被缓存
 */
// 应用速率限制，每分钟最多30次请求
export const GET = rateLimit({
  limit: 30,
  windowMs: 60 * 1000, // 1分钟
})(withAuth(
  async (request: NextRequest, { user, requestId }) => {
    // 记录API请求
    logger.log(`[菜单API:${requestId}] 获取用户菜单`)

    // 设置响应头，根据用户类型设置不同的缓存策略
    let cacheControl = '';

    // 检查用户是否已登录
    const isAuthenticated = !!user;

    if (isAuthenticated) {
      // 已登录用户：使用私有缓存，短时间有效，需要验证
      cacheControl = 'private, max-age=60, must-revalidate';
    } else {
      // 未登录用户：可以使用共享缓存，较长时间有效，但需要验证
      cacheControl = 'public, max-age=300, must-revalidate';
    }

    const headers = {
      'Cache-Control': cacheControl,
      'Pragma': 'no-cache',
      'Expires': '0'
    }

    try {
      // 用户身份已在上面检查过
      let userId = null;
      let roleCode = 'GUEST'; // 默认为访客角色

      if (isAuthenticated) {
        userId = user.id;
        roleCode = user.roleCode;
        logger.log(`[菜单API:${requestId}] 已登录用户，ID: ${userId}, 角色: ${roleCode}`);
      } else {
        logger.log(`[菜单API:${requestId}] 未登录用户，使用访客角色`);
      }

    let allMenus = []

    // 根据角色获取菜单
    if (roleCode === 'ADMIN') {
      logger.log(`[菜单API:${requestId}] 管理员用户，获取所有菜单`)

      // 获取所有可见的菜单
      allMenus = await prisma.menu.findMany({
        where: {
          visible: true
        },
        select: {
          id: true,
          code: true,
          name: true,
          path: true,
          icon: true,
          parentId: true,
          order: true,
          visible: true
        },
        orderBy: { order: 'asc' }
      })
    } else if (roleCode === 'GUEST') {
      logger.log(`[菜单API:${requestId}] 访客用户，获取公开菜单`)

      // 获取公开菜单（使用预定义的公开菜单代码列表）
      // 安全考虑：这里明确定义了未登录用户可以访问的菜单，避免信息泄露
      // 只包含登录相关的菜单，不包含敏感功能菜单
      const publicMenuCodes = ['login', 'register', 'forgot-password'];

      // 只选择必要的字段，避免返回敏感信息
      allMenus = await prisma.menu.findMany({
        where: {
          visible: true,
          code: {
            in: publicMenuCodes
          }
        },
        select: {
          id: true,
          code: true,
          name: true,
          path: true,
          icon: true,
          parentId: true,
          order: true,
          visible: true
          // 不返回其他可能敏感的字段
        },
        orderBy: { order: 'asc' }
      })
    } else {
      // 其他角色获取角色关联的菜单
      const role = await prisma.role.findUnique({
        where: { code: roleCode },
        include: {
          menus: {
            where: { visible: true },
            orderBy: { order: 'asc' }
          }
        }
      })

      if (!role) {
        return NextResponse.json({
          success: false,
          message: "用户角色不存在",
          requestId,
          timestamp: Date.now()
        }, { status: 404, headers })
      }

      // 获取菜单ID列表
      const menuIds = role.menus.map(menu => menu.id)

      // 获取所有菜单（包括子菜单）
      allMenus = await prisma.menu.findMany({
        where: {
          OR: [
            { id: { in: menuIds } },
            { parentId: { in: menuIds } }
          ],
          visible: true
        },
        select: {
          id: true,
          code: true,
          name: true,
          path: true,
          icon: true,
          parentId: true,
          order: true,
          visible: true
        },
        orderBy: { order: 'asc' }
      })
    }

    // 使用jCasbin验证权限
    let authorizedMenus = []

    // 根据角色处理菜单权限
    if (roleCode === 'ADMIN') {
      logger.log(`[菜单API:${requestId}] 管理员用户，自动授权所有菜单`)
      authorizedMenus = allMenus
    } else if (roleCode === 'GUEST') {
      logger.log(`[菜单API:${requestId}] 访客用户，使用公开菜单`)
      authorizedMenus = allMenus // 对于访客，公开菜单已经在上面的查询中筛选过了
    } else {
      // 其他角色需要验证权限
      for (const menu of allMenus) {
        const hasPermission = await CasbinService.checkPermission(
          roleCode,
          `menu:${menu.code}`,
          'view'
        )

        if (hasPermission || menu.parentId) {
          authorizedMenus.push(menu)
        }
      }
    }

    // 定义菜单项类型
    interface MenuItem {
      id: string;
      code: string;
      name: string;
      path: string;
      icon?: string;
      parentId?: string | null;
      order: number;
      visible: boolean;
      children?: MenuItem[];
      [key: string]: any; // 允许其他属性
    }

    // 构建菜单树
    const buildMenuTree = (items: MenuItem[], parentId: string | null = null): MenuItem[] => {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          ...item,
          children: buildMenuTree(items, item.id)
        }))
    }

    // 构建树形结构
    const menuTree = buildMenuTree(authorizedMenus)

    // 生成ETag（基于用户ID和菜单数据的哈希）
    const menuData = JSON.stringify(menuTree);
    const dataHash = await generateHash(isAuthenticated ? `${user.id}:${menuData}` : `guest:${menuData}`);
    const etag = `"${dataHash}"`;

    // 检查客户端的If-None-Match头
    const ifNoneMatch = request.headers.get('if-none-match');
    if (ifNoneMatch === etag) {
      // 内容未变更，返回304 Not Modified
      return new NextResponse(null, {
        status: 304,
        headers: {
          ...headers,
          'ETag': etag
        }
      });
    }

    // 添加ETag到响应头
    const responseHeaders = {
      ...headers,
      'ETag': etag
    };

    return NextResponse.json({
      success: true,
      data: menuTree,
      requestId
    }, { headers: responseHeaders })
  } catch (error) {
    logger.error(`[菜单API:${requestId}] 获取用户菜单失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取用户菜单失败",
      requestId,
      timestamp: Date.now()
    }, { status: 500, headers })
  }
  },
  { tryAuth: true, apiName: "获取用户菜单API" }
))
