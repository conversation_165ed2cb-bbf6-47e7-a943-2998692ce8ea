import { NextRequest } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthService } from "@/lib/auth-service"

export async function POST(request: NextRequest) {
  try {
    const user = await AuthService.getCurrentUser()
    if (!user) {
      return Response.json(
        { success: false, error: "未认证" },
        { status: 401 }
      )
    }

    // 检查是否有更新菜单的权限
    const hasPermission = await prisma.role.findFirst({
      where: {
        code: user.roleCode,
        permissions: {
          has: "menu:update"
        }
      }
    })

    if (!hasPermission) {
      return Response.json(
        { success: false, error: "无权限" },
        { status: 403 }
      )
    }

    const { updates } = await request.json()

    // 使用事务批量更新菜单顺序
    await prisma.$transaction(
      updates.map(({ id, order }: { id: string; order: number }) =>
        prisma.menu.update({
          where: { id },
          data: { order },
        })
      )
    )

    return Response.json({ success: true })
  } catch (error) {
    console.error("Reorder menus error:", error)
    return Response.json(
      { success: false, error: "更新菜单排序失败" },
      { status: 500 }
    )
  }
} 