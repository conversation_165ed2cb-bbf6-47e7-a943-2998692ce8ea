import { type NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"

/**
 * 刷新任务列表API
 * 用于强制刷新任务列表缓存
 */
export async function POST(request: NextRequest) {
  try {
    logger.log("刷新任务列表API: 开始处理");
    
    // 解析请求体
    const body = await request.json();
    const { forceRefresh = false } = body;
    
    logger.log(`刷新任务列表API: forceRefresh=${forceRefresh}`);
    
    // 这里可以添加实际的刷新逻辑，例如清除缓存
    // 目前只是返回成功响应，前端会重新获取数据
    
    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "任务列表刷新成功",
        data: {
          refreshed: true,
          timestamp: new Date().toISOString()
        },
      },
      { status: 200 },
    )
  } catch (error) {
    logger.error("刷新任务列表错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}
