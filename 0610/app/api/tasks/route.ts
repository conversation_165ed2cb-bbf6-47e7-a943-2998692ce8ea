import { type NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"

export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "任务管理API");
    if (response) {
      return response;
    }
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "10")
    const search = searchParams.get("search") || ""
    const status = searchParams.get("status") || ""
    const type = searchParams.get("type") || ""
    const startDate = searchParams.get("startDate") || ""
    const endDate = searchParams.get("endDate") || ""

    let total = 0;
    let totalPages = 0;
    let paginatedTasks = [];

    // 使用数据库查询
    logger.log("使用数据库查询获取任务列表");

      // 构建查询条件
      const where: any = {};

      // 检查用户角色，非管理员只能查看自己的任务
      // 使用roleCode进行大小写不敏感的比较
      const isAdmin = user.roleCode?.toUpperCase() === "ADMIN" || user.roleCode?.toUpperCase() === "SUPER";
      logger.log(`用户角色: ${user.roleCode}, 是否管理员: ${isAdmin}`);

      if (!isAdmin) {
        where.userId = user.id;
        logger.log(`普通用户，只能查看自己的任务: userId=${user.id}`);
      } else {
        logger.log(`管理员用户，可以查看所有任务`);
      }

      // 如果是历史任务查询或者不指定类型，返回所有任务
      if (type === "historical" || !type) {
        // 历史任务查询不需要额外的过滤条件
        logger.log("查询所有任务数据");

        // 对于历史任务，确保创建人字段存在
        const existingTasks = await db.task.count();
        logger.log(`数据库中现有任务数量: ${existingTasks}`);

        // 直接查询所有任务
        const allTasks = await db.task.findMany({
          where: isAdmin ? {} : { userId: user.id },
          orderBy: {
            createdAt: "desc"
          }
        });

        logger.log(`查询到 ${allTasks.length} 条任务数据`);

        // 如果数据库中有任务数据，直接返回
        if (allTasks.length > 0) {
          return NextResponse.json({
            code: 200,
            success: true,
            message: "获取任务列表成功",
            data: {
              list: allTasks,
              total: allTasks.length,
              page: 1,
              pageSize: allTasks.length,
              totalPages: 1
            }
          });
        }

        // 如果数据库中没有任务数据，尝试从远程获取
        try {
          logger.log("尝试从远程获取任务数据");

          // 导入 getTaskGroups 函数
          const { getTaskGroups } = await import("@/lib/api/video-call-service");

          // 获取远程任务数据
          const taskGroups = await getTaskGroups();

          if (taskGroups.success && Array.isArray(taskGroups.data) && taskGroups.data.length > 0) {
            logger.log(`成功获取 ${taskGroups.data.length} 条远程任务数据`);

            // 转换任务数据格式，使其与本地任务数据格式一致
            const remoteTasks = taskGroups.data.map(task => ({
              id: task.id,
              name: task.name,
              type: task.type || "5G视频通知", // 使用任务类型或默认类型
              content: task.content || task.name,
              status: task.status === "initialized" ? "未开始" :
                      task.status === "started" ? "外呼中" :
                      task.status === "paused" ? "已暂停" :
                      task.status === "finished" ? "已完成" :
                      task.status === "stopped" ? "已停止" : "未知",
              progress: task.status === "finished" ? 100 : 0,
              importTime: task.importTime || task.createdAt,
              startTime: task.startTime || task.createdAt,
              completionTime: task.completionTime,
              creator: task.creator || "系统导入",
              userId: user.id,
              externalId: task.externalId || task.id,
              createdAt: task.createdAt,
              updatedAt: task.updatedAt || task.createdAt,
            }));

            // 将远程任务数据保存到数据库中
            try {
              logger.log("尝试将远程任务数据保存到数据库...");

              // 对于每个任务，检查是否已存在，如果不存在则创建
              for (const task of remoteTasks) {
                const existingTask = await db.task.findFirst({
                  where: {
                    externalId: task.externalId
                  }
                });

                if (!existingTask) {
                  await db.task.create({
                    data: {
                      name: task.name,
                      type: task.type,
                      content: task.content,
                      status: task.status,
                      progress: task.progress,
                      startTime: task.startTime ? new Date(task.startTime) : null,
                      completionTime: task.completionTime ? new Date(task.completionTime) : null,
                      creator: task.creator,
                      userId: task.userId,
                      externalId: task.externalId,
                      importTime: task.importTime ? new Date(task.importTime) : new Date(),
                    }
                  });
                }
              }

              logger.log("远程任务数据已保存到数据库");

              // 重新查询数据库中的任务数据
              const updatedTasks = await db.task.findMany({
                where: isAdmin ? {} : { userId: user.id },
                orderBy: {
                  createdAt: "desc"
                }
              });

              return NextResponse.json({
                code: 200,
                success: true,
                message: "获取任务列表成功",
                data: {
                  list: updatedTasks,
                  total: updatedTasks.length,
                  page: 1,
                  pageSize: updatedTasks.length,
                  totalPages: 1
                }
              });
            } catch (dbError) {
              logger.error("保存远程任务数据到数据库失败:", dbError);

              // 如果保存到数据库失败，直接返回远程任务数据
              return NextResponse.json({
                code: 200,
                success: true,
                message: "获取远程任务数据成功",
                data: {
                  list: remoteTasks,
                  total: remoteTasks.length,
                  page: 1,
                  pageSize: remoteTasks.length,
                  totalPages: 1
                }
              });
            }
          } else {
            logger.error("获取远程任务数据失败:", taskGroups.message);

            // 如果远程API调用失败，返回空数组
            return NextResponse.json({
              code: 200,
              success: true,
              message: "获取任务列表成功（无数据）",
              data: {
                list: [],
                total: 0,
                page: 1,
                pageSize: 10,
                totalPages: 0
              }
            });
          }
        } catch (error) {
          logger.error("获取远程任务数据失败:", error);

          // 如果远程API调用失败，返回空数组
          return NextResponse.json({
            code: 200,
            success: true,
            message: "获取任务列表成功（无数据）",
            data: {
              list: [],
              total: 0,
              page: 1,
              pageSize: 10,
              totalPages: 0
            }
          });
        }

        // 如果远程也没有数据，返回空数组
        return NextResponse.json({
          code: 200,
          success: true,
          message: "获取任务列表成功",
          data: {
            list: [],
            total: 0,
            page: 1,
            pageSize: 10,
            totalPages: 0
          }
        });
      }

      if (search) {
        where.OR = [
          { name: { contains: search } },
          { content: { contains: search } }
        ];
      }

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      if (startDate) {
        where.startTime = {
          ...where.startTime,
          gte: new Date(startDate)
        };
      }

      if (endDate) {
        where.startTime = {
          ...where.startTime,
          lte: new Date(endDate)
        };
      }

      // 查询总数
      total = await db.task.count({ where });
      totalPages = Math.ceil(total / pageSize);

      // 查询分页数据
      paginatedTasks = await db.task.findMany({
        where,
        skip: (page - 1) * pageSize,
        take: pageSize,
        orderBy: { createdAt: 'desc' }
      });

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "获取任务列表成功",
        data: {
          list: paginatedTasks,
          total,
          page,
          pageSize,
          totalPages,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("获取任务列表错误:", error);

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "创建任务API");
    if (response) {
      return response;
    }
    // 解析请求体
    const body = await request.json();
    const { name, content, callType, startTime, resource, phoneNumber, smsType, smsTemplate } = body;

    // 验证请求参数
    if (!name || !content || !callType || !startTime || !resource) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少必要参数",
          data: null,
        },
        { status: 400 }
      );
    }

    let newTask;
    // 创建新任务（数据库）
    logger.log("使用数据库创建任务");
    newTask = await db.task.create({
      data: {
        name,
        type: callType,
        content,
        startTime: new Date(startTime),
        progress: 0,
        creator: (user.roleCode?.toUpperCase() === "SUPER" || user.roleCode?.toUpperCase() === "ADMIN") ? "管理员" : user.username,
        status: "未开始",
        userId: user.id,
        // 其他字段根据实际数据库模型添加
      }
    });

    // 返回成功响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: "创建任务成功",
        data: newTask,
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("创建任务错误:", error);

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 }
    );
  }
}

