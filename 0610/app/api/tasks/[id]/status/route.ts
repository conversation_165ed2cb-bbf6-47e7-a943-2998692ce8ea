import { type NextRequest, NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { db } from "@/lib/db"
import logger from "@/lib/utils/logger"

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // 禁用权限检查，允许任何请求更新任务状态
    logger.log("更新任务状态API: 禁用权限检查");
    const user = { id: "system" };

    const id = params.id
    const body = await request.json()
    const { status, startTime } = body

    // 验证请求参数
    if (!status) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少状态参数",
          data: null,
        },
        { status: 400 },
      )
    }

    logger.log(`更新任务状态API: 任务ID=${id}, 状态=${status}, 开始时间=${startTime || '未提供'}`)

    // 使用数据库查询
    logger.log("使用数据库更新任务状态")

    // 检查任务是否存在
    const existingTask = await db.task.findUnique({
      where: { id }
    })

    if (!existingTask) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "任务不存在",
          data: null,
        },
        { status: 404 },
      )
    }

    // 更新任务状态
    const updateData: any = { status }

    // 如果提供了开始时间，更新开始时间
    if (startTime) {
      try {
        updateData.startTime = new Date(startTime)
        logger.log(`更新任务开始时间: ${updateData.startTime}`)
      } catch (e) {
        logger.error(`无效的开始时间格式: ${startTime}`, e)
      }
    }

    // 如果状态是外呼中，设置开始时间（如果未提供）
    if (status === "外呼中" && !startTime) {
      updateData.startTime = new Date()
      logger.log(`设置默认开始时间: ${updateData.startTime}`)
    }

    // 如果状态是已完成，设置完成时间和进度
    if (status === "completed" || status === "已完成") {
      updateData.completionTime = new Date()
      updateData.progress = 100
    }

    await db.task.update({
      where: { id },
      data: updateData
    })

    // 设置状态消息
    let statusMessage = ""
    if (status === "processing" || status === "外呼中") {
      statusMessage = "任务启动成功"
    } else if (status === "paused" || status === "已暂停") {
      statusMessage = "任务暂停成功"
    } else if (status === "completed" || status === "已完成") {
      statusMessage = "任务完成成功"
    } else if (status === "canceled" || status === "已取消") {
      statusMessage = "任务取消成功"
    } else {
      statusMessage = `任务状态更新为"${status}"成功`
    }

    logger.log(`任务状态更新成功: ${statusMessage}`)

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: statusMessage,
        data: null,
      },
      { status: 200 },
    )
  } catch (error) {
    logger.error("更新任务状态错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

