import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { generateRandomPassword } from "@/lib/utils"
import { NotificationService } from "@/lib/notification-service"
import { emailService } from "@/lib/email-service"

/**
 * 重置用户密码
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[管理员用户API:${requestId}] 重置用户密码: ${params.id}`)

  try {
    // 获取请求体
    const body = await request.json().catch(() => ({}));
    const { password: customPassword } = body;

    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);
    if (response) {
      logger.debug(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以重置用户密码",
        requestId
      }, { status: 403 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 使用自定义密码或生成随机密码
    let newPassword = customPassword;

    // 如果没有提供自定义密码，生成随机密码
    if (!newPassword) {
      newPassword = generateRandomPassword(12);
    }

    // 加密密码
    const hashedPassword = await hash(newPassword, 10)

    // 更新用户密码
    await prisma.user.update({
      where: { id: params.id },
      data: {
        password: hashedPassword
      }
    })

    // 创建系统通知
    try {
      await NotificationService.createPasswordResetNotification(
        params.id,
        admin.id
      )
    } catch (notificationError) {
      console.error(`[管理员用户API:${requestId}] 创建密码重置通知失败:`, notificationError)
      // 通知创建失败不影响主流程
    }

    // 发送邮件通知用户密码已重置
    try {
      if (existingUser.email) {
        // 使用邮件服务发送密码重置邮件
        const emailResult = await emailService.sendPasswordResetEmail(
          existingUser.email,
          existingUser.name || existingUser.username,
          newPassword
        )

        if (emailResult) {
          console.log(`[管理员用户API:${requestId}] 密码重置邮件已添加到队列`)
        } else {
          console.warn(`[管理员用户API:${requestId}] 密码重置邮件添加到队列失败`)
        }
      } else {
        console.warn(`[管理员用户API:${requestId}] 用户没有邮箱，无法发送密码重置邮件`)
      }
    } catch (emailError) {
      console.error(`[管理员用户API:${requestId}] 发送密码重置邮件失败:`, emailError)
      // 邮件发送失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '密码重置成功',
      data: {
        newPassword: newPassword
      },
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 重置用户密码失败:`, error)
    return NextResponse.json({
      success: false,
      message: "重置用户密码失败",
      requestId
    }, { status: 500 })
  }
}
