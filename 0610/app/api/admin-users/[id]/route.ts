import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取单个用户
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[管理员用户API:${requestId}] 获取用户详情: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);
    if (response) {
      logger.error(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以查看用户详情",
        requestId
      }, { status: 403 })
    }

    // 获取用户
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        roleCode: true,
        status: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: user,
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 获取用户详情失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取用户详情失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 更新用户
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[管理员用户API:${requestId}] 更新用户: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);    if (response) {
      logger.error(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以更新用户",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    console.log(`[管理员用户API:${requestId}] 请求体:`, body)

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 检查用户名和邮箱是否已被其他用户使用
    if (body.username !== existingUser.username || body.email !== existingUser.email) {
      const duplicateUser = await prisma.user.findFirst({
        where: {
          OR: [
            { username: body.username },
            { email: body.email }
          ],
          NOT: {
            id: params.id
          }
        }
      })

      if (duplicateUser) {
        return NextResponse.json({
          success: false,
          message: "用户名或邮箱已被其他用户使用",
          requestId
        }, { status: 400 })
      }
    }

    // 检查角色是否存在
    if (body.roleCode) {
      const role = await prisma.role.findFirst({
        where: { code: body.roleCode }
      })

      if (!role) {
        return NextResponse.json({
          success: false,
          message: "角色不存在",
          requestId
        }, { status: 400 })
      }
    }

    // 更新用户数据
    const updateData: any = {}
    if (body.username) updateData.username = body.username
    if (body.email) updateData.email = body.email
    if (body.name !== undefined) updateData.name = body.name
    if (body.roleCode) updateData.roleCode = body.roleCode
    if (body.status) updateData.status = body.status

    // 如果提供了密码，则更新密码
    if (body.password && body.password.trim() !== '') {
      updateData.password = await hash(body.password, 10)
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id: params.id },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        roleCode: true,
        status: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return NextResponse.json({
      success: true,
      data: user,
      message: '用户更新成功',
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 更新用户失败:`, error)
    return NextResponse.json({
      success: false,
      message: "更新用户失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 删除用户
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[管理员用户API:${requestId}] 删除用户: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, `管理员用户API:${requestId}`);    if (response) {
      console.log(`[管理员用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以删除用户",
        requestId
      }, { status: 403 })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id }
    })

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 不允许删除自己
    if (existingUser.id === admin.id) {
      return NextResponse.json({
        success: false,
        message: "不能删除当前登录的用户",
        requestId
      }, { status: 400 })
    }

    // 删除用户
    await prisma.user.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: '用户删除成功',
      requestId
    })
  } catch (error) {
    console.error(`[管理员用户API:${requestId}] 删除用户失败:`, error)
    return NextResponse.json({
      success: false,
      message: "删除用户失败",
      requestId
    }, { status: 500 })
  }
}
