import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import AesUtils from "@/lib/api/crypto"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 获取请求头部
 */
function getHeaders() {
  const signature = AesUtils.generateSignature(ORG_CODE)
  return {
    "Content-Type": "application/json; charset=utf-8",
    "access-token": signature,
  }
}

/**
 * 代理获取任务列表
 * 解决CORS问题
 */
export async function POST(request: Request) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取任务列表API");
    if (response) {
      return response;
    }

    // 解析请求体
    const body = await request.json();
    const { 
      page = 1, 
      pageSize = 10, 
      name, 
      type, 
      startTime, 
      endTime, 
      userId 
    } = body;

    // 构建请求参数
    const requestData = {
      orgCode: ORG_CODE,
      loginName: LOGIN_NAME,
      sign: AesUtils.generateSignature(ORG_CODE),
      page,
      pageSize,
      name,
      type,
      startTime,
      endTime,
      userId // 用于权限控制，只查看自己的任务
    };

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/taskList`, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify(requestData),
    });

    const data = await apiResponse.json();

    if (data.errCode === 0) {
      return NextResponse.json({
        success: true,
        message: "获取任务列表成功",
        data: {
          list: data.result.list || [],
          total: data.result.total || 0,
          page: page,
          pageSize: pageSize,
          totalPages: Math.ceil((data.result.total || 0) / pageSize)
        },
      });
    } else {
      return NextResponse.json({
        success: false,
        message: `获取任务列表失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "获取任务列表失败，请稍后重试",
      data: null,
    }, { status: 500 });
  }
}
