import { NextResponse } from "next/server"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import AesUtils from "@/lib/api/crypto"
import CallbackConfig from "@/lib/api/callback-config"

// 安全地获取环境变量
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://services.vcrm.vip:8000";
const ORG_CODE = process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || "XUNMENGdorg";
const LOGIN_NAME = process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || "XUNMENG001";

/**
 * 获取请求头部
 */
function getHeaders() {
  const signature = AesUtils.generateSignature(ORG_CODE)
  return {
    "Content-Type": "application/json; charset=utf-8",
    "access-token": signature,
  }
}

/**
 * 代理任务导入
 * 解决CORS问题
 */
export async function POST(request: Request) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "任务导入API");
    if (response) {
      return response;
    }

    // 解析请求体
    const taskData = await request.json();

    // 构建请求数据
    const deliverList = taskData.phoneNumbers.map((phone: string) => ({
      phone,
      otherInfo: [{ title: "content", value: taskData.content }],
    }));

    // 处理短信相关参数
    const isRelateSendMessage = !!taskData.smsType && taskData.smsType === "文本短信";
    const isRelateVideoMessage = !!taskData.smsType && taskData.smsType === "视频短信";

    // 确定起呼方式
    let calloutMode = taskData.calloutMode || 1; // 默认双向视频起呼

    // 如果没有指定起呼方式，根据外呼类型确定
    if (!taskData.calloutMode && (taskData.callType === "5G语音互动" || taskData.callType === "5G语音通话")) {
      calloutMode = 2; // 语音起呼
    }

    // 构建请求数据
    const requestData = {
      orgCode: ORG_CODE,
      sign: AesUtils.generateSignature(ORG_CODE),
      loginName: LOGIN_NAME,
      name: taskData.name,
      mediaType: "videoBot", // 固定为 videoBot
      videoBotId: taskData.resource,
      deliverList,
      // 短信相关参数
      isRelateSendMessage,
      isRelateVideoMessage,
      msgTemplateId: isRelateSendMessage ? taskData.smsTemplate || "" : "",
      videoMessageTemplateId: isRelateVideoMessage ? taskData.smsTemplate || "" : "",
      msgTitle: "系统通知",
      // 短信发送时机：1-拨打电话时, 2-电话接通时, 3-通话结束后
      sendMessagePhase: taskData.smsPhase || 1,
      sendVideoMessagePhase: taskData.smsPhase || 1,
      // 起呼方式：1：双向视频起呼 2：语音起呼
      calloutMode,
      // 苹果手机分流相关参数
      isVoiceDivide: taskData.isVoiceDivide || false,
      audioBotId: taskData.audioBotId || "",
      shuntAudioBotId: taskData.shuntAudioBotId || "",
      // 闪信相关参数
      sendFlashMessage: taskData.sendFlashMessage || false,
      flashMessageTemplateId: taskData.sendFlashMessage ? (taskData.flashMessageTemplateId || "") : "",
      // 用户ID，用于权限控制
      userId: taskData.userId || user.id,
      // 回调地址配置
      callbackUrl: CallbackConfig.callResultCallback.url,
      smsCallbackUrl: CallbackConfig.smsStatusCallback.url,
    };

    // 服务器端发起请求，避免CORS问题
    const apiResponse = await fetch(`${API_BASE_URL}/api/mediaDeliverPlatform/external/create`, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify(requestData),
    });

    const data = await apiResponse.json();

    if (data.errCode === 0) {
      // 任务导入成功，将任务保存到本地数据库
      try {
        const { db } = await import("@/lib/db");

        // 创建新任务记录
        const newTask = await db.task.create({
          data: {
            name: taskData.name,
            type: taskData.callType,
            content: taskData.content,
            startTime: new Date(taskData.startTime),
            progress: 0,
            status: "待启动",
            creator: user.username || "系统用户",
            userId: user.id,
            externalId: data.result?.taskId || `task-${Date.now()}`,
            importTime: new Date(),
          }
        });



        return NextResponse.json({
          success: true,
          message: "任务导入成功",
          data: {
            ...data.result,
            id: newTask.id, // 返回本地数据库ID
            externalId: data.result?.taskId
          },
        });
      } catch (dbError) {

        // 即使保存到数据库失败，仍然返回成功，但记录错误
        return NextResponse.json({
          success: true,
          message: "任务导入成功，但本地保存失败",
          data: data.result,
        });
      }
    } else {
      return NextResponse.json({
        success: false,
        message: `任务导入失败: ${data.errInfo || "未知错误"}`,
        data: null,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "任务导入失败，请稍后重试",
      data: null,
    }, { status: 500 });
  }
}
