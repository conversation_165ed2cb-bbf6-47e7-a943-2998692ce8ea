import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import * as jose from 'jose';
import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
);

/**
 * 获取单个通知类型
 *
 * @route GET /api/notifications/types/[id]
 * @access 需要 notifications:view 权限
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取cookie中的token
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    // 验证token
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    const userId = payload.sub;
    // 简化处理，默认为普通用户
    const roleCode = 'user';

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的用户ID',
      }, { status: 401 });
    }

    // 检查用户是否有权限查看通知类型
    const hasViewPermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:view"
    );

    if (!hasViewPermission) {
      return NextResponse.json({
        success: false,
        message: '无权查看通知类型',
      }, { status: 403 });
    }

    // 获取通知类型
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的通知类型ID',
      }, { status: 400 });
    }

    const notificationType = await prisma.notificationType.findUnique({
      where: { id },
      include: {
        children: true,
        parent: true
      }
    });

    if (!notificationType) {
      return NextResponse.json({
        success: false,
        message: '通知类型不存在',
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: notificationType,
    });
  } catch (error) {
    console.error('获取通知类型失败:', error);
    return NextResponse.json({
      success: false,
      message: '获取通知类型失败',
    }, { status: 500 });
  }
}

/**
 * 更新通知类型
 *
 * @route PUT /api/notifications/types/[id]
 * @access 需要 notifications:edit 权限
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取cookie中的token
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    // 验证token
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    const userId = payload.sub;
    // 简化处理，默认为普通用户
    const roleCode = 'user';

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的用户ID',
      }, { status: 401 });
    }

    // 检查用户是否有权限编辑通知类型
    const hasEditPermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:edit"
    );

    if (!hasEditPermission) {
      return NextResponse.json({
        success: false,
        message: '无权编辑通知类型',
      }, { status: 403 });
    }

    // 获取请求体
    const body = await req.json();
    const { name, code, description, parentId } = body;

    // 验证必填字段
    if (!name || !code) {
      return NextResponse.json({
        success: false,
        message: '名称和代码不能为空',
      }, { status: 400 });
    }

    // 获取通知类型ID
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的通知类型ID',
      }, { status: 400 });
    }

    // 检查通知类型是否存在
    const existingType = await prisma.notificationType.findUnique({
      where: { id }
    });

    if (!existingType) {
      return NextResponse.json({
        success: false,
        message: '通知类型不存在',
      }, { status: 404 });
    }

    // 更新通知类型
    const updatedType = await prisma.notificationType.update({
      where: { id },
      data: {
        name,
        code,
        description,
        parentId: parentId ? parseInt(parentId) : null,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      message: '更新通知类型成功',
      data: updatedType
    });
  } catch (error) {
    console.error('更新通知类型失败:', error);
    return NextResponse.json({
      success: false,
      message: '更新通知类型失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}

/**
 * 删除通知类型
 *
 * @route DELETE /api/notifications/types/[id]
 * @access 需要 notifications:delete 权限
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取cookie中的token
    const cookieStore = cookies();
    const token = cookieStore.get('token')?.value;

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 });
    }

    // 验证token
    const { payload } = await jose.jwtVerify(token, JWT_SECRET);
    const userId = payload.sub;
    // 简化处理，默认为普通用户
    const roleCode = 'user';

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的用户ID',
      }, { status: 401 });
    }

    // 检查用户是否有权限删除通知类型
    const hasDeletePermission = await hasResourcePermission(
      { sub: userId, role: { code: roleCode } },
      "notifications",
      "notifications:delete"
    );

    if (!hasDeletePermission) {
      return NextResponse.json({
        success: false,
        message: '无权删除通知类型',
      }, { status: 403 });
    }

    // 获取通知类型ID
    const id = parseInt(params.id);
    if (isNaN(id)) {
      return NextResponse.json({
        success: false,
        message: '无效的通知类型ID',
      }, { status: 400 });
    }

    // 检查通知类型是否存在
    const existingType = await prisma.notificationType.findUnique({
      where: { id },
      include: {
        children: true,
        notifications: {
          take: 1
        }
      }
    });

    if (!existingType) {
      return NextResponse.json({
        success: false,
        message: '通知类型不存在',
      }, { status: 404 });
    }

    // 检查是否有子类型
    if (existingType.children && existingType.children.length > 0) {
      return NextResponse.json({
        success: false,
        message: '无法删除含有子类型的通知类型，请先删除所有子类型',
      }, { status: 400 });
    }

    // 检查是否有关联的通知
    if (existingType.notifications && existingType.notifications.length > 0) {
      return NextResponse.json({
        success: false,
        message: '无法删除已被使用的通知类型，请先删除关联的通知',
      }, { status: 400 });
    }

    // 删除通知类型
    await prisma.notificationType.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: '删除通知类型成功'
    });
  } catch (error) {
    console.error('删除通知类型失败:', error);
    return NextResponse.json({
      success: false,
      message: '删除通知类型失败: ' + (error instanceof Error ? error.message : '未知错误'),
    }, { status: 500 });
  }
}
