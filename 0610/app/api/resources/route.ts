import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { ResourceFormData } from "@/lib/abac/types";
import { AuthMiddleware } from "@/lib/middleware/auth-middleware";
import logger from '@/lib/utils/logger';

/**
 * 获取资源列表
 */
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "获取资源列表API");
    if (response) {
      return response;
    }

    const resources = await prisma.resource.findMany({
      include: {
        operations: true,
      },
    });

    return NextResponse.json(resources);
  } catch (error) {
    logger.error("获取资源列表错误:", error);
    return NextResponse.json(
      { error: "获取资源列表失败" },
      { status: 500 }
    );
  }
}

/**
 * 创建资源
 */
export async function POST(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "创建资源API");
    if (response) {
      return response;
    }

    const data: ResourceFormData = await request.json();
    const { code, name, type, description, operationIds } = data;

    // 验证资源代码是否已存在
    const existingResource = await prisma.resource.findUnique({
      where: { code },
    });

    if (existingResource) {
      return NextResponse.json(
        { error: "资源代码已存在" },
        { status: 400 }
      );
    }

    // 创建资源
    const resource = await prisma.resource.create({
      data: {
        code,
        name,
        type,
        description,
        operations: {
          connect: operationIds?.map(id => ({ id })) || [],
        },
      },
      include: {
        operations: true,
      },
    });

    return NextResponse.json(resource);
  } catch (error) {
    logger.error("创建资源错误:", error);
    return NextResponse.json(
      { error: "创建资源失败" },
      { status: 500 }
    );
  }
}

/**
 * 更新资源
 */
export async function PUT(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "更新资源API");
    if (response) {
      return response;
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "资源ID不能为空" },
        { status: 400 }
      );
    }

    const data: ResourceFormData = await request.json();
    const { code, name, type, description, operationIds } = data;

    // 验证资源是否存在
    const existingResource = await prisma.resource.findUnique({
      where: { id },
    });

    if (!existingResource) {
      return NextResponse.json(
        { error: "资源不存在" },
        { status: 404 }
      );
    }

    // 验证资源代码是否已存在
    if (code !== existingResource.code) {
      const codeExists = await prisma.resource.findUnique({
        where: { code },
      });

      if (codeExists) {
        return NextResponse.json(
          { error: "资源代码已存在" },
          { status: 400 }
        );
      }
    }

    // 更新资源
    const resource = await prisma.resource.update({
      where: { id },
      data: {
        code,
        name,
        type,
        description,
        operations: {
          set: operationIds?.map(id => ({ id })) || [],
        },
      },
      include: {
        operations: true,
      },
    });

    return NextResponse.json(resource);
  } catch (error) {
    logger.error("更新资源错误:", error);
    return NextResponse.json(
      { error: "更新资源失败" },
      { status: 500 }
    );
  }
}

/**
 * 删除资源
 */
export async function DELETE(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response } = await AuthMiddleware.requireAdmin(request, "删除资源API");
    if (response) {
      return response;
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "资源ID不能为空" },
        { status: 400 }
      );
    }

    // 验证资源是否存在
    const existingResource = await prisma.resource.findUnique({
      where: { id },
    });

    if (!existingResource) {
      return NextResponse.json(
        { error: "资源不存在" },
        { status: 404 }
      );
    }

    // 删除资源
    await prisma.resource.delete({
      where: { id },
    });

    return NextResponse.json({ message: "删除成功" });
  } catch (error) {
    logger.error("删除资源错误:", error);
    return NextResponse.json(
      { error: "删除资源失败" },
      { status: 500 }
    );
  }
}