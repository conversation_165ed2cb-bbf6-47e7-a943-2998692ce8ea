import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth/next"
import { options } from "../../../auth/[...nextauth]/options"
import { CasbinService } from "@/lib/services/casbin-service"

/**
 * 获取角色的菜单权限
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色菜单API:${requestId}] 获取角色菜单权限: ${params.id}`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话，返回401
    if (!session?.user) {
      console.log(`[角色菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    // 获取角色
    const role = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true
      }
    })

    if (!role) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 获取所有菜单
    const allMenus = await prisma.menu.findMany({
      orderBy: {
        order: 'asc'
      }
    })

    // 构建菜单树
    const buildMenuTree = (items: any[], parentId: string | null = null) => {
      return items
        .filter(item => item.parentId === parentId)
        .map(item => ({
          ...item,
          children: buildMenuTree(items, item.id),
          checked: role.menus.some(m => m.id === item.id)
        }))
    }

    // 构建树形结构
    const menuTree = buildMenuTree(allMenus)

    return NextResponse.json({
      success: true,
      data: {
        role,
        menuTree
      },
      requestId
    })
  } catch (error) {
    console.error(`[角色菜单API:${requestId}] 获取角色菜单权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取角色菜单权限失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 更新角色的菜单权限
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色菜单API:${requestId}] 更新角色菜单权限: ${params.id}`)

  try {
    // 获取会话信息
    const session = await getServerSession(options)
    
    // 如果没有会话或不是管理员，返回403
    if (!session?.user || session.user.roleCode !== 'ADMIN') {
      console.log(`[角色菜单API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以更新角色菜单权限",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    const menuIds = body.menuIds

    if (!Array.isArray(menuIds)) {
      return NextResponse.json({
        success: false,
        message: "menuIds必须是数组",
        requestId
      }, { status: 400 })
    }

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true
      }
    })

    if (!existingRole) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 获取当前关联的菜单
    const currentMenuIds = existingRole.menus.map(menu => menu.id)
    
    // 获取要添加和删除的菜单
    const menusToAdd = menuIds.filter(id => !currentMenuIds.includes(id))
    const menusToRemove = currentMenuIds.filter(id => !menuIds.includes(id))

    // 获取菜单代码映射
    const menuCodeMap = new Map()
    if (menusToAdd.length > 0 || menusToRemove.length > 0) {
      const allMenus = await prisma.menu.findMany({
        where: {
          id: {
            in: [...menusToAdd, ...menusToRemove]
          }
        },
        select: {
          id: true,
          code: true
        }
      })
      
      allMenus.forEach(menu => {
        menuCodeMap.set(menu.id, menu.code)
      })
    }

    // 添加新菜单
    if (menusToAdd.length > 0) {
      await prisma.role.update({
        where: { id: params.id },
        data: {
          menus: {
            connect: menusToAdd.map(id => ({ id }))
          }
        }
      })

      // 为每个新菜单添加jCasbin权限
      for (const menuId of menusToAdd) {
        const menuCode = menuCodeMap.get(menuId)
        if (menuCode) {
          await CasbinService.addPermissionForRole(existingRole.code, `menu:${menuCode}`, 'view')
        }
      }
    }

    // 移除旧菜单
    if (menusToRemove.length > 0) {
      await prisma.role.update({
        where: { id: params.id },
        data: {
          menus: {
            disconnect: menusToRemove.map(id => ({ id }))
          }
        }
      })

      // 为每个旧菜单移除jCasbin权限
      for (const menuId of menusToRemove) {
        const menuCode = menuCodeMap.get(menuId)
        if (menuCode) {
          await CasbinService.removePermissionForRole(existingRole.code, `menu:${menuCode}`, 'view')
        }
      }
    }

    // 获取更新后的角色
    const updatedRole = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedRole,
      message: '角色菜单权限更新成功',
      requestId
    })
  } catch (error) {
    console.error(`[角色菜单API:${requestId}] 更新角色菜单权限失败:`, error)
    return NextResponse.json({
      success: false,
      message: "更新角色菜单权限失败",
      requestId
    }, { status: 500 })
  }
}
