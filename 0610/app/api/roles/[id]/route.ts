import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { CasbinService } from "@/lib/services/casbin-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取单个角色
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[角色API:${requestId}] 获取角色详情: ${params.id}`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取角色详情API");
    if (response) {
      logger.error(`[角色API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    // 获取角色
    const role = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true,
        operations: true
      }
    })

    if (!role) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 转换为前端需要的格式
    const formattedRole = {
      id: role.id,
      name: role.name,
      code: role.code,
      type: role.type,
      description: role.description,
      permissions: role.permissions,
      menuItems: role.menus.map(menu => menu.id),
      operationIds: role.operations.map(op => op.id),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }

    return NextResponse.json({
      success: true,
      data: formattedRole,
      requestId
    })
  } catch (error) {
    console.error(`[角色API:${requestId}] 获取角色详情失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取角色详情失败",
      requestId
    }, { status: 500 })
  }
}

/**
 * 更新角色
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色API:${requestId}] 更新角色: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "更新角色API");    if (response) {
      logger.error(`[角色API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以更新角色",
        requestId
      }, { status: 403 })
    }

    // 获取请求体
    const body = await request.json()
    console.log(`[角色API:${requestId}] 请求体:`, body)

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true,
        operations: true
      }
    })

    if (!existingRole) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 如果是系统角色，不允许修改代码
    if (existingRole.type === 'system' && body.code && body.code !== existingRole.code) {
      return NextResponse.json({
        success: false,
        message: "不允许修改系统角色的代码",
        requestId
      }, { status: 400 })
    }

    // 如果修改了代码，检查新代码是否已存在
    if (body.code && body.code !== existingRole.code) {
      const codeExists = await prisma.role.findUnique({
        where: { code: body.code }
      })

      if (codeExists) {
        return NextResponse.json({
          success: false,
          message: `角色代码 '${body.code}' 已存在`,
          requestId
        }, { status: 400 })
      }
    }

    // 更新角色基本信息
    const updateData: any = {}
    if (body.name) updateData.name = body.name
    if (body.code) updateData.code = body.code
    if (body.type) updateData.type = body.type
    if (body.description !== undefined) updateData.description = body.description
    if (body.permissions) updateData.permissions = body.permissions

    // 更新角色
    const role = await prisma.role.update({
      where: { id: params.id },
      data: updateData
    })

    // 处理菜单关联
    if (body.menuIds && Array.isArray(body.menuIds)) {
      // 获取当前关联的菜单
      const currentMenus = existingRole.menus.map(menu => menu.id)

      // 获取要添加和删除的菜单
      const menusToAdd = body.menuIds.filter(id => !currentMenus.includes(id))
      const menusToRemove = currentMenus.filter(id => !body.menuIds.includes(id))

      // 获取菜单代码映射
      const menuCodeMap = new Map()
      if (menusToAdd.length > 0 || menusToRemove.length > 0) {
        const allMenus = await prisma.menu.findMany({
          where: {
            id: {
              in: [...menusToAdd, ...menusToRemove]
            }
          },
          select: {
            id: true,
            code: true
          }
        })

        allMenus.forEach(menu => {
          menuCodeMap.set(menu.id, menu.code)
        })
      }

      // 添加新菜单
      if (menusToAdd.length > 0) {
        await prisma.role.update({
          where: { id: params.id },
          data: {
            menus: {
              connect: menusToAdd.map(id => ({ id }))
            }
          }
        })

        // 为每个新菜单添加jCasbin权限
        for (const menuId of menusToAdd) {
          const menuCode = menuCodeMap.get(menuId)
          if (menuCode) {
            await CasbinService.addPermissionForRole(role.code, `menu:${menuCode}`, 'view')
          }
        }
      }

      // 移除旧菜单
      if (menusToRemove.length > 0) {
        await prisma.role.update({
          where: { id: params.id },
          data: {
            menus: {
              disconnect: menusToRemove.map(id => ({ id }))
            }
          }
        })

        // 为每个旧菜单移除jCasbin权限
        for (const menuId of menusToRemove) {
          const menuCode = menuCodeMap.get(menuId)
          if (menuCode) {
            await CasbinService.removePermissionForRole(role.code, `menu:${menuCode}`, 'view')
          }
        }
      }
    }

    // 处理操作关联
    if (body.operationIds && Array.isArray(body.operationIds)) {
      // 获取当前关联的操作
      const currentOperations = existingRole.operations.map(op => op.id)

      // 获取要添加和删除的操作
      const operationsToAdd = body.operationIds.filter(id => !currentOperations.includes(id))
      const operationsToRemove = currentOperations.filter(id => !body.operationIds.includes(id))

      // 获取操作代码映射
      const operationCodeMap = new Map()
      if (operationsToAdd.length > 0 || operationsToRemove.length > 0) {
        const allOperations = await prisma.operation.findMany({
          where: {
            id: {
              in: [...operationsToAdd, ...operationsToRemove]
            }
          },
          select: {
            id: true,
            code: true
          }
        })

        allOperations.forEach(op => {
          operationCodeMap.set(op.id, op.code)
        })
      }

      // 添加新操作
      if (operationsToAdd.length > 0) {
        await prisma.role.update({
          where: { id: params.id },
          data: {
            operations: {
              connect: operationsToAdd.map(id => ({ id }))
            }
          }
        })

        // 为每个新操作添加jCasbin权限
        for (const opId of operationsToAdd) {
          const opCode = operationCodeMap.get(opId)
          if (opCode) {
            await CasbinService.addPermissionForRole(role.code, `operation:${opCode}`, 'execute')
          }
        }
      }

      // 移除旧操作
      if (operationsToRemove.length > 0) {
        await prisma.role.update({
          where: { id: params.id },
          data: {
            operations: {
              disconnect: operationsToRemove.map(id => ({ id }))
            }
          }
        })

        // 为每个旧操作移除jCasbin权限
        for (const opId of operationsToRemove) {
          const opCode = operationCodeMap.get(opId)
          if (opCode) {
            await CasbinService.removePermissionForRole(role.code, `operation:${opCode}`, 'execute')
          }
        }
      }
    }

    // 获取更新后的角色
    const updatedRole = await prisma.role.findUnique({
      where: { id: params.id },
      include: {
        menus: true,
        operations: true
      }
    })

    // 转换为前端需要的格式
    const formattedRole = {
      id: updatedRole.id,
      name: updatedRole.name,
      code: updatedRole.code,
      type: updatedRole.type,
      description: updatedRole.description,
      permissions: updatedRole.permissions,
      menuItems: updatedRole.menus.map(menu => menu.id),
      operationIds: updatedRole.operations.map(op => op.id),
      createdAt: updatedRole.createdAt,
      updatedAt: updatedRole.updatedAt
    }

    return NextResponse.json({
      success: true,
      data: formattedRole,
      message: '角色更新成功',
      requestId
    })
  } catch (error) {
    console.error(`[角色API:${requestId}] 更新角色失败:`, error)
    return NextResponse.json({
      success: false,
      message: "更新角色失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 删除角色
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[角色API:${requestId}] 删除角色: ${params.id}`)

  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "删除角色API");    if (response) {
      console.log(`[角色API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "只有管理员可以删除角色",
        requestId
      }, { status: 403 })
    }

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id: params.id }
    })

    if (!existingRole) {
      return NextResponse.json({
        success: false,
        message: "角色不存在",
        requestId
      }, { status: 404 })
    }

    // 不允许删除系统角色
    if (existingRole.type === 'system') {
      return NextResponse.json({
        success: false,
        message: "不允许删除系统角色",
        requestId
      }, { status: 400 })
    }

    // 检查是否有用户使用该角色
    const usersWithRole = await prisma.user.count({
      where: { roleCode: existingRole.code }
    })

    if (usersWithRole > 0) {
      return NextResponse.json({
        success: false,
        message: `该角色正在被 ${usersWithRole} 个用户使用，无法删除`,
        requestId
      }, { status: 400 })
    }

    // 删除角色
    await prisma.role.delete({
      where: { id: params.id }
    })

    // 删除jCasbin中的角色权限
    const permissions = await CasbinService.getRolePermissions(existingRole.code)
    for (const permission of permissions) {
      await CasbinService.removePermissionForRole(existingRole.code, permission[1], permission[2])
    }

    return NextResponse.json({
      success: true,
      message: '角色删除成功',
      requestId
    })
  } catch (error) {
    console.error(`[角色API:${requestId}] 删除角色失败:`, error)
    return NextResponse.json({
      success: false,
      message: "删除角色失败",
      requestId
    }, { status: 500 })
  }
}
