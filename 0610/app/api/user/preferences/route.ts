import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取用户偏好设置
 */
export async function GET(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  logger.debug(`[用户偏好API:${requestId}] 获取用户偏好设置`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "获取用户偏好API");
    if (response) {
      logger.error(`[用户偏好API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    const userId = user.id

    // 获取用户偏好设置
    const userPreferences = await prisma.userPreference.findUnique({
      where: { userId }
    })

    return NextResponse.json({
      success: true,
      data: userPreferences || { userId, preferences: {} },
      requestId
    })
  } catch (error) {
    console.error(`[用户偏好API:${requestId}] 获取用户偏好设置失败:`, error)
    return NextResponse.json({
      success: false,
      message: "获取用户偏好设置失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}

/**
 * 保存用户偏好设置
 */
export async function POST(request: NextRequest) {
  const requestId = Math.random().toString(36).substring(2, 10)
  console.log(`[用户偏好API:${requestId}] 保存用户偏好设置`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, "保存用户偏好API");    if (response) {
      console.log(`[用户偏好API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    const userId = user.id

    // 获取请求体
    const body = await request.json()
    const { key, value } = body

    if (!key) {
      return NextResponse.json({
        success: false,
        message: "缺少偏好设置键",
        requestId
      }, { status: 400 })
    }

    // 获取现有偏好设置
    let userPreference = await prisma.userPreference.findUnique({
      where: { userId }
    })

    // 如果不存在，则创建新的偏好设置
    if (!userPreference) {
      userPreference = await prisma.userPreference.create({
        data: {
          userId,
          preferences: {}
        }
      })
    }

    // 更新偏好设置
    const updatedPreferences = {
      ...userPreference.preferences,
      [key]: value
    }

    // 保存更新后的偏好设置
    const updatedUserPreference = await prisma.userPreference.update({
      where: { userId },
      data: {
        preferences: updatedPreferences
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedUserPreference,
      message: "用户偏好设置已保存",
      requestId
    })
  } catch (error) {
    console.error(`[用户偏好API:${requestId}] 保存用户偏好设置失败:`, error)
    return NextResponse.json({
      success: false,
      message: "保存用户偏好设置失败",
      error: error instanceof Error ? error.message : String(error),
      requestId
    }, { status: 500 })
  }
}
