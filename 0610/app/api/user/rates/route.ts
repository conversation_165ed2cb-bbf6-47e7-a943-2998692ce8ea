/**
 * 用户费率查询API
 * 允许用户查询自己的费率信息
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"
import { BillingService } from "@/lib/services/billing-service"

/**
 * 获取当前用户的费率信息
 *
 * @route GET /api/user/rates
 * @access 需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 获取当前用户
    const user = await UnifiedAuthService.getCurrentUser(request, "费率查询API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 查询用户的费率
    const rates = await prisma.rate.findMany({
      where: {
        customerId: user.id
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // 处理费率信息，添加显示名称
    const processedRates = rates.map(rate => ({
      ...rate,
      billingIncrementName: BillingService.getBillingIncrementName(rate.billingIncrement || 'PER_MINUTE'),
      billingUnitName: BillingService.getBillingUnitName(rate.billingIncrement || 'PER_MINUTE'),
      billingUnitSeconds: BillingService.getBillingUnitSeconds(rate.billingIncrement || 'PER_MINUTE'),
    }))

    return NextResponse.json(
      {
        success: true,
        message: '获取费率成功',
        data: processedRates
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("获取用户费率错误:", error)
    return NextResponse.json(
      {
        success: false,
        message: '获取费率失败: ' + (error instanceof Error ? error.message : '未知错误')
      },
      { status: 500 }
    )
  }
}
