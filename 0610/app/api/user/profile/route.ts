import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function GET(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "用户资料API");
    if (response) {
      return response;
    }

    // 获取用户ID
    const userId = user.id;

    // 获取用户信息
    const userProfile = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!userProfile) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 清除敏感信息
    const { password, ...safeUser } = userProfile

    return NextResponse.json({
      success: true,
      data: safeUser
    })
  } catch (error) {
    logger.error("获取用户资料失败:", error)
    return NextResponse.json(
      { success: false, message: "获取用户资料失败" },
      { status: 500 }
    )
  }
}

export async function PUT(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "用户资料API");
    if (response) {
      return response;
    }

    // 获取用户ID
    const userId = user.id;

    // 2. 获取请求数据
    const data = await req.json()

    // 3. 验证用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 4. 更新用户信息
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name: data.name,
        image: data.image,
        phone: data.phone,
        wechat: data.wechat,
        province: data.province,
        city: data.city,
        district: data.district,
        address: data.address,
        // 仅更新允许的字段，email 需要通过专门的流程更新
      },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    // 5. 清除敏感信息并返回
    const { password, ...safeUser } = updatedUser

    // 6. 如果头像已更新，调用会话更新和刷新 API
    if (data.image && data.image !== existingUser.image) {
      try {
        // 调用会话更新 API
        fetch(`${req.nextUrl.origin}/api/auth/session/update`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': req.headers.get('cookie') || ''
          },
          body: JSON.stringify({
            field: 'image',
            value: data.image
          })
        }).catch(err => {
          logger.error('调用会话更新 API 失败:', err);
        });

        // 调用会话强制刷新 API
        fetch(`${req.nextUrl.origin}/api/auth/session/force-refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': req.headers.get('cookie') || ''
          }
        }).catch(err => {
          logger.error('调用会话强制刷新 API 失败:', err);
        });
      } catch (error) {
        logger.error('准备调用会话 API 时出错:', error);
        // 不影响主流程，继续返回
      }
    }

    return NextResponse.json({
      success: true,
      data: safeUser
    })
  } catch (error) {
    console.error("更新用户资料失败:", error)
    return NextResponse.json(
      { success: false, message: "更新用户资料失败" },
      { status: 500 }
    )
  }
}