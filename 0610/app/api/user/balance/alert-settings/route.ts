/**
 * 余额预警设置API
 * 允许用户设置余额预警阈值和通知方式
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { UnifiedAuthService } from "@/lib/unified-auth-service"

/**
 * 获取当前用户的余额预警设置
 *
 * @route GET /api/user/balance/alert-settings
 * @access 需要用户登录
 */
export async function GET(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 获取当前用户
    const user = await UnifiedAuthService.getCurrentUser(request, "余额预警设置API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 查询用户的余额预警设置
    const settings = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        balanceAlertThreshold: true,
        balanceAlertEmail: true,
        balanceAlertSms: true,
      }
    })

    // 查询用户的通知设置，用于获取站内通知状态
    const notificationSettings = await prisma.userNotificationSettings.findUnique({
      where: { userId: user.id },
      select: {
        appEnabled: true
      }
    })

    return NextResponse.json(
      {
        success: true,
        message: '获取余额预警设置成功',
        data: {
          threshold: settings?.balanceAlertThreshold || 0,
          enableEmail: settings?.balanceAlertEmail || false,
          enableSms: settings?.balanceAlertSms || false,
          enableApp: notificationSettings?.appEnabled || false,
        }
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("获取余额预警设置错误:", error)
    return NextResponse.json(
      {
        success: false,
        message: '获取余额预警设置失败: ' + (error instanceof Error ? error.message : '未知错误')
      },
      { status: 500 }
    )
  }
}

/**
 * 更新当前用户的余额预警设置
 *
 * @route PUT /api/user/balance/alert-settings
 * @access 需要用户登录
 */
export async function PUT(request: NextRequest) {
  try {
    // 设置响应头
    const headers = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-store',
    }

    // 获取当前用户
    const user = await UnifiedAuthService.getCurrentUser(request, "余额预警设置API")
    if (!user) {
      return NextResponse.json(
        { success: false, message: '未授权，请登录后再试', error: 'Unauthorized' },
        { status: 401, headers }
      )
    }

    // 解析请求体
    const body = await request.json()
    const { threshold, enableEmail, enableSms, enableApp } = body

    // 验证阈值
    if (threshold === undefined || threshold < 0) {
      return NextResponse.json(
        { success: false, message: '预警阈值必须是非负数' },
        { status: 400, headers }
      )
    }

    // 更新用户的预警设置
    await prisma.user.update({
      where: { id: user.id },
      data: {
        balanceAlertThreshold: threshold,
        balanceAlertEmail: enableEmail === true,
        balanceAlertSms: enableSms === true,
      }
    })

    // 更新用户的通知设置
    await prisma.userNotificationSettings.upsert({
      where: { userId: user.id },
      update: {
        appEnabled: enableApp === true
      },
      create: {
        userId: user.id,
        emailEnabled: true,
        smsEnabled: false,
        appEnabled: enableApp === true,
        types: ["SYSTEM", "SECURITY", "TASK", "BALANCE"]
      }
    })

    return NextResponse.json(
      {
        success: true,
        message: '余额预警设置更新成功'
      },
      { status: 200, headers }
    )
  } catch (error) {
    console.error("更新余额预警设置错误:", error)
    return NextResponse.json(
      {
        success: false,
        message: '更新余额预警设置失败: ' + (error instanceof Error ? error.message : '未知错误')
      },
      { status: 500 }
    )
  }
}
