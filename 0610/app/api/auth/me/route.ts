import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { v4 as uuidv4 } from 'uuid'
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

/**
 * 获取当前用户信息
 *
 * @route GET /api/auth/me
 * @access 所有已登录用户可访问
 */
export async function GET(req: NextRequest) {
  const requestId = uuidv4().substring(0, 8)
  logger.debug(`[获取用户API:${requestId}] 获取当前用户信息API被调用`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, `获取用户API:${requestId}`);
    if (response) {
      console.log(`[获取用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: '未授权，请先登录',
      }, { status: 401 })
    }

    const finalUserId = user.id;
    console.log(`[获取用户API:${requestId}] 用户ID:`, finalUserId)

    // 从数据库查询用户信息
    const userDetails = await prisma.user.findUnique({
      where: { id: finalUserId },
      include: {
        role: true
      }
    })

    if (!userDetails) {
      console.log(`[获取用户API:${requestId}] 用户不存在: ${finalUserId}`)
      return NextResponse.json({
        success: false,
        message: '用户不存在，请重新登录',
      }, { status: 404 })
    }

    console.log(`[获取用户API:${requestId}] 找到用户: ID=${userDetails.id}, 用户名=${userDetails.username}, 角色=${userDetails.role?.type || 'user'}`)

    // 确保角色信息存在
    if (!userDetails.role) {
      console.log(`[获取用户API:${requestId}] 警告: 用户角色信息不存在，尝试修复`)
      try {
        const role = await prisma.role.findUnique({
          where: { code: userDetails.roleCode },
        })
        if (role) {
          userDetails.role = role
          console.log(`[获取用户API:${requestId}] 成功修复用户角色信息`)
        }
      } catch (roleError) {
        console.error(`[获取用户API:${requestId}] 获取角色信息失败:`, roleError)
      }
    }

    // 返回用户信息，确保使用数据库中的角色信息
    const userData = {
      id: userDetails.id,
      username: userDetails.username,
      role: userDetails.role?.type || 'user',
      roleCode: userDetails.roleCode,
      email: userDetails.email,
      name: userDetails.name
    }

    return NextResponse.json({
      success: true,
      message: '获取用户信息成功',
      data: userData
    })

  } catch (error) {
    console.error(`[获取用户API:${requestId}] 获取用户信息错误:`, error)
    return NextResponse.json({
      success: false,
      message: '获取用户信息失败: ' + (error instanceof Error ? error.message : '未知错误'),
      requestId
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
  }
}