import logger from '@/lib/utils/logger';

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { v4 as uuidv4 } from 'uuid'
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"

export async function GET(request: NextRequest) {
  const requestId = uuidv4().substring(0, 8)
  logger.debug(`[当前用户API:${requestId}] 获取当前用户信息`)

  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(request, `当前用户API:${requestId}`);
    if (response) {
      logger.debug(`[当前用户API:${requestId}] 未授权访问`)
      return NextResponse.json({
        success: false,
        message: "未授权访问",
        requestId
      }, { status: 401 })
    }

    const userId = user.id;
    console.log(`[当前用户API:${requestId}] 获取到用户ID: ${userId}`)

    // 从数据库获取最新的用户信息
    const userWithDetails = await prisma.user.findUnique({
      where: { id: userId as string },
      include: {
        role: {
          include: {
            menus: true,
            permissions: true
          }
        }
      }
    })

    // 如果是管理员用户，获取所有菜单
    let allMenus = [];
    if (userWithDetails && (userWithDetails.roleCode === 'ADMIN' || userWithDetails.roleCode === 'admin')) {
      logger.log(`[当前用户API:${requestId}] 用户是管理员，获取所有菜单`);
      allMenus = await prisma.menu.findMany({
        where: { visible: true },
        orderBy: { order: 'asc' }
      });
    }

    if (!userWithDetails) {
      logger.debug(`[当前用户API:${requestId}] 数据库中未找到用户`)
      return NextResponse.json({
        success: false,
        message: "用户不存在",
        requestId
      }, { status: 404 })
    }

    // 确保返回必要的用户信息
    const { password: _, ...userWithoutPassword } = userWithDetails

    // 如果是管理员并且有菜单数据，将所有菜单添加到用户角色中
    let roleData = userWithDetails.role;
    if (userWithDetails.roleCode === 'ADMIN' || userWithDetails.roleCode === 'admin') {
      if (allMenus && allMenus.length > 0) {
        logger.log(`[当前用户API:${requestId}] 为管理员设置所有菜单，菜单数量: ${allMenus.length}`);
        roleData = {
          ...userWithDetails.role,
          menus: allMenus
        };
      }

      // 确保管理员有所有权限
      if (!roleData.permissions || !roleData.permissions.includes('*')) {
        roleData.permissions = ['*', ...roleData.permissions || []];
      }
    }

    const responseData = {
      ...userWithoutPassword,
      role: roleData
    }

    // 更新用户最后活动时间
    await prisma.user.update({
      where: { id: userWithDetails.id },
      data: { lastActiveAt: new Date() }
    })

    console.log(`[当前用户API:${requestId}] 成功获取用户信息:`, {
      id: userWithDetails.id,
      username: userWithDetails.username,
      roleCode: userWithDetails.roleCode
    })

    return NextResponse.json({
      success: true,
      data: responseData,
      requestId
    }, {
      status: 200,
      headers: {
        "Cache-Control": "no-store, no-cache, must-revalidate"
      }
    })

  } catch (error) {
    console.error(`[当前用户API:${requestId}] 服务器错误:`, error)
    return NextResponse.json({
      success: false,
      message: "服务器内部错误",
      requestId
    }, { status: 500 })
  }
}