import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { options } from "../../[...nextauth]/options"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { cookies } from "next/headers"
import { sign } from "jsonwebtoken"

// 获取 JWT 密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET || "your-secret-key"
)

/**
 * 刷新用户会话
 * 此API用于在用户信息变更后刷新NextAuth会话
 */
export async function POST(req: NextRequest) {
  try {
    // 使用权限检查中间件检查用户是否已登录
    const { response, user } = await AuthMiddleware.requireAuth(req, "会话刷新API");
    if (response) {
      return response;
    }

    // 获取当前会话
    const session = await getServerSession(options)
    if (!session) {
      return NextResponse.json(
        { success: false, message: "未找到有效会话" },
        { status: 401 }
      )
    }

    // 从数据库获取最新的用户信息
    const updatedUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, message: "用户不存在" },
        { status: 404 }
      )
    }

    // 添加时间戳到图片URL，避免缓存问题
    if (updatedUser.image) {
      updatedUser.image = updatedUser.image.includes('?t=') 
        ? updatedUser.image 
        : `${updatedUser.image}?t=${Date.now()}`;
    }

    // 创建新的会话令牌
    const { password, ...userWithoutPassword } = updatedUser;
    
    // 创建新的会话对象
    const newSession = {
      ...session,
      user: {
        ...session.user,
        id: updatedUser.id,
        name: updatedUser.name,
        email: updatedUser.email,
        image: updatedUser.image,
        username: updatedUser.username,
        roleCode: updatedUser.roleCode,
        role: updatedUser.role
      }
    };

    // 返回更新后的会话信息
    return NextResponse.json({
      success: true,
      data: {
        session: newSession,
        user: userWithoutPassword
      },
      message: "会话已刷新"
    })
  } catch (error) {
    console.error("刷新会话失败:", error)
    return NextResponse.json(
      { success: false, message: "刷新会话失败" },
      { status: 500 }
    )
  }
}
