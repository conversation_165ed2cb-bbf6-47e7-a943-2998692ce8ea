/**
 * 认证日志导出API
 * 提供认证日志的导出功能
 */

import { NextRequest, NextResponse } from "next/server"
import { AuthLogService } from "@/lib/auth-log-service"
import { SystemLogService } from "@/lib/system-log-service"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import { checkPermission } from "@/lib/casbin/enforcer"
import { format } from "date-fns"
import { zhCN } from "date-fns/locale"

// 导出认证日志
export async function GET(request: NextRequest) {
  try {
    // 使用权限检查中间件检查管理员权限
    const { response, user: admin } = await AuthMiddleware.requireAdmin(request, "认证日志导出API");
    if (response) {
      return NextResponse.json({ success: false, message: "未授权访问" }, { status: 401 })
    }

    // 检查权限
    const hasPermission = await checkPermission(request, "logs:view")
    if (!hasPermission) {
      return NextResponse.json({ success: false, message: "没有权限访问" }, { status: 403 })
    }

    // 获取查询参数
    const searchParams = request.nextUrl.searchParams
    const userId = searchParams.get("userId") || undefined
    const action = searchParams.get("action") || undefined
    const status = searchParams.get("status") || undefined
    const startDateStr = searchParams.get("startDate")
    const endDateStr = searchParams.get("endDate")
    const exportFormat = searchParams.get("format") || "json"

    // 转换日期
    const startDate = startDateStr ? new Date(startDateStr) : undefined
    const endDate = endDateStr ? new Date(endDateStr) : undefined

    // 查询日志记录（最多导出10000条）
    const result = await AuthLogService.query({
      userId,
      action,
      status,
      startDate,
      endDate,
      page: 1,
      pageSize: 10000
    })

    const logs = result.logs

    // 记录导出日志的操作
    await SystemLogService.log({
      userId: admin.id,
      action: "export",
      module: "logs",
      details: {
        type: "auth_logs",
        query: {
          userId,
          action,
          status,
          startDate,
          endDate
        },
        count: logs.length
      }
    })

    // 转换日志数据为导出格式
    const data = logs.map(log => ({
      time: format(new Date(log.createdAt), "yyyy-MM-dd HH:mm:ss", { locale: zhCN }),
      user: log.user ? (log.user.name || log.user.username) : "未知用户",
      email: log.user?.email || "-",
      action: getActionLabel(log.action),
      status: getStatusLabel(log.status),
      ipAddress: log.ipAddress || "-",
      userAgent: log.userAgent || "-",
      details: log.details ? JSON.stringify(log.details) : "-"
    }))

    // 根据格式返回不同类型的数据
    const headers = new Headers()
    const timestamp = format(new Date(), "yyyyMMdd_HHmmss")

    if (exportFormat === "csv") {
      // CSV格式
      const csvHeader = "\uFEFF时间,用户,邮箱,操作,状态,IP地址,用户代理,详情\n";
      const csvRows = data.map(item => {
        return `"${item.time}","${item.user}","${item.email}","${item.action}","${item.status}","${item.ipAddress}","${item.userAgent}","${item.details}"`;
      }).join("\n");

      headers.set("Content-Type", "text/csv;charset=utf-8");
      headers.set("Content-Disposition", `attachment; filename="认证日志_${timestamp}.csv"`);

      return new NextResponse(csvHeader + csvRows, {
        status: 200,
        headers
      });
    } else if (exportFormat === "excel") {
      // Excel格式 (实际上是CSV，但设置为Excel兼容格式)
      const csvHeader = "\uFEFF时间,用户,邮箱,操作,状态,IP地址,用户代理,详情\n";
      const csvRows = data.map(item => {
        return `"${item.time}","${item.user}","${item.email}","${item.action}","${item.status}","${item.ipAddress}","${item.userAgent}","${item.details}"`;
      }).join("\n");

      headers.set("Content-Type", "application/vnd.ms-excel;charset=utf-8");
      headers.set("Content-Disposition", `attachment; filename="认证日志_${timestamp}.xls"`);

      return new NextResponse(csvHeader + csvRows, {
        status: 200,
        headers
      });
    } else {
      // 默认JSON格式
      headers.set("Content-Type", "application/json");
      headers.set("Content-Disposition", `attachment; filename="认证日志_${timestamp}.json"`);

      return NextResponse.json(data, {
        status: 200,
        headers
      });
    }
  } catch (error) {
    console.error("导出认证日志失败:", error)
    return NextResponse.json({ success: false, message: "导出认证日志失败" }, { status: 500 })
  }
}

// 获取操作标签
function getActionLabel(actionCode: string) {
  const actionMap: Record<string, string> = {
    "login_attempt": "登录尝试",
    "token_validation": "令牌验证",
    "session_create": "会话创建",
    "session_refresh": "会话刷新",
    "session_invalidate": "会话注销"
  }

  return actionMap[actionCode] || actionCode
}

// 获取状态标签
function getStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    "success": "成功",
    "error": "失败",
    "warning": "警告",
    "info": "信息"
  }

  return statusMap[status] || status
}
