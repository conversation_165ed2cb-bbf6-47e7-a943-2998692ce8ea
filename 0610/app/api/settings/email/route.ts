import logger from '@/lib/utils/logger';

/**
 * 邮件设置API
 * 用于获取和更新系统邮件设置
 */

import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { AuthMiddleware } from "@/lib/middleware/auth-middleware"
import nodemailer from "nodemailer"

/**
 * 获取邮件设置
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, '获取邮件设置');
    if (authResult.response) {
      return authResult.response;
    }

    // 从数据库获取系统设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      return NextResponse.json({
        success: false,
        message: '系统设置不存在'
      }, { status: 404 });
    }

    // 提取邮件设置
    const defaultSettings = {
      host: process.env.EMAIL_HOST || 'smtp.qq.com',
      port: parseInt(process.env.EMAIL_PORT || '465'),
      secure: true,
      auth: {
        user: process.env.EMAIL_USER || '',
        pass: process.env.EMAIL_PASS || '' // 从环境变量中读取授权码
      },
      from: process.env.EMAIL_FROM || `"系统通知" <${process.env.EMAIL_USER || ''}>`,
      enableAutoRetry: true,
      retryInterval: 30, // 分钟
      maxRetries: 3
    };

    // 安全地提取邮件设置
    let emailSettings = defaultSettings;
    if (settings.emailSettings && typeof settings.emailSettings === 'object') {
      emailSettings = settings.emailSettings as any;
    }

    // 检查是否有授权码
    let hasPassword = false;

    // 检查设置中的授权码
    if (typeof emailSettings === 'object' &&
        'auth' in emailSettings &&
        emailSettings.auth &&
        typeof emailSettings.auth === 'object' &&
        'pass' in emailSettings.auth &&
        emailSettings.auth.pass &&
        typeof emailSettings.auth.pass === 'string' &&
        emailSettings.auth.pass.length > 0) {
      hasPassword = true;
    }

    // 输出日志，方便调试，但不记录敏感信息
    logger.debug('邮件设置状态:', {
      hasEmailSettings: !!settings.emailSettings,
      hasAuthConfig: !!(typeof emailSettings === 'object' && 'auth' in emailSettings),
      hasPassword: hasPassword
    });

    // 创建带掩码的授权信息
    const authWithMaskedPass = {
      user: typeof emailSettings === 'object' &&
            'auth' in emailSettings &&
            typeof emailSettings.auth === 'object' &&
            'user' in emailSettings.auth ?
            String(emailSettings.auth.user) : '',
      pass: hasPassword ? '******' : '' // 使用占位符代替实际密码
    };

    // 安全地提取各个字段
    const responseData = {
      host: typeof emailSettings === 'object' && 'host' in emailSettings ? String(emailSettings.host) : defaultSettings.host,
      port: typeof emailSettings === 'object' && 'port' in emailSettings ? Number(emailSettings.port) : defaultSettings.port,
      secure: typeof emailSettings === 'object' && 'secure' in emailSettings ? Boolean(emailSettings.secure) : defaultSettings.secure,
      from: typeof emailSettings === 'object' && 'from' in emailSettings ? String(emailSettings.from) : defaultSettings.from,
      enableAutoRetry: typeof emailSettings === 'object' && 'enableAutoRetry' in emailSettings ? Boolean(emailSettings.enableAutoRetry) : defaultSettings.enableAutoRetry,
      retryInterval: typeof emailSettings === 'object' && 'retryInterval' in emailSettings ? Number(emailSettings.retryInterval) : defaultSettings.retryInterval,
      maxRetries: typeof emailSettings === 'object' && 'maxRetries' in emailSettings ? Number(emailSettings.maxRetries) : defaultSettings.maxRetries,
      auth: authWithMaskedPass
    };

    return NextResponse.json({
      success: true,
      data: responseData
    });
  } catch (error) {
    logger.error("获取邮件设置失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "获取邮件设置失败"
    }, { status: 500 });
  }
}

/**
 * 更新邮件设置
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, '更新邮件设置');
    if (authResult.response) {
      return authResult.response;
    }

    // 获取请求数据
    const data = await request.json();

    // 验证必要字段
    if (!data.host || !data.port || !data.auth?.user) {
      return NextResponse.json({
        success: false,
        message: "邮件服务器配置不完整"
      }, { status: 400 });
    }

    // 获取当前设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings) {
      return NextResponse.json({
        success: false,
        message: "系统设置不存在"
      }, { status: 404 });
    }

    // 合并现有邮件设置
    const currentEmailSettings = settings.emailSettings || {};
    const currentAuth = typeof currentEmailSettings === 'object' &&
                      'auth' in currentEmailSettings &&
                      currentEmailSettings.auth !== null ?
                      currentEmailSettings.auth : {};
    const currentAuthPass = currentAuth &&
                          typeof currentAuth === 'object' &&
                          'pass' in currentAuth &&
                          currentAuth.pass !== null ?
                          String(currentAuth.pass) : '';

    // 如果提供了新的授权码，则验证邮件服务器连接
    if (data.auth.pass && data.auth.pass.length > 0) {
      try {
        // 创建临时传输器进行验证
        const transporter = nodemailer.createTransport({
          host: data.host,
          port: parseInt(data.port.toString()),
          secure: data.secure,
          auth: {
            user: data.auth.user,
            pass: data.auth.pass
          },
          tls: {
            rejectUnauthorized: false
          }
        });

        // 验证连接
        await transporter.verify();
      } catch (error) {
        return NextResponse.json({
          success: false,
          message: "邮件服务器连接验证失败: " + (error instanceof Error ? error.message : "未知错误")
        }, { status: 400 });
      }
    } else if (data.auth.pass === '') {
      // 如果提供了空字符串，表示保留原来的授权码
      // 检查是否有原来的授权码
      if (!currentAuthPass) {
        return NextResponse.json({
          success: false,
          message: "没有可用的邮箱授权码，请输入授权码"
        }, { status: 400 });
      }
    }

    // 创建新的邮件设置对象
    const emailSettings = {
      host: data.host,
      port: parseInt(data.port.toString()),
      secure: data.secure,
      from: data.from || `"系统通知" <${data.auth.user}>`,
      enableAutoRetry: data.enableAutoRetry !== undefined ? data.enableAutoRetry : true,
      retryInterval: data.retryInterval || 30,
      maxRetries: data.maxRetries || 3,
      auth: {
        user: data.auth.user,
        // 处理授权码更新逻辑
        pass: data.auth.pass !== undefined
          ? (data.auth.pass || currentAuthPass || '') // 如果提供了pass字段（即使是空字符串），则检查是否保留原来的
          : (currentAuthPass || '') // 如果没有提供字段，则保留原来的
      }
    };

    // 输出日志，方便调试，但不记录敏感信息
    logger.debug('更新邮件设置:', {
      host: emailSettings.host,
      port: emailSettings.port,
      secure: emailSettings.secure,
      from: emailSettings.from,
      hasAuthUser: !!emailSettings.auth?.user,
      hasAuthPass: !!emailSettings.auth?.pass
      // 出于安全考虑，不记录授权码长度等敏感信息
    });

    // 检查系统设置是否存在
    const existingSettings = await prisma.systemSettings.findFirst();

    if (existingSettings) {
      // 更新现有设置
      await prisma.systemSettings.update({
        where: {
          id: existingSettings.id
        },
        data: {
          emailSettings: emailSettings
        }
      });
    } else {
      // 创建新设置
      await prisma.systemSettings.create({
        data: {
          siteName: "外呼管理系统",
          logo: "/logo.png",
          footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
          description: "高效的外呼任务管理平台",
          keywords: "外呼,管理系统,任务管理",
          applicationName: "外呼管理系统",
          appleMobileWebAppTitle: "外呼系统",
          theme: {
            primaryColor: "#0284c7",
            mode: "system"
          },
          features: {
            enableRegistration: true,
            enablePasswordReset: true,
            enableNotifications: true
          },
          security: {
            passwordMinLength: 8,
            requireSpecialChar: true,
            requireNumber: true,
            requireUppercase: true,
            loginAttempts: 5,
            sessionTimeout: 30
          },
          loginPage: {
            backgroundImage: "/placeholder.svg?height=1080&width=1920",
            backgroundEffect: "all",
            title: "外呼管理系统",
            subtitle: "提升工作效率的得力助手",
            features: [
              { icon: "user", text: "专业的客户管理" },
              { icon: "lock", text: "安全的数据保护" },
              { icon: "mail", text: "高效的沟通工具" }
            ]
          },
          emailSettings: emailSettings
        }
      });
    }

    // 更新环境变量（仅在开发环境中有效）
    if (process.env.NODE_ENV === 'development') {
      process.env.EMAIL_HOST = data.host;
      process.env.EMAIL_PORT = data.port.toString();
      process.env.EMAIL_SECURE = data.secure ? 'true' : 'false';
      process.env.EMAIL_USER = data.auth.user;
      if (data.auth.pass) {
        process.env.EMAIL_PASS = data.auth.pass;
      }
      process.env.EMAIL_FROM = data.from || `"系统通知" <${data.auth.user}>`;
    }

    return NextResponse.json({
      success: true,
      message: "邮件设置已更新",
      data: {
        host: emailSettings.host,
        port: emailSettings.port,
        secure: emailSettings.secure,
        from: emailSettings.from,
        enableAutoRetry: emailSettings.enableAutoRetry,
        retryInterval: emailSettings.retryInterval,
        maxRetries: emailSettings.maxRetries,
        auth: {
          user: emailSettings.auth.user,
          pass: data.auth.pass ? '******' : '' // 不返回实际密码
        }
      }
    });
  } catch (error) {
    logger.error("更新邮件设置失败:", error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "更新邮件设置失败"
    }, { status: 500 });
  }
}

/**
 * 测试邮件设置
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await AuthMiddleware.requireAdmin(request, '发送测试邮件');
    if (authResult.response) {
      return authResult.response;
    }

    // 获取请求数据
    const data = await request.json();

    // 验证必要字段
    if (!data.email) {
      return NextResponse.json({
        success: false,
        message: "测试邮箱地址不能为空"
      }, { status: 400 });
    }

    // 获取当前邮件设置
    const settings = await prisma.systemSettings.findFirst();
    if (!settings || !settings.emailSettings) {
      return NextResponse.json({
        success: false,
        message: "邮件设置不存在，请先配置邮件设置"
      }, { status: 404 });
    }

    const emailSettings = settings.emailSettings as any;

    // 验证邮件设置是否完整
    if (!emailSettings.host || !emailSettings.port || !emailSettings.auth?.user || !emailSettings.auth?.pass) {
      return NextResponse.json({
        success: false,
        message: "邮件设置不完整，请先完成邮件设置配置"
      }, { status: 400 });
    }

    // 创建传输器
    const transporter = nodemailer.createTransport({
      host: emailSettings.host,
      port: parseInt(emailSettings.port.toString()),
      secure: emailSettings.secure,
      auth: {
        user: emailSettings.auth.user,
        pass: emailSettings.auth.pass
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // 发送测试邮件
    const info = await transporter.sendMail({
      from: emailSettings.from || `"系统通知" <${emailSettings.auth.user}>`,
      to: data.email,
      subject: "邮件设置测试",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">邮件设置测试</h2>
          <p style="color: #666; line-height: 1.6;">这是一封测试邮件，用于验证系统邮件设置是否正确。</p>
          <p style="color: #666; line-height: 1.6;">如果您收到这封邮件，说明系统邮件设置已配置成功。</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; color: #888; font-size: 12px;">发送时间: ${new Date().toLocaleString()}</p>
            <p style="margin: 5px 0 0; color: #888; font-size: 12px;">服务器: ${emailSettings.host}:${emailSettings.port}</p>
          </div>
        </div>
      `
    });

    return NextResponse.json({
      success: true,
      message: "测试邮件已发送",
      data: {
        messageId: info.messageId,
        to: data.email
      }
    });
  } catch (error) {
    logger.error("发送测试邮件失败:", error);
    return NextResponse.json({
      success: false,
      message: "发送测试邮件失败: " + (error instanceof Error ? error.message : "未知错误")
    }, { status: 500 });
  }
}
