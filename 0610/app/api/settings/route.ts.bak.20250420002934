import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

/**
 * 获取系统设置
 */
export async function GET() {
  try {
    // 从数据库获取系统设置
    const settings = await prisma.systemSettings.findFirst()

    // 如果没有设置记录，创建默认设置
    if (!settings) {
      const defaultSettings = {
        siteName: "外呼管理系统",
        logo: "/logo.png",
        footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
        description: "高效的外呼任务管理平台",
        keywords: "外呼,管理系统,任务管理",
        applicationName: "外呼管理系统",
        appleMobileWebAppTitle: "外呼系统",
        theme: {
          primaryColor: "#0284c7",
          mode: "system"
        },
        features: {
          enableRegistration: true,
          enablePasswordReset: true,
          enableNotifications: true
        },
        security: {
          passwordMinLength: 8,
          requireSpecialChar: true,
          requireNumber: true,
          requireUppercase: true,
          loginAttempts: 5,
          sessionTimeout: 30
        },
        loginPage: {
          backgroundEffect: "all",
          title: "外呼管理系统",
          subtitle: "提升工作效率的得力助手",
          features: [
            { icon: "user", text: "专业的客户管理" },
            { icon: "lock", text: "安全的数据保护" },
            { icon: "mail", text: "高效的沟通工具" }
          ]
        }
      }

      // 创建默认设置记录
      const newSettings = await prisma.systemSettings.create({
        data: defaultSettings
      })

      return NextResponse.json({
        success: true,
        data: newSettings
      })
    }

    return NextResponse.json({
      success: true,
      data: settings
    })
  } catch (error) {
    console.error("获取系统设置失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取系统设置失败"
      },
      { status: 500 }
    )
  }
}

/**
 * 更新系统设置
 */
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // 验证必要字段
    if (!data.siteName) {
      return NextResponse.json(
        {
          success: false,
          message: "网站名称不能为空"
        },
        { status: 400 }
      )
    }

    // 确保 theme 和 features 是对象
    const theme = typeof data.theme === 'object' ? data.theme : {}
    const features = typeof data.features === 'object' ? data.features : {}

    // 确保 security 是对象
    const security = typeof data.security === 'object' ? data.security : {}

    // 确保 loginPage 是对象
    const loginPage = typeof data.loginPage === 'object' ? data.loginPage : {}

    // 更新系统设置
    const settings = await prisma.systemSettings.upsert({
      where: {
        id: 1
      },
      update: {
        siteName: data.siteName,
        logo: data.logo || "/logo.png",
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      },
      create: {
        siteName: data.siteName,
        logo: data.logo || "/logo.png",
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      }
    })

    return NextResponse.json({
      success: true,
      data: settings
    })
  } catch (error) {
    console.error("更新系统设置失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新系统设置失败"
      },
      { status: 500 }
    )
  }
}