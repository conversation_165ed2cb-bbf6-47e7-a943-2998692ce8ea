import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { revalidatePath } from "next/cache"

/**
 * 获取系统设置
 */
export async function GET() {
  try {
    // 从数据库获取系统设置
    const settings = await prisma.systemSettings.findFirst()

    // 如果没有设置记录，创建默认设置
    if (!settings) {
      const defaultSettings = {
        siteName: "外呼管理系统",
        logo: "/logo.png",
        footerText: `© ${new Date().getFullYear()} 外呼管理系统 版权所有`,
        description: "高效的外呼任务管理平台",
        keywords: "外呼,管理系统,任务管理",
        applicationName: "外呼管理系统",
        appleMobileWebAppTitle: "外呼系统",
        theme: {
          primaryColor: "#0284c7",
          mode: "system"
        },
        features: {
          enableRegistration: true,
          enablePasswordReset: true,
          enableNotifications: true
        },
        security: {
          passwordMinLength: 8,
          requireSpecialChar: true,
          requireNumber: true,
          requireUppercase: true,
          loginAttempts: 5,
          sessionTimeout: 30
        },
        loginPage: {
          backgroundEffect: "all",
          title: "外呼管理系统",
          subtitle: "提升工作效率的得力助手",
          features: [
            { icon: "user", text: "专业的客户管理" },
            { icon: "lock", text: "安全的数据保护" },
            { icon: "mail", text: "高效的沟通工具" }
          ]
        }
      }

      // 创建默认设置记录
      const newSettings = await prisma.systemSettings.create({
        data: defaultSettings
      })

      return NextResponse.json({
        success: true,
        data: newSettings
      })
    }

    // 添加时间戳，确保客户端不使用缓存
    const timestamp = Date.now()

    // 如果有Logo，添加时间戳参数
    if (settings?.logo && !settings.logo.includes('?')) {
      settings.logo = `${settings.logo}?t=${timestamp}`
    }

    // 重新验证相关路径的缓存
    revalidatePath('/', 'layout') // 重新验证根路径的布局
    revalidatePath('/login', 'page') // 重新验证登录页面
    revalidatePath('/dashboard', 'layout') // 重新验证仪表盘布局

    return NextResponse.json({
      success: true,
      data: settings,
      timestamp: timestamp
    })
  } catch (error) {
    console.error("获取系统设置失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "获取系统设置失败"
      },
      { status: 500 }
    )
  }
}

/**
 * 更新系统设置
 */
export async function POST(request: Request) {
  try {
    const data = await request.json()

    // 验证必要字段
    if (!data.siteName) {
      return NextResponse.json(
        {
          success: false,
          message: "网站名称不能为空"
        },
        { status: 400 }
      )
    }

    // 确保 theme 和 features 是对象
    const theme = typeof data.theme === 'object' ? data.theme : {}
    const features = typeof data.features === 'object' ? data.features : {}

    // 确保 security 是对象
    const security = typeof data.security === 'object' ? data.security : {}

    // 确保 loginPage 是对象
    const loginPage = typeof data.loginPage === 'object' ? data.loginPage : {}

    // 记录旧的Logo和网站名称，用于检测是否需要触发特殊更新
    const oldSettings = await prisma.systemSettings.findFirst({
      where: { id: 1 }
    })

    const logoChanged = oldSettings && oldSettings.logo !== data.logo && data.logo
    const nameChanged = oldSettings && oldSettings.siteName !== data.siteName && data.siteName

    // 处理Logo URL，添加时间戳
    const timestamp = Date.now()
    let logoUrl = data.logo || "/logo.png"

    // 如果有Logo且不包含时间戳参数，添加时间戳
    if (logoUrl && !logoUrl.includes('?')) {
      logoUrl = `${logoUrl}?t=${timestamp}`
    }

    // 更新系统设置
    const settings = await prisma.systemSettings.upsert({
      where: {
        id: 1
      },
      update: {
        siteName: data.siteName,
        logo: logoUrl,
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      },
      create: {
        siteName: data.siteName,
        logo: logoUrl,
        footerText: data.footerText,
        description: data.description,
        keywords: data.keywords,
        applicationName: data.applicationName,
        appleMobileWebAppTitle: data.appleMobileWebAppTitle,
        theme,
        features,
        security,
        loginPage
      }
    })

    // 重新验证相关路径的缓存
    revalidatePath('/', 'layout') // 重新验证根路径的布局
    revalidatePath('/login', 'page') // 重新验证登录页面
    revalidatePath('/dashboard', 'layout') // 重新验证仪表盘布局
    revalidatePath('/settings', 'page') // 重新验证设置页面

    // 如果Logo或网站名称发生变化，触发特殊事件
    if (logoChanged || nameChanged) {
      // 在响应中添加标记，表示需要刷新
      const responseData = {
        success: true,
        data: settings,
        timestamp: Date.now(),
        needsRefresh: true,
        changes: {
          logo: logoChanged,
          name: nameChanged
        }
      }

      return NextResponse.json(responseData)
    } else {
      // 添加时间戳，确保客户端不使用缓存
      const responseData = {
        success: true,
        data: settings,
        timestamp: Date.now()
      }

      return NextResponse.json(responseData)
    }
  } catch (error) {
    console.error("更新系统设置失败:", error)
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "更新系统设置失败"
      },
      { status: 500 }
    )
  }
}