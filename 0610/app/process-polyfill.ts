"use client"

import logger from '@/lib/utils/logger';

// 为客户端提供全局 process polyfill
if (typeof window !== 'undefined') {
  try {
    // 检查是否已经存在 process 对象
    if (!window.process) {
      // 初始化 window.ENV 对象，用于存储环境变量
      if (!window.ENV) {
        window.ENV = {
          NODE_ENV: 'production', // 默认为生产环境
          NEXT_PUBLIC_API_BASE_URL: "/api"
        };
      }

      // 检查是否为开发环境
      const isDev = window.location.hostname === 'localhost' ||
                    window.location.hostname === '127.0.0.1' ||
                    window.location.hostname.includes('.local');

      if (isDev) {
        window.ENV.NODE_ENV = 'development';
      }

      // @ts-ignore
      window.process = {
        env: {
          // 安全地设置 NODE_ENV
          NODE_ENV: window.ENV.NODE_ENV,
          // 添加所有环境变量
          ...window.ENV
        },
        browser: true,
        version: '',
        versions: {},
        nextTick: function(fn: Function) {
          setTimeout(fn, 0);
        },
      };

      logger.log('全局 Process polyfill 已加载，环境:', window.ENV.NODE_ENV);
    }
  } catch (error) {
    logger.error('加载全局 process polyfill 失败:', error);

    // 确保即使出错也创建一个基本的 process 对象
    try {
      // @ts-ignore
      if (!window.process) {
        // @ts-ignore
        window.process = {
          env: { NODE_ENV: 'production' },
          browser: true,
          nextTick: function(fn: Function) {
            setTimeout(fn, 0);
          }
        };
      }
    } catch (e) {
      // 忽略错误
    }
  }
}

// 导出一个函数，确保这个文件可以被正确导入
export function ensureProcessPolyfill() {
  // 这个函数不做任何事情，只是确保 polyfill 被加载
  return true;
}

// 导出一个函数，用于获取当前环境
export function getEnvironment() {
  try {
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV) {
      return process.env.NODE_ENV;
    }

    if (typeof window !== 'undefined') {
      // @ts-ignore
      if (window.ENV && window.ENV.NODE_ENV) {
        // @ts-ignore
        return window.ENV.NODE_ENV;
      }

      // 检查URL是否包含localhost或特定的开发域名
      const hostname = window.location.hostname;
      if (hostname === 'localhost' ||
          hostname === '127.0.0.1' ||
          hostname.includes('.local')) {
        return 'development';
      }
    }

    return 'production';
  } catch (e) {
    return 'production';
  }
}
