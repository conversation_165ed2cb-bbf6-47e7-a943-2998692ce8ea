"use client"

/**
 * 操作权限管理页面
 * 提供操作权限的列表展示、创建、编辑和删除功能
 */

import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { DataTable } from "@/components/ui/data-table"
import { FormDialog } from "@/components/ui/form-dialog"
import { toast } from "sonner"
import { Plus } from "lucide-react"

interface Operation {
  id: string
  code: string
  name: string
  type: string
  description: string | null
  menuId: string
  menu: {
    id: string
    name: string
  }
  roles: {
    id: string
    name: string
  }[]
}

interface Menu {
  id: string
  name: string
}

interface Role {
  id: string
  name: string
}

export default function OperationsPage() {
  const [operations, setOperations] = useState<Operation[]>([])
  const [menus, setMenus] = useState<Menu[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingOperation, setEditingOperation] = useState<Operation | null>(null)
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    type: "",
    description: "",
    menuId: "",
    roleIds: [] as string[],
  })

  // 获取操作权限列表
  const fetchOperations = async () => {
    try {
      const response = await fetch("/api/operations")
      const data = await response.json()
      if (data.success) {
        setOperations(data.operations)
      } else {
        toast.error(data.error || "获取操作权限列表失败")
      }
    } catch (error) {
      toast.error("获取操作权限列表失败")
    }
  }

  // 获取菜单列表
  const fetchMenus = async () => {
    try {
      const response = await fetch("/api/menus")
      const data = await response.json()
      if (data.success) {
        setMenus(data.menus)
      }
    } catch (error) {
      toast.error("获取菜单列表失败")
    }
  }

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      const response = await fetch("/api/roles")
      const data = await response.json()
      if (data.success) {
        setRoles(data.roles)
      }
    } catch (error) {
      toast.error("获取角色列表失败")
    }
  }

  useEffect(() => {
    fetchOperations()
    fetchMenus()
    fetchRoles()
  }, [])

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const url = "/api/operations"
      const method = editingOperation ? "PUT" : "POST"
      const body = editingOperation
        ? { ...formData, id: editingOperation.id }
        : formData

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      })

      const data = await response.json()
      if (data.success) {
        toast.success(
          editingOperation
            ? "更新操作权限成功"
            : "创建操作权限成功"
        )
        setIsDialogOpen(false)
        setEditingOperation(null)
        setFormData({
          code: "",
          name: "",
          type: "",
          description: "",
          menuId: "",
          roleIds: [],
        })
        fetchOperations()
      } else {
        toast.error(data.error || "操作失败")
      }
    } catch (error) {
      toast.error("操作失败")
    }
  }

  // 处理删除操作
  const handleDelete = async (operation: Operation) => {
    if (!confirm("确定要删除这个操作权限吗？")) return

    try {
      const response = await fetch(`/api/operations?id=${operation.id}`, {
        method: "DELETE",
      })

      const data = await response.json()
      if (data.success) {
        toast.success("删除操作权限成功")
        fetchOperations()
      } else {
        toast.error(data.error || "删除操作权限失败")
      }
    } catch (error) {
      toast.error("删除操作权限失败")
    }
  }

  // 打开编辑对话框
  const handleEdit = (operation: Operation) => {
    setEditingOperation(operation)
    setFormData({
      code: operation.code,
      name: operation.name,
      type: operation.type,
      description: operation.description || "",
      menuId: operation.menuId,
      roleIds: operation.roles.map((role) => role.id),
    })
    setIsDialogOpen(true)
  }

  const columns = [
    {
      key: "code",
      title: "操作代码",
    },
    {
      key: "name",
      title: "操作名称",
    },
    {
      key: "type",
      title: "操作类型",
    },
    {
      key: "description",
      title: "操作描述",
    },
    {
      key: "menu",
      title: "所属菜单",
      render: (operation: Operation) => operation.menu.name,
    },
    {
      key: "roles",
      title: "关联角色",
      render: (operation: Operation) =>
        operation.roles.map((role) => role.name).join(", "),
    },
  ]

  const formFields = [
    {
      name: "code",
      label: "操作代码",
      type: "text" as const,
      required: true,
      value: formData.code,
      onChange: (value: string) =>
        setFormData({ ...formData, code: value }),
    },
    {
      name: "name",
      label: "操作名称",
      type: "text" as const,
      required: true,
      value: formData.name,
      onChange: (value: string) =>
        setFormData({ ...formData, name: value }),
    },
    {
      name: "type",
      label: "操作类型",
      type: "select" as const,
      required: true,
      options: [
        { value: "button", label: "按钮" },
        { value: "api", label: "API" },
        { value: "data", label: "数据" },
      ],
      value: formData.type,
      onChange: (value: string) =>
        setFormData({ ...formData, type: value }),
    },
    {
      name: "description",
      label: "操作描述",
      type: "textarea" as const,
      value: formData.description,
      onChange: (value: string) =>
        setFormData({ ...formData, description: value }),
    },
    {
      name: "menuId",
      label: "所属菜单",
      type: "select" as const,
      required: true,
      options: menus.map((menu) => ({
        value: menu.id,
        label: menu.name,
      })),
      value: formData.menuId,
      onChange: (value: string) =>
        setFormData({ ...formData, menuId: value }),
    },
    {
      name: "roleIds",
      label: "关联角色",
      type: "select" as const,
      required: true,
      options: roles.map((role) => ({
        value: role.id,
        label: role.name,
      })),
      value: formData.roleIds[0] || "",
      onChange: (value: string) =>
        setFormData({ ...formData, roleIds: [value] }),
    },
  ]

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">操作权限管理</h1>
        <FormDialog
          title={editingOperation ? "编辑操作权限" : "新建操作权限"}
          trigger={
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              新建操作权限
            </Button>
          }
          fields={formFields}
          onSubmit={handleSubmit}
          onCancel={() => {
            setIsDialogOpen(false)
            setEditingOperation(null)
            setFormData({
              code: "",
              name: "",
              type: "",
              description: "",
              menuId: "",
              roleIds: [],
            })
          }}
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
        />
      </div>

      <div className="rounded-md border">
        <table className="w-full">
          <thead>
            <tr>
              {columns.map((column) => (
                <th key={column.key} className="p-4 text-left font-medium">
                  {column.title}
                </th>
              ))}
              <th className="p-4 text-left font-medium">操作</th>
            </tr>
          </thead>
          <tbody>
            {operations.length === 0 ? (
              <tr>
                <td colSpan={columns.length + 1} className="p-4 text-center">
                  暂无数据
                </td>
              </tr>
            ) : (
              operations.map((operation) => (
                <tr key={operation.id}>
                  {columns.map((column) => (
                    <td key={column.key} className="p-4">
                      {column.render ? column.render(operation) : (operation as any)[column.key]}
                    </td>
                  ))}
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(operation)}
                      >
                        编辑
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(operation)}
                      >
                        删除
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  )
}