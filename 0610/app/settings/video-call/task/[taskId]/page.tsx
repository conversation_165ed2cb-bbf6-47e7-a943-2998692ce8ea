"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { videoCallApi } from "@/services/video-call-api"
import { ArrowLeft, RefreshCw, BarChart2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// 任务状态映射
const taskStatusMap = {
  initialized: { label: "待启动", color: "bg-yellow-100 text-yellow-800" },
  started: { label: "已启动", color: "bg-green-100 text-green-800" },
  paused: { label: "已暂停", color: "bg-orange-100 text-orange-800" },
  finished: { label: "已完成", color: "bg-blue-100 text-blue-800" },
  stopped: { label: "已停止", color: "bg-red-100 text-red-800" },
}

export default function TaskDetailPage({ params }: { params: { taskId: string } }) {
  const router = useRouter()
  const { taskId } = params
  const [loading, setLoading] = useState(false)
  const [taskDetail, setTaskDetail] = useState<any>(null)
  const [activeTab, setActiveTab] = useState("overview")

  // 加载任务详情
  const loadTaskDetail = async () => {
    try {
      setLoading(true)
      const result = await videoCallApi.getTaskDetail(taskId)

      if (result.errCode === 0 && result.result) {
        setTaskDetail(result.result)
      } else {
        toast({
          title: "获取任务详情失败",
          description: result.errInfo || "请检查网络连接和API配置",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "获取任务详情失败",
        description: "请检查网络连接和API配置",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadTaskDetail()
  }, [taskId])

  if (!taskDetail) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>加载任务详情...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h3 className="text-lg font-medium">{taskDetail.taskId}</h3>
            <p className="text-sm text-muted-foreground">任务详情</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={loadTaskDetail} disabled={loading}>
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
            刷新
          </Button>
        </div>
      </div>
      <Separator />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart2 className="mr-2 h-4 w-4" />
            概览
          </TabsTrigger>
          <TabsTrigger value="statistics">
            <BarChart2 className="mr-2 h-4 w-4" />
            统计分析
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>任务信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-1">
                  <p className="text-sm font-medium">任务ID</p>
                  <p className="text-sm">{taskDetail.taskId}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">任务名称</p>
                  <p className="text-sm">{taskDetail.name || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">创建人</p>
                  <p className="text-sm">{taskDetail.creator || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">创建时间</p>
                  <p className="text-sm">{taskDetail.createdAt || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">设定开始时间</p>
                  <p className="text-sm">{taskDetail.settingStartTime || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">实际开始时间</p>
                  <p className="text-sm">{taskDetail.startedAt || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">完成时间</p>
                  <p className="text-sm">{taskDetail.endedAt || "-"}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">外呼类型</p>
                  <p className="text-sm">
                    {taskDetail.callType === 1
                      ? "视频通知"
                      : taskDetail.callType === 2
                        ? "视频互动"
                        : taskDetail.callType === 3
                          ? "语音互动"
                          : "-"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>外呼内容</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">外呼内容名称</p>
                    <p className="text-sm">{taskDetail.videoBotName || "-"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">外呼内容ID</p>
                    <p className="text-sm">{taskDetail.videoBotId || "-"}</p>
                  </div>
                  {taskDetail.audioBotName && (
                    <>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">语音分流内容名称</p>
                        <p className="text-sm">{taskDetail.audioBotName}</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">语音分流内容ID</p>
                        <p className="text-sm">{taskDetail.audioBotId || "-"}</p>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>任务配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">任务线路</p>
                    <p className="text-sm">{taskDetail.trunkName || "-"}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">任务并发</p>
                    <p className="text-sm">{taskDetail.concurrency || "-"}</p>
                  </div>
                  {taskDetail.flashMessageTemplateName && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">闪信模板</p>
                      <p className="text-sm">{taskDetail.flashMessageTemplateName}</p>
                    </div>
                  )}
                  {taskDetail.messageTemplateName && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">文本短信模板</p>
                      <p className="text-sm">{taskDetail.messageTemplateName}</p>
                    </div>
                  )}
                  {taskDetail.videoMessageTemplateName && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">视频短信模板</p>
                      <p className="text-sm">{taskDetail.videoMessageTemplateName}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>任务数据</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <p className="text-sm font-medium">导入数量</p>
                  <p className="text-2xl font-bold">{taskDetail.importNumber || 0}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">有效数量</p>
                  <p className="text-2xl font-bold">{taskDetail.deliverNumber || 0}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium">已呼数量</p>
                  <p className="text-2xl font-bold">{taskDetail.calledCount || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>统计分析</CardTitle>
              <CardDescription>查看任务的详细统计分析数据</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40">
                <Button onClick={() => router.push(`/settings/video-call/statistics?taskId=${taskId}`)}>
                  <BarChart2 className="mr-2 h-4 w-4" />
                  查看详细统计
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

