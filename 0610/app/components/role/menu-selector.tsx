"use client"

import { useState, useEffect, useMemo } from "react"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { ScrollArea } from "@/components/ui/scroll-area"
import { toast } from "sonner"
import { ChevronDown, ChevronRight, FolderTree, Search, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

interface MenuItem {
  id: string
  name: string  // 菜单名称
  code: string  // 菜单编码
  path?: string // 菜单路径
  icon?: string // 菜单图标
  parentId?: string | null // 父菜单ID
  children?: MenuItem[] // 子菜单
  order?: number // 排序
  visible?: boolean // 是否可见
}

interface MenuSelectorProps {
  selectedMenus: string[] // 已选择的菜单ID列表
  onChange: (menus: string[]) => void // 选择变更回调
  maxHeight?: number // 最大高度
  showBadges?: boolean // 是否显示徽章
  showSearch?: boolean // 是否显示搜索框
}

export function MenuSelector({
  selectedMenus,
  onChange,
  maxHeight = 400,
  showBadges = true,
  showSearch = true
}: MenuSelectorProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({})
  const [searchQuery, setSearchQuery] = useState('')

  // 加载菜单列表
  const loadMenuItems = async (isRefreshing = false) => {
    try {
      if (isRefreshing) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      const response = await fetch("/api/menus", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache"
        },
        cache: "no-store"
      })
      const data = await response.json()

      if (data.success) {
        // 处理数据并构建菜单树
        let menuData: MenuItem[] = []

        // 如果数据在menus字段中
        if (data.menus && Array.isArray(data.menus)) {
          menuData = data.menus
        }
        // 如果数据在data字段中
        else if (data.data && Array.isArray(data.data)) {
          menuData = data.data
        }
        // 如果数据在items字段中
        else if (data.items && Array.isArray(data.items)) {
          menuData = data.items
        }
        // 如果数据直接在根级别且是数组
        else if (Array.isArray(data)) {
          menuData = data
        }
        else {
          toast.error('菜单数据格式不正确')
          return
        }

        // 根据数据字段进行映射
        const mappedMenus = menuData.map(menu => {
          return {
            id: menu.id,
            name: menu.name || menu.title || '',
            code: menu.code || '',
            path: menu.path || menu.href || '',
            icon: menu.icon || '',
            parentId: menu.parentId || menu.parent || null,
            order: menu.order || 0,
            visible: menu.visible !== false
          }
        })

        // 构建菜单树
        const menuTree = buildMenuTree(mappedMenus)
        setMenuItems(menuTree)

        // 默认展开所有父菜单
        const expanded: Record<string, boolean> = {}
        mappedMenus.forEach(menu => {
          if (!menu.parentId) {
            expanded[menu.id] = true
          }
        })
        setExpandedItems(expanded)

        if (isRefreshing) {
          toast.success('菜单数据已刷新')
        }
      } else {
        toast.error(`加载菜单列表失败: ${data.error || data.message || '未知错误'}`)
      }
    } catch (error) {
      // 显示错误信息
      const errorMsg = error instanceof Error ? error.message : "未知错误"
      toast.error(`加载菜单列表失败`)

      // 加载失败时设置为空数组
      setMenuItems([])
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // 构建菜单树
  const buildMenuTree = (menus: MenuItem[]): MenuItem[] => {
    // 创建一个映射表，用于快速查找菜单
    const menuMap: Record<string, MenuItem> = {}
    menus.forEach(menu => {
      menuMap[menu.id] = { ...menu, children: [] }
    })

    // 构建树结构
    const rootMenus: MenuItem[] = []

    menus.forEach(menu => {
      const menuWithChildren = menuMap[menu.id]

      if (!menu.parentId) {
        // 没有父菜单，则为根菜单
        rootMenus.push(menuWithChildren)
      } else if (menuMap[menu.parentId]) {
        // 有父菜单，则添加到父菜单的children中
        if (!menuMap[menu.parentId].children) {
          menuMap[menu.parentId].children = []
        }
        menuMap[menu.parentId].children!.push(menuWithChildren)
      } else {
        // 父菜单不存在，作为根菜单处理
        rootMenus.push(menuWithChildren)
      }
    })

    // 按照order排序
    const sortMenus = (items: MenuItem[]): MenuItem[] => {
      return items
        .sort((a, b) => (a.order || 0) - (b.order || 0))
        .map(item => {
          if (item.children && item.children.length > 0) {
            return { ...item, children: sortMenus(item.children) }
          }
          return item
        })
    }

    return sortMenus(rootMenus)
  }

  // 处理菜单选择变化
  const handleMenuChange = (menuId: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedMenus, menuId])
    } else {
      onChange(selectedMenus.filter(id => id !== menuId))
    }
  }

  // 切换展开/折叠状态
  const toggleExpand = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }))
  }

  // 选择所有菜单
  const selectAllMenus = () => {
    const allMenuIds = getAllMenuIds(menuItems)
    onChange(allMenuIds)
  }

  // 取消选择所有菜单
  const deselectAllMenus = () => {
    onChange([])
  }

  // 获取所有菜单ID
  const getAllMenuIds = (items: MenuItem[]): string[] => {
    return items.reduce((acc: string[], item) => {
      if (item.id) {
        acc.push(item.id)
      }
      if (item.children && item.children.length > 0) {
        acc = [...acc, ...getAllMenuIds(item.children)]
      }
      return acc
    }, [])
  }

  // 刷新菜单数据
  const refreshMenus = () => {
    loadMenuItems(true)
  }

  // 搜索菜单
  const filteredMenuItems = useMemo(() => {
    if (!searchQuery.trim()) {
      return menuItems
    }

    const query = searchQuery.toLowerCase()

    // 递归搜索菜单
    const searchMenus = (items: MenuItem[]): MenuItem[] => {
      return items
        .map(item => {
          // 检查当前菜单是否匹配
          const nameMatch = item.name.toLowerCase().includes(query)
          const codeMatch = item.code.toLowerCase().includes(query)
          const pathMatch = item.path?.toLowerCase().includes(query) || false

          // 如果有子菜单，递归搜索
          let filteredChildren: MenuItem[] = []
          if (item.children && item.children.length > 0) {
            filteredChildren = searchMenus(item.children)
          }

          // 如果当前菜单匹配或者子菜单有匹配项，则保留
          if (nameMatch || codeMatch || pathMatch || filteredChildren.length > 0) {
            return {
              ...item,
              children: filteredChildren
            }
          }

          // 否则返回null，后面会过滤掉
          return null
        })
        .filter((item): item is MenuItem => item !== null)
    }

    return searchMenus(menuItems)
  }, [menuItems, searchQuery])

  // 渲染菜单项
  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems[item.id]

    return (
      <div key={item.id} className="mb-2">
        <div
          className={`flex items-center space-x-2 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 ${
            level > 0 ? `ml-${level * 4}` : ''
          }`}
        >
          {hasChildren && (
            <button
              type="button"
              onClick={() => toggleExpand(item.id)}
              className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            >
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </button>
          )}
          {!hasChildren && <div className="w-6"></div>}

          <Checkbox
            id={`menu-${item.id}`}
            checked={selectedMenus.includes(item.id)}
            onCheckedChange={(checked) =>
              handleMenuChange(item.id, checked === true)
            }
          />

          <Label
            htmlFor={`menu-${item.id}`}
            className="font-medium cursor-pointer flex-1"
          >
            {item.name}
            {item.path && (
              <span className="text-xs text-muted-foreground ml-2">
                {item.path}
              </span>
            )}
          </Label>

          <span className="text-xs text-muted-foreground">
            {item.code}
          </span>
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-2">
            {item.children.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  // 初始加载
  useEffect(() => {
    loadMenuItems()

    // 移除自动刷新功能，避免在用户选择菜单权限时被刷新掉
    // 用户可以手动点击刷新按钮来刷新菜单数据
  }, [])

  if (loading) {
    return (
      <div className="space-y-2">
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {showBadges && (
        <div className="flex justify-between items-center">
          <div className="space-x-2">
            <Badge variant="outline">已选择 {selectedMenus.length} 项</Badge>
            <Badge variant="outline">共 {getAllMenuIds(filteredMenuItems).length} 项</Badge>
          </div>
          <div className="space-x-2">
            <button
              type="button"
              className="text-xs text-blue-500 hover:underline"
              onClick={selectAllMenus}
            >
              全选
            </button>
            <button
              type="button"
              className="text-xs text-blue-500 hover:underline"
              onClick={deselectAllMenus}
            >
              取消全选
            </button>
          </div>
        </div>
      )}

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">菜单权限</CardTitle>
          <CardDescription>
            选择此角色可以访问的菜单项
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {showSearch && (
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索菜单..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={refreshMenus}
                  disabled={refreshing}
                >
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            )}

            <ScrollArea className={`pr-4`} style={{ height: maxHeight }}>
              {filteredMenuItems.length > 0 ? (
                filteredMenuItems.map(item => renderMenuItem(item))
              ) : (
                <div className="py-6 text-center text-muted-foreground">
                  {searchQuery ? '没有找到匹配的菜单项' : '没有可用的菜单项'}
                </div>
              )}
            </ScrollArea>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
