import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "@/components/ui/use-toast"

interface UserEnableDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  userId: string
  userName: string
  onSuccess: (userId: string) => void
}

export function UserEnableDialog({
  open,
  onOpenChange,
  userId,
  userName,
  onSuccess,
}: UserEnableDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch(`/api/customers/${userId}/enable`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "操作成功",
          description: "用户已成功启用",
        })
        
        // 调用成功回调
        onSuccess(userId)
        
        // 关闭对话框
        onOpenChange(false)
      } else {
        toast({
          title: "操作失败",
          description: data.message || "启用用户失败",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('启用用户错误:', error)
      toast({
        title: "操作失败",
        description: "启用用户时发生错误，请稍后重试",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>启用用户</DialogTitle>
            <DialogDescription>
              您正在启用用户 <span className="font-semibold">{userName}</span>。启用后，该用户将可以正常登录和使用系统。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="mt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "处理中..." : "确认启用"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
