"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { DashboardHeader } from "@/components/dashboard-header"
import { DashboardShell } from "@/components/dashboard-shell"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, ArrowLeft, Search, Users } from "lucide-react"

interface CustomerNotFoundProps {
  customerId: string
  errorMessage?: string
}

export function CustomerNotFound({ customerId, errorMessage }: CustomerNotFoundProps) {
  return (
    <DashboardShell>
      <DashboardHeader heading="用户不存在">
        <Button variant="outline" asChild>
          <Link href="/accounts/customer">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回用户列表
          </Link>
        </Button>
      </DashboardHeader>
      
      <Card className="border-red-200">
        <CardHeader className="bg-red-50 border-b border-red-100">
          <CardTitle className="text-red-700 flex items-center">
            <AlertCircle className="mr-2 h-5 w-5" />
            未找到用户信息
          </CardTitle>
          <CardDescription className="text-red-600">
            系统无法找到您请求的用户信息
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>查询错误</AlertTitle>
            <AlertDescription>
              未找到ID为 <span className="font-mono bg-red-100 px-1 rounded">{customerId}</span> 的用户信息
              {errorMessage && (
                <div className="mt-2 text-sm">
                  错误详情: {errorMessage}
                </div>
              )}
            </AlertDescription>
          </Alert>
          
          <div className="space-y-4">
            <h3 className="text-lg font-medium">可能的原因:</h3>
            <ul className="list-disc pl-5 space-y-2">
              <li>用户ID或用户名输入错误</li>
              <li>用户可能已被删除</li>
              <li>您可能没有权限查看此用户</li>
              <li>系统内部错误</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6">建议操作:</h3>
            <div className="grid gap-4 mt-4">
              <Button asChild>
                <Link href="/accounts/customer">
                  <Users className="mr-2 h-4 w-4" />
                  返回用户列表
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href="/accounts/customer?search=true">
                  <Search className="mr-2 h-4 w-4" />
                  搜索用户
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}
