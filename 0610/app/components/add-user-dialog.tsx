"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "@/components/ui/use-toast"
import { useAsyncError } from "@/hooks/use-async-error"
import { PasswordInput } from "@/components/password-input"
import { usePasswordRules } from "@/hooks/use-password-rules"

interface AddUserDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUserAdded: (user: any) => void
}

export function AddUserDialog({ open, onOpenChange, onUserAdded }: AddUserDialogProps) {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: "",
    accountType: "personal",
  })
  const { isLoading: isSubmitting, handleAsync } = useAsyncError()
  const { rules: passwordRules } = usePasswordRules()

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.username || !formData.email || !formData.password) {
      toast({
        title: "表单不完整",
        description: "请填写必填字段（用户名、邮箱和密码）",
        variant: "destructive",
      })
      return
    }

    // 不再使用name字段，在后端会自动使用username作为昵称
    const submitData = {
      ...formData
    }

    // 验证密码
    if (formData.password) {
      // 密码一致性验证
      if (formData.password !== formData.confirmPassword) {
        toast({
          title: "密码不匹配",
          description: "两次输入的密码不一致，请重新输入",
          variant: "destructive",
        })
        return
      }

      // 密码强度验证 - 使用系统统一的密码规则
      const hasLowerCase = /[a-z]/.test(formData.password)
      const hasUpperCase = /[A-Z]/.test(formData.password)
      const hasNumber = /[0-9]/.test(formData.password)
      const hasSpecialChar = /[^A-Za-z0-9]/.test(formData.password)
      const isLongEnough = formData.password.length >= passwordRules.minLength

      const isValid =
        isLongEnough &&
        (hasUpperCase || !passwordRules.requireUppercase) &&
        (hasLowerCase || !passwordRules.requireLowercase) &&
        (hasNumber || !passwordRules.requireNumber) &&
        (hasSpecialChar || !passwordRules.requireSpecialChar)

      if (!isValid) {
        toast({
          title: "密码不符合要求",
          description: "请确保密码符合所有安全要求",
          variant: "destructive",
        })
        return
      }
    }

    await handleAsync(async () => {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
        credentials: 'include',
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "添加成功",
          description: `用户 ${submitData.username} 已成功添加`,
          variant: "success",
          className: "bg-green-500 text-white border-green-600"
        })

        // 重置表单
        setFormData({
          username: "",
          email: "",
          phone: "",
          password: "",
          confirmPassword: "",
          accountType: "personal",
        })

        // 关闭对话框
        onOpenChange(false)

        // 通知父组件
        // 确保传递正确的用户数据格式
        const userData = data.data || { id: Date.now().toString() }
        onUserAdded(userData)
      } else {
        throw new Error(data.message || "无法添加用户，请稍后重试")
      }
    }, {
      context: { action: '添加用户', userData: { username: formData.username, email: formData.email } },
      onError: () => {
        // 即使出错也关闭对话框，因为用户可能已经创建成功
        onOpenChange(false)
        onUserAdded({ id: Date.now().toString() })
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>添加新用户</DialogTitle>
          <DialogDescription>
            填写以下信息创建新用户账户。创建后，系统将自动发送邮件通知用户。
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="username" className="text-right">
                用户名 <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3 space-y-1">
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => handleChange("username", e.target.value)}
                  required
                />
                <p className="text-xs text-gray-500">用户的登录名称</p>
              </div>
            </div>
            {/* 用户名称字段已移除，将默认使用用户名作为昵称 */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                邮箱 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                电话
              </Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleChange("phone", e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                密码 <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <PasswordInput
                  id="password"
                  label=""
                  value={formData.password}
                  onChange={(value) => handleChange("password", value)}
                  placeholder="请输入密码"
                  showStrengthIndicator={true}
                  rules={passwordRules}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="confirmPassword" className="text-right">
                确认密码 <span className="text-red-500">*</span>
              </Label>
              <div className="col-span-3">
                <PasswordInput
                  id="confirmPassword"
                  label=""
                  value={formData.confirmPassword}
                  onChange={(value) => handleChange("confirmPassword", value)}
                  placeholder="请再次输入密码"
                  showStrengthIndicator={false}
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="accountType" className="text-right">
                账户类型
              </Label>
              <Select
                value={formData.accountType}
                onValueChange={(value) => handleChange("accountType", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择账户类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">个人账户</SelectItem>
                  <SelectItem value="enterprise">企业账户</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSubmitting}>
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "添加中..." : "添加用户"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
