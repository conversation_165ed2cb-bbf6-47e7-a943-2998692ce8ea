'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import dialog from '@/lib/utils/dialog'

/**
 * 对话框示例组件
 * 展示如何使用对话框工具函数替代 alert 函数
 */
export default function DialogExample() {
  const [activeTab, setActiveTab] = useState('old')

  // 使用原生 alert 函数
  const handleOldAlert = () => {
    alert('这是一个使用原生 alert 函数的提示信息')
  }

  const handleOldSuccess = () => {
    alert('操作成功！')
  }

  const handleOldError = () => {
    alert('操作失败：网络连接错误')
  }

  // 使用对话框工具函数
  const handleNewAlert = () => {
    dialog.showMessage('这是一个使用对话框工具函数的提示信息')
  }

  const handleNewSuccess = () => {
    dialog.showSuccess('操作成功！')
  }

  const handleNewError = () => {
    dialog.showError('操作失败：网络连接错误')
  }

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>对话框示例</CardTitle>
        <CardDescription>
          展示如何使用对话框工具函数替代原生 alert 函数，提供更好的用户体验
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="old">原生 Alert</TabsTrigger>
            <TabsTrigger value="new">现代对话框</TabsTrigger>
          </TabsList>
          <TabsContent value="old" className="mt-4">
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                原生 alert 函数会阻塞用户界面，用户体验较差。点击下面的按钮查看效果：
              </p>
              <div className="flex flex-col gap-2 sm:flex-row">
                <Button onClick={handleOldAlert}>显示提示信息</Button>
                <Button onClick={handleOldSuccess} variant="outline">
                  显示成功信息
                </Button>
                <Button onClick={handleOldError} variant="destructive">
                  显示错误信息
                </Button>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="new" className="mt-4">
            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                现代对话框不会阻塞用户界面，提供更好的用户体验。点击下面的按钮查看效果：
              </p>
              <div className="flex flex-col gap-2 sm:flex-row">
                <Button onClick={handleNewAlert}>显示提示信息</Button>
                <Button onClick={handleNewSuccess} variant="outline">
                  显示成功信息
                </Button>
                <Button onClick={handleNewError} variant="destructive">
                  显示错误信息
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-gray-500">
          注意：此示例仅用于展示，实际应用中应根据具体需求选择合适的对话框组件。
        </p>
      </CardFooter>
    </Card>
  )
}
