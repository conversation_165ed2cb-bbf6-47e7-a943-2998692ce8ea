'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { SelectItem } from '@/components/ui/select'

interface SmsTemplateFallbackProps {
  isLoading: boolean
  templates: any[]
  type: 'text' | 'video' | 'flash'
}

/**
 * 短信模板回退组件
 * 
 * 当无法获取短信模板时，提供友好的界面和提示
 * 
 * @example
 * ```tsx
 * <SelectContent>
 *   <SmsTemplateFallback 
 *     isLoading={loadingTemplates} 
 *     templates={textSmsTemplates} 
 *     type="text" 
 *   />
 * </SelectContent>
 * ```
 */
export function SmsTemplateFallback({
  isLoading,
  templates,
  type
}: SmsTemplateFallbackProps) {
  // 根据类型显示不同的提示文本
  const getTypeText = () => {
    switch (type) {
      case 'text':
        return '文本短信'
      case 'video':
        return '视频短信'
      case 'flash':
        return '闪信'
      default:
        return '短信'
    }
  }

  if (isLoading) {
    return (
      <SelectItem value="loading" disabled>
        加载中...
      </SelectItem>
    )
  }

  if (templates.length === 0) {
    return (
      <div className="px-2 py-4 text-center">
        <p className="text-gray-500 text-sm">
          暂无可用的{getTypeText()}模板
        </p>
        <p className="text-gray-400 text-xs mt-1">
          系统将自动调整设置
        </p>
      </div>
    )
  }

  return null
}
