import logger from '@/lib/utils/logger';

/**
 * 根布局组件
 * 为整个应用提供基础布局结构和全局配置
 *
 * 特性：
 * - 配置网站元数据（标题、描述等）
 * - 集成主题切换功能
 * - 提供认证上下文
 * - 支持 Toast 通知
 * - 使用 Inter 字体
 *
 * @example
 * ```tsx
 * // 页面会自动被包裹在这个布局中
 * <RootLayout>
 *   <YourPage />
 * </RootLayout>
 * ```
 */

import "@/app/globals.css"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
// import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { TooltipProvider } from "@/components/ui/tooltip"
import Script from "next/script"
import { RoutePrefetcher } from "@/components/route-prefetcher"
import { SidebarProvider } from "@/components/ui/sidebar"
import { getMetadata } from "@/lib/get-metadata"
import { SystemInitializer } from "@/app/components/system-initializer"
import { Providers } from "./providers"
import { getServerSession } from "next-auth/next"
import { options } from "./api/auth/[...nextauth]/options"
import { LayoutProcessPolyfill } from "./layout-process-polyfill"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  preload: true,
  adjustFontFallback: true,
  fallback: ['system-ui', 'arial'],
})

// 动态生成元数据
export async function generateMetadata(): Promise<Metadata> {
  // 获取动态元数据
  const dynamicMetadata = await getMetadata()

  // 添加静态元数据
  return {
    ...dynamicMetadata,
    generator: "v0.dev",
    authors: [{ name: "Your Company" }],
    manifest: "/manifest.json",
    other: {
      ...dynamicMetadata.other,
      "apple-mobile-web-app-capable": "yes",
      "apple-mobile-web-app-status-bar-style": "default",
      "format-detection": "telephone=no",
      "mobile-web-app-capable": "yes",
    }
  }
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "#0f172a" }
  ],
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: "cover",
}

interface RootLayoutProps {
  /** 子组件，将被包裹在布局中 */
  children: React.ReactNode
}

export default async function RootLayout({ children }: RootLayoutProps) {
  // 获取服务器端会话
  const session = await getServerSession(options);

  return (
    <html
      lang="zh-CN"
      suppressHydrationWarning
    >
      <head>
        <link
          rel="preload"
          href="/dashboard"
          as="document"
        />
        <link
          rel="preload"
          href="/tasks"
          as="document"
        />
        <link
          rel="preconnect"
          href={process.env.NEXT_PUBLIC_API_URL}
          crossOrigin="anonymous"
        />
        <link
          rel="apple-touch-icon"
          href="/icons/icon-192x192.png"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="外呼系统" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <Script
          id="init-env"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // 初始化环境变量
              window.ENV = {
                NEXT_PUBLIC_API_BASE_URL: "${process.env.NEXT_PUBLIC_API_BASE_URL || '/api'}",
                NEXT_PUBLIC_VIDEO_CALL_ORG_CODE: "${process.env.NEXT_PUBLIC_VIDEO_CALL_ORG_CODE || 'XUNMENGdorg'}",
                NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME: "${process.env.NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME || 'XUNMENG001'}",
                NEXT_PUBLIC_VIDEO_CALL_APP_ID: "${process.env.NEXT_PUBLIC_VIDEO_CALL_APP_ID || ''}",
                NODE_ENV: "${process.env.NODE_ENV || 'production'}"
              };
            `
          }}
        />
        <Script
          id="register-sw"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js').then(
                    function(registration) {
                      // 使用安全的日志方式
                      const isDev = window.location.hostname === 'localhost' ||
                                   window.location.hostname === '127.0.0.1' ||
                                   window.location.hostname.includes('.local');
                      if (isDev) {
                        logger.log('ServiceWorker registration successful');
                      }
                    },
                    function(err) {
                      // 使用安全的错误日志方式
                      const errMsg = err && err.message ? err.message : 'Unknown error';
                      logger.error('ServiceWorker registration failed: ' + errMsg);
                    }
                  );
                });
              }
            `,
          }}
        />

        {/* 添加安全处理脚本 */}
        <Script
          id="security-enhancements"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              // 在生产环境中禁用控制台日志
              const isDev = window.location.hostname === 'localhost' ||
                           window.location.hostname === '127.0.0.1' ||
                           window.location.hostname.includes('.local');

              if (!isDev) {
                // 保存原始控制台方法的引用
                const originalConsole = {
                  log: console.log,
                  info: console.info,
                  warn: console.warn,
                  debug: console.debug
                };

                // 重写非关键控制台方法
                console.log = console.info = console.debug = function() {};

                // 保留警告和错误，但过滤敏感信息
                console.warn = function(...args) {
                  // 过滤掉可能包含敏感信息的参数
                  const safeArgs = args.map(arg => {
                    if (typeof arg === 'object' && arg !== null) {
                      return '[Object filtered for security]';
                    }
                    if (typeof arg === 'string' && (
                      arg.includes('@') ||
                      arg.includes('token') ||
                      arg.includes('password') ||
                      arg.includes('admin')
                    )) {
                      return '[String filtered for security]';
                    }
                    return arg;
                  });
                  originalConsole.warn.apply(console, safeArgs);
                };

                // 错误日志仍然保留，但过滤敏感信息
                const originalError = console.error;
                console.error = function(...args) {
                  // 过滤掉可能包含敏感信息的参数
                  const safeArgs = args.map(arg => {
                    if (typeof arg === 'object' && arg !== null) {
                      return '[Object filtered for security]';
                    }
                    if (typeof arg === 'string' && (
                      arg.includes('@') ||
                      arg.includes('token') ||
                      arg.includes('password') ||
                      arg.includes('admin')
                    )) {
                      return '[String filtered for security]';
                    }
                    return arg;
                  });
                  originalError.apply(console, safeArgs);
                };
              }
            `,
          }}
        />
      </head>
      <body className={`${inter.className} overflow-x-hidden`}>
        <Providers session={session}>
          <SidebarProvider>
            <TooltipProvider>
              <LayoutProcessPolyfill />
              <RoutePrefetcher />
              <SystemInitializer />
              {children}
              <Toaster />
            </TooltipProvider>
          </SidebarProvider>
        </Providers>
      </body>
    </html>
  )
}

import './globals.css'