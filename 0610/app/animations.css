/* 渐变动画 */
@keyframes gradient-x {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-y {
  0%,
  100% {
    background-position: 50% 0%;
  }
  50% {
    background-position: 50% 100%;
  }
}

@keyframes gradient-xy {
  0% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

.animate-gradient-y {
  animation: gradient-y 15s ease infinite;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

