import Link from "next/link"

export default function NotificationDetail({ params }: { params: { id: string } }) {
  // 示例通知数据
  const notifications = [
    {
      id: 1,
      title: "系统更新",
      message: "系统已更新到最新版本 v2.3.0",
      time: "10分钟前",
      read: false,
      type: "system",
      color: "blue",
      content: `
        <p>尊敬的用户：</p>
        <p>我们很高兴地通知您，外呼管理系统已成功更新到最新版本 v2.3.0。此次更新包含多项重要功能改进和性能优化，旨在提升您的使用体验。</p>
        <h4>更新内容：</h4>
        <ul>
          <li>全新的通知中心，支持多种类型的通知管理</li>
          <li>优化的任务分配算法，提高外呼效率</li>
          <li>改进的用户界面，更加直观和易用</li>
          <li>修复了多个已知问题，提高系统稳定性</li>
        </ul>
        <p>如需了解更多详情，请查看<a href="#">更新日志</a>。</p>
        <p>如您在使用过程中遇到任何问题，请随时<a href="#">联系我们</a>。</p>
        <p>感谢您对我们的支持！</p>
      `,
    },
    {
      id: 2,
      title: "新任务分配",
      message: "您有一个新的外呼任务已分配",
      time: "30分钟前",
      read: false,
      type: "task",
      color: "green",
      content: `
        <p>尊敬的用户：</p>
        <p>您有一个新的外呼任务已分配给您。请及时处理。</p>
        <h4>任务详情：</h4>
        <ul>
          <li>任务编号：TK-20230615-001</li>
          <li>任务类型：客户回访</li>
          <li>目标客户数：120</li>
          <li>预计完成时间：2023-06-20</li>
        </ul>
        <p>您可以在<a href="#">任务管理</a>页面查看详细信息并开始处理。</p>
      `,
    },
  ]

  const notification = notifications.find((n) => n.id === Number.parseInt(params.id)) || notifications[0]

  // 获取通知类型的颜色
  const getNotificationStyle = (type: string, color: string) => {
    const colorMap: Record<string, { bg: string; text: string; border: string; lightBg: string }> = {
      blue: { bg: "bg-blue-100", text: "text-blue-700", border: "border-blue-300", lightBg: "bg-blue-50" },
      green: { bg: "bg-green-100", text: "text-green-700", border: "border-green-300", lightBg: "bg-green-50" },
      red: { bg: "bg-red-100", text: "text-red-700", border: "border-red-300", lightBg: "bg-red-50" },
      purple: { bg: "bg-purple-100", text: "text-purple-700", border: "border-purple-300", lightBg: "bg-purple-50" },
      orange: { bg: "bg-orange-100", text: "text-orange-700", border: "border-orange-300", lightBg: "bg-orange-50" },
      teal: { bg: "bg-teal-100", text: "text-teal-700", border: "border-teal-300", lightBg: "bg-teal-50" },
    }

    return colorMap[color] || colorMap.blue
  }

  const { bg, text, border, lightBg } = getNotificationStyle(notification.type, notification.color)

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* 侧边栏 */}
      <div className="w-64 bg-white border-r shadow-sm hidden md:block">
        <div className="h-16 flex items-center border-b px-6">
          <span className="font-bold text-lg">外呼管理系统</span>
        </div>
        <div className="p-4">
          <nav className="space-y-2">
            <Link href="/dashboard" className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-500"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <rect width="7" height="9" x="3" y="3" rx="1" />
                <rect width="7" height="5" x="14" y="3" rx="1" />
                <rect width="7" height="9" x="14" y="12" rx="1" />
                <rect width="7" height="5" x="3" y="16" rx="1" />
              </svg>
              <span>仪表盘</span>
            </Link>
            <div>
              <button className="flex items-center justify-between w-full px-3 py-2 rounded-md hover:bg-gray-100">
                <div className="flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                    <circle cx="9" cy="7" r="4" />
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                    <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  </svg>
                  <span>账户管理</span>
                </div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <polyline points="9 18 15 12 9 6" />
                </svg>
              </button>
            </div>
            <div>
              <button className="flex items-center justify-between w-full px-3 py-2 rounded-md hover:bg-gray-100">
                <div className="flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-gray-500"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="17 8 12 3 7 8" />
                    <line x1="12" x2="12" y1="3" y2="15" />
                  </svg>
                  <span>任务管理</span>
                </div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <polyline points="9 18 15 12 9 6" />
                </svg>
              </button>
            </div>
            <Link
              href="/notifications-with-sidebar"
              className="flex items-center gap-2 px-3 py-2 rounded-md bg-blue-50 text-blue-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3Zm-8.27 4a2 2 0 0 1-3.46 0" />
              </svg>
              <span>通知中心</span>
            </Link>
            <Link href="/settings" className="flex items-center gap-2 px-3 py-2 rounded-md hover:bg-gray-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-500"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
              <span>系统设置</span>
            </Link>
          </nav>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1">
        {/* 顶部导航栏 */}
        <header className="h-16 bg-white border-b flex items-center justify-between px-6 shadow-sm">
          <button className="md:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <div className="flex items-center gap-4">
            <button className="p-1 rounded-full hover:bg-gray-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
            </button>
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white">
              管
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="p-6">
          <div className="mb-4">
            <Link href="/notifications-with-sidebar" className="text-blue-600 hover:underline flex items-center gap-1">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
              </svg>
              返回通知列表
            </Link>
          </div>

          {/* 通知详情 */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className={`${lightBg} px-6 py-4 border-b ${border}`}>
              <div className="flex items-center gap-3">
                <div className={`rounded-full p-2 ${bg} ${text}`}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3Zm-8.27 4a2 2 0 0 1-3.46 0" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-xl font-bold">{notification.title}</h1>
                  <p className="text-gray-500 text-sm">{notification.time}</p>
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: notification.content }}></div>
            </div>
            <div className="px-6 py-4 bg-gray-50 border-t flex justify-between">
              <button className="px-4 py-2 rounded-md border hover:bg-gray-100">标记为已读</button>
              <button className={`px-4 py-2 rounded-md ${bg} ${text}`}>确认收到</button>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

