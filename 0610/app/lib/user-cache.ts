/**
 * 用户信息缓存服务
 * 
 * 用于缓存常用用户信息，减少数据库查询，提高性能
 */

import { LRUCache } from 'lru-cache'

// 定义缓存选项
const options = {
  // 最大缓存项数
  max: 500,
  
  // 缓存项有效期（10分钟）
  ttl: 1000 * 60 * 10,
  
  // 是否允许过期项被自动删除
  allowStale: false,
  
  // 更新缓存项时是否更新过期时间
  updateAgeOnGet: true,
  
  // 缓存项大小计算函数
  sizeCalculation: (value: any, key: string) => {
    // 简单估算缓存项大小（字节）
    return JSON.stringify(value).length + key.length
  },
  
  // 缓存最大大小（约5MB）
  maxSize: 5 * 1024 * 1024,
}

// 创建LRU缓存实例
const userCache = new LRUCache(options)

// 用户ID缓存（用户名 -> 用户ID）
const userIdCache = new LRUCache(options)

/**
 * 用户缓存服务
 */
export class UserCacheService {
  /**
   * 获取缓存的用户信息
   * @param id 用户ID
   * @returns 用户信息或null
   */
  static getUser(id: string) {
    return userCache.get(id) || null
  }
  
  /**
   * 缓存用户信息
   * @param id 用户ID
   * @param userData 用户数据
   */
  static setUser(id: string, userData: any) {
    userCache.set(id, userData)
    
    // 同时缓存用户名到ID的映射
    if (userData.username) {
      userIdCache.set(userData.username, id)
    }
  }
  
  /**
   * 通过用户名获取用户ID
   * @param username 用户名
   * @returns 用户ID或null
   */
  static getUserIdByUsername(username: string) {
    return userIdCache.get(username) || null
  }
  
  /**
   * 设置用户名到ID的映射
   * @param username 用户名
   * @param id 用户ID
   */
  static setUserIdByUsername(username: string, id: string) {
    userIdCache.set(username, id)
  }
  
  /**
   * 清除用户缓存
   * @param id 用户ID
   */
  static clearUser(id: string) {
    const userData = userCache.get(id)
    userCache.delete(id)
    
    // 同时清除用户名到ID的映射
    if (userData && userData.username) {
      userIdCache.delete(userData.username)
    }
  }
  
  /**
   * 清除所有缓存
   */
  static clearAll() {
    userCache.clear()
    userIdCache.clear()
  }
}
