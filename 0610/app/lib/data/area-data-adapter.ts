/**
 * area-data 库适配器
 * 将 area-data 库的数据结构转换为当前项目使用的格式
 */
import { pca, pcaa } from 'area-data';
import logger from '@/lib/utils/logger';

// 定义区域类型
export interface Region {
  label: string;
  value: string;
}

/**
 * 获取所有省份
 * @returns 省份列表
 */
export const provinces: Region[] = Object.entries(pca['86']).map(([code, name]) => ({
  label: name,
  value: code,
}));

/**
 * 根据省份代码获取城市列表
 * @param province 省份代码
 * @returns 城市列表
 */
export function getCitiesByProvince(province: string): Region[] {
  // 如果省份不存在，返回空数组
  if (!pcaa[province]) {
    logger.warn(`省份代码 ${province} 不存在`);
    return [];
  }

  return Object.entries(pcaa[province]).map(([code, name]) => ({
    label: name,
    value: code,
  }));
}

/**
 * 根据省份代码和城市代码获取区县列表
 * @param province 省份代码
 * @param city 城市代码
 * @returns 区县列表
 */
export function getDistrictsByCity(province: string, city: string): Region[] {
  // 如果城市不存在，返回空数组
  if (!pcaa[city]) {
    logger.warn(`城市代码 ${city} 不存在`);
    return [];
  }

  return Object.entries(pcaa[city]).map(([code, name]) => ({
    label: name,
    value: code,
  }));
}

/**
 * 根据代码获取省份名称
 * @param code 省份代码
 * @returns 省份名称
 */
export function getProvinceName(code: string): string {
  return pca['86'][code] || '';
}

/**
 * 根据代码获取城市名称
 * @param provinceCode 省份代码
 * @param cityCode 城市代码
 * @returns 城市名称
 */
export function getCityName(provinceCode: string, cityCode: string): string {
  return pcaa[provinceCode]?.[cityCode] || '';
}

/**
 * 根据代码获取区县名称
 * @param cityCode 城市代码
 * @param districtCode 区县代码
 * @returns 区县名称
 */
export function getDistrictName(cityCode: string, districtCode: string): string {
  return pcaa[cityCode]?.[districtCode] || '';
}
