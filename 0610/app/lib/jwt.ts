import { sign, verify } from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'

export interface JwtPayload {
  userId: string
  username: string
  role: string
  iat?: number
  exp?: number
}

export function verifyJwtToken(token: string) {
  try {
    const payload = verify(token, JWT_SECRET)
    return {
      valid: true,
      payload
    }
  } catch (error) {
    console.error('JWT验证错误:', error)
    return {
      valid: false,
      payload: null
    }
  }
}

export function createJwtToken(payload: any) {
  const token = sign(payload, JWT_SECRET, {
    algorithm: 'HS256',
    expiresIn: '24h'
  })
  return token
}