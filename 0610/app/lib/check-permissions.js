import logger from '@/lib/utils/logger';

/**
 * 权限检查工具
 * 此脚本用于验证用户权限是否正确配置
 * 
 * 使用方法: node check-permissions.js
 */

// 导入PrismaClient
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkAdminPermissions() {
  logger.log('===== 检查管理员权限配置 =====');
  
  try {
    const adminRole = await prisma.role.findUnique({
      where: { code: 'ADMIN' },
      select: {
        id: true,
        code: true,
        name: true,
        permissions: true
      }
    });
    
    if (!adminRole) {
      logger.error('错误: 未找到ADMIN角色');
      return;
    }
    
    logger.log(`角色名称: ${adminRole.name}`);
    logger.log(`角色ID: ${adminRole.id}`);
    logger.log(`权限总数: ${adminRole.permissions.length}`);
    
    // 检查关键权限
    const requiredPermissions = [
      'role:create', 'role:update', 'role:delete', 'role:view',
      'roles:create', 'roles:update', 'roles:delete', 'roles:view',
      'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE', 'ROLE_VIEW', 'ROLE_MANAGE',
      '*'
    ];
    
    logger.log('\n关键权限检查:');
    for (const perm of requiredPermissions) {
      const hasPermission = adminRole.permissions.includes(perm);
      logger.log(`- ${perm}: ${hasPermission ? '✅ 已配置' : '❌ 未配置'}`);
    }
    
    logger.log('\n其他配置的权限:');
    const otherPerms = adminRole.permissions.filter(p => !requiredPermissions.includes(p));
    if (otherPerms.length > 0) {
      otherPerms.forEach(p => logger.log(`- ${p}`));
    } else {
      logger.log('无其他权限');
    }
  } catch (error) {
    logger.error('检查权限时出错:', error);
  }
}

async function checkAllUsersPermissions() {
  logger.log('\n===== 检查所有用户的角色和权限 =====');
  
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        roleCode: true,
        permissions: true,
        role: {
          select: {
            code: true,
            name: true,
            permissions: true
          }
        }
      }
    });
    
    logger.log(`用户总数: ${users.length}`);
    
    for (const user of users) {
      logger.log(`\n用户: ${user.username}`);
      logger.log(`角色: ${user.role?.name || '未设置'} (${user.roleCode})`);
      logger.log(`用户直接权限: ${user.permissions.length}`);
      logger.log(`角色权限: ${user.role?.permissions.length || 0}`);
      
      // 检查是否有关键权限
      const allPermissions = [...(user.permissions || []), ...(user.role?.permissions || [])];
      const canCreateRole = 
        allPermissions.includes('role:create') || 
        allPermissions.includes('roles:create') || 
        allPermissions.includes('ROLE_CREATE') ||
        allPermissions.includes('ROLE_MANAGE') ||
        allPermissions.includes('*');
        
      logger.log(`可以创建角色: ${canCreateRole ? '✅ 是' : '❌ 否'}`);
    }
  } catch (error) {
    logger.error('检查用户权限时出错:', error);
  }
}

async function main() {
  try {
    await checkAdminPermissions();
    await checkAllUsersPermissions();
  } catch (error) {
    logger.error('执行检查时出错:', error);
  } finally {
    // 关闭数据库连接
    await prisma.$disconnect();
  }
}

// 运行检查
main(); 