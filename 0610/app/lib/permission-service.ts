import { prisma } from "@/lib/prisma";
import { hasResourcePermission } from "@/lib/abac/permission";
import { JWT } from "next-auth/jwt";
import logger from '@/lib/utils/logger';

/**
 * 统一权限服务
 * 兼容RBAC和ABAC权限检查方式，作为过渡层使用
 */
export class PermissionService {
  /**
   * 统一权限检查入口
   * @param userId 用户ID
   * @param permissionKey 权限键(如 'role:create')
   * @param context 权限上下文
   */
  static async hasPermission(
    userId: string, 
    permissionKey: string,
    context?: Record<string, any>
  ): Promise<boolean> {
    try {
      logger.log(`[权限检查] 用户(${userId})请求权限(${permissionKey})`, context);
      
      // 检查用户是否存在
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          roleCode: true,
          permissions: true,
          role: {
            select: {
              code: true,
              permissions: true
            }
          }
        }
      });

      if (!user) {
        logger.log(`[权限检查] 用户(${userId})不存在，拒绝访问`);
        return false;
      }

      // 1. 超级管理员检查 - 权限通配符
      if (user.permissions?.includes('*') || user.role?.permissions.includes('*')) {
        logger.log(`[权限检查] 用户(${userId})具有超级管理员权限，允许访问`);
        return true;
      }

      // 2. 直接权限检查 - 精确匹配权限字符串
      if (user.permissions?.includes(permissionKey) || user.role?.permissions.includes(permissionKey)) {
        logger.log(`[权限检查] 用户(${userId})直接拥有权限(${permissionKey})，允许访问`);
        return true;
      }
      
      // 3. 资源:操作格式检查 - 支持roles:create和role:create两种格式
      const [resource, operation] = permissionKey.split(':');
      const alternativeKey = resource.endsWith('s') 
        ? `${resource.slice(0, -1)}:${operation}` 
        : `${resource}s:${operation}`;
        
      if (user.permissions?.includes(alternativeKey) || user.role?.permissions.includes(alternativeKey)) {
        logger.log(`[权限检查] 用户(${userId})拥有替代权限(${alternativeKey})，允许访问`);
        return true;
      }
      
      // 4. ABAC方式检查 - 如果提供了context
      if (context?.resourceCode && context?.operationCode) {
        // 创建简单token对象以复用现有函数
        const simpleToken: JWT = { sub: userId };
        const abacResult = await hasResourcePermission(
          simpleToken, 
          context.resourceCode, 
          context.operationCode
        );
        
        logger.log(`[权限检查] 用户(${userId})ABAC检查结果: ${abacResult}`);
        return abacResult;
      }
      
      // 5. 角色管理特殊检查 - 兼容旧的角色管理权限定义
      if (
        permissionKey === 'role:create' || 
        permissionKey === 'roles:create' ||
        permissionKey === 'role:manage' ||
        permissionKey === 'roles:manage'
      ) {
        // 检查是否有ROLE_CREATE, ROLE_MANAGE权限
        const legacyPermissions = ['ROLE_CREATE', 'ROLE_MANAGE', 'ROLE_ADMIN'];
        const hasLegacyPermission = legacyPermissions.some(p => 
          user.permissions?.includes(p) || user.role?.permissions.includes(p)
        );
        
        if (hasLegacyPermission) {
          logger.log(`[权限检查] 用户(${userId})拥有旧版角色管理权限，允许访问`);
          return true;
        }
      }

      logger.log(`[权限检查] 用户(${userId})没有所需权限(${permissionKey})，拒绝访问`);
      return false;
    } catch (error) {
      logger.error("[权限检查错误]:", error);
      // 生产环境出错默认拒绝访问
      return false;
    }
  }

  /**
   * 权限检查 - 兼容路由处理程序
   * @param token JWT token
   * @param resource 资源代码
   * @param operation 操作代码
   */
  static async checkResourcePermission(
    token: JWT,
    resource: string,
    operation: string
  ): Promise<boolean> {
    if (!token || !token.sub) return false;
    
    return this.hasPermission(
      token.sub,
      `${resource}:${operation}`,
      { resourceCode: resource, operationCode: operation }
    );
  }
} 