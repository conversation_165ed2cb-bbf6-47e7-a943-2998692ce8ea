/**
 * 系统常量定义文件
 * 集中管理系统中使用的常量值
 */

// JWT密钥
export const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

// Cookie名称
export const TOKEN_NAME = "token"

// 用户状态
export const USER_STATUS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  PENDING: "pending"
}

// 用户类型
export const USER_TYPES = {
  ADMIN: "admin",
  PERSONAL: "personal",
  ENTERPRISE: "enterprise"
}

// 认证状态
export const VERIFICATION_STATUS = {
  APPROVED: "approved",
  REJECTED: "rejected",
  PENDING: "pending"
}

// 通知类型
export const NOTIFICATION_TYPES = {
  SYSTEM: "system",
  USER: "user",
  VERIFICATION: "verification",
  PAYMENT: "payment"
}

// 通知优先级
export const NOTIFICATION_PRIORITIES = {
  LOW: "low",
  MEDIUM: "medium",
  HIGH: "high"
}

// 通知状态
export const NOTIFICATION_STATUS = {
  DRAFT: "draft",
  PUBLISHED: "published",
  ARCHIVED: "archived"
}

// 角色代码
export const ROLE_CODES = {
  ADMIN: "ADMIN",
  MANAGER: "MANAGER",
  OPERATOR: "OPERATOR",
  USER: "USER",
  ENTERPRISE: "ENTERPRISE"
}

// 操作类型
export const OPERATION_TYPES = {
  CREATE: "create",
  READ: "read",
  UPDATE: "update",
  DELETE: "delete"
}

// 资源类型
export const RESOURCE_TYPES = {
  USER: "user",
  ROLE: "role",
  MENU: "menu",
  NOTIFICATION: "notification",
  VERIFICATION: "verification",
  PAYMENT: "payment"
}
