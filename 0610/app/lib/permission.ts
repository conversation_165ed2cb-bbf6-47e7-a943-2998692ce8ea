import { NextRequest } from 'next/server';
import { cookies } from 'next/headers';
import { verify } from 'jsonwebtoken';
import { prisma } from './prisma';
import logger from '@/lib/utils/logger';

// 定义JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars';

/**
 * 从请求中获取并验证token
 */
async function getTokenData(req: NextRequest) {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    return null;
  }

  try {
    const payload = verify(token, JWT_SECRET) as any;
    return payload;
  } catch (error) {
    logger.error('Token验证失败:', error);
    return null;
  }
}

/**
 * 检查用户是否有指定权限
 */
export async function checkPermission(req: NextRequest, permission: string): Promise<boolean> {
  try {
    const payload = await getTokenData(req);
    if (!payload || !payload.sub) {
      return false;
    }

    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: payload.sub },
      select: { roleCode: true }
    });

    if (!user) {
      return false;
    }

    // 检查是否为管理员角色
    if (user.roleCode && user.roleCode.toUpperCase() === 'ADMIN') {
      logger.log('用户是管理员，自动授予所有权限');
      return true;
    }

    // 获取角色权限
    const role = await prisma.role.findUnique({
      where: { code: user.roleCode },
      select: { permissions: true }
    });

    if (!role) {
      return false;
    }

    // 检查是否有所需权限
    return role.permissions.includes('*') || role.permissions.includes(permission);
  } catch (error) {
    logger.error('权限检查失败:', error);
    return false;
  }
}