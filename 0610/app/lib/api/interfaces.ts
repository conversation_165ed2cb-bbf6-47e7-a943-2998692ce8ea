/**
 * API接口类型定义
 */

import type { User, UserData } from "@/hooks/use-auth"

// 通用API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

// 分页数据格式
export interface PaginatedData<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  pageSize?: number
}

// 登录参数
export interface LoginParams {
  identifier: string
  password: string
  remember?: boolean
}

// 登录结果
export interface LoginResponse {
  token: string
  user: UserData
}

// 用户信息
export type UserInfo = UserData

// 添加或修改 Task 接口，确保包含所需字段

export interface Task {
  id: string
  name: string
  type: string
  content: string
  importTime: string
  startTime: string
  progress: number
  status: string
  completionTime: string | null
  creator: string
  // 可以添加其他字段
}

// 创建任务参数
export interface TaskCreateParams {
  name: string
  content: string
  callType: string
  startTime: string
  resource: string
  phoneNumber?: string
  smsType?: string
  smsTemplate?: string
}

// 客户信息
export interface Customer {
  id: string
  name: string
  phone: string
  status: string
  balance: number
  createdAt: string
  lastCallTime?: string
  remarks?: string
  otherInfo?: Array<{ title: string; value: string }>
}

// 充值记录
export interface RechargeRecord {
  id: string
  customerId: string
  amount: number
  balance: number
  paymentMethod: string
  operator: string
  createdAt: string
  remarks?: string
}

// 5G视频外呼API接口类型定义
export interface VideoCallApiConfig {
  orgCode: string
  aesKey: string
  aesIv: string
  loginName: string
  appId: string
}

// 外呼任务创建参数
export interface VideoCallTaskParams {
  taskGroupId?: string
  orgCode: string
  loginName: string
  name: string
  mediaType: string
  videoBotId: string
  shuntAudioBotId?: string
  isVoiceDivide?: boolean
  audioBotId?: string
  deliverList: Array<{
    name?: string
    phone: string
    otherInfo?: Array<{ title: string; value: string }>
  }>
  isRelateSendMessage?: boolean
  msgTemplateId?: string
  msgTitle?: string
  sendMessagePhase?: number
  isRelateVideoMessage?: boolean
  videoMessageTemplateId?: string
  sendVideoMessagePhase?: number
  calloutMode?: number
  sendFlashMessage?: boolean
  flashMessageTemplateId?: string
  blackListGroups?: Array<{ groupId: string; groupName: string }>
  encryptType?: number
}

// 外呼任务创建响应
export interface VideoCallTaskResult {
  taskId: string
  batchId?: string
}

// 外呼结果回执
export interface VideoCallResult {
  taskGroupId?: string
  mediaDeliverId: string
  name: string
  type: number
  templateId: string
  callId: string
  phone: string
  isConnected: number
  holdingTime: number
  userName?: string
  connectType: number
  intention: string
  callStartTime: string
  callOnTime?: string
  callEndTime: string
  waitingTime: number
  comment?: Array<{ title: string; value: string }>
  callRelation?: string
  callinCompleteReason?: string[]
  ringingDuration?: number
  totalInteractNum?: number
  effectiveInteractNum?: number
  hungupNode?: string
  callNumber?: string
  fullAudioUrl?: string
  hangupBy?: string
  callDetailText?: Array<{ AI: string; CUST: string }>
  completionRate?: string
  conversationLabels?: string[]
  intentLevelAndLabels?: Array<{ intention: string; labels: string[] }>
  isFriendAdded?: number
}

// 任务统计数据
export interface TaskStatistics {
  callTotalCount: number
  callonCount: number
  callonNotCount: number
  videoCallonCount: number
  audioCallonCount: number
  totalHoldingTime: number
  videoTotalHoldingTime: number
  audioTotalHoldingTime: number
  callonRate: number
  videoCallonRate: number
  audioCallonRate: number
  avgTotalHoldingTime: number
  avgVideoHoldingTime: number
  avgAudioHoldingTime: number
  playFinishCount: number
  videoPlayFinishCount: number
  audioPlayFinishCount: number
  playFinishRate: number
  videoPlayFinishRate: number
  audioPlayFinishRate: number
}

// 短信统计数据
export interface MessageStatistics {
  smsSendCount: number
  smsSendSuccessCount: number
  smsReceivedCount: number
  videoSmsSendCount: number
  videoSmsSendSuccessCount: number
  videoSmsReceivedCount: number
  textSmsSendCount: number
  textSmsSendSuccessCount: number
  textSmsReceivedCount: number
  smsUrlAccessCount: number
  videoSmsUrlAccessCount: number
  textSmsUrlAccesCount: number
  smsSendRate: number
  smsSendSuccessRate: number
  smsReceivedRate: number
}

// 黑名单组
export interface BlacklistGroup {
  groupId: string
  groupName: string
}

// Bot信息
export interface BotInfo {
  _id: string
  name: string
  fields?: Array<{ name: string; chineseName: string }>
}

