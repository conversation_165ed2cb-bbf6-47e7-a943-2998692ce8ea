/**
 * 日志工具函数
 * 在开发环境中输出日志，在生产环境中不输出
 * 确保敏感信息不会在生产环境中泄露
 */

import { isDevelopment, isProduction } from '@/lib/utils/env';
import logger from '@/lib/utils/logger';

/**
 * 检查是否包含敏感信息的函数
 * @param obj 要检查的对象
 * @returns 是否包含敏感信息
 */
function containsSensitiveInfo(obj: any): boolean {
  if (obj === null || obj === undefined) {
    return false;
  }

  // 如果是字符串，检查是否包含敏感信息
  if (typeof obj === 'string') {
    // 检查是否包含邮箱地址
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;
    if (emailRegex.test(obj)) {
      return true;
    }

    // 检查是否包含可能的用户ID
    const idRegex = /[a-zA-Z0-9]{20,}/;
    if (idRegex.test(obj)) {
      return true;
    }

    // 检查是否包含角色信息
    const roleRegex = /(admin|ADMIN|superadmin|SUPERADMIN)/i;
    if (roleRegex.test(obj)) {
      return true;
    }
  }

  // 如果是对象或数组，递归检查
  if (typeof obj === 'object') {
    // 检查常见的敏感字段名
    const sensitiveKeys = ['email', 'password', 'token', 'secret', 'key', 'auth', 'credentials', 'permissions', 'roles'];

    for (const key in obj) {
      // 检查键名是否敏感
      if (sensitiveKeys.some(sensitiveKey => key.toLowerCase().includes(sensitiveKey))) {
        return true;
      }

      // 递归检查值
      if (containsSensitiveInfo(obj[key])) {
        return true;
      }
    }
  }

  return false;
}

/**
 * 清理敏感信息的函数
 * @param obj 要清理的对象
 * @returns 清理后的对象
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj;
  }

  // 如果是数组，清理每个元素
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  // 如果是对象，清理每个属性
  const sanitized: any = {};
  const sensitiveKeys = ['email', 'password', 'token', 'secret', 'key', 'auth', 'credentials', 'permissions', 'roles'];

  for (const key in obj) {
    // 跳过敏感字段
    if (sensitiveKeys.some(sensitiveKey => key.toLowerCase().includes(sensitiveKey))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof obj[key] === 'object') {
      // 递归清理嵌套对象
      sanitized[key] = sanitizeObject(obj[key]);
    } else {
      sanitized[key] = obj[key];
    }
  }

  return sanitized;
}

/**
 * 安全处理日志参数
 * @param args 日志参数
 * @returns 处理后的日志参数
 */
function safeLogArgs(args: any[]): any[] {
  return args.map(arg => {
    // 如果是对象且包含敏感信息，清理它
    if (typeof arg === 'object' && arg !== null && containsSensitiveInfo(arg)) {
      return sanitizeObject(arg);
    }

    // 如果是字符串且包含敏感信息，替换它
    if (typeof arg === 'string' && containsSensitiveInfo(arg)) {
      return '[SENSITIVE INFORMATION REDACTED]';
    }

    return arg;
  });
}

/**
 * 输出普通日志
 * @param message 日志消息
 * @param optionalParams 其他参数
 */
export function log(message?: any, ...optionalParams: any[]): void {
  if (isDevelopment()) {
    // 在开发环境中，仍然显示完整信息，但添加警告
    if (containsSensitiveInfo(message) || optionalParams.some(param => containsSensitiveInfo(param))) {
      logger.warn('警告: 日志中包含可能的敏感信息，在生产环境中将被隐藏');
    }
    logger.log(message, ...optionalParams);
  }
}

/**
 * 输出警告日志
 * @param message 日志消息
 * @param optionalParams 其他参数
 */
export function warn(message?: any, ...optionalParams: any[]): void {
  if (isDevelopment()) {
    logger.warn(message, ...optionalParams);
  } else {
    // 在生产环境中，可以考虑将警告发送到日志服务，但要确保清理敏感信息
    // 这里可以添加发送到日志服务的代码
  }
}

/**
 * 输出错误日志
 * @param message 日志消息
 * @param optionalParams 其他参数
 */
export function error(message?: any, ...optionalParams: any[]): void {
  if (isDevelopment()) {
    // 在开发环境中显示完整错误
    logger.error(message, ...optionalParams);
  } else {
    // 在生产环境中，清理敏感信息后再输出
    const safeMessage = containsSensitiveInfo(message) ? '[SENSITIVE ERROR MESSAGE REDACTED]' : message;
    const safeParams = safeLogArgs(optionalParams);
    logger.error(safeMessage, ...safeParams);

    // 这里可以添加发送到错误跟踪服务的代码
  }
}

/**
 * 性能日志
 * 仅在开发环境中输出性能信息
 */
export function performance(component: string, time: number): void {
  if (isDevelopment()) {
    logger.log(`[性能监控] 组件 ${component} 渲染耗时: ${time.toFixed(2)}ms`);
  }
}

/**
 * 日志工具对象
 */
const logger = {
  log,
  warn,
  error,
  performance,
};

export default logger;
