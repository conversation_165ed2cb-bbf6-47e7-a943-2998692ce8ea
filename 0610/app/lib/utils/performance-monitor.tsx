/**
 * 性能监控工具
 * 用于监控组件渲染性能，仅在开发环境中输出性能信息
 */

import React from 'react';
import logger from './logger';
import { isDevelopment, isProduction } from '@/lib/utils/env';

/**
 * 组件渲染开始时间记录
 */
const componentStartTimes = new Map<string, number>();

/**
 * 开始监控组件渲染性能
 * @param componentName 组件名称
 */
export function startMonitoring(componentName: string): void {
  if (isDevelopment()) {
    componentStartTimes.set(componentName, performance.now());
    logger.log(`[性能监控] 开始监控: ${componentName}`);
  }
}

/**
 * 结束监控组件渲染性能并输出耗时
 * @param componentName 组件名称
 */
export function endMonitoring(componentName: string): void {
  if (isDevelopment()) {
    const startTime = componentStartTimes.get(componentName);
    if (startTime) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      logger.performance(componentName, duration);
      componentStartTimes.delete(componentName);
    } else {
      logger.warn(`[性能监控] 未找到组件 ${componentName} 的开始时间`);
    }
  }
}

/**
 * 使用高阶组件监控组件渲染性能
 * @param Component 要监控的组件
 * @param componentName 组件名称，默认为组件的displayName或name
 * @returns 包装后的组件
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
): React.FC<P> {
  // 仅在开发环境中进行性能监控
  if (isProduction()) {
    return Component as React.FC<P>;
  }

  const displayName = componentName || Component.displayName || Component.name || 'UnknownComponent';

  const WrappedComponent: React.FC<P> = (props) => {
    // 使用React的useEffect钩子来监控组件渲染
    React.useEffect(() => {
      const name = `${displayName}`;
      startMonitoring(name);

      return () => {
        endMonitoring(name);
      };
    }, []);

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `WithPerformanceMonitoring(${displayName})`;

  return WrappedComponent;
}

/**
 * 性能监控工具对象
 */
const performanceMonitor = {
  startMonitoring,
  endMonitoring,
  withPerformanceMonitoring,
};

export default performanceMonitor;
