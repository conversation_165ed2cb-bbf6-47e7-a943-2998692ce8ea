/**
 * 图标加载工具
 * 用于安全加载图标，避免控制台错误
 */

import logger from './logger';

/**
 * 检查图标URL是否有效
 * @param url 图标URL
 * @returns 是否有效
 */
export async function isIconUrlValid(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    logger.error('检查图标URL时出错:', error);
    return false;
  }
}

/**
 * 获取安全的图标URL
 * 如果图标不存在，返回默认图标
 * @param iconName 图标名称
 * @param size 图标大小
 * @param defaultIcon 默认图标
 * @returns 图标URL
 */
export async function getSafeIconUrl(
  iconName: string,
  size: number = 24,
  defaultIcon: string = '/icons/default-icon.svg'
): Promise<string> {
  const iconUrl = `/icons/${iconName}-${size}.svg`;
  
  if (await isIconUrlValid(iconUrl)) {
    return iconUrl;
  }
  
  // 尝试PNG格式
  const pngUrl = `/icons/${iconName}-${size}.png`;
  if (await isIconUrlValid(pngUrl)) {
    return pngUrl;
  }
  
  // 如果都不存在，返回默认图标
  logger.warn(`图标 ${iconName} 不存在，使用默认图标`);
  return defaultIcon;
}

/**
 * 预加载图标
 * @param iconNames 图标名称数组
 * @param sizes 图标大小数组
 */
export function preloadIcons(iconNames: string[], sizes: number[] = [24, 32]): void {
  if (typeof window === 'undefined') {
    return; // 服务器端不执行
  }
  
  iconNames.forEach(iconName => {
    sizes.forEach(size => {
      const img = new Image();
      img.src = `/icons/${iconName}-${size}.svg`;
      
      img.onerror = () => {
        // 尝试PNG格式
        const pngImg = new Image();
        pngImg.src = `/icons/${iconName}-${size}.png`;
        
        pngImg.onerror = () => {
          logger.warn(`无法预加载图标: ${iconName}-${size}`);
        };
      };
    });
  });
}

/**
 * 图标加载工具对象
 */
const iconLoader = {
  isIconUrlValid,
  getSafeIconUrl,
  preloadIcons,
};

export default iconLoader;
