import * as z from "zod"

export const menuSchema = z.object({
  id: z.string().optional(),
  code: z.string().min(1, "菜单代码不能为空"),
  name: z.string().min(1, "菜单名称不能为空"),
  path: z.string().optional(),
  icon: z.string().optional(),
  order: z.number().default(0),
  visible: z.boolean().default(true),
  parentId: z.string().nullable().optional(),
})

export type MenuFormValues = z.infer<typeof menuSchema> 