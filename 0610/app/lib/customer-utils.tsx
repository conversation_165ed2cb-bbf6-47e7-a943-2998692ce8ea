import React from "react"
import { Badge } from "@/components/ui/badge"

// 业务类型映射
const BUSINESS_TYPE_MAP: Record<string, string> = {
  "VIDEO_NOTIFICATION": "视频通知",
  "VIDEO_INTERACTION": "视频交互",
  "VOICE_INTERACTION": "语音交互",
}

// 获取业务类型名称
export function getBusinessTypeName(code: string | null | undefined): string | null {
  if (!code) return null
  return BUSINESS_TYPE_MAP[code] || code
}

// 获取状态标签
export function getStatusBadge(status: string) {
  switch (status) {
    case "active":
    case "enabled":
      return <Badge className="bg-green-500 hover:bg-green-600 text-white">启用</Badge>
    case "inactive":
    case "disabled":
      return <Badge className="bg-red-500 hover:bg-red-600 text-white">停用</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-300">待审核</Badge>
    default:
      return <Badge variant="secondary">未知</Badge>
  }
}

// 获取账户类型标签
export function getAccountTypeBadge(accountType: string) {
  switch (accountType) {
    case "personal":
      return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">个人用户</Badge>
    case "enterprise":
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">企业用户</Badge>
    default:
      return <Badge variant="outline">未知类型</Badge>
  }
}

// 认证状态相关的颜色常量
const VERIFICATION_COLORS = {
  approved: "bg-green-500 text-white hover:bg-green-600",
  rejected: "bg-red-500 text-white hover:bg-red-600",
  pending: "bg-yellow-500 text-white hover:bg-yellow-600",
  none: "bg-gray-200 text-gray-700 hover:bg-gray-300"
}

// 获取用户认证状态（显示认证类型和状态）
export function getUserVerificationBadge(personalVerification: string | null, enterpriseVerification: string | null) {
  // 个人认证已通过
  if (personalVerification === "approved") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.approved}>个人认证已通过</Badge>
      </div>
    )
  }
  // 企业认证已通过
  else if (enterpriseVerification === "approved") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.approved}>企业认证已通过</Badge>
      </div>
    )
  }
  // 个人认证审核中
  else if (personalVerification === "pending") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.pending}>个人认证审核中</Badge>
      </div>
    )
  }
  // 企业认证审核中
  else if (enterpriseVerification === "pending") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.pending}>企业认证审核中</Badge>
      </div>
    )
  }
  // 个人认证已拒绝
  else if (personalVerification === "rejected") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.rejected}>个人认证已拒绝</Badge>
      </div>
    )
  }
  // 企业认证已拒绝
  else if (enterpriseVerification === "rejected") {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.rejected}>企业认证已拒绝</Badge>
      </div>
    )
  }
  // 未提交认证
  else {
    return (
      <div className="flex flex-col gap-1">
        <Badge className={VERIFICATION_COLORS.none}>未提交认证</Badge>
      </div>
    )
  }
}

// 获取角色标签
export function getRoleBadge(role: string) {
  switch (role) {
    case "admin":
      return <Badge className="bg-red-500 text-white hover:bg-red-600">管理员</Badge>
    case "manager":
      return <Badge className="bg-blue-500 text-white hover:bg-blue-600">经理</Badge>
    case "operator":
      return <Badge className="bg-indigo-500 text-white hover:bg-indigo-600">操作员</Badge>
    case "enterprise":
      return <Badge className="bg-blue-500 text-white hover:bg-blue-600">企业用户</Badge>
    case "user":
      return <Badge className="bg-purple-500 text-white hover:bg-purple-600">普通用户</Badge>
    default:
      return <Badge className="bg-gray-500 text-white hover:bg-gray-600">未知角色</Badge>
  }
}
