import nodemailer from 'nodemailer';
import logger from '@/lib/utils/logger';

/**
 * 直接发送邮件，不依赖于环境变量配置
 * 如果环境变量未配置，会使用Ethereal测试账户
 *
 * @param to 收件人邮箱
 * @param subject 邮件主题
 * @param html 邮件HTML内容
 * @returns 发送结果，包含messageId和预览URL
 */
export async function sendDirectEmail(to: string, subject: string, html: string) {
  try {
    // 创建一个测试账户（如果没有配置环境变量）
    let testAccount;
    let previewUrl = '';

    // 检查是否配置了邮件服务
    const isConfigured = process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASS;

    logger.debug('邮件环境变量检查:', {
      EMAIL_HOST: process.env.EMAIL_HOST || '未设置',
      EMAIL_USER: process.env.EMAIL_USER ? '已设置' : '未设置',
      EMAIL_PASS: process.env.EMAIL_PASS ? '已设置' : '未设置',
      EMAIL_PORT: process.env.EMAIL_PORT || '未设置',
      EMAIL_SECURE: process.env.EMAIL_SECURE || '未设置',
      EMAIL_FROM: process.env.EMAIL_FROM || '未设置',
      isConfigured: isConfigured ? '是' : '否'
    });

    if (!isConfigured) {
      logger.log('邮件服务未配置，使用Ethereal测试账户');
      try {
        testAccount = await nodemailer.createTestAccount();
        logger.debug('测试账户创建成功:', {
          user: testAccount.user,
          pass: testAccount.pass
        });
      } catch (testAccountError) {
        logger.error('创建测试账户失败:', testAccountError);
        // 使用默认测试账户
        testAccount = {
          user: '<EMAIL>',
          pass: 'testpassword'
        };
      }
    }

    // 创建传输对象
    const transportConfig = {
      host: process.env.EMAIL_HOST || 'smtp.ethereal.email',
      port: parseInt(process.env.EMAIL_PORT || '587'),
      secure: isConfigured ? (process.env.EMAIL_SECURE === 'true') : false,
      auth: {
        user: process.env.EMAIL_USER || (testAccount?.user || ''),
        pass: process.env.EMAIL_PASS || (testAccount?.pass || ''),
      },
    };

    logger.debug('邮件传输配置:', {
      host: transportConfig.host,
      port: transportConfig.port,
      secure: transportConfig.secure,
      user: transportConfig.auth.user
    });

    const transporter = nodemailer.createTransport(transportConfig);

    // 验证传输器配置
    try {
      logger.log('开始验证邮件传输器...');
      await Promise.race([
        transporter.verify(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('邮件服务器连接超时')), 3000))
      ]);
      logger.log('邮件传输器验证成功');
    } catch (verifyError) {
      logger.error('邮件传输器验证失败:', verifyError);

      // 如果是测试账户，则忽略验证错误，继续尝试发送
      if (!isConfigured) {
        logger.log('使用测试账户，忽略验证错误，继续尝试发送');
      } else {
        throw verifyError;
      }
    }

    // 发送邮件（添加超时控制）
    logger.log('开始发送邮件到:', to, '主题:', subject);

    const mailOptions = {
      from: process.env.EMAIL_FROM || '"系统通知" <<EMAIL>>',
      to,
      subject,
      html,
    };

    logger.debug('邮件配置:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    const info = await Promise.race([
      transporter.sendMail(mailOptions),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('邮件发送超时，已加入队列')), 8000)
      )
    ]);

    // 如果使用测试账户，获取预览URL
    if (testAccount) {
      try {
        previewUrl = nodemailer.getTestMessageUrl(info) || '';
        logger.log('测试邮件预览URL:', previewUrl);
      } catch (previewError) {
        logger.error('获取测试邮件预览URL失败:', previewError);
      }
    }

    logger.debug('邮件发送成功:', {
      to,
      subject,
      messageId: info.messageId || 'unknown',
      previewUrl: previewUrl || 'none'
    });

    return {
      success: true,
      messageId: info.messageId || 'unknown',
      previewUrl,
      info: JSON.stringify(info)
    };
  } catch (error) {
    logger.error('直接发送邮件失败:', error);

    // 将错误重新抛出，让调用者处理
    throw error;
  }
}
