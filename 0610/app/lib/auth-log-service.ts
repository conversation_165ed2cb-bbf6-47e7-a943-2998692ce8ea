/**
 * 认证日志服务
 * 提供记录认证相关操作的日志功能
 * 
 * 功能：
 * - 记录登录尝试
 * - 记录令牌验证
 * - 记录认证错误
 * - 记录会话操作
 * 
 * @module AuthLogService
 */

import { prisma } from "@/lib/prisma"
import { SystemLogService } from "./system-log-service"

export interface AuthLogOptions {
  userId: string
  action: string
  status: 'success' | 'error' | 'warning' | 'info'
  details?: any
  ipAddress?: string
  userAgent?: string
  requestId?: string
}

/**
 * 认证日志服务类
 * 提供记录认证相关操作的日志功能
 */
export class AuthLogService {
  /**
   * 记录认证操作日志
   * @param options 日志选项
   * @returns 创建的日志记录
   */
  static async log(options: AuthLogOptions) {
    try {
      // 使用系统日志服务记录日志，但指定模块为auth
      return await SystemLogService.log({
        userId: options.userId,
        action: options.action,
        module: 'auth', // 统一使用auth模块
        details: {
          ...options.details,
          requestId: options.requestId,
          timestamp: new Date().toISOString()
        },
        ipAddress: options.ipAddress,
        userAgent: options.userAgent,
        status: options.status
      })
    } catch (error) {
      console.error("[AuthLogService] 记录认证日志失败:", error)
      // 日志记录失败不应影响主要业务流程
      return null
    }
  }

  /**
   * 记录登录尝试
   * @param options 登录尝试信息
   */
  static async logLoginAttempt({
    userId,
    username,
    success,
    reason,
    ipAddress,
    userAgent,
    requestId
  }: {
    userId?: string
    username: string
    success: boolean
    reason?: string
    ipAddress?: string
    userAgent?: string
    requestId?: string
  }) {
    return this.log({
      userId: userId || 'anonymous',
      action: 'login_attempt',
      status: success ? 'success' : 'error',
      details: {
        username,
        success,
        reason,
        requestId
      },
      ipAddress,
      userAgent,
      requestId
    })
  }

  /**
   * 记录令牌验证
   * @param options 令牌验证信息
   */
  static async logTokenValidation({
    userId,
    success,
    tokenType,
    reason,
    ipAddress,
    userAgent,
    requestId,
    path
  }: {
    userId?: string
    success: boolean
    tokenType?: string
    reason?: string
    ipAddress?: string
    userAgent?: string
    requestId?: string
    path?: string
  }) {
    return this.log({
      userId: userId || 'anonymous',
      action: 'token_validation',
      status: success ? 'success' : 'error',
      details: {
        tokenType,
        success,
        reason,
        path,
        requestId
      },
      ipAddress,
      userAgent,
      requestId
    })
  }

  /**
   * 记录会话操作
   * @param options 会话操作信息
   */
  static async logSessionOperation({
    userId,
    operation,
    success,
    reason,
    ipAddress,
    userAgent,
    requestId
  }: {
    userId: string
    operation: 'create' | 'refresh' | 'invalidate'
    success: boolean
    reason?: string
    ipAddress?: string
    userAgent?: string
    requestId?: string
  }) {
    return this.log({
      userId,
      action: `session_${operation}`,
      status: success ? 'success' : 'error',
      details: {
        operation,
        success,
        reason,
        requestId
      },
      ipAddress,
      userAgent,
      requestId
    })
  }

  /**
   * 查询认证日志
   * @param options 查询选项
   * @returns 日志记录列表和总数
   */
  static async query(options: {
    userId?: string
    action?: string
    startDate?: Date
    endDate?: Date
    page?: number
    pageSize?: number
    status?: string
  }) {
    const {
      userId,
      action,
      startDate,
      endDate,
      page = 1,
      pageSize = 10,
      status
    } = options

    // 构建查询条件
    const where = {
      module: 'auth', // 只查询认证模块的日志
      ...(userId ? { userId } : {}),
      ...(action ? { action } : {}),
      ...(status ? { status } : {}),
      ...(startDate || endDate ? {
        createdAt: {
          ...(startDate ? { gte: startDate } : {}),
          ...(endDate ? { lte: endDate } : {})
        }
      } : {})
    }

    // 查询总数
    const total = await prisma.system_log.count({ where })

    // 查询日志记录
    const logs = await prisma.system_log.findMany({
      where,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            name: true,
            email: true,
            image: true,
            roleCode: true
          }
        }
      }
    })

    return {
      logs,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }
}
