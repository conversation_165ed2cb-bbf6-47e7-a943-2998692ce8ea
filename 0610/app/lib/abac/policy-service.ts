import { prisma } from "@/lib/prisma";
import { Policy, PolicyCondition } from "./types";
import { Cache } from "./cache";

/**
 * 策略服务类
 * 用于管理和评估ABAC策略
 */
export class PolicyService {
  private cache: Cache<string, Policy>;

  constructor() {
    // 创建策略缓存，设置5分钟过期
    this.cache = new Cache<string, Policy>(300);
  }

  /**
   * 获取所有策略
   * @param includeDisabled 是否包含禁用策略
   * @returns 策略列表
   */
  async getAllPolicies(includeDisabled = false) {
    return prisma.policy.findMany({
      where: includeDisabled ? {} : { enabled: true },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    });
  }

  /**
   * 获取策略
   * @param id 策略ID
   * @returns 策略
   */
  async getPolicy(id: string) {
    return prisma.policy.findUnique({
      where: { id },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    });
  }

  /**
   * 创建策略
   * @param data 策略数据
   * @returns 创建的策略
   */
  async createPolicy(data: {
    resourceCode: string;
    operationCode: string;
    description?: string;
    conditions: PolicyCondition[];
  }): Promise<Policy> {
    const { resourceCode, operationCode, description, conditions } = data;

    // 验证资源是否存在
    const resource = await prisma.resource.findUnique({
      where: { code: resourceCode },
    });

    if (!resource) {
      throw new Error("资源不存在");
    }

    // 验证操作是否存在
    const operation = await prisma.operation.findUnique({
      where: { code: operationCode },
    });

    if (!operation) {
      throw new Error("操作不存在");
    }

    // 创建策略
    const policy = await prisma.policy.create({
      data: {
        resourceCode,
        operationCode,
        description,
        conditions: {
          create: conditions.map(condition => ({
            attribute: condition.attribute,
            operator: condition.operator,
            value: condition.value,
            description: condition.description,
          })),
        },
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    }) as unknown as Policy;

    // 更新缓存
    this.cache.set(this.getCacheKey(resourceCode, operationCode), policy);

    return policy;
  }

  /**
   * 更新策略
   * @param id 策略ID
   * @param data 策略数据
   * @returns 更新后的策略
   */
  async updatePolicy(
    id: string,
    data: {
      resourceCode: string;
      operationCode: string;
      description?: string;
      conditions: PolicyCondition[];
    }
  ): Promise<Policy> {
    const { resourceCode, operationCode, description, conditions } = data;

    // 验证策略是否存在
    const existingPolicy = await prisma.policy.findUnique({
      where: { id },
    });

    if (!existingPolicy) {
      throw new Error("策略不存在");
    }

    // 验证资源是否存在
    const resource = await prisma.resource.findUnique({
      where: { code: resourceCode },
    });

    if (!resource) {
      throw new Error("资源不存在");
    }

    // 验证操作是否存在
    const operation = await prisma.operation.findUnique({
      where: { code: operationCode },
    });

    if (!operation) {
      throw new Error("操作不存在");
    }

    // 更新策略
    const policy = await prisma.policy.update({
      where: { id },
      data: {
        resourceCode,
        operationCode,
        description,
        conditions: {
          deleteMany: {},
          create: conditions.map(condition => ({
            attribute: condition.attribute,
            operator: condition.operator,
            value: condition.value,
            description: condition.description,
          })),
        },
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    }) as unknown as Policy;

    // 更新缓存
    this.cache.set(this.getCacheKey(resourceCode, operationCode), policy);

    return policy;
  }

  /**
   * 删除策略
   * @param id 策略ID
   */
  async deletePolicy(id: string): Promise<void> {
    // 验证策略是否存在
    const existingPolicy = await prisma.policy.findUnique({
      where: { id },
    });

    if (!existingPolicy) {
      throw new Error("策略不存在");
    }

    // 删除策略
    await prisma.policy.delete({
      where: { id },
    });

    // 从缓存中删除
    this.cache.delete(
      this.getCacheKey(
        existingPolicy.resourceCode,
        existingPolicy.operationCode
      )
    );
  }

  /**
   * 启用策略
   * @param id 策略ID
   * @returns 策略
   */
  async enablePolicy(id: string) {
    return prisma.policy.update({
      where: { id },
      data: { enabled: true },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    });
  }

  /**
   * 禁用策略
   * @param id 策略ID
   * @returns 策略
   */
  async disablePolicy(id: string) {
    return prisma.policy.update({
      where: { id },
      data: { enabled: false },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    });
  }

  /**
   * 检查策略是否存在
   * @param resourceCode 资源代码
   * @param operationCode 操作代码
   * @returns 是否存在
   */
  async hasPolicy(resourceCode: string, operationCode: string) {
    const count = await prisma.policy.count({
      where: {
        resourceCode,
        operationCode,
      },
    });
    return count > 0;
  }

  /**
   * 获取策略列表
   * @param resourceCode 资源代码
   * @param operationCode 操作代码
   * @returns 策略列表
   */
  async getPoliciesByResourceAndOperation(
    resourceCode: string,
    operationCode: string
  ) {
    return prisma.policy.findMany({
      where: {
        resourceCode,
        operationCode,
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    });
  }

  /**
   * 获取策略
   * @param resourceCode 资源代码
   * @param operationCode 操作代码
   * @returns 策略列表
   */
  async getPolicies(
    resourceCode: string,
    operationCode: string
  ): Promise<Policy[]> {
    // 尝试从缓存获取
    const cacheKey = this.getCacheKey(resourceCode, operationCode);
    const cachedPolicy = this.cache.get(cacheKey);
    if (cachedPolicy) {
      return [cachedPolicy];
    }

    // 从数据库获取
    const policies = await prisma.policy.findMany({
      where: {
        resourceCode,
        operationCode,
      },
      include: {
        conditions: true,
        resource: true,
        operation: true,
      },
    }) as unknown as Policy[];

    // 更新缓存
    if (policies.length > 0) {
      this.cache.set(cacheKey, policies[0]);
    }

    return policies;
  }

  /**
   * 评估策略
   * @param resourceCode 资源代码
   * @param operationCode 操作代码
   * @param context 上下文数据
   * @returns 是否允许访问
   */
  async evaluatePolicy(
    resourceCode: string,
    operationCode: string,
    context: Record<string, any>
  ): Promise<boolean> {
    // 获取策略
    const policies = await this.getPolicies(resourceCode, operationCode);

    // 如果没有策略，默认拒绝访问
    if (policies.length === 0) {
      return false;
    }

    // 评估所有策略
    for (const policy of policies) {
      if (await this.evaluatePolicyConditions(policy, context)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 评估策略条件
   * @param policy 策略
   * @param context 上下文数据
   * @returns 是否满足条件
   */
  private async evaluatePolicyConditions(
    policy: Policy,
    context: Record<string, any>
  ): Promise<boolean> {
    // 如果没有条件，默认允许访问
    if (!policy.conditions || policy.conditions.length === 0) {
      return true;
    }

    // 评估所有条件
    for (const condition of policy.conditions) {
      const value = this.getAttributeValue(context, condition.attribute);
      if (!this.evaluateCondition(condition, value)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取属性值
   * @param context 上下文数据
   * @param attribute 属性路径
   * @returns 属性值
   */
  private getAttributeValue(
    context: Record<string, any>,
    attribute: string
  ): any {
    const parts = attribute.split(".");
    let value = context;

    for (const part of parts) {
      if (value === undefined || value === null) {
        return undefined;
      }
      value = value[part];
    }

    return value;
  }

  /**
   * 评估条件
   * @param condition 条件
   * @param value 实际值
   * @returns 是否满足条件
   */
  private evaluateCondition(
    condition: PolicyCondition,
    value: any
  ): boolean {
    switch (condition.operator) {
      case "equals":
        return value === condition.value;
      case "notEquals":
        return value !== condition.value;
      case "contains":
        return String(value).includes(String(condition.value));
      case "notContains":
        return !String(value).includes(String(condition.value));
      case "startsWith":
        return String(value).startsWith(String(condition.value));
      case "endsWith":
        return String(value).endsWith(String(condition.value));
      case "greaterThan":
        return Number(value) > Number(condition.value);
      case "lessThan":
        return Number(value) < Number(condition.value);
      case "greaterThanOrEquals":
        return Number(value) >= Number(condition.value);
      case "lessThanOrEquals":
        return Number(value) <= Number(condition.value);
      default:
        return false;
    }
  }

  /**
   * 获取缓存键
   * @param resourceCode 资源代码
   * @param operationCode 操作代码
   * @returns 缓存键
   */
  private getCacheKey(resourceCode: string, operationCode: string): string {
    return `policy:${resourceCode}:${operationCode}`;
  }
}