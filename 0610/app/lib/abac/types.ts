/**
 * ABAC (基于属性的访问控制) 类型定义
 * 作为现有 RBAC 系统的补充，不替代现有权限逻辑
 */

/**
 * 资源类型
 */
export type ResourceType = "menu" | "api" | "data" | "file";

/**
 * 操作类型
 */
export type OperationType = "view" | "create" | "update" | "delete";

/**
 * 条件操作符
 */
export type ConditionOperator = 
  | 'eq'      // 等于
  | 'ne'      // 不等于
  | 'gt'      // 大于
  | 'lt'      // 小于
  | 'gte'     // 大于等于
  | 'lte'     // 小于等于
  | 'in'      // 在列表中
  | 'contains' // 包含
  | 'startsWith' // 以...开始
  | 'endsWith'   // 以...结束

/**
 * 条件类型
 */
export type ConditionType = 
  | 'user'      // 用户属性条件 (如: 用户部门、级别等)
  | 'resource'  // 资源属性条件 (如: 资源所有者、状态等)
  | 'context'   // 上下文条件 (如: 时间、IP地址等)

/**
 * 资源
 */
export interface Resource {
  id: string;
  code: string;
  name: string;
  type: ResourceType;
  description?: string;
  roles?: Role[];
  operations?: Operation[];
  policies?: Policy[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 操作
 */
export interface Operation {
  id: string;
  code: string;
  name: string;
  type: OperationType;
  description?: string;
  menuId?: string;
  menu?: Menu;
  roles?: Role[];
  resources?: Resource[];
  policies?: Policy[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 条件
 */
export interface Condition {
  id: string;
  attribute: string;
  operator: ConditionOperator;
  value: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 策略
 */
export interface Policy {
  id: string;
  resourceCode: string;
  operationCode: string;
  description?: string;
  conditions: PolicyCondition[];
  resource?: Resource;
  operation?: Operation;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 策略条件
 */
export interface PolicyCondition {
  /** 唯一标识符 */
  id: string;
  /** 属性名 */
  attribute: string;
  /** 操作符 */
  operator: string;
  /** 属性值 */
  value: string;
  /** 条件描述 */
  description?: string;
  /** 关联的策略ID */
  policyId: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

/**
 * 策略表单数据
 */
export interface PolicyFormData {
  resourceCode: string;
  operationCode: string;
  description?: string;
  conditions: PolicyCondition[];
}

/**
 * 资源表单数据
 */
export interface ResourceFormData {
  code: string;
  name: string;
  type: ResourceType;
  description?: string;
  operationIds?: string[];
}

/**
 * 操作表单数据
 */
export interface OperationFormData {
  code: string;
  name: string;
  type: OperationType;
  description?: string;
  menuId?: string;
}

/**
 * 角色
 */
export interface Role {
  id: string;
  name: string;
  displayName: string;
  type: string;
  description: string;
  permissions: string[];
  users?: User[];
  menus?: Menu[];
  operations?: Operation[];
  resources?: Resource[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 菜单
 */
export interface Menu {
  id: string;
  code: string;
  name: string;
  path: string;
  icon?: string;
  parentId?: string;
  order: number;
  visible: boolean;
  parent?: Menu;
  children?: Menu[];
  operations?: Operation[];
  roles?: Role[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户
 */
export interface User {
  id: string;
  username: string;
  email: string;
  permissions: string[];
  roleCode: string;
  emailVerified?: Date;
  image?: string;
  name?: string;
  lastLoginAt?: Date;
  role?: Role;
  createdAt: Date;
  updatedAt: Date;
}

// 单个权限条件
export interface PermissionCondition {
  id?: string;
  permissionKey: string;    // 权限键 (如: "role:create")
  conditionType: ConditionType;
  attributeName: string;    // 属性名称
  operator: ConditionOperator;
  attributeValue: any;      // 属性值
  roleCode?: string;        // 关联的角色代码 (可选)
  description?: string;     // 条件描述
}

// 条件解析结果
export interface ConditionEvaluationResult {
  permitted: boolean;       // 是否允许
  failedConditions?: PermissionCondition[]; // 失败的条件列表
  message?: string;         // 失败原因消息
}

/**
 * 权限上下文接口
 * 在进行ABAC权限检查时提供的上下文信息
 */
export interface PermissionContext {
  /**
   * 资源ID（可选）
   * 例如：用户ID、角色ID等
   */
  resourceId?: string;
  
  /**
   * 资源类型（可选）
   * 例如：'user', 'role', 'menu' 等
   */
  resourceType?: string;
  
  /**
   * 资源所有者ID（可选）
   * 用于检查资源是否属于某用户
   */
  ownerId?: string;
  
  /**
   * 操作类型（可选）
   * 例如：'create', 'read', 'update', 'delete'
   */
  operation?: string;
  
  /**
   * 其他属性
   * 可以包含任何额外的上下文信息
   */
  [key: string]: any;
}

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  /**
   * 是否有权限
   */
  hasPermission: boolean;
  
  /**
   * 拒绝原因（可选）
   */
  reason?: string;
}