import { Policy, Role } from "./types"

/**
 * 资源类型
 */
export const RESOURCE_TYPES = {
  MENU: "menu",
  BUTTON: "button",
  PAGE: "page",
  API: "api",
  FEATURE: "feature",
  DATA: "data",
} as const

/**
 * 操作类型
 */
export const ACTION_TYPES = {
  VIEW: "view",
  CREATE: "create",
  UPDATE: "update",
  DELETE: "delete",
  MANAGE: "manage",
} as const

/**
 * 角色配置
 */
export const defaultRoles: Record<string, Role> = {
  ADMIN: {
    id: 'role_001',
    name: 'admin',
    displayName: '管理员',
    type: 'admin',
    description: '系统管理员',
    permissions: [
      '*',
      'role:view',
      'role:create',
      'role:update',
      'role:delete'
    ],
  },
  USER: {
    id: 'role_002',
    name: 'user',
    displayName: '普通用户',
    type: 'customer',
    description: '普通用户',
    permissions: ['read', 'notifications:list'],
  },
  MANAGER: {
    id: 'role_003',
    name: 'manager',
    displayName: '管理员',
    type: 'manager',
    description: '部门管理员',
    permissions: ['read', 'notifications:list', 'notifications:create'],
  },
} as const

/**
 * 资源权限配置
 */
export const RESOURCE_PERMISSIONS = {
  USER: {
    resource: "user",
    actions: {
      view: ["USER_VIEW"],
      create: ["USER_CREATE"],
      update: ["USER_UPDATE"],
      delete: ["USER_DELETE"],
      manage: ["USER_MANAGE"],
    },
  },
  ROLE: {
    resource: "role",
    actions: {
      view: ["ROLE_VIEW"],
      create: ["ROLE_CREATE"],
      update: ["ROLE_UPDATE"],
      delete: ["ROLE_DELETE"],
      manage: ["ROLE_MANAGE"],
    },
  },
  PERMISSION: {
    resource: "permission",
    actions: {
      view: ["PERMISSION_VIEW"],
      create: ["PERMISSION_CREATE"],
      update: ["PERMISSION_UPDATE"],
      delete: ["PERMISSION_DELETE"],
      manage: ["PERMISSION_MANAGE"],
    },
  },
  SETTINGS: {
    resource: "settings",
    actions: {
      view: ["SETTINGS_VIEW"],
      update: ["SETTINGS_UPDATE"],
      manage: ["SETTINGS_MANAGE"],
    },
  },
} as const

/**
 * 菜单配置
 */
export const MENU_CONFIG = [
  {
    title: "仪表盘",
    link: "/dashboard",
    icon: "dashboard",
    resource: "dashboard",
    action: "view",
    permissions: ["DASHBOARD_VIEW"],
  },
  {
    title: "任务管理",
    link: "/tasks",
    icon: "task",
    resource: "task",
    action: "view",
    permissions: ["TASK_VIEW"],
    children: [
      {
        title: "我的任务",
        link: "/tasks/my",
        icon: "my-task",
        resource: "task",
        action: "view",
        permissions: ["TASK_VIEW"],
      },
      {
        title: "任务列表",
        link: "/tasks/list",
        icon: "task-list",
        resource: "task",
        action: "manage",
        permissions: ["TASK_MANAGE"],
      },
    ],
  },
  {
    title: "系统管理",
    link: "/system",
    icon: "system",
    resource: "system",
    action: "manage",
    permissions: ["SYSTEM_MANAGE"],
    children: [
      {
        title: "用户管理",
        link: "/system/users",
        icon: "user",
        resource: "user",
        action: "manage",
        permissions: ["USER_MANAGE"],
      },
      {
        title: "角色管理",
        link: "/system/roles",
        icon: "role",
        resource: "role",
        action: "manage",
        permissions: ["ROLE_MANAGE"],
      },
      {
        title: "权限管理",
        link: "/system/permissions",
        icon: "permission",
        resource: "permission",
        action: "manage",
        permissions: ["PERMISSION_MANAGE"],
      },
      {
        title: "系统设置",
        link: "/system/settings",
        icon: "settings",
        resource: "settings",
        action: "manage",
        permissions: ["SETTINGS_MANAGE"],
      },
    ],
  },
] as const

/**
 * 默认策略配置
 */
export const DEFAULT_POLICIES: Omit<Policy, "id" | "createdAt" | "updatedAt">[] = [
  {
    name: "admin-full-access",
    description: "管理员完全访问权限",
    effect: "allow",
    priority: 100,
    enabled: true,
    resource: "*",
    action: "*",
    conditions: undefined,
    code: "ADMIN_FULL_ACCESS",
  },
  {
    name: "user-basic-access",
    description: "用户基本访问权限",
    effect: "allow",
    priority: 50,
    enabled: true,
    resource: "profile",
    action: "view,update",
    conditions: undefined,
    code: "USER_BASIC_ACCESS",
  },
] as const 