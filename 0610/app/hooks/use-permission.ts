import { useCallback, useEffect, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { PermissionContext } from "@/lib/abac/types";
import logger from '@/lib/utils/logger';

/**
 * 权限检查 Hook
 * 前端组件使用此 Hook 检查用户是否有特定权限
 */
export function usePermission() {
  const { user } = useAuth();

  /**
   * 检查用户是否有特定权限
   * @param permissionKey 权限标识符
   * @param context 可选的上下文数据 (用于ABAC条件评估)
   * @returns 是否有权限
   */
  const checkPermission = useCallback(async (
    permissionKey: string,
    context?: PermissionContext
  ): Promise<boolean> => {
    if (!user?.id) {
      return false;
    }

    try {
      // 调用API进行权限检查
      const response = await fetch('/api/auth/check-permission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          permissionKey,
          context
        }),
      });

      if (!response.ok) {
        throw new Error('权限检查请求失败');
      }

      const data = await response.json();
      return !!data.hasPermission;
    } catch (error) {
      logger.error('权限检查出错:', error);
      return false;
    }
  }, [user?.id]);

  /**
   * 组件级权限检查
   * 返回可直接用于条件渲染的布尔值
   */
  const useHasPermission = (
    permissionKey: string,
    context?: PermissionContext
  ) => {
    const [hasPermission, setHasPermission] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
      let isMounted = true;
      setLoading(true);

      // 如果没有用户信息，直接返回无权限
      if (!user?.id) {
        setHasPermission(false);
        setLoading(false);
        return;
      }

      // 检查权限
      checkPermission(permissionKey, context)
        .then((result) => {
          if (isMounted) {
            setHasPermission(result);
            setLoading(false);
          }
        })
        .catch(() => {
          if (isMounted) {
            setHasPermission(false);
            setLoading(false);
          }
        });

      return () => {
        isMounted = false;
      };
    }, [user?.id, permissionKey, context]);

    return { hasPermission, loading };
  };

  return {
    checkPermission,
    useHasPermission,
  };
}

export default usePermission; 