# 项目清理建议报告

## 🎯 清理目标
- 提高代码可维护性
- 减少项目体积
- 优化目录结构

## 📋 建议清理的文件和目录

### 1. 备份文件（可安全删除）
\n### 备份文件列表:
./tools/backup-api-routes.sh
./database_backups/backup_20250417_090916.sql
./app/api/tasks/route.ts.backup.20240624
./app/(dashboard)/settings/page.tsx.backup
./app/(dashboard)/settings/page.tsx.backup2
./cleanup-backup-20250605-183831/database_backups/backup_20250417_090916.sql
./cleanup-backup-20250605-183831/backup.sql
./cleanup-backup-20250605-183922/database_backups/backup_20250417_090916.sql
./cleanup-backup-20250605-183922/backup.sql
./backup.sql
./app/(dashboard)/settings/page.tsx.backup

### 2. 调试代码（需要人工审查）

#### Console.log 语句位置:
./cleanup-project.sh:# 查找 console.log（排除 node_modules）
./cleanup-project.sh:echo "剩余 console.log: $(grep -r "console\.log" . --exclude-dir=node_modules | wc -l)"
./contexts/auth-context.tsx:          console.log("使用 NextAuth 会话数据")
./contexts/auth-context.tsx:        console.log("尝试获取自定义认证状态...")
./contexts/auth-context.tsx:          console.log("设置用户数据:", response.data)
./contexts/auth-context.tsx:          console.log("未找到用户数据:", response.message)
./contexts/auth-context.tsx:      console.log("尝试登录...")
./contexts/auth-context.tsx:        console.log("登录成功，设置用户数据:", response.data.user)
./contexts/auth-context.tsx:        console.log("登录失败:", response.message)
./app/components/user-credit-dialog.tsx:        console.log(`授信额度清零操作: 用户ID=${userId}, 备注=${remarks.trim() || "系统授信额度清零"}`)
./app/components/user-credit-dialog.tsx:        console.log(`授信额度操作: 用户ID=${userId}, 金额=${creditAmount}, 类型=${operationType}, 备注=${remarks.trim() || "系统授信额度调整"}`)
./app/components/user-credit-dialog.tsx:      console.log('发送请求到:', url, '数据:', requestData)
./app/components/user-credit-dialog.tsx:      console.log('原始响应文本:', responseText)
./app/components/user-credit-dialog.tsx:        console.log('授信调整成功，新授信额度:', actualNewCreditLimit)
./app/components/role/operation-selector.tsx:      console.log('加载操作数据响应:', data)
./app/components/user-recharge-dialog.tsx:        console.log(`账户余额清零操作: 用户ID=${userId}, 当前余额=${currentBalance}, 备注=${remarks.trim() || "系统账户余额清零"}`)
./app/components/user-recharge-dialog.tsx:        console.log(`账户${operationType === "recharge" ? "充值" : "扣费"}操作: 用户ID=${userId}, 金额=${amountValue}, 备注=${remarks.trim() || ""}`)
./app/components/user-recharge-dialog.tsx:      console.log('发送请求到:', url, '数据:', requestData)
./app/components/user-recharge-dialog.tsx:      console.log('原始响应文本:', responseText)
./app/components/user-recharge-dialog.tsx:        console.log('操作成功，新余额:', actualNewBalance)

#### TODO 和 FIXME 标记:
./cleanup-project.sh:#### TODO 和 FIXME 标记:
./cleanup-project.sh:# 查找 TODO 和 FIXME
./cleanup-project.sh:grep -r "TODO\|FIXME" . --exclude-dir=node_modules --exclude-dir="$BACKUP_DIR" | head -10 >> "$REPORT_FILE" 2>/dev/null || true
./cleanup-project.sh:echo "剩余 TODO/FIXME: $(grep -r "TODO\|FIXME" . --exclude-dir=node_modules | wc -l)"
./app/api/admin-users/[id]/reset-password/route.ts.bak.20250423:    // TODO: 发送邮件通知用户密码已重置
./docs/上线检查清单.md:  - [ ] 所有 TODO 和 FIXME 标记已处理
./cleanup-backup-20250605-183831/backups/20250426_134140_debug_code_backup/app/api/admin/user/verification/route.ts:    // TODO: 实现系统通知功能
./cleanup-backup-20250605-183831/backups/20250426_134140_debug_code_backup/app/api/cron/verification-check/route.ts:          // TODO: 实现系统通知功能
./cleanup-backup-20250605-183831/backups/api-routes-20250423/app/api/admin/user/verification/route.ts:    // TODO: 实现系统通知功能
./cleanup-backup-20250605-183831/backups/api-routes-20250423/app/api/admin-users/[id]/reset-password/route.ts:    // TODO: 发送邮件通知用户密码已重置

## 🔄 建议的文件重组

### 组件文件重组建议:
- 将业务相关组件移动到 `src/components/business/`
- 将通用组件移动到 `src/components/common/`
- 将表单组件移动到 `src/components/forms/`

### Hooks 重组建议:
- 将业务逻辑 hooks 移动到 `src/hooks/business/`
- 将通用 hooks 移动到 `src/hooks/common/`

### 服务文件重组建议:
- 将 API 相关服务移动到 `src/services/api/`
- 将认证服务移动到 `src/services/auth/`
- 将通知服务移动到 `src/services/notification/`

## ⚠️ 注意事项
1. 移动文件前请确保更新所有导入路径
2. 建议分批进行，每次只重组一个模块
3. 每次重组后进行完整测试
4. 保持 Git 提交记录清晰

## 🛠️ 下一步操作建议
1. 审查此报告
2. 手动删除确认无用的备份文件
3. 逐步清理调试代码
4. 分模块进行文件重组
5. 更新文档和导入路径
