{"version": 3, "file": "tippy.cjs.js", "sources": ["../src/constants.ts", "../src/utils.ts", "../src/dom-utils.ts", "../src/bindGlobalEventListeners.ts", "../src/browser.ts", "../src/validation.ts", "../src/props.ts", "../src/template.ts", "../src/createTippy.ts", "../src/index.ts", "../src/addons/createSingleton.ts", "../src/addons/delegate.ts", "../src/plugins/animateFill.ts", "../src/plugins/followCursor.ts", "../src/plugins/inlinePositioning.ts", "../src/plugins/sticky.ts", "../build/base.js"], "sourcesContent": ["export const ROUND_ARROW =\n  '<svg width=\"16\" height=\"6\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z\"></svg>';\n\nexport const BOX_CLASS = `__NAMESPACE_PREFIX__-box`;\nexport const CONTENT_CLASS = `__NAMESPACE_PREFIX__-content`;\nexport const BACKDROP_CLASS = `__NAMESPACE_PREFIX__-backdrop`;\nexport const ARROW_CLASS = `__NAMESPACE_PREFIX__-arrow`;\nexport const SVG_ARROW_CLASS = `__NAMESPACE_PREFIX__-svg-arrow`;\n\nexport const TOUCH_OPTIONS = {passive: true, capture: true};\n\nexport const TIPPY_DEFAULT_APPEND_TO = () => document.body;\n", "import {BasePlacement, Placement} from './types';\n\nexport function hasOwnProperty(\n  obj: Record<string, unknown>,\n  key: string\n): boolean {\n  return {}.hasOwnProperty.call(obj, key);\n}\n\nexport function getValueAtIndexOrReturn<T>(\n  value: T | [T | null, T | null],\n  index: number,\n  defaultValue: T | [T, T]\n): T {\n  if (Array.isArray(value)) {\n    const v = value[index];\n    return v == null\n      ? Array.isArray(defaultValue)\n        ? defaultValue[index]\n        : defaultValue\n      : v;\n  }\n\n  return value;\n}\n\nexport function isType(value: any, type: string): boolean {\n  const str = {}.toString.call(value);\n  return str.indexOf('[object') === 0 && str.indexOf(`${type}]`) > -1;\n}\n\nexport function invokeWithArgsOrReturn(value: any, args: any[]): any {\n  return typeof value === 'function' ? value(...args) : value;\n}\n\nexport function debounce<T>(\n  fn: (arg: T) => void,\n  ms: number\n): (arg: T) => void {\n  // Avoid wrapping in `setTimeout` if ms is 0 anyway\n  if (ms === 0) {\n    return fn;\n  }\n\n  let timeout: any;\n\n  return (arg): void => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => {\n      fn(arg);\n    }, ms);\n  };\n}\n\nexport function removeProperties<T>(obj: T, keys: string[]): Partial<T> {\n  const clone = {...obj};\n  keys.forEach((key) => {\n    delete (clone as any)[key];\n  });\n  return clone;\n}\n\nexport function splitBySpaces(value: string): string[] {\n  return value.split(/\\s+/).filter(Boolean);\n}\n\nexport function normalizeToArray<T>(value: T | T[]): T[] {\n  return ([] as T[]).concat(value);\n}\n\nexport function pushIfUnique<T>(arr: T[], value: T): void {\n  if (arr.indexOf(value) === -1) {\n    arr.push(value);\n  }\n}\n\nexport function appendPxIfNumber(value: string | number): string {\n  return typeof value === 'number' ? `${value}px` : value;\n}\n\nexport function unique<T>(arr: T[]): T[] {\n  return arr.filter((item, index) => arr.indexOf(item) === index);\n}\n\nexport function getNumber(value: string | number): number {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n\nexport function getBasePlacement(placement: Placement): BasePlacement {\n  return placement.split('-')[0] as BasePlacement;\n}\n\nexport function arrayFrom(value: ArrayLike<any>): any[] {\n  return [].slice.call(value);\n}\n\nexport function removeUndefinedProps(\n  obj: Record<string, unknown>\n): Partial<Record<string, unknown>> {\n  return Object.keys(obj).reduce((acc, key) => {\n    if (obj[key] !== undefined) {\n      (acc as any)[key] = obj[key];\n    }\n\n    return acc;\n  }, {});\n}\n", "import {ReferenceElement, Targets} from './types';\nimport {PopperTreeData} from './types-internal';\nimport {arrayFrom, isType, normalizeToArray, getBasePlacement} from './utils';\n\nexport function div(): HTMLDivElement {\n  return document.createElement('div');\n}\n\nexport function isElement(value: unknown): value is Element | DocumentFragment {\n  return ['Element', 'Fragment'].some((type) => isType(value, type));\n}\n\nexport function isNodeList(value: unknown): value is NodeList {\n  return isType(value, 'NodeList');\n}\n\nexport function isMouseEvent(value: unknown): value is MouseEvent {\n  return isType(value, 'MouseEvent');\n}\n\nexport function isReferenceElement(value: any): value is ReferenceElement {\n  return !!(value && value._tippy && value._tippy.reference === value);\n}\n\nexport function getArrayOfElements(value: Targets): Element[] {\n  if (isElement(value)) {\n    return [value];\n  }\n\n  if (isNodeList(value)) {\n    return arrayFrom(value);\n  }\n\n  if (Array.isArray(value)) {\n    return value;\n  }\n\n  return arrayFrom(document.querySelectorAll(value));\n}\n\nexport function setTransitionDuration(\n  els: (HTMLDivElement | null)[],\n  value: number\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.style.transitionDuration = `${value}ms`;\n    }\n  });\n}\n\nexport function setVisibilityState(\n  els: (HTMLDivElement | null)[],\n  state: 'visible' | 'hidden'\n): void {\n  els.forEach((el) => {\n    if (el) {\n      el.setAttribute('data-state', state);\n    }\n  });\n}\n\nexport function getOwnerDocument(\n  elementOrElements: Element | Element[]\n): Document {\n  const [element] = normalizeToArray(elementOrElements);\n\n  // Elements created via a <template> have an ownerDocument with no reference to the body\n  return element?.ownerDocument?.body ? element.ownerDocument : document;\n}\n\nexport function isCursorOutsideInteractiveBorder(\n  popperTreeData: PopperTreeData[],\n  event: MouseEvent\n): boolean {\n  const {clientX, clientY} = event;\n\n  return popperTreeData.every(({popperRect, popperState, props}) => {\n    const {interactiveBorder} = props;\n    const basePlacement = getBasePlacement(popperState.placement);\n    const offsetData = popperState.modifiersData.offset;\n\n    if (!offsetData) {\n      return true;\n    }\n\n    const topDistance = basePlacement === 'bottom' ? offsetData.top!.y : 0;\n    const bottomDistance = basePlacement === 'top' ? offsetData.bottom!.y : 0;\n    const leftDistance = basePlacement === 'right' ? offsetData.left!.x : 0;\n    const rightDistance = basePlacement === 'left' ? offsetData.right!.x : 0;\n\n    const exceedsTop =\n      popperRect.top - clientY + topDistance > interactiveBorder;\n    const exceedsBottom =\n      clientY - popperRect.bottom - bottomDistance > interactiveBorder;\n    const exceedsLeft =\n      popperRect.left - clientX + leftDistance > interactiveBorder;\n    const exceedsRight =\n      clientX - popperRect.right - rightDistance > interactiveBorder;\n\n    return exceedsTop || exceedsBottom || exceedsLeft || exceedsRight;\n  });\n}\n\nexport function updateTransitionEndListener(\n  box: HTMLDivElement,\n  action: 'add' | 'remove',\n  listener: (event: TransitionEvent) => void\n): void {\n  const method = `${action}EventListener` as\n    | 'addEventListener'\n    | 'removeEventListener';\n\n  // some browsers apparently support `transition` (unprefixed) but only fire\n  // `webkitTransitionEnd`...\n  ['transitionend', 'webkitTransitionEnd'].forEach((event) => {\n    box[method](event, listener as EventListener);\n  });\n}\n\n/**\n * Compared to xxx.contains, this function works for dom structures with shadow\n * dom\n */\nexport function actualContains(parent: Element, child: Element): boolean {\n  let target = child;\n  while (target) {\n    if (parent.contains(target)) {\n      return true;\n    }\n    target = (target.getRootNode?.() as any)?.host;\n  }\n  return false;\n}\n", "import {TOUCH_OPTIONS} from './constants';\nimport {isReferenceElement} from './dom-utils';\n\nexport const currentInput = {isTouch: false};\nlet lastMouseMoveTime = 0;\n\n/**\n * When a `touchstart` event is fired, it's assumed the user is using touch\n * input. We'll bind a `mousemove` event listener to listen for mouse input in\n * the future. This way, the `isTouch` property is fully dynamic and will handle\n * hybrid devices that use a mix of touch + mouse input.\n */\nexport function onDocumentTouchStart(): void {\n  if (currentInput.isTouch) {\n    return;\n  }\n\n  currentInput.isTouch = true;\n\n  if (window.performance) {\n    document.addEventListener('mousemove', onDocumentMouseMove);\n  }\n}\n\n/**\n * When two `mousemove` event are fired consecutively within 20ms, it's assumed\n * the user is using mouse input again. `mousemove` can fire on touch devices as\n * well, but very rarely that quickly.\n */\nexport function onDocumentMouseMove(): void {\n  const now = performance.now();\n\n  if (now - lastMouseMoveTime < 20) {\n    currentInput.isTouch = false;\n\n    document.removeEventListener('mousemove', onDocumentMouseMove);\n  }\n\n  lastMouseMoveTime = now;\n}\n\n/**\n * When an element is in focus and has a tippy, leaving the tab/window and\n * returning causes it to show again. For mouse users this is unexpected, but\n * for keyboard use it makes sense.\n * TODO: find a better technique to solve this problem\n */\nexport function onWindowBlur(): void {\n  const activeElement = document.activeElement as HTMLElement | null;\n\n  if (isReferenceElement(activeElement)) {\n    const instance = activeElement._tippy!;\n\n    if (activeElement.blur && !instance.state.isVisible) {\n      activeElement.blur();\n    }\n  }\n}\n\nexport default function bindGlobalEventListeners(): void {\n  document.addEventListener('touchstart', onDocumentTouchStart, TOUCH_OPTIONS);\n  window.addEventListener('blur', onWindowBlur);\n}\n", "export const isBrowser =\n  typeof window !== 'undefined' && typeof document !== 'undefined';\n\nexport const isIE11 = isBrowser\n  ? // @ts-ignore\n    !!window.msCrypto\n  : false;\n", "import {Targets} from './types';\n\nexport function createMemoryLeakWarning(method: string): string {\n  const txt = method === 'destroy' ? 'n already-' : ' ';\n\n  return [\n    `${method}() was called on a${txt}destroyed instance. This is a no-op but`,\n    'indicates a potential memory leak.',\n  ].join(' ');\n}\n\nexport function clean(value: string): string {\n  const spacesAndTabs = /[ \\t]{2,}/g;\n  const lineStartWithSpaces = /^[ \\t]*/gm;\n\n  return value\n    .replace(spacesAndTabs, ' ')\n    .replace(lineStartWithSpaces, '')\n    .trim();\n}\n\nfunction getDevMessage(message: string): string {\n  return clean(`\n  %ctippy.js\n\n  %c${clean(message)}\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  `);\n}\n\nexport function getFormattedMessage(message: string): string[] {\n  return [\n    getDevMessage(message),\n    // title\n    'color: #00C584; font-size: 1.3em; font-weight: bold;',\n    // message\n    'line-height: 1.5',\n    // footer\n    'color: #a6a095;',\n  ];\n}\n\n// Assume warnings and errors never have the same message\nlet visitedMessages: Set<string>;\nif (__DEV__) {\n  resetVisitedMessages();\n}\n\nexport function resetVisitedMessages(): void {\n  visitedMessages = new Set();\n}\n\nexport function warnWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.warn(...getFormattedMessage(message));\n  }\n}\n\nexport function errorWhen(condition: boolean, message: string): void {\n  if (condition && !visitedMessages.has(message)) {\n    visitedMessages.add(message);\n    console.error(...getFormattedMessage(message));\n  }\n}\n\nexport function validateTargets(targets: Targets): void {\n  const didPassFalsyValue = !targets;\n  const didPassPlainObject =\n    Object.prototype.toString.call(targets) === '[object Object]' &&\n    !(targets as any).addEventListener;\n\n  errorWhen(\n    didPassFalsyValue,\n    [\n      'tippy() was passed',\n      '`' + String(targets) + '`',\n      'as its targets (first) argument. Valid types are: String, Element,',\n      'Element[], or NodeList.',\n    ].join(' ')\n  );\n\n  errorWhen(\n    didPassPlainObject,\n    [\n      'tippy() was passed a plain object which is not supported as an argument',\n      'for virtual positioning. Use props.getReferenceClientRect instead.',\n    ].join(' ')\n  );\n}\n", "import {DefaultProps, Plugin, Props, ReferenceElement, Tippy} from './types';\nimport {\n  hasOwnProperty,\n  removeProperties,\n  invokeWithArgsOrReturn,\n} from './utils';\nimport {warnWhen} from './validation';\nimport {TIPPY_DEFAULT_APPEND_TO} from './constants';\n\nconst pluginProps = {\n  animateFill: false,\n  followCursor: false,\n  inlinePositioning: false,\n  sticky: false,\n};\n\nconst renderProps = {\n  allowHTML: false,\n  animation: 'fade',\n  arrow: true,\n  content: '',\n  inertia: false,\n  maxWidth: 350,\n  role: 'tooltip',\n  theme: '',\n  zIndex: 9999,\n};\n\nexport const defaultProps: DefaultProps = {\n  appendTo: TIPPY_DEFAULT_APPEND_TO,\n  aria: {\n    content: 'auto',\n    expanded: 'auto',\n  },\n  delay: 0,\n  duration: [300, 250],\n  getReferenceClientRect: null,\n  hideOnClick: true,\n  ignoreAttributes: false,\n  interactive: false,\n  interactiveBorder: 2,\n  interactiveDebounce: 0,\n  moveTransition: '',\n  offset: [0, 10],\n  onAfterUpdate() {},\n  onBeforeUpdate() {},\n  onCreate() {},\n  onDestroy() {},\n  onHidden() {},\n  onHide() {},\n  onMount() {},\n  onShow() {},\n  onShown() {},\n  onTrigger() {},\n  onUntrigger() {},\n  onClickOutside() {},\n  placement: 'top',\n  plugins: [],\n  popperOptions: {},\n  render: null,\n  showOnCreate: false,\n  touch: true,\n  trigger: 'mouseenter focus',\n  triggerTarget: null,\n  ...pluginProps,\n  ...renderProps,\n};\n\nconst defaultKeys = Object.keys(defaultProps);\n\nexport const setDefaultProps: Tippy['setDefaultProps'] = (partialProps) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateProps(partialProps, []);\n  }\n\n  const keys = Object.keys(partialProps) as Array<keyof DefaultProps>;\n  keys.forEach((key) => {\n    (defaultProps as any)[key] = partialProps[key];\n  });\n};\n\nexport function getExtendedPassedProps(\n  passedProps: Partial<Props> & Record<string, unknown>\n): Partial<Props> {\n  const plugins = passedProps.plugins || [];\n  const pluginProps = plugins.reduce<Record<string, unknown>>((acc, plugin) => {\n    const {name, defaultValue} = plugin;\n\n    if (name) {\n      acc[name] =\n        passedProps[name] !== undefined\n          ? passedProps[name]\n          : (defaultProps as any)[name] ?? defaultValue;\n    }\n\n    return acc;\n  }, {});\n\n  return {\n    ...passedProps,\n    ...pluginProps,\n  };\n}\n\nexport function getDataAttributeProps(\n  reference: ReferenceElement,\n  plugins: Plugin[]\n): Record<string, unknown> {\n  const propKeys = plugins\n    ? Object.keys(getExtendedPassedProps({...defaultProps, plugins}))\n    : defaultKeys;\n\n  const props = propKeys.reduce(\n    (acc: Partial<Props> & Record<string, unknown>, key) => {\n      const valueAsString = (\n        reference.getAttribute(`data-tippy-${key}`) || ''\n      ).trim();\n\n      if (!valueAsString) {\n        return acc;\n      }\n\n      if (key === 'content') {\n        acc[key] = valueAsString;\n      } else {\n        try {\n          acc[key] = JSON.parse(valueAsString);\n        } catch (e) {\n          acc[key] = valueAsString;\n        }\n      }\n\n      return acc;\n    },\n    {}\n  );\n\n  return props;\n}\n\nexport function evaluateProps(\n  reference: ReferenceElement,\n  props: Props\n): Props {\n  const out = {\n    ...props,\n    content: invokeWithArgsOrReturn(props.content, [reference]),\n    ...(props.ignoreAttributes\n      ? {}\n      : getDataAttributeProps(reference, props.plugins)),\n  };\n\n  out.aria = {\n    ...defaultProps.aria,\n    ...out.aria,\n  };\n\n  out.aria = {\n    expanded:\n      out.aria.expanded === 'auto' ? props.interactive : out.aria.expanded,\n    content:\n      out.aria.content === 'auto'\n        ? props.interactive\n          ? null\n          : 'describedby'\n        : out.aria.content,\n  };\n\n  return out;\n}\n\nexport function validateProps(\n  partialProps: Partial<Props> = {},\n  plugins: Plugin[] = []\n): void {\n  const keys = Object.keys(partialProps) as Array<keyof Props>;\n  keys.forEach((prop) => {\n    const nonPluginProps = removeProperties(\n      defaultProps,\n      Object.keys(pluginProps)\n    );\n\n    let didPassUnknownProp = !hasOwnProperty(nonPluginProps, prop);\n\n    // Check if the prop exists in `plugins`\n    if (didPassUnknownProp) {\n      didPassUnknownProp =\n        plugins.filter((plugin) => plugin.name === prop).length === 0;\n    }\n\n    warnWhen(\n      didPassUnknownProp,\n      [\n        `\\`${prop}\\``,\n        \"is not a valid prop. You may have spelled it incorrectly, or if it's\",\n        'a plugin, forgot to pass it in an array as props.plugins.',\n        '\\n\\n',\n        'All props: https://atomiks.github.io/tippyjs/v6/all-props/\\n',\n        'Plugins: https://atomiks.github.io/tippyjs/v6/plugins/',\n      ].join(' ')\n    );\n  });\n}\n", "import {\n  ARROW_CLASS,\n  BACKDROP_CLASS,\n  BOX_CLASS,\n  CONTENT_CLASS,\n  SVG_ARROW_CLASS,\n} from './constants';\nimport {div, isElement} from './dom-utils';\nimport {Instance, PopperElement, Props} from './types';\nimport {PopperChildren} from './types-internal';\nimport {arrayFrom} from './utils';\n\n// Firefox extensions don't allow .innerHTML = \"...\" property. This tricks it.\nconst innerHTML = (): 'innerHTML' => 'innerHTML';\n\nfunction dangerouslySetInnerHTML(element: Element, html: string): void {\n  element[innerHTML()] = html;\n}\n\nfunction createArrowElement(value: Props['arrow']): HTMLDivElement {\n  const arrow = div();\n\n  if (value === true) {\n    arrow.className = ARROW_CLASS;\n  } else {\n    arrow.className = SVG_ARROW_CLASS;\n\n    if (isElement(value)) {\n      arrow.appendChild(value);\n    } else {\n      dangerouslySetInnerHTML(arrow, value as string);\n    }\n  }\n\n  return arrow;\n}\n\nexport function setContent(content: HTMLDivElement, props: Props): void {\n  if (isElement(props.content)) {\n    dangerouslySetInnerHTML(content, '');\n    content.appendChild(props.content);\n  } else if (typeof props.content !== 'function') {\n    if (props.allowHTML) {\n      dangerouslySetInnerHTML(content, props.content);\n    } else {\n      content.textContent = props.content;\n    }\n  }\n}\n\nexport function getChildren(popper: PopperElement): PopperChildren {\n  const box = popper.firstElementChild as HTMLDivElement;\n  const boxChildren = arrayFrom(box.children);\n\n  return {\n    box,\n    content: boxChildren.find((node) => node.classList.contains(CONTENT_CLASS)),\n    arrow: boxChildren.find(\n      (node) =>\n        node.classList.contains(ARROW_CLASS) ||\n        node.classList.contains(SVG_ARROW_CLASS)\n    ),\n    backdrop: boxChildren.find((node) =>\n      node.classList.contains(BACKDROP_CLASS)\n    ),\n  };\n}\n\nexport function render(\n  instance: Instance\n): {\n  popper: PopperElement;\n  onUpdate?: (prevProps: Props, nextProps: Props) => void;\n} {\n  const popper = div();\n\n  const box = div();\n  box.className = BOX_CLASS;\n  box.setAttribute('data-state', 'hidden');\n  box.setAttribute('tabindex', '-1');\n\n  const content = div();\n  content.className = CONTENT_CLASS;\n  content.setAttribute('data-state', 'hidden');\n\n  setContent(content, instance.props);\n\n  popper.appendChild(box);\n  box.appendChild(content);\n\n  onUpdate(instance.props, instance.props);\n\n  function onUpdate(prevProps: Props, nextProps: Props): void {\n    const {box, content, arrow} = getChildren(popper);\n\n    if (nextProps.theme) {\n      box.setAttribute('data-theme', nextProps.theme);\n    } else {\n      box.removeAttribute('data-theme');\n    }\n\n    if (typeof nextProps.animation === 'string') {\n      box.setAttribute('data-animation', nextProps.animation);\n    } else {\n      box.removeAttribute('data-animation');\n    }\n\n    if (nextProps.inertia) {\n      box.setAttribute('data-inertia', '');\n    } else {\n      box.removeAttribute('data-inertia');\n    }\n\n    box.style.maxWidth =\n      typeof nextProps.maxWidth === 'number'\n        ? `${nextProps.maxWidth}px`\n        : nextProps.maxWidth;\n\n    if (nextProps.role) {\n      box.setAttribute('role', nextProps.role);\n    } else {\n      box.removeAttribute('role');\n    }\n\n    if (\n      prevProps.content !== nextProps.content ||\n      prevProps.allowHTML !== nextProps.allowHTML\n    ) {\n      setContent(content, instance.props);\n    }\n\n    if (nextProps.arrow) {\n      if (!arrow) {\n        box.appendChild(createArrowElement(nextProps.arrow));\n      } else if (prevProps.arrow !== nextProps.arrow) {\n        box.removeChild(arrow);\n        box.appendChild(createArrowElement(nextProps.arrow));\n      }\n    } else if (arrow) {\n      box.removeChild(arrow!);\n    }\n  }\n\n  return {\n    popper,\n    onUpdate,\n  };\n}\n\n// Runtime check to identify if the render function is the default one; this\n// way we can apply default CSS transitions logic and it can be tree-shaken away\nrender.$$tippy = true;\n", "import {createPopper, StrictModifiers, Modifier} from '@popperjs/core';\nimport {currentInput} from './bindGlobalEventListeners';\nimport {isIE11} from './browser';\nimport {TIPPY_DEFAULT_APPEND_TO, TOUCH_OPTIONS} from './constants';\nimport {\n  actualContains,\n  div,\n  getOwnerDocument,\n  isCursorOutsideInteractiveBorder,\n  isMouseEvent,\n  setTransitionDuration,\n  setVisibilityState,\n  updateTransitionEndListener,\n} from './dom-utils';\nimport {defaultProps, evaluateProps, getExtendedPassedProps} from './props';\nimport {getChildren} from './template';\nimport {\n  Content,\n  Instance,\n  LifecycleHooks,\n  PopperElement,\n  Props,\n  ReferenceElement,\n} from './types';\nimport {ListenerObject, PopperTreeData, PopperChildren} from './types-internal';\nimport {\n  arrayFrom,\n  debounce,\n  getValueAtIndexOrReturn,\n  invokeWithArgsOrReturn,\n  normalizeToArray,\n  pushIfUnique,\n  splitBySpaces,\n  unique,\n  removeUndefinedProps,\n} from './utils';\nimport {createMemoryLeakWarning, errorWhen, warnWhen} from './validation';\n\nlet idCounter = 1;\nlet mouseMoveListeners: ((event: MouseEvent) => void)[] = [];\n\n// Used by `hideAll()`\nexport let mountedInstances: Instance[] = [];\n\nexport default function createTippy(\n  reference: ReferenceElement,\n  passedProps: Partial<Props>\n): Instance {\n  const props = evaluateProps(reference, {\n    ...defaultProps,\n    ...getExtendedPassedProps(removeUndefinedProps(passedProps)),\n  });\n\n  // ===========================================================================\n  // 🔒 Private members\n  // ===========================================================================\n  let showTimeout: any;\n  let hideTimeout: any;\n  let scheduleHideAnimationFrame: number;\n  let isVisibleFromClick = false;\n  let didHideDueToDocumentMouseDown = false;\n  let didTouchMove = false;\n  let ignoreOnFirstUpdate = false;\n  let lastTriggerEvent: Event | undefined;\n  let currentTransitionEndListener: (event: TransitionEvent) => void;\n  let onFirstUpdate: () => void;\n  let listeners: ListenerObject[] = [];\n  let debouncedOnMouseMove = debounce(onMouseMove, props.interactiveDebounce);\n  let currentTarget: Element;\n\n  // ===========================================================================\n  // 🔑 Public members\n  // ===========================================================================\n  const id = idCounter++;\n  const popperInstance = null;\n  const plugins = unique(props.plugins);\n\n  const state = {\n    // Is the instance currently enabled?\n    isEnabled: true,\n    // Is the tippy currently showing and not transitioning out?\n    isVisible: false,\n    // Has the instance been destroyed?\n    isDestroyed: false,\n    // Is the tippy currently mounted to the DOM?\n    isMounted: false,\n    // Has the tippy finished transitioning in?\n    isShown: false,\n  };\n\n  const instance: Instance = {\n    // properties\n    id,\n    reference,\n    popper: div(),\n    popperInstance,\n    props,\n    state,\n    plugins,\n    // methods\n    clearDelayTimeouts,\n    setProps,\n    setContent,\n    show,\n    hide,\n    hideWithInteractivity,\n    enable,\n    disable,\n    unmount,\n    destroy,\n  };\n\n  // TODO: Investigate why this early return causes a TDZ error in the tests —\n  // it doesn't seem to happen in the browser\n  /* istanbul ignore if */\n  if (!props.render) {\n    if (__DEV__) {\n      errorWhen(true, 'render() function has not been supplied.');\n    }\n\n    return instance;\n  }\n\n  // ===========================================================================\n  // Initial mutations\n  // ===========================================================================\n  const {popper, onUpdate} = props.render(instance);\n\n  popper.setAttribute('data-__NAMESPACE_PREFIX__-root', '');\n  popper.id = `__NAMESPACE_PREFIX__-${instance.id}`;\n\n  instance.popper = popper;\n  reference._tippy = instance;\n  popper._tippy = instance;\n\n  const pluginsHooks = plugins.map((plugin) => plugin.fn(instance));\n  const hasAriaExpanded = reference.hasAttribute('aria-expanded');\n\n  addListeners();\n  handleAriaExpandedAttribute();\n  handleStyles();\n\n  invokeHook('onCreate', [instance]);\n\n  if (props.showOnCreate) {\n    scheduleShow();\n  }\n\n  // Prevent a tippy with a delay from hiding if the cursor left then returned\n  // before it started hiding\n  popper.addEventListener('mouseenter', () => {\n    if (instance.props.interactive && instance.state.isVisible) {\n      instance.clearDelayTimeouts();\n    }\n  });\n\n  popper.addEventListener('mouseleave', () => {\n    if (\n      instance.props.interactive &&\n      instance.props.trigger.indexOf('mouseenter') >= 0\n    ) {\n      getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    }\n  });\n\n  return instance;\n\n  // ===========================================================================\n  // 🔒 Private methods\n  // ===========================================================================\n  function getNormalizedTouchSettings(): [string | boolean, number] {\n    const {touch} = instance.props;\n    return Array.isArray(touch) ? touch : [touch, 0];\n  }\n\n  function getIsCustomTouchBehavior(): boolean {\n    return getNormalizedTouchSettings()[0] === 'hold';\n  }\n\n  function getIsDefaultRenderFn(): boolean {\n    // @ts-ignore\n    return !!instance.props.render?.$$tippy;\n  }\n\n  function getCurrentTarget(): Element {\n    return currentTarget || reference;\n  }\n\n  function getDocument(): Document {\n    const parent = getCurrentTarget().parentNode as Element;\n    return parent ? getOwnerDocument(parent) : document;\n  }\n\n  function getDefaultTemplateChildren(): PopperChildren {\n    return getChildren(popper);\n  }\n\n  function getDelay(isShow: boolean): number {\n    // For touch or keyboard input, force `0` delay for UX reasons\n    // Also if the instance is mounted but not visible (transitioning out),\n    // ignore delay\n    if (\n      (instance.state.isMounted && !instance.state.isVisible) ||\n      currentInput.isTouch ||\n      (lastTriggerEvent && lastTriggerEvent.type === 'focus')\n    ) {\n      return 0;\n    }\n\n    return getValueAtIndexOrReturn(\n      instance.props.delay,\n      isShow ? 0 : 1,\n      defaultProps.delay\n    );\n  }\n\n  function handleStyles(fromHide = false): void {\n    popper.style.pointerEvents =\n      instance.props.interactive && !fromHide ? '' : 'none';\n    popper.style.zIndex = `${instance.props.zIndex}`;\n  }\n\n  function invokeHook(\n    hook: keyof LifecycleHooks,\n    args: [Instance, any?],\n    shouldInvokePropsHook = true\n  ): void {\n    pluginsHooks.forEach((pluginHooks) => {\n      if (pluginHooks[hook]) {\n        pluginHooks[hook]!(...args);\n      }\n    });\n\n    if (shouldInvokePropsHook) {\n      instance.props[hook](...args);\n    }\n  }\n\n  function handleAriaContentAttribute(): void {\n    const {aria} = instance.props;\n\n    if (!aria.content) {\n      return;\n    }\n\n    const attr = `aria-${aria.content}`;\n    const id = popper.id;\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      const currentValue = node.getAttribute(attr);\n\n      if (instance.state.isVisible) {\n        node.setAttribute(attr, currentValue ? `${currentValue} ${id}` : id);\n      } else {\n        const nextValue = currentValue && currentValue.replace(id, '').trim();\n\n        if (nextValue) {\n          node.setAttribute(attr, nextValue);\n        } else {\n          node.removeAttribute(attr);\n        }\n      }\n    });\n  }\n\n  function handleAriaExpandedAttribute(): void {\n    if (hasAriaExpanded || !instance.props.aria.expanded) {\n      return;\n    }\n\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n\n    nodes.forEach((node) => {\n      if (instance.props.interactive) {\n        node.setAttribute(\n          'aria-expanded',\n          instance.state.isVisible && node === getCurrentTarget()\n            ? 'true'\n            : 'false'\n        );\n      } else {\n        node.removeAttribute('aria-expanded');\n      }\n    });\n  }\n\n  function cleanupInteractiveMouseListeners(): void {\n    getDocument().removeEventListener('mousemove', debouncedOnMouseMove);\n    mouseMoveListeners = mouseMoveListeners.filter(\n      (listener) => listener !== debouncedOnMouseMove\n    );\n  }\n\n  function onDocumentPress(event: MouseEvent | TouchEvent): void {\n    // Moved finger to scroll instead of an intentional tap outside\n    if (currentInput.isTouch) {\n      if (didTouchMove || event.type === 'mousedown') {\n        return;\n      }\n    }\n\n    const actualTarget =\n      (event.composedPath && event.composedPath()[0]) || event.target;\n\n    // Clicked on interactive popper\n    if (\n      instance.props.interactive &&\n      actualContains(popper, actualTarget as Element)\n    ) {\n      return;\n    }\n\n    // Clicked on the event listeners target\n    if (\n      normalizeToArray(instance.props.triggerTarget || reference).some((el) =>\n        actualContains(el, actualTarget as Element)\n      )\n    ) {\n      if (currentInput.isTouch) {\n        return;\n      }\n\n      if (\n        instance.state.isVisible &&\n        instance.props.trigger.indexOf('click') >= 0\n      ) {\n        return;\n      }\n    } else {\n      invokeHook('onClickOutside', [instance, event]);\n    }\n\n    if (instance.props.hideOnClick === true) {\n      instance.clearDelayTimeouts();\n      instance.hide();\n\n      // `mousedown` event is fired right before `focus` if pressing the\n      // currentTarget. This lets a tippy with `focus` trigger know that it\n      // should not show\n      didHideDueToDocumentMouseDown = true;\n      setTimeout(() => {\n        didHideDueToDocumentMouseDown = false;\n      });\n\n      // The listener gets added in `scheduleShow()`, but this may be hiding it\n      // before it shows, and hide()'s early bail-out behavior can prevent it\n      // from being cleaned up\n      if (!instance.state.isMounted) {\n        removeDocumentPress();\n      }\n    }\n  }\n\n  function onTouchMove(): void {\n    didTouchMove = true;\n  }\n\n  function onTouchStart(): void {\n    didTouchMove = false;\n  }\n\n  function addDocumentPress(): void {\n    const doc = getDocument();\n    doc.addEventListener('mousedown', onDocumentPress, true);\n    doc.addEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.addEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.addEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function removeDocumentPress(): void {\n    const doc = getDocument();\n    doc.removeEventListener('mousedown', onDocumentPress, true);\n    doc.removeEventListener('touchend', onDocumentPress, TOUCH_OPTIONS);\n    doc.removeEventListener('touchstart', onTouchStart, TOUCH_OPTIONS);\n    doc.removeEventListener('touchmove', onTouchMove, TOUCH_OPTIONS);\n  }\n\n  function onTransitionedOut(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, () => {\n      if (\n        !instance.state.isVisible &&\n        popper.parentNode &&\n        popper.parentNode.contains(popper)\n      ) {\n        callback();\n      }\n    });\n  }\n\n  function onTransitionedIn(duration: number, callback: () => void): void {\n    onTransitionEnd(duration, callback);\n  }\n\n  function onTransitionEnd(duration: number, callback: () => void): void {\n    const box = getDefaultTemplateChildren().box;\n\n    function listener(event: TransitionEvent): void {\n      if (event.target === box) {\n        updateTransitionEndListener(box, 'remove', listener);\n        callback();\n      }\n    }\n\n    // Make callback synchronous if duration is 0\n    // `transitionend` won't fire otherwise\n    if (duration === 0) {\n      return callback();\n    }\n\n    updateTransitionEndListener(box, 'remove', currentTransitionEndListener);\n    updateTransitionEndListener(box, 'add', listener);\n\n    currentTransitionEndListener = listener;\n  }\n\n  function on(\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    const nodes = normalizeToArray(instance.props.triggerTarget || reference);\n    nodes.forEach((node) => {\n      node.addEventListener(eventType, handler, options);\n      listeners.push({node, eventType, handler, options});\n    });\n  }\n\n  function addListeners(): void {\n    if (getIsCustomTouchBehavior()) {\n      on('touchstart', onTrigger, {passive: true});\n      on('touchend', onMouseLeave as EventListener, {passive: true});\n    }\n\n    splitBySpaces(instance.props.trigger).forEach((eventType) => {\n      if (eventType === 'manual') {\n        return;\n      }\n\n      on(eventType, onTrigger);\n\n      switch (eventType) {\n        case 'mouseenter':\n          on('mouseleave', onMouseLeave as EventListener);\n          break;\n        case 'focus':\n          on(isIE11 ? 'focusout' : 'blur', onBlurOrFocusOut as EventListener);\n          break;\n        case 'focusin':\n          on('focusout', onBlurOrFocusOut as EventListener);\n          break;\n      }\n    });\n  }\n\n  function removeListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function onTrigger(event: Event): void {\n    let shouldScheduleClickHide = false;\n\n    if (\n      !instance.state.isEnabled ||\n      isEventListenerStopped(event) ||\n      didHideDueToDocumentMouseDown\n    ) {\n      return;\n    }\n\n    const wasFocused = lastTriggerEvent?.type === 'focus';\n\n    lastTriggerEvent = event;\n    currentTarget = event.currentTarget as Element;\n\n    handleAriaExpandedAttribute();\n\n    if (!instance.state.isVisible && isMouseEvent(event)) {\n      // If scrolling, `mouseenter` events can be fired if the cursor lands\n      // over a new target, but `mousemove` events don't get fired. This\n      // causes interactive tooltips to get stuck open until the cursor is\n      // moved\n      mouseMoveListeners.forEach((listener) => listener(event));\n    }\n\n    // Toggle show/hide when clicking click-triggered tooltips\n    if (\n      event.type === 'click' &&\n      (instance.props.trigger.indexOf('mouseenter') < 0 ||\n        isVisibleFromClick) &&\n      instance.props.hideOnClick !== false &&\n      instance.state.isVisible\n    ) {\n      shouldScheduleClickHide = true;\n    } else {\n      scheduleShow(event);\n    }\n\n    if (event.type === 'click') {\n      isVisibleFromClick = !shouldScheduleClickHide;\n    }\n\n    if (shouldScheduleClickHide && !wasFocused) {\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseMove(event: MouseEvent): void {\n    const target = event.target as Node;\n    const isCursorOverReferenceOrPopper =\n      getCurrentTarget().contains(target) || popper.contains(target);\n\n    if (event.type === 'mousemove' && isCursorOverReferenceOrPopper) {\n      return;\n    }\n\n    const popperTreeData = getNestedPopperTree()\n      .concat(popper)\n      .map((popper) => {\n        const instance = popper._tippy!;\n        const state = instance.popperInstance?.state;\n\n        if (state) {\n          return {\n            popperRect: popper.getBoundingClientRect(),\n            popperState: state,\n            props,\n          };\n        }\n\n        return null;\n      })\n      .filter(Boolean) as PopperTreeData[];\n\n    if (isCursorOutsideInteractiveBorder(popperTreeData, event)) {\n      cleanupInteractiveMouseListeners();\n      scheduleHide(event);\n    }\n  }\n\n  function onMouseLeave(event: MouseEvent): void {\n    const shouldBail =\n      isEventListenerStopped(event) ||\n      (instance.props.trigger.indexOf('click') >= 0 && isVisibleFromClick);\n\n    if (shouldBail) {\n      return;\n    }\n\n    if (instance.props.interactive) {\n      instance.hideWithInteractivity(event);\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function onBlurOrFocusOut(event: FocusEvent): void {\n    if (\n      instance.props.trigger.indexOf('focusin') < 0 &&\n      event.target !== getCurrentTarget()\n    ) {\n      return;\n    }\n\n    // If focus was moved to within the popper\n    if (\n      instance.props.interactive &&\n      event.relatedTarget &&\n      popper.contains(event.relatedTarget as Element)\n    ) {\n      return;\n    }\n\n    scheduleHide(event);\n  }\n\n  function isEventListenerStopped(event: Event): boolean {\n    return currentInput.isTouch\n      ? getIsCustomTouchBehavior() !== event.type.indexOf('touch') >= 0\n      : false;\n  }\n\n  function createPopperInstance(): void {\n    destroyPopperInstance();\n\n    const {\n      popperOptions,\n      placement,\n      offset,\n      getReferenceClientRect,\n      moveTransition,\n    } = instance.props;\n\n    const arrow = getIsDefaultRenderFn() ? getChildren(popper).arrow : null;\n\n    const computedReference = getReferenceClientRect\n      ? {\n          getBoundingClientRect: getReferenceClientRect,\n          contextElement:\n            getReferenceClientRect.contextElement || getCurrentTarget(),\n        }\n      : reference;\n\n    const tippyModifier: Modifier<'$$tippy', Record<string, unknown>> = {\n      name: '$$tippy',\n      enabled: true,\n      phase: 'beforeWrite',\n      requires: ['computeStyles'],\n      fn({state}) {\n        if (getIsDefaultRenderFn()) {\n          const {box} = getDefaultTemplateChildren();\n\n          ['placement', 'reference-hidden', 'escaped'].forEach((attr) => {\n            if (attr === 'placement') {\n              box.setAttribute('data-placement', state.placement);\n            } else {\n              if (state.attributes.popper[`data-popper-${attr}`]) {\n                box.setAttribute(`data-${attr}`, '');\n              } else {\n                box.removeAttribute(`data-${attr}`);\n              }\n            }\n          });\n\n          state.attributes.popper = {};\n        }\n      },\n    };\n\n    type TippyModifier = Modifier<'$$tippy', Record<string, unknown>>;\n    type ExtendedModifiers = StrictModifiers | Partial<TippyModifier>;\n\n    const modifiers: Array<ExtendedModifiers> = [\n      {\n        name: 'offset',\n        options: {\n          offset,\n        },\n      },\n      {\n        name: 'preventOverflow',\n        options: {\n          padding: {\n            top: 2,\n            bottom: 2,\n            left: 5,\n            right: 5,\n          },\n        },\n      },\n      {\n        name: 'flip',\n        options: {\n          padding: 5,\n        },\n      },\n      {\n        name: 'computeStyles',\n        options: {\n          adaptive: !moveTransition,\n        },\n      },\n      tippyModifier,\n    ];\n\n    if (getIsDefaultRenderFn() && arrow) {\n      modifiers.push({\n        name: 'arrow',\n        options: {\n          element: arrow,\n          padding: 3,\n        },\n      });\n    }\n\n    modifiers.push(...(popperOptions?.modifiers || []));\n\n    instance.popperInstance = createPopper<ExtendedModifiers>(\n      computedReference,\n      popper,\n      {\n        ...popperOptions,\n        placement,\n        onFirstUpdate,\n        modifiers,\n      }\n    );\n  }\n\n  function destroyPopperInstance(): void {\n    if (instance.popperInstance) {\n      instance.popperInstance.destroy();\n      instance.popperInstance = null;\n    }\n  }\n\n  function mount(): void {\n    const {appendTo} = instance.props;\n\n    let parentNode: any;\n\n    // By default, we'll append the popper to the triggerTargets's parentNode so\n    // it's directly after the reference element so the elements inside the\n    // tippy can be tabbed to\n    // If there are clipping issues, the user can specify a different appendTo\n    // and ensure focus management is handled correctly manually\n    const node = getCurrentTarget();\n\n    if (\n      (instance.props.interactive && appendTo === TIPPY_DEFAULT_APPEND_TO) ||\n      appendTo === 'parent'\n    ) {\n      parentNode = node.parentNode;\n    } else {\n      parentNode = invokeWithArgsOrReturn(appendTo, [node]);\n    }\n\n    // The popper element needs to exist on the DOM before its position can be\n    // updated as Popper needs to read its dimensions\n    if (!parentNode.contains(popper)) {\n      parentNode.appendChild(popper);\n    }\n\n    instance.state.isMounted = true;\n\n    createPopperInstance();\n\n    /* istanbul ignore else */\n    if (__DEV__) {\n      // Accessibility check\n      warnWhen(\n        instance.props.interactive &&\n          appendTo === defaultProps.appendTo &&\n          node.nextElementSibling !== popper,\n        [\n          'Interactive tippy element may not be accessible via keyboard',\n          'navigation because it is not directly after the reference element',\n          'in the DOM source order.',\n          '\\n\\n',\n          'Using a wrapper <div> or <span> tag around the reference element',\n          'solves this by creating a new parentNode context.',\n          '\\n\\n',\n          'Specifying `appendTo: document.body` silences this warning, but it',\n          'assumes you are using a focus management solution to handle',\n          'keyboard navigation.',\n          '\\n\\n',\n          'See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity',\n        ].join(' ')\n      );\n    }\n  }\n\n  function getNestedPopperTree(): PopperElement[] {\n    return arrayFrom(\n      popper.querySelectorAll('[data-__NAMESPACE_PREFIX__-root]')\n    );\n  }\n\n  function scheduleShow(event?: Event): void {\n    instance.clearDelayTimeouts();\n\n    if (event) {\n      invokeHook('onTrigger', [instance, event]);\n    }\n\n    addDocumentPress();\n\n    let delay = getDelay(true);\n    const [touchValue, touchDelay] = getNormalizedTouchSettings();\n\n    if (currentInput.isTouch && touchValue === 'hold' && touchDelay) {\n      delay = touchDelay;\n    }\n\n    if (delay) {\n      showTimeout = setTimeout(() => {\n        instance.show();\n      }, delay);\n    } else {\n      instance.show();\n    }\n  }\n\n  function scheduleHide(event: Event): void {\n    instance.clearDelayTimeouts();\n\n    invokeHook('onUntrigger', [instance, event]);\n\n    if (!instance.state.isVisible) {\n      removeDocumentPress();\n\n      return;\n    }\n\n    // For interactive tippies, scheduleHide is added to a document.body handler\n    // from onMouseLeave so must intercept scheduled hides from mousemove/leave\n    // events when trigger contains mouseenter and click, and the tip is\n    // currently shown as a result of a click.\n    if (\n      instance.props.trigger.indexOf('mouseenter') >= 0 &&\n      instance.props.trigger.indexOf('click') >= 0 &&\n      ['mouseleave', 'mousemove'].indexOf(event.type) >= 0 &&\n      isVisibleFromClick\n    ) {\n      return;\n    }\n\n    const delay = getDelay(false);\n\n    if (delay) {\n      hideTimeout = setTimeout(() => {\n        if (instance.state.isVisible) {\n          instance.hide();\n        }\n      }, delay);\n    } else {\n      // Fixes a `transitionend` problem when it fires 1 frame too\n      // late sometimes, we don't want hide() to be called.\n      scheduleHideAnimationFrame = requestAnimationFrame(() => {\n        instance.hide();\n      });\n    }\n  }\n\n  // ===========================================================================\n  // 🔑 Public methods\n  // ===========================================================================\n  function enable(): void {\n    instance.state.isEnabled = true;\n  }\n\n  function disable(): void {\n    // Disabling the instance should also hide it\n    // https://github.com/atomiks/tippy.js-react/issues/106\n    instance.hide();\n    instance.state.isEnabled = false;\n  }\n\n  function clearDelayTimeouts(): void {\n    clearTimeout(showTimeout);\n    clearTimeout(hideTimeout);\n    cancelAnimationFrame(scheduleHideAnimationFrame);\n  }\n\n  function setProps(partialProps: Partial<Props>): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('setProps'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    invokeHook('onBeforeUpdate', [instance, partialProps]);\n\n    removeListeners();\n\n    const prevProps = instance.props;\n    const nextProps = evaluateProps(reference, {\n      ...prevProps,\n      ...removeUndefinedProps(partialProps),\n      ignoreAttributes: true,\n    });\n\n    instance.props = nextProps;\n\n    addListeners();\n\n    if (prevProps.interactiveDebounce !== nextProps.interactiveDebounce) {\n      cleanupInteractiveMouseListeners();\n      debouncedOnMouseMove = debounce(\n        onMouseMove,\n        nextProps.interactiveDebounce\n      );\n    }\n\n    // Ensure stale aria-expanded attributes are removed\n    if (prevProps.triggerTarget && !nextProps.triggerTarget) {\n      normalizeToArray(prevProps.triggerTarget).forEach((node) => {\n        node.removeAttribute('aria-expanded');\n      });\n    } else if (nextProps.triggerTarget) {\n      reference.removeAttribute('aria-expanded');\n    }\n\n    handleAriaExpandedAttribute();\n    handleStyles();\n\n    if (onUpdate) {\n      onUpdate(prevProps, nextProps);\n    }\n\n    if (instance.popperInstance) {\n      createPopperInstance();\n\n      // Fixes an issue with nested tippies if they are all getting re-rendered,\n      // and the nested ones get re-rendered first.\n      // https://github.com/atomiks/tippyjs-react/issues/177\n      // TODO: find a cleaner / more efficient solution(!)\n      getNestedPopperTree().forEach((nestedPopper) => {\n        // React (and other UI libs likely) requires a rAF wrapper as it flushes\n        // its work in one\n        requestAnimationFrame(nestedPopper._tippy!.popperInstance!.forceUpdate);\n      });\n    }\n\n    invokeHook('onAfterUpdate', [instance, partialProps]);\n  }\n\n  function setContent(content: Content): void {\n    instance.setProps({content});\n  }\n\n  function show(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('show'));\n    }\n\n    // Early bail-out\n    const isAlreadyVisible = instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const isTouchAndTouchDisabled =\n      currentInput.isTouch && !instance.props.touch;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      0,\n      defaultProps.duration\n    );\n\n    if (\n      isAlreadyVisible ||\n      isDestroyed ||\n      isDisabled ||\n      isTouchAndTouchDisabled\n    ) {\n      return;\n    }\n\n    // Normalize `disabled` behavior across browsers.\n    // Firefox allows events on disabled elements, but Chrome doesn't.\n    // Using a wrapper element (i.e. <span>) is recommended.\n    if (getCurrentTarget().hasAttribute('disabled')) {\n      return;\n    }\n\n    invokeHook('onShow', [instance], false);\n    if (instance.props.onShow(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = true;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'visible';\n    }\n\n    handleStyles();\n    addDocumentPress();\n\n    if (!instance.state.isMounted) {\n      popper.style.transition = 'none';\n    }\n\n    // If flipping to the opposite side after hiding at least once, the\n    // animation will use the wrong placement without resetting the duration\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n      setTransitionDuration([box, content], 0);\n    }\n\n    onFirstUpdate = (): void => {\n      if (!instance.state.isVisible || ignoreOnFirstUpdate) {\n        return;\n      }\n\n      ignoreOnFirstUpdate = true;\n\n      // reflow\n      void popper.offsetHeight;\n\n      popper.style.transition = instance.props.moveTransition;\n\n      if (getIsDefaultRenderFn() && instance.props.animation) {\n        const {box, content} = getDefaultTemplateChildren();\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'visible');\n      }\n\n      handleAriaContentAttribute();\n      handleAriaExpandedAttribute();\n\n      pushIfUnique(mountedInstances, instance);\n\n      // certain modifiers (e.g. `maxSize`) require a second update after the\n      // popper has been positioned for the first time\n      instance.popperInstance?.forceUpdate();\n\n      invokeHook('onMount', [instance]);\n\n      if (instance.props.animation && getIsDefaultRenderFn()) {\n        onTransitionedIn(duration, () => {\n          instance.state.isShown = true;\n          invokeHook('onShown', [instance]);\n        });\n      }\n    };\n\n    mount();\n  }\n\n  function hide(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('hide'));\n    }\n\n    // Early bail-out\n    const isAlreadyHidden = !instance.state.isVisible;\n    const isDestroyed = instance.state.isDestroyed;\n    const isDisabled = !instance.state.isEnabled;\n    const duration = getValueAtIndexOrReturn(\n      instance.props.duration,\n      1,\n      defaultProps.duration\n    );\n\n    if (isAlreadyHidden || isDestroyed || isDisabled) {\n      return;\n    }\n\n    invokeHook('onHide', [instance], false);\n    if (instance.props.onHide(instance) === false) {\n      return;\n    }\n\n    instance.state.isVisible = false;\n    instance.state.isShown = false;\n    ignoreOnFirstUpdate = false;\n    isVisibleFromClick = false;\n\n    if (getIsDefaultRenderFn()) {\n      popper.style.visibility = 'hidden';\n    }\n\n    cleanupInteractiveMouseListeners();\n    removeDocumentPress();\n    handleStyles(true);\n\n    if (getIsDefaultRenderFn()) {\n      const {box, content} = getDefaultTemplateChildren();\n\n      if (instance.props.animation) {\n        setTransitionDuration([box, content], duration);\n        setVisibilityState([box, content], 'hidden');\n      }\n    }\n\n    handleAriaContentAttribute();\n    handleAriaExpandedAttribute();\n\n    if (instance.props.animation) {\n      if (getIsDefaultRenderFn()) {\n        onTransitionedOut(duration, instance.unmount);\n      }\n    } else {\n      instance.unmount();\n    }\n  }\n\n  function hideWithInteractivity(event: MouseEvent): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(\n        instance.state.isDestroyed,\n        createMemoryLeakWarning('hideWithInteractivity')\n      );\n    }\n\n    getDocument().addEventListener('mousemove', debouncedOnMouseMove);\n    pushIfUnique(mouseMoveListeners, debouncedOnMouseMove);\n    debouncedOnMouseMove(event);\n  }\n\n  function unmount(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('unmount'));\n    }\n\n    if (instance.state.isVisible) {\n      instance.hide();\n    }\n\n    if (!instance.state.isMounted) {\n      return;\n    }\n\n    destroyPopperInstance();\n\n    // If a popper is not interactive, it will be appended outside the popper\n    // tree by default. This seems mainly for interactive tippies, but we should\n    // find a workaround if possible\n    getNestedPopperTree().forEach((nestedPopper) => {\n      nestedPopper._tippy!.unmount();\n    });\n\n    if (popper.parentNode) {\n      popper.parentNode.removeChild(popper);\n    }\n\n    mountedInstances = mountedInstances.filter((i) => i !== instance);\n\n    instance.state.isMounted = false;\n    invokeHook('onHidden', [instance]);\n  }\n\n  function destroy(): void {\n    /* istanbul ignore else */\n    if (__DEV__) {\n      warnWhen(instance.state.isDestroyed, createMemoryLeakWarning('destroy'));\n    }\n\n    if (instance.state.isDestroyed) {\n      return;\n    }\n\n    instance.clearDelayTimeouts();\n    instance.unmount();\n\n    removeListeners();\n\n    delete reference._tippy;\n\n    instance.state.isDestroyed = true;\n\n    invokeHook('onDestroy', [instance]);\n  }\n}\n", "import bindGlobalEventListeners, {\n  currentInput,\n} from './bindGlobalEventListeners';\nimport createTippy, {mountedInstances} from './createTippy';\nimport {getArrayOfElements, isElement, isReferenceElement} from './dom-utils';\nimport {defaultProps, setDefaultProps, validateProps} from './props';\nimport {HideAll, HideAllOptions, Instance, Props, Targets} from './types';\nimport {validateTargets, warnWhen} from './validation';\n\nfunction tippy(\n  targets: Targets,\n  optionalProps: Partial<Props> = {}\n): Instance | Instance[] {\n  const plugins = defaultProps.plugins.concat(optionalProps.plugins || []);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    validateTargets(targets);\n    validateProps(optionalProps, plugins);\n  }\n\n  bindGlobalEventListeners();\n\n  const passedProps: Partial<Props> = {...optionalProps, plugins};\n\n  const elements = getArrayOfElements(targets);\n\n  /* istanbul ignore else */\n  if (__DEV__) {\n    const isSingleContentElement = isElement(passedProps.content);\n    const isMoreThanOneReferenceElement = elements.length > 1;\n    warnWhen(\n      isSingleContentElement && isMoreThanOneReferenceElement,\n      [\n        'tippy() was passed an Element as the `content` prop, but more than',\n        'one tippy instance was created by this invocation. This means the',\n        'content element will only be appended to the last tippy instance.',\n        '\\n\\n',\n        'Instead, pass the .innerHTML of the element, or use a function that',\n        'returns a cloned version of the element instead.',\n        '\\n\\n',\n        '1) content: element.innerHTML\\n',\n        '2) content: () => element.cloneNode(true)',\n      ].join(' ')\n    );\n  }\n\n  const instances = elements.reduce<Instance[]>(\n    (acc, reference): Instance[] => {\n      const instance = reference && createTippy(reference, passedProps);\n\n      if (instance) {\n        acc.push(instance);\n      }\n\n      return acc;\n    },\n    []\n  );\n\n  return isElement(targets) ? instances[0] : instances;\n}\n\ntippy.defaultProps = defaultProps;\ntippy.setDefaultProps = setDefaultProps;\ntippy.currentInput = currentInput;\n\nexport default tippy;\n\nexport const hideAll: HideAll = ({\n  exclude: excludedReferenceOrInstance,\n  duration,\n}: HideAllOptions = {}) => {\n  mountedInstances.forEach((instance) => {\n    let isExcluded = false;\n\n    if (excludedReferenceOrInstance) {\n      isExcluded = isReferenceElement(excludedReferenceOrInstance)\n        ? instance.reference === excludedReferenceOrInstance\n        : instance.popper === (excludedReferenceOrInstance as Instance).popper;\n    }\n\n    if (!isExcluded) {\n      const originalDuration = instance.props.duration;\n\n      instance.setProps({duration});\n      instance.hide();\n\n      if (!instance.state.isDestroyed) {\n        instance.setProps({duration: originalDuration});\n      }\n    }\n  });\n};\n", "import tippy from '..';\nimport {div} from '../dom-utils';\nimport {\n  C<PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  CreateSingletonProps,\n  ReferenceElement,\n  CreateSingletonInstance,\n  Instance,\n  Props,\n} from '../types';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\nimport {applyStyles, Modifier} from '@popperjs/core';\n\n// The default `applyStyles` modifier has a cleanup function that gets called\n// every time the popper is destroyed (i.e. a new target), removing the styles\n// and causing transitions to break for singletons when the console is open, but\n// most notably for non-transform styles being used, `gpuAcceleration: false`.\nconst applyStylesModifier: Modifier<'applyStyles', Record<string, unknown>> = {\n  ...applyStyles,\n  effect({state}) {\n    const initialStyles = {\n      popper: {\n        position: state.options.strategy,\n        left: '0',\n        top: '0',\n        margin: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n      reference: {},\n    };\n\n    Object.assign(state.elements.popper.style, initialStyles.popper);\n    state.styles = initialStyles;\n\n    if (state.elements.arrow) {\n      Object.assign(state.elements.arrow.style, initialStyles.arrow);\n    }\n\n    // intentionally return no cleanup function\n    // return () => { ... }\n  },\n};\n\nconst createSingleton: CreateSingleton = (\n  tippyInstances,\n  optionalProps = {}\n) => {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !Array.isArray(tippyInstances),\n      [\n        'The first argument passed to createSingleton() must be an array of',\n        'tippy instances. The passed value was',\n        String(tippyInstances),\n      ].join(' ')\n    );\n  }\n\n  let individualInstances = tippyInstances;\n  let references: Array<ReferenceElement> = [];\n  let triggerTargets: Array<Element> = [];\n  let currentTarget: Element | null;\n  let overrides = optionalProps.overrides;\n  let interceptSetPropsCleanups: Array<() => void> = [];\n  let shownOnCreate = false;\n\n  function setTriggerTargets(): void {\n    triggerTargets = individualInstances\n      .map((instance) =>\n        normalizeToArray(instance.props.triggerTarget || instance.reference)\n      )\n      .reduce((acc, item) => acc.concat(item), []);\n  }\n\n  function setReferences(): void {\n    references = individualInstances.map((instance) => instance.reference);\n  }\n\n  function enableInstances(isEnabled: boolean): void {\n    individualInstances.forEach((instance) => {\n      if (isEnabled) {\n        instance.enable();\n      } else {\n        instance.disable();\n      }\n    });\n  }\n\n  function interceptSetProps(singleton: Instance): Array<() => void> {\n    return individualInstances.map((instance) => {\n      const originalSetProps = instance.setProps;\n\n      instance.setProps = (props): void => {\n        originalSetProps(props);\n\n        if (instance.reference === currentTarget) {\n          singleton.setProps(props);\n        }\n      };\n\n      return (): void => {\n        instance.setProps = originalSetProps;\n      };\n    });\n  }\n\n  // have to pass singleton, as it maybe undefined on first call\n  function prepareInstance(\n    singleton: Instance,\n    target: ReferenceElement\n  ): void {\n    const index = triggerTargets.indexOf(target);\n\n    // bail-out\n    if (target === currentTarget) {\n      return;\n    }\n\n    currentTarget = target;\n\n    const overrideProps: Partial<Props> = (overrides || [])\n      .concat('content')\n      .reduce((acc, prop) => {\n        (acc as any)[prop] = individualInstances[index].props[prop];\n        return acc;\n      }, {});\n\n    singleton.setProps({\n      ...overrideProps,\n      getReferenceClientRect:\n        typeof overrideProps.getReferenceClientRect === 'function'\n          ? overrideProps.getReferenceClientRect\n          : (): ClientRect => references[index]?.getBoundingClientRect(),\n    });\n  }\n\n  enableInstances(false);\n  setReferences();\n  setTriggerTargets();\n\n  const plugin: Plugin = {\n    fn() {\n      return {\n        onDestroy(): void {\n          enableInstances(true);\n        },\n        onHidden(): void {\n          currentTarget = null;\n        },\n        onClickOutside(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            currentTarget = null;\n          }\n        },\n        onShow(instance): void {\n          if (instance.props.showOnCreate && !shownOnCreate) {\n            shownOnCreate = true;\n            prepareInstance(instance, references[0]);\n          }\n        },\n        onTrigger(instance, event): void {\n          prepareInstance(instance, event.currentTarget as Element);\n        },\n      };\n    },\n  };\n\n  const singleton = tippy(div(), {\n    ...removeProperties(optionalProps, ['overrides']),\n    plugins: [plugin, ...(optionalProps.plugins || [])],\n    triggerTarget: triggerTargets,\n    popperOptions: {\n      ...optionalProps.popperOptions,\n      modifiers: [\n        ...(optionalProps.popperOptions?.modifiers || []),\n        applyStylesModifier,\n      ],\n    },\n  }) as CreateSingletonInstance<CreateSingletonProps>;\n\n  const originalShow = singleton.show;\n\n  singleton.show = (target?: ReferenceElement | Instance | number): void => {\n    originalShow();\n\n    // first time, showOnCreate or programmatic call with no params\n    // default to showing first instance\n    if (!currentTarget && target == null) {\n      return prepareInstance(singleton, references[0]);\n    }\n\n    // triggered from event (do nothing as prepareInstance already called by onTrigger)\n    // programmatic call with no params when already visible (do nothing again)\n    if (currentTarget && target == null) {\n      return;\n    }\n\n    // target is index of instance\n    if (typeof target === 'number') {\n      return (\n        references[target] && prepareInstance(singleton, references[target])\n      );\n    }\n\n    // target is a child tippy instance\n    if (individualInstances.indexOf(target as Instance) >= 0) {\n      const ref = (target as Instance).reference;\n      return prepareInstance(singleton, ref);\n    }\n\n    // target is a ReferenceElement\n    if (references.indexOf(target as ReferenceElement) >= 0) {\n      return prepareInstance(singleton, target as ReferenceElement);\n    }\n  };\n\n  singleton.showNext = (): void => {\n    const first = references[0];\n    if (!currentTarget) {\n      return singleton.show(0);\n    }\n    const index = references.indexOf(currentTarget);\n    singleton.show(references[index + 1] || first);\n  };\n\n  singleton.showPrevious = (): void => {\n    const last = references[references.length - 1];\n    if (!currentTarget) {\n      return singleton.show(last);\n    }\n    const index = references.indexOf(currentTarget);\n    const target = references[index - 1] || last;\n    singleton.show(target);\n  };\n\n  const originalSetProps = singleton.setProps;\n\n  singleton.setProps = (props): void => {\n    overrides = props.overrides || overrides;\n    originalSetProps(props);\n  };\n\n  singleton.setInstances = (nextInstances): void => {\n    enableInstances(true);\n    interceptSetPropsCleanups.forEach((fn) => fn());\n\n    individualInstances = nextInstances;\n\n    enableInstances(false);\n    setReferences();\n    setTriggerTargets();\n    interceptSetPropsCleanups = interceptSetProps(singleton);\n\n    singleton.setProps({triggerTarget: triggerTargets});\n  };\n\n  interceptSetPropsCleanups = interceptSetProps(singleton);\n\n  return singleton;\n};\n\nexport default createSingleton;\n", "import tippy from '..';\nimport {TOUCH_OPTIONS} from '../constants';\nimport {defaultProps} from '../props';\nimport {Instance, Props, Targets} from '../types';\nimport {ListenerObject} from '../types-internal';\nimport {normalizeToArray, removeProperties} from '../utils';\nimport {errorWhen} from '../validation';\n\nconst BUBBLING_EVENTS_MAP = {\n  mouseover: 'mouseenter',\n  focusin: 'focus',\n  click: 'click',\n};\n\n/**\n * Creates a delegate instance that controls the creation of tippy instances\n * for child elements (`target` CSS selector).\n */\nfunction delegate(\n  targets: Targets,\n  props: Partial<Props> & {target: string}\n): Instance | Instance[] {\n  /* istanbul ignore else */\n  if (__DEV__) {\n    errorWhen(\n      !(props && props.target),\n      [\n        'You must specity a `target` prop indicating a CSS selector string matching',\n        'the target elements that should receive a tippy.',\n      ].join(' ')\n    );\n  }\n\n  let listeners: ListenerObject[] = [];\n  let childTippyInstances: Instance[] = [];\n  let disabled = false;\n\n  const {target} = props;\n\n  const nativeProps = removeProperties(props, ['target']);\n  const parentProps = {...nativeProps, trigger: 'manual', touch: false};\n  const childProps = {\n    touch: defaultProps.touch,\n    ...nativeProps,\n    showOnCreate: true,\n  };\n\n  const returnValue = tippy(targets, parentProps);\n  const normalizedReturnValue = normalizeToArray(returnValue);\n\n  function onTrigger(event: Event): void {\n    if (!event.target || disabled) {\n      return;\n    }\n\n    const targetNode = (event.target as Element).closest(target);\n\n    if (!targetNode) {\n      return;\n    }\n\n    // Get relevant trigger with fallbacks:\n    // 1. Check `data-tippy-trigger` attribute on target node\n    // 2. Fallback to `trigger` passed to `delegate()`\n    // 3. Fallback to `defaultProps.trigger`\n    const trigger =\n      targetNode.getAttribute('data-tippy-trigger') ||\n      props.trigger ||\n      defaultProps.trigger;\n\n    // @ts-ignore\n    if (targetNode._tippy) {\n      return;\n    }\n\n    if (event.type === 'touchstart' && typeof childProps.touch === 'boolean') {\n      return;\n    }\n\n    if (\n      event.type !== 'touchstart' &&\n      trigger.indexOf((BUBBLING_EVENTS_MAP as any)[event.type]) < 0\n    ) {\n      return;\n    }\n\n    const instance = tippy(targetNode, childProps);\n\n    if (instance) {\n      childTippyInstances = childTippyInstances.concat(instance);\n    }\n  }\n\n  function on(\n    node: Element,\n    eventType: string,\n    handler: EventListener,\n    options: boolean | Record<string, unknown> = false\n  ): void {\n    node.addEventListener(eventType, handler, options);\n    listeners.push({node, eventType, handler, options});\n  }\n\n  function addEventListeners(instance: Instance): void {\n    const {reference} = instance;\n\n    on(reference, 'touchstart', onTrigger, TOUCH_OPTIONS);\n    on(reference, 'mouseover', onTrigger);\n    on(reference, 'focusin', onTrigger);\n    on(reference, 'click', onTrigger);\n  }\n\n  function removeEventListeners(): void {\n    listeners.forEach(({node, eventType, handler, options}: ListenerObject) => {\n      node.removeEventListener(eventType, handler, options);\n    });\n    listeners = [];\n  }\n\n  function applyMutations(instance: Instance): void {\n    const originalDestroy = instance.destroy;\n    const originalEnable = instance.enable;\n    const originalDisable = instance.disable;\n\n    instance.destroy = (shouldDestroyChildInstances = true): void => {\n      if (shouldDestroyChildInstances) {\n        childTippyInstances.forEach((instance) => {\n          instance.destroy();\n        });\n      }\n\n      childTippyInstances = [];\n\n      removeEventListeners();\n      originalDestroy();\n    };\n\n    instance.enable = (): void => {\n      originalEnable();\n      childTippyInstances.forEach((instance) => instance.enable());\n      disabled = false;\n    };\n\n    instance.disable = (): void => {\n      originalDisable();\n      childTippyInstances.forEach((instance) => instance.disable());\n      disabled = true;\n    };\n\n    addEventListeners(instance);\n  }\n\n  normalizedReturnValue.forEach(applyMutations);\n\n  return returnValue;\n}\n\nexport default delegate;\n", "import {BACKDROP_CLASS} from '../constants';\nimport {div, setVisibilityState} from '../dom-utils';\nimport {getChildren} from '../template';\nimport {AnimateFill} from '../types';\nimport {errorWhen} from '../validation';\n\nconst animateFill: AnimateFill = {\n  name: 'animateFill',\n  defaultValue: false,\n  fn(instance) {\n    // @ts-ignore\n    if (!instance.props.render?.$$tippy) {\n      if (__DEV__) {\n        errorWhen(\n          instance.props.animateFill,\n          'The `animateFill` plugin requires the default render function.'\n        );\n      }\n\n      return {};\n    }\n\n    const {box, content} = getChildren(instance.popper);\n\n    const backdrop = instance.props.animateFill\n      ? createBackdropElement()\n      : null;\n\n    return {\n      onCreate(): void {\n        if (backdrop) {\n          box.insertBefore(backdrop, box.firstElementChild!);\n          box.setAttribute('data-animatefill', '');\n          box.style.overflow = 'hidden';\n\n          instance.setProps({arrow: false, animation: 'shift-away'});\n        }\n      },\n      onMount(): void {\n        if (backdrop) {\n          const {transitionDuration} = box.style;\n          const duration = Number(transitionDuration.replace('ms', ''));\n\n          // The content should fade in after the backdrop has mostly filled the\n          // tooltip element. `clip-path` is the other alternative but is not\n          // well-supported and is buggy on some devices.\n          content.style.transitionDelay = `${Math.round(duration / 10)}ms`;\n\n          backdrop.style.transitionDuration = transitionDuration;\n          setVisibilityState([backdrop], 'visible');\n        }\n      },\n      onShow(): void {\n        if (backdrop) {\n          backdrop.style.transitionDuration = '0ms';\n        }\n      },\n      onHide(): void {\n        if (backdrop) {\n          setVisibilityState([backdrop], 'hidden');\n        }\n      },\n    };\n  },\n};\n\nexport default animateFill;\n\nfunction createBackdropElement(): HTMLDivElement {\n  const backdrop = div();\n  backdrop.className = BACKDROP_CLASS;\n  setVisibilityState([backdrop], 'hidden');\n  return backdrop;\n}\n", "import {getOwnerDocument, isMouseEvent} from '../dom-utils';\nimport {FollowCursor, Instance} from '../types';\n\nlet mouseCoords = {clientX: 0, clientY: 0};\nlet activeInstances: Array<{instance: Instance; doc: Document}> = [];\n\nfunction storeMouseCoords({clientX, clientY}: MouseEvent): void {\n  mouseCoords = {clientX, clientY};\n}\n\nfunction addMouseCoordsListener(doc: Document): void {\n  doc.addEventListener('mousemove', storeMouseCoords);\n}\n\nfunction removeMouseCoordsListener(doc: Document): void {\n  doc.removeEventListener('mousemove', storeMouseCoords);\n}\n\nconst followCursor: FollowCursor = {\n  name: 'followCursor',\n  defaultValue: false,\n  fn(instance) {\n    const reference = instance.reference;\n    const doc = getOwnerDocument(instance.props.triggerTarget || reference);\n\n    let isInternalUpdate = false;\n    let wasFocusEvent = false;\n    let isUnmounted = true;\n    let prevProps = instance.props;\n\n    function getIsInitialBehavior(): boolean {\n      return (\n        instance.props.followCursor === 'initial' && instance.state.isVisible\n      );\n    }\n\n    function addListener(): void {\n      doc.addEventListener('mousemove', onMouseMove);\n    }\n\n    function removeListener(): void {\n      doc.removeEventListener('mousemove', onMouseMove);\n    }\n\n    function unsetGetReferenceClientRect(): void {\n      isInternalUpdate = true;\n      instance.setProps({getReferenceClientRect: null});\n      isInternalUpdate = false;\n    }\n\n    function onMouseMove(event: MouseEvent): void {\n      // If the instance is interactive, avoid updating the position unless it's\n      // over the reference element\n      const isCursorOverReference = event.target\n        ? reference.contains(event.target as Node)\n        : true;\n      const {followCursor} = instance.props;\n      const {clientX, clientY} = event;\n\n      const rect = reference.getBoundingClientRect();\n      const relativeX = clientX - rect.left;\n      const relativeY = clientY - rect.top;\n\n      if (isCursorOverReference || !instance.props.interactive) {\n        instance.setProps({\n          // @ts-ignore - unneeded DOMRect properties\n          getReferenceClientRect() {\n            const rect = reference.getBoundingClientRect();\n\n            let x = clientX;\n            let y = clientY;\n\n            if (followCursor === 'initial') {\n              x = rect.left + relativeX;\n              y = rect.top + relativeY;\n            }\n\n            const top = followCursor === 'horizontal' ? rect.top : y;\n            const right = followCursor === 'vertical' ? rect.right : x;\n            const bottom = followCursor === 'horizontal' ? rect.bottom : y;\n            const left = followCursor === 'vertical' ? rect.left : x;\n\n            return {\n              width: right - left,\n              height: bottom - top,\n              top,\n              right,\n              bottom,\n              left,\n            };\n          },\n        });\n      }\n    }\n\n    function create(): void {\n      if (instance.props.followCursor) {\n        activeInstances.push({instance, doc});\n        addMouseCoordsListener(doc);\n      }\n    }\n\n    function destroy(): void {\n      activeInstances = activeInstances.filter(\n        (data) => data.instance !== instance\n      );\n\n      if (activeInstances.filter((data) => data.doc === doc).length === 0) {\n        removeMouseCoordsListener(doc);\n      }\n    }\n\n    return {\n      onCreate: create,\n      onDestroy: destroy,\n      onBeforeUpdate(): void {\n        prevProps = instance.props;\n      },\n      onAfterUpdate(_, {followCursor}): void {\n        if (isInternalUpdate) {\n          return;\n        }\n\n        if (\n          followCursor !== undefined &&\n          prevProps.followCursor !== followCursor\n        ) {\n          destroy();\n\n          if (followCursor) {\n            create();\n\n            if (\n              instance.state.isMounted &&\n              !wasFocusEvent &&\n              !getIsInitialBehavior()\n            ) {\n              addListener();\n            }\n          } else {\n            removeListener();\n            unsetGetReferenceClientRect();\n          }\n        }\n      },\n      onMount(): void {\n        if (instance.props.followCursor && !wasFocusEvent) {\n          if (isUnmounted) {\n            onMouseMove(mouseCoords as MouseEvent);\n            isUnmounted = false;\n          }\n\n          if (!getIsInitialBehavior()) {\n            addListener();\n          }\n        }\n      },\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          mouseCoords = {clientX: event.clientX, clientY: event.clientY};\n        }\n        wasFocusEvent = event.type === 'focus';\n      },\n      onHidden(): void {\n        if (instance.props.followCursor) {\n          unsetGetReferenceClientRect();\n          removeListener();\n          isUnmounted = true;\n        }\n      },\n    };\n  },\n};\n\nexport default followCursor;\n", "import {Modifier, Placement} from '@popperjs/core';\nimport {isMouseEvent} from '../dom-utils';\nimport {BasePlacement, InlinePositioning, Props} from '../types';\nimport {arrayFrom, getBasePlacement} from '../utils';\n\nfunction getProps(props: Props, modifier: Modifier<any, any>): Partial<Props> {\n  return {\n    popperOptions: {\n      ...props.popperOptions,\n      modifiers: [\n        ...(props.popperOptions?.modifiers || []).filter(\n          ({name}) => name !== modifier.name\n        ),\n        modifier,\n      ],\n    },\n  };\n}\n\nconst inlinePositioning: InlinePositioning = {\n  name: 'inlinePositioning',\n  defaultValue: false,\n  fn(instance) {\n    const {reference} = instance;\n\n    function isEnabled(): boolean {\n      return !!instance.props.inlinePositioning;\n    }\n\n    let placement: Placement;\n    let cursorRectIndex = -1;\n    let isInternalUpdate = false;\n    let triedPlacements: Array<string> = [];\n\n    const modifier: Modifier<\n      'tippyInlinePositioning',\n      Record<string, unknown>\n    > = {\n      name: 'tippyInlinePositioning',\n      enabled: true,\n      phase: 'afterWrite',\n      fn({state}) {\n        if (isEnabled()) {\n          if (triedPlacements.indexOf(state.placement) !== -1) {\n            triedPlacements = [];\n          }\n\n          if (\n            placement !== state.placement &&\n            triedPlacements.indexOf(state.placement) === -1\n          ) {\n            triedPlacements.push(state.placement);\n            instance.setProps({\n              // @ts-ignore - unneeded DOMRect properties\n              getReferenceClientRect: () =>\n                getReferenceClientRect(state.placement),\n            });\n          }\n\n          placement = state.placement;\n        }\n      },\n    };\n\n    function getReferenceClientRect(placement: Placement): Partial<DOMRect> {\n      return getInlineBoundingClientRect(\n        getBasePlacement(placement),\n        reference.getBoundingClientRect(),\n        arrayFrom(reference.getClientRects()),\n        cursorRectIndex\n      );\n    }\n\n    function setInternalProps(partialProps: Partial<Props>): void {\n      isInternalUpdate = true;\n      instance.setProps(partialProps);\n      isInternalUpdate = false;\n    }\n\n    function addModifier(): void {\n      if (!isInternalUpdate) {\n        setInternalProps(getProps(instance.props, modifier));\n      }\n    }\n\n    return {\n      onCreate: addModifier,\n      onAfterUpdate: addModifier,\n      onTrigger(_, event): void {\n        if (isMouseEvent(event)) {\n          const rects = arrayFrom(instance.reference.getClientRects());\n          const cursorRect = rects.find(\n            (rect) =>\n              rect.left - 2 <= event.clientX &&\n              rect.right + 2 >= event.clientX &&\n              rect.top - 2 <= event.clientY &&\n              rect.bottom + 2 >= event.clientY\n          );\n          const index = rects.indexOf(cursorRect);\n          cursorRectIndex = index > -1 ? index : cursorRectIndex;\n        }\n      },\n      onHidden(): void {\n        cursorRectIndex = -1;\n      },\n    };\n  },\n};\n\nexport default inlinePositioning;\n\nexport function getInlineBoundingClientRect(\n  currentBasePlacement: BasePlacement | null,\n  boundingRect: DOMRect,\n  clientRects: DOMRect[],\n  cursorRectIndex: number\n): {\n  top: number;\n  bottom: number;\n  left: number;\n  right: number;\n  width: number;\n  height: number;\n} {\n  // Not an inline element, or placement is not yet known\n  if (clientRects.length < 2 || currentBasePlacement === null) {\n    return boundingRect;\n  }\n\n  // There are two rects and they are disjoined\n  if (\n    clientRects.length === 2 &&\n    cursorRectIndex >= 0 &&\n    clientRects[0].left > clientRects[1].right\n  ) {\n    return clientRects[cursorRectIndex] || boundingRect;\n  }\n\n  switch (currentBasePlacement) {\n    case 'top':\n    case 'bottom': {\n      const firstRect = clientRects[0];\n      const lastRect = clientRects[clientRects.length - 1];\n      const isTop = currentBasePlacement === 'top';\n\n      const top = firstRect.top;\n      const bottom = lastRect.bottom;\n      const left = isTop ? firstRect.left : lastRect.left;\n      const right = isTop ? firstRect.right : lastRect.right;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    case 'left':\n    case 'right': {\n      const minLeft = Math.min(...clientRects.map((rects) => rects.left));\n      const maxRight = Math.max(...clientRects.map((rects) => rects.right));\n      const measureRects = clientRects.filter((rect) =>\n        currentBasePlacement === 'left'\n          ? rect.left === minLeft\n          : rect.right === maxRight\n      );\n\n      const top = measureRects[0].top;\n      const bottom = measureRects[measureRects.length - 1].bottom;\n      const left = minLeft;\n      const right = maxRight;\n      const width = right - left;\n      const height = bottom - top;\n\n      return {top, bottom, left, right, width, height};\n    }\n    default: {\n      return boundingRect;\n    }\n  }\n}\n", "import {VirtualElement} from '@popperjs/core';\nimport {ReferenceElement, Sticky} from '../types';\n\nconst sticky: Sticky = {\n  name: 'sticky',\n  defaultValue: false,\n  fn(instance) {\n    const {reference, popper} = instance;\n\n    function getReference(): ReferenceElement | VirtualElement {\n      return instance.popperInstance\n        ? instance.popperInstance.state.elements.reference\n        : reference;\n    }\n\n    function shouldCheck(value: 'reference' | 'popper'): boolean {\n      return instance.props.sticky === true || instance.props.sticky === value;\n    }\n\n    let prevRefRect: ClientRect | null = null;\n    let prevPopRect: ClientRect | null = null;\n\n    function updatePosition(): void {\n      const currentRefRect = shouldCheck('reference')\n        ? getReference().getBoundingClientRect()\n        : null;\n      const currentPopRect = shouldCheck('popper')\n        ? popper.getBoundingClientRect()\n        : null;\n\n      if (\n        (currentRefRect && areRectsDifferent(prevRefRect, currentRefRect)) ||\n        (currentPopRect && areRectsDifferent(prevPopRect, currentPopRect))\n      ) {\n        if (instance.popperInstance) {\n          instance.popperInstance.update();\n        }\n      }\n\n      prevRefRect = currentRefRect;\n      prevPopRect = currentPopRect;\n\n      if (instance.state.isMounted) {\n        requestAnimationFrame(updatePosition);\n      }\n    }\n\n    return {\n      onMount(): void {\n        if (instance.props.sticky) {\n          updatePosition();\n        }\n      },\n    };\n  },\n};\n\nexport default sticky;\n\nfunction areRectsDifferent(\n  rectA: ClientRect | null,\n  rectB: ClientRect | null\n): boolean {\n  if (rectA && rectB) {\n    return (\n      rectA.top !== rectB.top ||\n      rectA.right !== rectB.right ||\n      rectA.bottom !== rectB.bottom ||\n      rectA.left !== rectB.left\n    );\n  }\n\n  return true;\n}\n", "import tippy from '../src';\nimport {render} from '../src/template';\n\ntippy.setDefaultProps({render});\n\nexport {default, hideAll} from '../src';\nexport {default as createSingleton} from '../src/addons/createSingleton';\nexport {default as delegate} from '../src/addons/delegate';\nexport {default as animateFill} from '../src/plugins/animateFill';\nexport {default as followCursor} from '../src/plugins/followCursor';\nexport {default as inlinePositioning} from '../src/plugins/inlinePositioning';\nexport {default as sticky} from '../src/plugins/sticky';\nexport {ROUND_ARROW as roundArrow} from '../src/constants';\n"], "names": ["ROUND_ARROW", "BOX_CLASS", "CONTENT_CLASS", "BACKDROP_CLASS", "ARROW_CLASS", "SVG_ARROW_CLASS", "TOUCH_OPTIONS", "passive", "capture", "TIPPY_DEFAULT_APPEND_TO", "document", "body", "hasOwnProperty", "obj", "key", "call", "getValueAtIndexOrReturn", "value", "index", "defaultValue", "Array", "isArray", "v", "isType", "type", "str", "toString", "indexOf", "invokeWithArgsOrReturn", "args", "debounce", "fn", "ms", "timeout", "arg", "clearTimeout", "setTimeout", "removeProperties", "keys", "clone", "for<PERSON>ach", "splitBySpaces", "split", "filter", "Boolean", "normalizeToArray", "concat", "pushIfUnique", "arr", "push", "unique", "item", "getBasePlacement", "placement", "arrayFrom", "slice", "removeUndefinedProps", "Object", "reduce", "acc", "undefined", "div", "createElement", "isElement", "some", "isNodeList", "isMouseEvent", "isReferenceElement", "_tippy", "reference", "getArrayOfElements", "querySelectorAll", "setTransitionDuration", "els", "el", "style", "transitionDuration", "setVisibilityState", "state", "setAttribute", "getOwnerDocument", "elementOrElements", "element", "ownerDocument", "isCursorOutsideInteractiveBorder", "popperTreeData", "event", "clientX", "clientY", "every", "popperRect", "popperState", "props", "interactiveBorder", "basePlacement", "offsetData", "modifiersData", "offset", "topDistance", "top", "y", "bottomDistance", "bottom", "leftDistance", "left", "x", "rightDistance", "right", "exceedsTop", "exceedsBottom", "exceedsLeft", "exceedsRight", "updateTransitionEndListener", "box", "action", "listener", "method", "actualContains", "parent", "child", "target", "contains", "getRootNode", "host", "currentInput", "is<PERSON><PERSON>ch", "lastMouseMoveTime", "onDocumentTouchStart", "window", "performance", "addEventListener", "onDocumentMouseMove", "now", "removeEventListener", "onWindowBlur", "activeElement", "instance", "blur", "isVisible", "bindGlobalEventListeners", "<PERSON><PERSON><PERSON><PERSON>", "isIE11", "msCrypto", "createMemoryLeakWarning", "txt", "join", "clean", "spacesAndTabs", "lineStartWithSpaces", "replace", "trim", "getDevMessage", "message", "getFormattedMessage", "visitedMessages", "resetVisitedMessages", "Set", "warn<PERSON><PERSON>", "condition", "has", "add", "console", "warn", "<PERSON><PERSON><PERSON>", "error", "validateTargets", "targets", "didPassFalsyValue", "didPassPlainObject", "prototype", "String", "pluginProps", "animateFill", "followCursor", "inlinePositioning", "sticky", "renderProps", "allowHTML", "animation", "arrow", "content", "inertia", "max<PERSON><PERSON><PERSON>", "role", "theme", "zIndex", "defaultProps", "appendTo", "aria", "expanded", "delay", "duration", "getReferenceClientRect", "hideOnClick", "ignoreAttributes", "interactive", "interactiveDebounce", "moveTransition", "onAfterUpdate", "onBeforeUpdate", "onCreate", "onDestroy", "onHidden", "onHide", "onMount", "onShow", "onShown", "onTrigger", "onUntrigger", "onClickOutside", "plugins", "popperOptions", "render", "showOnCreate", "touch", "trigger", "triggerTarget", "defaultKeys", "setDefaultProps", "partialProps", "validateProps", "getExtendedPassedProps", "passedProps", "plugin", "name", "getDataAttributeProps", "propKeys", "valueAsString", "getAttribute", "JSON", "parse", "e", "evaluateProps", "out", "prop", "nonPluginProps", "didPassUnknownProp", "length", "innerHTML", "dangerouslySetInnerHTML", "html", "createArrowElement", "className", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "textContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "popper", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "boxChildren", "children", "find", "node", "classList", "backdrop", "onUpdate", "prevProps", "nextProps", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "$$tippy", "idCounter", "mouseMoveListeners", "mountedInstances", "createTippy", "showTimeout", "hideTimeout", "scheduleHideAnimationFrame", "isVisibleFromClick", "didHideDueToDocumentMouseDown", "didTouchMove", "ignoreOnFirstUpdate", "lastTriggerEvent", "currentTransitionEndListener", "onFirstUpdate", "listeners", "debouncedOnMouseMove", "onMouseMove", "currentTarget", "id", "popperInstance", "isEnabled", "isDestroyed", "isMounted", "isShown", "clearDelayTimeouts", "setProps", "show", "hide", "hideWithInteractivity", "enable", "disable", "unmount", "destroy", "pluginsHooks", "map", "hasAriaExpanded", "hasAttribute", "addListeners", "handleAriaExpandedAttribute", "handleStyles", "invokeHook", "scheduleShow", "getDocument", "getNormalizedTouchSettings", "getIsCustomTouchBehavior", "getIsDefaultRenderFn", "getC<PERSON>rentTarget", "parentNode", "getDefaultTemplateChildren", "get<PERSON>elay", "isShow", "fromHide", "pointerEvents", "hook", "shouldInvokePropsHook", "pluginHooks", "handleAriaContentAttribute", "attr", "nodes", "currentValue", "nextValue", "cleanupInteractiveMouseListeners", "onDocumentPress", "actual<PERSON>arget", "<PERSON><PERSON><PERSON>", "removeDocumentPress", "onTouchMove", "onTouchStart", "addDocumentPress", "doc", "onTransitionedOut", "callback", "onTransitionEnd", "onTransitionedIn", "on", "eventType", "handler", "options", "onMouseLeave", "onBlurOrFocusOut", "removeListeners", "shouldScheduleClickHide", "isEventListenerStopped", "wasFocused", "scheduleHide", "isCursorOverReferenceOrPopper", "getNestedPopperTree", "getBoundingClientRect", "shouldBail", "relatedTarget", "createPopperInstance", "destroyPopperInstance", "computedReference", "contextElement", "tippyModifier", "enabled", "phase", "requires", "attributes", "modifiers", "padding", "adaptive", "createPopper", "mount", "nextElement<PERSON><PERSON>ling", "touchValue", "touchDelay", "requestAnimationFrame", "cancelAnimationFrame", "nestedPopper", "forceUpdate", "isAlreadyVisible", "isDisabled", "isTouchAndTouchDisabled", "visibility", "transition", "offsetHeight", "isAlreadyHidden", "i", "tippy", "optionalProps", "elements", "isSingleContentElement", "isMoreThanOneReferenceElement", "instances", "hide<PERSON>ll", "excludedReferenceOrInstance", "exclude", "isExcluded", "originalDuration", "applyStylesModifier", "applyStyles", "effect", "initialStyles", "position", "strategy", "margin", "assign", "styles", "createSingleton", "tippyInstances", "individualInstances", "references", "triggerTargets", "overrides", "interceptSetPropsCleanups", "shownOnCreate", "setTriggerTargets", "setReferences", "enableInstances", "interceptSetProps", "singleton", "originalSetProps", "prepareInstance", "overrideProps", "originalShow", "ref", "showNext", "first", "showPrevious", "last", "setInstances", "nextInstances", "BUBBLING_EVENTS_MAP", "mouseover", "focusin", "click", "delegate", "childTippyInstances", "disabled", "nativeProps", "parentProps", "childProps", "returnValue", "normalizedReturnValue", "targetNode", "closest", "addEventListeners", "removeEventListeners", "applyMutations", "original<PERSON><PERSON>roy", "originalEnable", "originalDisable", "shouldDestroyChildInstances", "createBackdropElement", "insertBefore", "overflow", "Number", "transitionDelay", "Math", "round", "mouseCoords", "activeInstances", "storeMouseCoords", "addMouseCoordsListener", "removeMouseCoordsListener", "isInternalUpdate", "wasFocusEvent", "isUnmounted", "getIsInitialBehavior", "addListener", "removeListener", "unsetGetReferenceClientRect", "isCursorOverReference", "rect", "relativeX", "relativeY", "width", "height", "create", "data", "_", "getProps", "modifier", "cursorRectIndex", "triedPlacements", "getInlineBoundingClientRect", "getClientRects", "setInternalProps", "addModifier", "rects", "cursorRect", "currentBasePlacement", "boundingRect", "clientRects", "firstRect", "lastRect", "isTop", "minLeft", "min", "maxRight", "max", "measureRects", "getReference", "<PERSON><PERSON><PERSON><PERSON>", "prevRefRect", "prevPopRect", "updatePosition", "currentRefRect", "currentPopRect", "areRectsDifferent", "update", "rectA", "rectB"], "mappings": ";;;;;;;;;;;IAAaA,WAAW,GACtB;AAEK,IAAMC,SAAS,cAAf;AACA,IAAMC,aAAa,kBAAnB;AACA,IAAMC,cAAc,mBAApB;AACA,IAAMC,WAAW,gBAAjB;AACA,IAAMC,eAAe,oBAArB;AAEA,IAAMC,aAAa,GAAG;AAACC,EAAAA,OAAO,EAAE,IAAV;AAAgBC,EAAAA,OAAO,EAAE;AAAzB,CAAtB;AAEA,IAAMC,uBAAuB,GAAG,SAA1BA,uBAA0B;AAAA,SAAMC,QAAQ,CAACC,IAAf;AAAA,CAAhC;;ACTA,SAASC,cAAT,CACLC,GADK,EAELC,GAFK,EAGI;AACT,SAAO,GAAGF,cAAH,CAAkBG,IAAlB,CAAuBF,GAAvB,EAA4BC,GAA5B,CAAP;AACD;AAED,AAAO,SAASE,uBAAT,CACLC,KADK,EAELC,KAFK,EAGLC,YAHK,EAIF;AACH,MAAIC,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,QAAMK,CAAC,GAAGL,KAAK,CAACC,KAAD,CAAf;AACA,WAAOI,CAAC,IAAI,IAAL,GACHF,KAAK,CAACC,OAAN,CAAcF,YAAd,IACEA,YAAY,CAACD,KAAD,CADd,GAEEC,YAHC,GAIHG,CAJJ;AAKD;;AAED,SAAOL,KAAP;AACD;AAED,AAAO,SAASM,MAAT,CAAgBN,KAAhB,EAA4BO,IAA5B,EAAmD;AACxD,MAAMC,GAAG,GAAG,GAAGC,QAAH,CAAYX,IAAZ,CAAiBE,KAAjB,CAAZ;AACA,SAAOQ,GAAG,CAACE,OAAJ,CAAY,SAAZ,MAA2B,CAA3B,IAAgCF,GAAG,CAACE,OAAJ,CAAeH,IAAf,UAA0B,CAAC,CAAlE;AACD;AAED,AAAO,SAASI,sBAAT,CAAgCX,KAAhC,EAA4CY,IAA5C,EAA8D;AACnE,SAAO,OAAOZ,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,MAAL,SAASY,IAAT,CAA9B,GAA+CZ,KAAtD;AACD;AAED,AAAO,SAASa,QAAT,CACLC,EADK,EAELC,EAFK,EAGa;AAClB;AACA,MAAIA,EAAE,KAAK,CAAX,EAAc;AACZ,WAAOD,EAAP;AACD;;AAED,MAAIE,OAAJ;AAEA,SAAO,UAACC,GAAD,EAAe;AACpBC,IAAAA,YAAY,CAACF,OAAD,CAAZ;AACAA,IAAAA,OAAO,GAAGG,UAAU,CAAC,YAAM;AACzBL,MAAAA,EAAE,CAACG,GAAD,CAAF;AACD,KAFmB,EAEjBF,EAFiB,CAApB;AAGD,GALD;AAMD;AAED,AAAO,SAASK,gBAAT,CAA6BxB,GAA7B,EAAqCyB,IAArC,EAAiE;AACtE,MAAMC,KAAK,qBAAO1B,GAAP,CAAX;AACAyB,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACpB,WAAQyB,KAAD,CAAezB,GAAf,CAAP;AACD,GAFD;AAGA,SAAOyB,KAAP;AACD;AAED,AAAO,SAASE,aAAT,CAAuBxB,KAAvB,EAAgD;AACrD,SAAOA,KAAK,CAACyB,KAAN,CAAY,KAAZ,EAAmBC,MAAnB,CAA0BC,OAA1B,CAAP;AACD;AAED,AAAO,SAASC,gBAAT,CAA6B5B,KAA7B,EAAkD;AACvD,SAAQ,EAAD,CAAY6B,MAAZ,CAAmB7B,KAAnB,CAAP;AACD;AAED,AAAO,SAAS8B,YAAT,CAAyBC,GAAzB,EAAmC/B,KAAnC,EAAmD;AACxD,MAAI+B,GAAG,CAACrB,OAAJ,CAAYV,KAAZ,MAAuB,CAAC,CAA5B,EAA+B;AAC7B+B,IAAAA,GAAG,CAACC,IAAJ,CAAShC,KAAT;AACD;AACF;AAED,AAIO,SAASiC,MAAT,CAAmBF,GAAnB,EAAkC;AACvC,SAAOA,GAAG,CAACL,MAAJ,CAAW,UAACQ,IAAD,EAAOjC,KAAP;AAAA,WAAiB8B,GAAG,CAACrB,OAAJ,CAAYwB,IAAZ,MAAsBjC,KAAvC;AAAA,GAAX,CAAP;AACD;AAED,AAIO,SAASkC,gBAAT,CAA0BC,SAA1B,EAA+D;AACpE,SAAOA,SAAS,CAACX,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAP;AACD;AAED,AAAO,SAASY,SAAT,CAAmBrC,KAAnB,EAAiD;AACtD,SAAO,GAAGsC,KAAH,CAASxC,IAAT,CAAcE,KAAd,CAAP;AACD;AAED,AAAO,SAASuC,oBAAT,CACL3C,GADK,EAE6B;AAClC,SAAO4C,MAAM,CAACnB,IAAP,CAAYzB,GAAZ,EAAiB6C,MAAjB,CAAwB,UAACC,GAAD,EAAM7C,GAAN,EAAc;AAC3C,QAAID,GAAG,CAACC,GAAD,CAAH,KAAa8C,SAAjB,EAA4B;AACzBD,MAAAA,GAAD,CAAa7C,GAAb,IAAoBD,GAAG,CAACC,GAAD,CAAvB;AACD;;AAED,WAAO6C,GAAP;AACD,GANM,EAMJ,EANI,CAAP;AAOD;;ACtGM,SAASE,GAAT,GAA+B;AACpC,SAAOnD,QAAQ,CAACoD,aAAT,CAAuB,KAAvB,CAAP;AACD;AAED,AAAO,SAASC,SAAT,CAAmB9C,KAAnB,EAAwE;AAC7E,SAAO,CAAC,SAAD,EAAY,UAAZ,EAAwB+C,IAAxB,CAA6B,UAACxC,IAAD;AAAA,WAAUD,MAAM,CAACN,KAAD,EAAQO,IAAR,CAAhB;AAAA,GAA7B,CAAP;AACD;AAED,AAAO,SAASyC,UAAT,CAAoBhD,KAApB,EAAuD;AAC5D,SAAOM,MAAM,CAACN,KAAD,EAAQ,UAAR,CAAb;AACD;AAED,AAAO,SAASiD,YAAT,CAAsBjD,KAAtB,EAA2D;AAChE,SAAOM,MAAM,CAACN,KAAD,EAAQ,YAAR,CAAb;AACD;AAED,AAAO,SAASkD,kBAAT,CAA4BlD,KAA5B,EAAmE;AACxE,SAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACmD,MAAf,IAAyBnD,KAAK,CAACmD,MAAN,CAAaC,SAAb,KAA2BpD,KAAtD,CAAR;AACD;AAED,AAAO,SAASqD,kBAAT,CAA4BrD,KAA5B,EAAuD;AAC5D,MAAI8C,SAAS,CAAC9C,KAAD,CAAb,EAAsB;AACpB,WAAO,CAACA,KAAD,CAAP;AACD;;AAED,MAAIgD,UAAU,CAAChD,KAAD,CAAd,EAAuB;AACrB,WAAOqC,SAAS,CAACrC,KAAD,CAAhB;AACD;;AAED,MAAIG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CAAJ,EAA0B;AACxB,WAAOA,KAAP;AACD;;AAED,SAAOqC,SAAS,CAAC5C,QAAQ,CAAC6D,gBAAT,CAA0BtD,KAA1B,CAAD,CAAhB;AACD;AAED,AAAO,SAASuD,qBAAT,CACLC,GADK,EAELxD,KAFK,EAGC;AACNwD,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACC,KAAH,CAASC,kBAAT,GAAiC3D,KAAjC;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAAS4D,kBAAT,CACLJ,GADK,EAELK,KAFK,EAGC;AACNL,EAAAA,GAAG,CAACjC,OAAJ,CAAY,UAACkC,EAAD,EAAQ;AAClB,QAAIA,EAAJ,EAAQ;AACNA,MAAAA,EAAE,CAACK,YAAH,CAAgB,YAAhB,EAA8BD,KAA9B;AACD;AACF,GAJD;AAKD;AAED,AAAO,SAASE,gBAAT,CACLC,iBADK,EAEK;AAAA;;AACV,0BAAkBpC,gBAAgB,CAACoC,iBAAD,CAAlC;AAAA,MAAOC,OAAP,wBADU;;;AAIV,SAAOA,OAAO,QAAP,6BAAAA,OAAO,CAAEC,aAAT,mCAAwBxE,IAAxB,GAA+BuE,OAAO,CAACC,aAAvC,GAAuDzE,QAA9D;AACD;AAED,AAAO,SAAS0E,gCAAT,CACLC,cADK,EAELC,KAFK,EAGI;AACT,MAAOC,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;AAAA,MAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;AAEA,SAAOH,cAAc,CAACI,KAAf,CAAqB,gBAAsC;AAAA,QAApCC,UAAoC,QAApCA,UAAoC;AAAA,QAAxBC,WAAwB,QAAxBA,WAAwB;AAAA,QAAXC,KAAW,QAAXA,KAAW;AAChE,QAAOC,iBAAP,GAA4BD,KAA5B,CAAOC,iBAAP;AACA,QAAMC,aAAa,GAAG1C,gBAAgB,CAACuC,WAAW,CAACtC,SAAb,CAAtC;AACA,QAAM0C,UAAU,GAAGJ,WAAW,CAACK,aAAZ,CAA0BC,MAA7C;;AAEA,QAAI,CAACF,UAAL,EAAiB;AACf,aAAO,IAAP;AACD;;AAED,QAAMG,WAAW,GAAGJ,aAAa,KAAK,QAAlB,GAA6BC,UAAU,CAACI,GAAX,CAAgBC,CAA7C,GAAiD,CAArE;AACA,QAAMC,cAAc,GAAGP,aAAa,KAAK,KAAlB,GAA0BC,UAAU,CAACO,MAAX,CAAmBF,CAA7C,GAAiD,CAAxE;AACA,QAAMG,YAAY,GAAGT,aAAa,KAAK,OAAlB,GAA4BC,UAAU,CAACS,IAAX,CAAiBC,CAA7C,GAAiD,CAAtE;AACA,QAAMC,aAAa,GAAGZ,aAAa,KAAK,MAAlB,GAA2BC,UAAU,CAACY,KAAX,CAAkBF,CAA7C,GAAiD,CAAvE;AAEA,QAAMG,UAAU,GACdlB,UAAU,CAACS,GAAX,GAAiBX,OAAjB,GAA2BU,WAA3B,GAAyCL,iBAD3C;AAEA,QAAMgB,aAAa,GACjBrB,OAAO,GAAGE,UAAU,CAACY,MAArB,GAA8BD,cAA9B,GAA+CR,iBADjD;AAEA,QAAMiB,WAAW,GACfpB,UAAU,CAACc,IAAX,GAAkBjB,OAAlB,GAA4BgB,YAA5B,GAA2CV,iBAD7C;AAEA,QAAMkB,YAAY,GAChBxB,OAAO,GAAGG,UAAU,CAACiB,KAArB,GAA6BD,aAA7B,GAA6Cb,iBAD/C;AAGA,WAAOe,UAAU,IAAIC,aAAd,IAA+BC,WAA/B,IAA8CC,YAArD;AACD,GAxBM,CAAP;AAyBD;AAED,AAAO,SAASC,2BAAT,CACLC,GADK,EAELC,MAFK,EAGLC,QAHK,EAIC;AACN,MAAMC,MAAM,GAAMF,MAAN,kBAAZ,CADM;AAMN;;AACA,GAAC,eAAD,EAAkB,qBAAlB,EAAyC1E,OAAzC,CAAiD,UAAC8C,KAAD,EAAW;AAC1D2B,IAAAA,GAAG,CAACG,MAAD,CAAH,CAAY9B,KAAZ,EAAmB6B,QAAnB;AACD,GAFD;AAGD;AAED;AACA;AACA;AACA;;AACA,AAAO,SAASE,cAAT,CAAwBC,MAAxB,EAAyCC,KAAzC,EAAkE;AACvE,MAAIC,MAAM,GAAGD,KAAb;;AACA,SAAOC,MAAP,EAAe;AAAA;;AACb,QAAIF,MAAM,CAACG,QAAP,CAAgBD,MAAhB,CAAJ,EAA6B;AAC3B,aAAO,IAAP;AACD;;AACDA,IAAAA,MAAM,GAAIA,MAAM,CAACE,WAAX,2CAAIF,MAAM,CAACE,WAAP,EAAJ,qBAAG,oBAAiCC,IAA1C;AACD;;AACD,SAAO,KAAP;AACD;;AClIM,IAAMC,YAAY,GAAG;AAACC,EAAAA,OAAO,EAAE;AAAV,CAArB;AACP,IAAIC,iBAAiB,GAAG,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASC,oBAAT,GAAsC;AAC3C,MAAIH,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAEDD,EAAAA,YAAY,CAACC,OAAb,GAAuB,IAAvB;;AAEA,MAAIG,MAAM,CAACC,WAAX,EAAwB;AACtBvH,IAAAA,QAAQ,CAACwH,gBAAT,CAA0B,WAA1B,EAAuCC,mBAAvC;AACD;AACF;AAED;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASA,mBAAT,GAAqC;AAC1C,MAAMC,GAAG,GAAGH,WAAW,CAACG,GAAZ,EAAZ;;AAEA,MAAIA,GAAG,GAAGN,iBAAN,GAA0B,EAA9B,EAAkC;AAChCF,IAAAA,YAAY,CAACC,OAAb,GAAuB,KAAvB;AAEAnH,IAAAA,QAAQ,CAAC2H,mBAAT,CAA6B,WAA7B,EAA0CF,mBAA1C;AACD;;AAEDL,EAAAA,iBAAiB,GAAGM,GAApB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASE,YAAT,GAA8B;AACnC,MAAMC,aAAa,GAAG7H,QAAQ,CAAC6H,aAA/B;;AAEA,MAAIpE,kBAAkB,CAACoE,aAAD,CAAtB,EAAuC;AACrC,QAAMC,QAAQ,GAAGD,aAAa,CAACnE,MAA/B;;AAEA,QAAImE,aAAa,CAACE,IAAd,IAAsB,CAACD,QAAQ,CAAC1D,KAAT,CAAe4D,SAA1C,EAAqD;AACnDH,MAAAA,aAAa,CAACE,IAAd;AACD;AACF;AACF;AAED,AAAe,SAASE,wBAAT,GAA0C;AACvDjI,EAAAA,QAAQ,CAACwH,gBAAT,CAA0B,YAA1B,EAAwCH,oBAAxC,EAA8DzH,aAA9D;AACA0H,EAAAA,MAAM,CAACE,gBAAP,CAAwB,MAAxB,EAAgCI,YAAhC;AACD;;AC9DM,IAAMM,SAAS,GACpB,OAAOZ,MAAP,KAAkB,WAAlB,IAAiC,OAAOtH,QAAP,KAAoB,WADhD;AAGP,AAAO,IAAMmI,MAAM,GAAGD,SAAS;AAE3B,CAAC,CAACZ,MAAM,CAACc,QAFkB,GAG3B,KAHG;;ACDA,SAASC,uBAAT,CAAiC3B,MAAjC,EAAyD;AAC9D,MAAM4B,GAAG,GAAG5B,MAAM,KAAK,SAAX,GAAuB,YAAvB,GAAsC,GAAlD;AAEA,SAAO,CACFA,MADE,0BACyB4B,GADzB,8CAEL,oCAFK,EAGLC,IAHK,CAGA,GAHA,CAAP;AAID;AAED,AAAO,SAASC,KAAT,CAAejI,KAAf,EAAsC;AAC3C,MAAMkI,aAAa,GAAG,YAAtB;AACA,MAAMC,mBAAmB,GAAG,WAA5B;AAEA,SAAOnI,KAAK,CACToI,OADI,CACIF,aADJ,EACmB,GADnB,EAEJE,OAFI,CAEID,mBAFJ,EAEyB,EAFzB,EAGJE,IAHI,EAAP;AAID;;AAED,SAASC,aAAT,CAAuBC,OAAvB,EAAgD;AAC9C,SAAON,KAAK,4BAGRA,KAAK,CAACM,OAAD,CAHG,0GAAZ;AAOD;;AAED,AAAO,SAASC,mBAAT,CAA6BD,OAA7B,EAAwD;AAC7D,SAAO,CACLD,aAAa,CAACC,OAAD,CADR;AAGL,wDAHK;AAKL,oBALK;AAOL,mBAPK,CAAP;AASD;;AAGD,IAAIE,eAAJ;;AACA,2CAAa;AACXC,EAAAA,oBAAoB;AACrB;;AAED,AAAO,SAASA,oBAAT,GAAsC;AAC3CD,EAAAA,eAAe,GAAG,IAAIE,GAAJ,EAAlB;AACD;AAED,AAAO,SAASC,QAAT,CAAkBC,SAAlB,EAAsCN,OAAtC,EAA6D;AAClE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,gBAAAS,OAAO,EAACC,IAAR,iBAAgBT,mBAAmB,CAACD,OAAD,CAAnC;AACD;AACF;AAED,AAAO,SAASW,SAAT,CAAmBL,SAAnB,EAAuCN,OAAvC,EAA8D;AACnE,MAAIM,SAAS,IAAI,CAACJ,eAAe,CAACK,GAAhB,CAAoBP,OAApB,CAAlB,EAAgD;AAAA;;AAC9CE,IAAAA,eAAe,CAACM,GAAhB,CAAoBR,OAApB;;AACA,iBAAAS,OAAO,EAACG,KAAR,kBAAiBX,mBAAmB,CAACD,OAAD,CAApC;AACD;AACF;AAED,AAAO,SAASa,eAAT,CAAyBC,OAAzB,EAAiD;AACtD,MAAMC,iBAAiB,GAAG,CAACD,OAA3B;AACA,MAAME,kBAAkB,GACtB/G,MAAM,CAACgH,SAAP,CAAiB/I,QAAjB,CAA0BX,IAA1B,CAA+BuJ,OAA/B,MAA4C,iBAA5C,IACA,CAAEA,OAAD,CAAiBpC,gBAFpB;AAIAiC,EAAAA,SAAS,CACPI,iBADO,EAEP,CACE,oBADF,EAEE,MAAMG,MAAM,CAACJ,OAAD,CAAZ,GAAwB,GAF1B,EAGE,oEAHF,EAIE,yBAJF,EAKErB,IALF,CAKO,GALP,CAFO,CAAT;AAUAkB,EAAAA,SAAS,CACPK,kBADO,EAEP,CACE,yEADF,EAEE,oEAFF,EAGEvB,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;ACjFD,IAAM0B,WAAW,GAAG;AAClBC,EAAAA,WAAW,EAAE,KADK;AAElBC,EAAAA,YAAY,EAAE,KAFI;AAGlBC,EAAAA,iBAAiB,EAAE,KAHD;AAIlBC,EAAAA,MAAM,EAAE;AAJU,CAApB;AAOA,IAAMC,WAAW,GAAG;AAClBC,EAAAA,SAAS,EAAE,KADO;AAElBC,EAAAA,SAAS,EAAE,MAFO;AAGlBC,EAAAA,KAAK,EAAE,IAHW;AAIlBC,EAAAA,OAAO,EAAE,EAJS;AAKlBC,EAAAA,OAAO,EAAE,KALS;AAMlBC,EAAAA,QAAQ,EAAE,GANQ;AAOlBC,EAAAA,IAAI,EAAE,SAPY;AAQlBC,EAAAA,KAAK,EAAE,EARW;AASlBC,EAAAA,MAAM,EAAE;AATU,CAApB;AAYA,AAAO,IAAMC,YAA0B;AACrCC,EAAAA,QAAQ,EAAElL,uBAD2B;AAErCmL,EAAAA,IAAI,EAAE;AACJR,IAAAA,OAAO,EAAE,MADL;AAEJS,IAAAA,QAAQ,EAAE;AAFN,GAF+B;AAMrCC,EAAAA,KAAK,EAAE,CAN8B;AAOrCC,EAAAA,QAAQ,EAAE,CAAC,GAAD,EAAM,GAAN,CAP2B;AAQrCC,EAAAA,sBAAsB,EAAE,IARa;AASrCC,EAAAA,WAAW,EAAE,IATwB;AAUrCC,EAAAA,gBAAgB,EAAE,KAVmB;AAWrCC,EAAAA,WAAW,EAAE,KAXwB;AAYrCtG,EAAAA,iBAAiB,EAAE,CAZkB;AAarCuG,EAAAA,mBAAmB,EAAE,CAbgB;AAcrCC,EAAAA,cAAc,EAAE,EAdqB;AAerCpG,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,EAAJ,CAf6B;AAgBrCqG,EAAAA,aAhBqC,2BAgBrB,EAhBqB;AAiBrCC,EAAAA,cAjBqC,4BAiBpB,EAjBoB;AAkBrCC,EAAAA,QAlBqC,sBAkB1B,EAlB0B;AAmBrCC,EAAAA,SAnBqC,uBAmBzB,EAnByB;AAoBrCC,EAAAA,QApBqC,sBAoB1B,EApB0B;AAqBrCC,EAAAA,MArBqC,oBAqB5B,EArB4B;AAsBrCC,EAAAA,OAtBqC,qBAsB3B,EAtB2B;AAuBrCC,EAAAA,MAvBqC,oBAuB5B,EAvB4B;AAwBrCC,EAAAA,OAxBqC,qBAwB3B,EAxB2B;AAyBrCC,EAAAA,SAzBqC,uBAyBzB,EAzByB;AA0BrCC,EAAAA,WA1BqC,yBA0BvB,EA1BuB;AA2BrCC,EAAAA,cA3BqC,4BA2BpB,EA3BoB;AA4BrC5J,EAAAA,SAAS,EAAE,KA5B0B;AA6BrC6J,EAAAA,OAAO,EAAE,EA7B4B;AA8BrCC,EAAAA,aAAa,EAAE,EA9BsB;AA+BrCC,EAAAA,MAAM,EAAE,IA/B6B;AAgCrCC,EAAAA,YAAY,EAAE,KAhCuB;AAiCrCC,EAAAA,KAAK,EAAE,IAjC8B;AAkCrCC,EAAAA,OAAO,EAAE,kBAlC4B;AAmCrCC,EAAAA,aAAa,EAAE;AAnCsB,GAoClC7C,WApCkC,EAqClCK,WArCkC,CAAhC;AAwCP,IAAMyC,WAAW,GAAGhK,MAAM,CAACnB,IAAP,CAAYoJ,YAAZ,CAApB;AAEA,AAAO,IAAMgC,eAAyC,GAAG,SAA5CA,eAA4C,CAACC,YAAD,EAAkB;AACzE;AACA,6CAAa;AACXC,IAAAA,aAAa,CAACD,YAAD,EAAe,EAAf,CAAb;AACD;;AAED,MAAMrL,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;AACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAAC1B,GAAD,EAAS;AACnB4K,IAAAA,YAAD,CAAsB5K,GAAtB,IAA6B6M,YAAY,CAAC7M,GAAD,CAAzC;AACD,GAFD;AAGD,CAVM;AAYP,AAAO,SAAS+M,sBAAT,CACLC,WADK,EAEW;AAChB,MAAMZ,OAAO,GAAGY,WAAW,CAACZ,OAAZ,IAAuB,EAAvC;AACA,MAAMvC,WAAW,GAAGuC,OAAO,CAACxJ,MAAR,CAAwC,UAACC,GAAD,EAAMoK,MAAN,EAAiB;AAC3E,QAAOC,IAAP,GAA6BD,MAA7B,CAAOC,IAAP;AAAA,QAAa7M,YAAb,GAA6B4M,MAA7B,CAAa5M,YAAb;;AAEA,QAAI6M,IAAJ,EAAU;AAAA;;AACRrK,MAAAA,GAAG,CAACqK,IAAD,CAAH,GACEF,WAAW,CAACE,IAAD,CAAX,KAAsBpK,SAAtB,GACIkK,WAAW,CAACE,IAAD,CADf,YAEKtC,YAAD,CAAsBsC,IAAtB,CAFJ,oBAEmC7M,YAHrC;AAID;;AAED,WAAOwC,GAAP;AACD,GAXmB,EAWjB,EAXiB,CAApB;AAaA,2BACKmK,WADL,EAEKnD,WAFL;AAID;AAED,AAAO,SAASsD,qBAAT,CACL5J,SADK,EAEL6I,OAFK,EAGoB;AACzB,MAAMgB,QAAQ,GAAGhB,OAAO,GACpBzJ,MAAM,CAACnB,IAAP,CAAYuL,sBAAsB,mBAAKnC,YAAL;AAAmBwB,IAAAA,OAAO,EAAPA;AAAnB,KAAlC,CADoB,GAEpBO,WAFJ;AAIA,MAAM7H,KAAK,GAAGsI,QAAQ,CAACxK,MAAT,CACZ,UAACC,GAAD,EAAgD7C,GAAhD,EAAwD;AACtD,QAAMqN,aAAa,GAAG,CACpB9J,SAAS,CAAC+J,YAAV,iBAAqCtN,GAArC,KAA+C,EAD3B,EAEpBwI,IAFoB,EAAtB;;AAIA,QAAI,CAAC6E,aAAL,EAAoB;AAClB,aAAOxK,GAAP;AACD;;AAED,QAAI7C,GAAG,KAAK,SAAZ,EAAuB;AACrB6C,MAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;AACD,KAFD,MAEO;AACL,UAAI;AACFxK,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWuN,IAAI,CAACC,KAAL,CAAWH,aAAX,CAAX;AACD,OAFD,CAEE,OAAOI,CAAP,EAAU;AACV5K,QAAAA,GAAG,CAAC7C,GAAD,CAAH,GAAWqN,aAAX;AACD;AACF;;AAED,WAAOxK,GAAP;AACD,GArBW,EAsBZ,EAtBY,CAAd;AAyBA,SAAOiC,KAAP;AACD;AAED,AAAO,SAAS4I,aAAT,CACLnK,SADK,EAELuB,KAFK,EAGE;AACP,MAAM6I,GAAG,qBACJ7I,KADI;AAEPwF,IAAAA,OAAO,EAAExJ,sBAAsB,CAACgE,KAAK,CAACwF,OAAP,EAAgB,CAAC/G,SAAD,CAAhB;AAFxB,KAGHuB,KAAK,CAACsG,gBAAN,GACA,EADA,GAEA+B,qBAAqB,CAAC5J,SAAD,EAAYuB,KAAK,CAACsH,OAAlB,CALlB,CAAT;AAQAuB,EAAAA,GAAG,CAAC7C,IAAJ,qBACKF,YAAY,CAACE,IADlB,EAEK6C,GAAG,CAAC7C,IAFT;AAKA6C,EAAAA,GAAG,CAAC7C,IAAJ,GAAW;AACTC,IAAAA,QAAQ,EACN4C,GAAG,CAAC7C,IAAJ,CAASC,QAAT,KAAsB,MAAtB,GAA+BjG,KAAK,CAACuG,WAArC,GAAmDsC,GAAG,CAAC7C,IAAJ,CAASC,QAFrD;AAGTT,IAAAA,OAAO,EACLqD,GAAG,CAAC7C,IAAJ,CAASR,OAAT,KAAqB,MAArB,GACIxF,KAAK,CAACuG,WAAN,GACE,IADF,GAEE,aAHN,GAIIsC,GAAG,CAAC7C,IAAJ,CAASR;AARN,GAAX;AAWA,SAAOqD,GAAP;AACD;AAED,AAAO,SAASb,aAAT,CACLD,YADK,EAELT,OAFK,EAGC;AAAA,MAFNS,YAEM;AAFNA,IAAAA,YAEM,GAFyB,EAEzB;AAAA;;AAAA,MADNT,OACM;AADNA,IAAAA,OACM,GADc,EACd;AAAA;;AACN,MAAM5K,IAAI,GAAGmB,MAAM,CAACnB,IAAP,CAAYqL,YAAZ,CAAb;AACArL,EAAAA,IAAI,CAACE,OAAL,CAAa,UAACkM,IAAD,EAAU;AACrB,QAAMC,cAAc,GAAGtM,gBAAgB,CACrCqJ,YADqC,EAErCjI,MAAM,CAACnB,IAAP,CAAYqI,WAAZ,CAFqC,CAAvC;AAKA,QAAIiE,kBAAkB,GAAG,CAAChO,cAAc,CAAC+N,cAAD,EAAiBD,IAAjB,CAAxC,CANqB;;AASrB,QAAIE,kBAAJ,EAAwB;AACtBA,MAAAA,kBAAkB,GAChB1B,OAAO,CAACvK,MAAR,CAAe,UAACoL,MAAD;AAAA,eAAYA,MAAM,CAACC,IAAP,KAAgBU,IAA5B;AAAA,OAAf,EAAiDG,MAAjD,KAA4D,CAD9D;AAED;;AAEDhF,IAAAA,QAAQ,CACN+E,kBADM,EAEN,OACOF,IADP,QAEE,sEAFF,EAGE,2DAHF,EAIE,MAJF,EAKE,8DALF,EAME,wDANF,EAOEzF,IAPF,CAOO,GAPP,CAFM,CAAR;AAWD,GAzBD;AA0BD;;AC9LD,IAAM6F,SAAS,GAAG,SAAZA,SAAY;AAAA,SAAmB,WAAnB;AAAA,CAAlB;;AAEA,SAASC,uBAAT,CAAiC7J,OAAjC,EAAmD8J,IAAnD,EAAuE;AACrE9J,EAAAA,OAAO,CAAC4J,SAAS,EAAV,CAAP,GAAuBE,IAAvB;AACD;;AAED,SAASC,kBAAT,CAA4BhO,KAA5B,EAAmE;AACjE,MAAMkK,KAAK,GAAGtH,GAAG,EAAjB;;AAEA,MAAI5C,KAAK,KAAK,IAAd,EAAoB;AAClBkK,IAAAA,KAAK,CAAC+D,SAAN,GAAkB9O,WAAlB;AACD,GAFD,MAEO;AACL+K,IAAAA,KAAK,CAAC+D,SAAN,GAAkB7O,eAAlB;;AAEA,QAAI0D,SAAS,CAAC9C,KAAD,CAAb,EAAsB;AACpBkK,MAAAA,KAAK,CAACgE,WAAN,CAAkBlO,KAAlB;AACD,KAFD,MAEO;AACL8N,MAAAA,uBAAuB,CAAC5D,KAAD,EAAQlK,KAAR,CAAvB;AACD;AACF;;AAED,SAAOkK,KAAP;AACD;;AAED,AAAO,SAASiE,UAAT,CAAoBhE,OAApB,EAA6CxF,KAA7C,EAAiE;AACtE,MAAI7B,SAAS,CAAC6B,KAAK,CAACwF,OAAP,CAAb,EAA8B;AAC5B2D,IAAAA,uBAAuB,CAAC3D,OAAD,EAAU,EAAV,CAAvB;AACAA,IAAAA,OAAO,CAAC+D,WAAR,CAAoBvJ,KAAK,CAACwF,OAA1B;AACD,GAHD,MAGO,IAAI,OAAOxF,KAAK,CAACwF,OAAb,KAAyB,UAA7B,EAAyC;AAC9C,QAAIxF,KAAK,CAACqF,SAAV,EAAqB;AACnB8D,MAAAA,uBAAuB,CAAC3D,OAAD,EAAUxF,KAAK,CAACwF,OAAhB,CAAvB;AACD,KAFD,MAEO;AACLA,MAAAA,OAAO,CAACiE,WAAR,GAAsBzJ,KAAK,CAACwF,OAA5B;AACD;AACF;AACF;AAED,AAAO,SAASkE,WAAT,CAAqBC,MAArB,EAA4D;AACjE,MAAMtI,GAAG,GAAGsI,MAAM,CAACC,iBAAnB;AACA,MAAMC,WAAW,GAAGnM,SAAS,CAAC2D,GAAG,CAACyI,QAAL,CAA7B;AAEA,SAAO;AACLzI,IAAAA,GAAG,EAAHA,GADK;AAELmE,IAAAA,OAAO,EAAEqE,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aAAUA,IAAI,CAACC,SAAL,CAAepI,QAAf,CAAwBvH,aAAxB,CAAV;AAAA,KAAjB,CAFJ;AAGLiL,IAAAA,KAAK,EAAEsE,WAAW,CAACE,IAAZ,CACL,UAACC,IAAD;AAAA,aACEA,IAAI,CAACC,SAAL,CAAepI,QAAf,CAAwBrH,WAAxB,KACAwP,IAAI,CAACC,SAAL,CAAepI,QAAf,CAAwBpH,eAAxB,CAFF;AAAA,KADK,CAHF;AAQLyP,IAAAA,QAAQ,EAAEL,WAAW,CAACE,IAAZ,CAAiB,UAACC,IAAD;AAAA,aACzBA,IAAI,CAACC,SAAL,CAAepI,QAAf,CAAwBtH,cAAxB,CADyB;AAAA,KAAjB;AARL,GAAP;AAYD;AAED,AAAO,SAASiN,MAAT,CACL5E,QADK,EAKL;AACA,MAAM+G,MAAM,GAAG1L,GAAG,EAAlB;AAEA,MAAMoD,GAAG,GAAGpD,GAAG,EAAf;AACAoD,EAAAA,GAAG,CAACiI,SAAJ,GAAgBjP,SAAhB;AACAgH,EAAAA,GAAG,CAAClC,YAAJ,CAAiB,YAAjB,EAA+B,QAA/B;AACAkC,EAAAA,GAAG,CAAClC,YAAJ,CAAiB,UAAjB,EAA6B,IAA7B;AAEA,MAAMqG,OAAO,GAAGvH,GAAG,EAAnB;AACAuH,EAAAA,OAAO,CAAC8D,SAAR,GAAoBhP,aAApB;AACAkL,EAAAA,OAAO,CAACrG,YAAR,CAAqB,YAArB,EAAmC,QAAnC;AAEAqK,EAAAA,UAAU,CAAChE,OAAD,EAAU5C,QAAQ,CAAC5C,KAAnB,CAAV;AAEA2J,EAAAA,MAAM,CAACJ,WAAP,CAAmBlI,GAAnB;AACAA,EAAAA,GAAG,CAACkI,WAAJ,CAAgB/D,OAAhB;AAEA2E,EAAAA,QAAQ,CAACvH,QAAQ,CAAC5C,KAAV,EAAiB4C,QAAQ,CAAC5C,KAA1B,CAAR;;AAEA,WAASmK,QAAT,CAAkBC,SAAlB,EAAoCC,SAApC,EAA4D;AAC1D,uBAA8BX,WAAW,CAACC,MAAD,CAAzC;AAAA,QAAOtI,GAAP,gBAAOA,GAAP;AAAA,QAAYmE,OAAZ,gBAAYA,OAAZ;AAAA,QAAqBD,KAArB,gBAAqBA,KAArB;;AAEA,QAAI8E,SAAS,CAACzE,KAAd,EAAqB;AACnBvE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,YAAjB,EAA+BkL,SAAS,CAACzE,KAAzC;AACD,KAFD,MAEO;AACLvE,MAAAA,GAAG,CAACiJ,eAAJ,CAAoB,YAApB;AACD;;AAED,QAAI,OAAOD,SAAS,CAAC/E,SAAjB,KAA+B,QAAnC,EAA6C;AAC3CjE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCkL,SAAS,CAAC/E,SAA7C;AACD,KAFD,MAEO;AACLjE,MAAAA,GAAG,CAACiJ,eAAJ,CAAoB,gBAApB;AACD;;AAED,QAAID,SAAS,CAAC5E,OAAd,EAAuB;AACrBpE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,cAAjB,EAAiC,EAAjC;AACD,KAFD,MAEO;AACLkC,MAAAA,GAAG,CAACiJ,eAAJ,CAAoB,cAApB;AACD;;AAEDjJ,IAAAA,GAAG,CAACtC,KAAJ,CAAU2G,QAAV,GACE,OAAO2E,SAAS,CAAC3E,QAAjB,KAA8B,QAA9B,GACO2E,SAAS,CAAC3E,QADjB,UAEI2E,SAAS,CAAC3E,QAHhB;;AAKA,QAAI2E,SAAS,CAAC1E,IAAd,EAAoB;AAClBtE,MAAAA,GAAG,CAAClC,YAAJ,CAAiB,MAAjB,EAAyBkL,SAAS,CAAC1E,IAAnC;AACD,KAFD,MAEO;AACLtE,MAAAA,GAAG,CAACiJ,eAAJ,CAAoB,MAApB;AACD;;AAED,QACEF,SAAS,CAAC5E,OAAV,KAAsB6E,SAAS,CAAC7E,OAAhC,IACA4E,SAAS,CAAC/E,SAAV,KAAwBgF,SAAS,CAAChF,SAFpC,EAGE;AACAmE,MAAAA,UAAU,CAAChE,OAAD,EAAU5C,QAAQ,CAAC5C,KAAnB,CAAV;AACD;;AAED,QAAIqK,SAAS,CAAC9E,KAAd,EAAqB;AACnB,UAAI,CAACA,KAAL,EAAY;AACVlE,QAAAA,GAAG,CAACkI,WAAJ,CAAgBF,kBAAkB,CAACgB,SAAS,CAAC9E,KAAX,CAAlC;AACD,OAFD,MAEO,IAAI6E,SAAS,CAAC7E,KAAV,KAAoB8E,SAAS,CAAC9E,KAAlC,EAAyC;AAC9ClE,QAAAA,GAAG,CAACkJ,WAAJ,CAAgBhF,KAAhB;AACAlE,QAAAA,GAAG,CAACkI,WAAJ,CAAgBF,kBAAkB,CAACgB,SAAS,CAAC9E,KAAX,CAAlC;AACD;AACF,KAPD,MAOO,IAAIA,KAAJ,EAAW;AAChBlE,MAAAA,GAAG,CAACkJ,WAAJ,CAAgBhF,KAAhB;AACD;AACF;;AAED,SAAO;AACLoE,IAAAA,MAAM,EAANA,MADK;AAELQ,IAAAA,QAAQ,EAARA;AAFK,GAAP;AAID;AAGD;;AACA3C,MAAM,CAACgD,OAAP,GAAiB,IAAjB;;ACjHA,IAAIC,SAAS,GAAG,CAAhB;AACA,IAAIC,kBAAmD,GAAG,EAA1D;;AAGA,AAAO,IAAIC,gBAA4B,GAAG,EAAnC;AAEP,AAAe,SAASC,WAAT,CACbnM,SADa,EAEbyJ,WAFa,EAGH;AACV,MAAMlI,KAAK,GAAG4I,aAAa,CAACnK,SAAD,oBACtBqH,YADsB,EAEtBmC,sBAAsB,CAACrK,oBAAoB,CAACsK,WAAD,CAArB,CAFA,EAA3B,CADU;AAOV;AACA;;AACA,MAAI2C,WAAJ;AACA,MAAIC,WAAJ;AACA,MAAIC,0BAAJ;AACA,MAAIC,kBAAkB,GAAG,KAAzB;AACA,MAAIC,6BAA6B,GAAG,KAApC;AACA,MAAIC,YAAY,GAAG,KAAnB;AACA,MAAIC,mBAAmB,GAAG,KAA1B;AACA,MAAIC,gBAAJ;AACA,MAAIC,4BAAJ;AACA,MAAIC,aAAJ;AACA,MAAIC,SAA2B,GAAG,EAAlC;AACA,MAAIC,oBAAoB,GAAGtP,QAAQ,CAACuP,WAAD,EAAczL,KAAK,CAACwG,mBAApB,CAAnC;AACA,MAAIkF,aAAJ,CArBU;AAwBV;AACA;;AACA,MAAMC,EAAE,GAAGlB,SAAS,EAApB;AACA,MAAMmB,cAAc,GAAG,IAAvB;AACA,MAAMtE,OAAO,GAAGhK,MAAM,CAAC0C,KAAK,CAACsH,OAAP,CAAtB;AAEA,MAAMpI,KAAK,GAAG;AACZ;AACA2M,IAAAA,SAAS,EAAE,IAFC;AAGZ;AACA/I,IAAAA,SAAS,EAAE,KAJC;AAKZ;AACAgJ,IAAAA,WAAW,EAAE,KAND;AAOZ;AACAC,IAAAA,SAAS,EAAE,KARC;AASZ;AACAC,IAAAA,OAAO,EAAE;AAVG,GAAd;AAaA,MAAMpJ,QAAkB,GAAG;AACzB;AACA+I,IAAAA,EAAE,EAAFA,EAFyB;AAGzBlN,IAAAA,SAAS,EAATA,SAHyB;AAIzBkL,IAAAA,MAAM,EAAE1L,GAAG,EAJc;AAKzB2N,IAAAA,cAAc,EAAdA,cALyB;AAMzB5L,IAAAA,KAAK,EAALA,KANyB;AAOzBd,IAAAA,KAAK,EAALA,KAPyB;AAQzBoI,IAAAA,OAAO,EAAPA,OARyB;AASzB;AACA2E,IAAAA,kBAAkB,EAAlBA,kBAVyB;AAWzBC,IAAAA,QAAQ,EAARA,QAXyB;AAYzB1C,IAAAA,UAAU,EAAVA,UAZyB;AAazB2C,IAAAA,IAAI,EAAJA,IAbyB;AAczBC,IAAAA,IAAI,EAAJA,IAdyB;AAezBC,IAAAA,qBAAqB,EAArBA,qBAfyB;AAgBzBC,IAAAA,MAAM,EAANA,MAhByB;AAiBzBC,IAAAA,OAAO,EAAPA,OAjByB;AAkBzBC,IAAAA,OAAO,EAAPA,OAlByB;AAmBzBC,IAAAA,OAAO,EAAPA;AAnByB,GAA3B,CA3CU;AAkEV;;AACA;;AACA,MAAI,CAACzM,KAAK,CAACwH,MAAX,EAAmB;AACjB,+CAAa;AACXjD,MAAAA,SAAS,CAAC,IAAD,EAAO,0CAAP,CAAT;AACD;;AAED,WAAO3B,QAAP;AACD,GA1ES;AA6EV;AACA;;;AACA,sBAA2B5C,KAAK,CAACwH,MAAN,CAAa5E,QAAb,CAA3B;AAAA,MAAO+G,MAAP,iBAAOA,MAAP;AAAA,MAAeQ,QAAf,iBAAeA,QAAf;;AAEAR,EAAAA,MAAM,CAACxK,YAAP,CAAoB,iBAApB,EAAsD,EAAtD;AACAwK,EAAAA,MAAM,CAACgC,EAAP,cAAoC/I,QAAQ,CAAC+I,EAA7C;AAEA/I,EAAAA,QAAQ,CAAC+G,MAAT,GAAkBA,MAAlB;AACAlL,EAAAA,SAAS,CAACD,MAAV,GAAmBoE,QAAnB;AACA+G,EAAAA,MAAM,CAACnL,MAAP,GAAgBoE,QAAhB;AAEA,MAAM8J,YAAY,GAAGpF,OAAO,CAACqF,GAAR,CAAY,UAACxE,MAAD;AAAA,WAAYA,MAAM,CAAChM,EAAP,CAAUyG,QAAV,CAAZ;AAAA,GAAZ,CAArB;AACA,MAAMgK,eAAe,GAAGnO,SAAS,CAACoO,YAAV,CAAuB,eAAvB,CAAxB;AAEAC,EAAAA,YAAY;AACZC,EAAAA,2BAA2B;AAC3BC,EAAAA,YAAY;AAEZC,EAAAA,UAAU,CAAC,UAAD,EAAa,CAACrK,QAAD,CAAb,CAAV;;AAEA,MAAI5C,KAAK,CAACyH,YAAV,EAAwB;AACtByF,IAAAA,YAAY;AACb,GAnGS;AAsGV;;;AACAvD,EAAAA,MAAM,CAACrH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;AAC1C,QAAIM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B3D,QAAQ,CAAC1D,KAAT,CAAe4D,SAAjD,EAA4D;AAC1DF,MAAAA,QAAQ,CAACqJ,kBAAT;AACD;AACF,GAJD;AAMAtC,EAAAA,MAAM,CAACrH,gBAAP,CAAwB,YAAxB,EAAsC,YAAM;AAC1C,QACEM,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA3D,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAFlD,EAGE;AACAoR,MAAAA,WAAW,GAAG7K,gBAAd,CAA+B,WAA/B,EAA4CkJ,oBAA5C;AACD;AACF,GAPD;AASA,SAAO5I,QAAP,CAtHU;AAyHV;AACA;;AACA,WAASwK,0BAAT,GAAkE;AAChE,QAAO1F,KAAP,GAAgB9E,QAAQ,CAAC5C,KAAzB,CAAO0H,KAAP;AACA,WAAOlM,KAAK,CAACC,OAAN,CAAciM,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,EAAQ,CAAR,CAAtC;AACD;;AAED,WAAS2F,wBAAT,GAA6C;AAC3C,WAAOD,0BAA0B,GAAG,CAAH,CAA1B,KAAoC,MAA3C;AACD;;AAED,WAASE,oBAAT,GAAyC;AAAA;;AACvC;AACA,WAAO,CAAC,2BAAC1K,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBgD,OAAxB,CAAR;AACD;;AAED,WAAS+C,gBAAT,GAAqC;AACnC,WAAO7B,aAAa,IAAIjN,SAAxB;AACD;;AAED,WAAS0O,WAAT,GAAiC;AAC/B,QAAMzL,MAAM,GAAG6L,gBAAgB,GAAGC,UAAlC;AACA,WAAO9L,MAAM,GAAGtC,gBAAgB,CAACsC,MAAD,CAAnB,GAA8B5G,QAA3C;AACD;;AAED,WAAS2S,0BAAT,GAAsD;AACpD,WAAO/D,WAAW,CAACC,MAAD,CAAlB;AACD;;AAED,WAAS+D,QAAT,CAAkBC,MAAlB,EAA2C;AACzC;AACA;AACA;AACA,QACG/K,QAAQ,CAAC1D,KAAT,CAAe6M,SAAf,IAA4B,CAACnJ,QAAQ,CAAC1D,KAAT,CAAe4D,SAA7C,IACAd,YAAY,CAACC,OADb,IAECmJ,gBAAgB,IAAIA,gBAAgB,CAACxP,IAAjB,KAA0B,OAHjD,EAIE;AACA,aAAO,CAAP;AACD;;AAED,WAAOR,uBAAuB,CAC5BwH,QAAQ,CAAC5C,KAAT,CAAekG,KADa,EAE5ByH,MAAM,GAAG,CAAH,GAAO,CAFe,EAG5B7H,YAAY,CAACI,KAHe,CAA9B;AAKD;;AAED,WAAS8G,YAAT,CAAsBY,QAAtB,EAA8C;AAAA,QAAxBA,QAAwB;AAAxBA,MAAAA,QAAwB,GAAb,KAAa;AAAA;;AAC5CjE,IAAAA,MAAM,CAAC5K,KAAP,CAAa8O,aAAb,GACEjL,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8B,CAACqH,QAA/B,GAA0C,EAA1C,GAA+C,MADjD;AAEAjE,IAAAA,MAAM,CAAC5K,KAAP,CAAa8G,MAAb,QAAyBjD,QAAQ,CAAC5C,KAAT,CAAe6F,MAAxC;AACD;;AAED,WAASoH,UAAT,CACEa,IADF,EAEE7R,IAFF,EAGE8R,qBAHF,EAIQ;AAAA,QADNA,qBACM;AADNA,MAAAA,qBACM,GADkB,IAClB;AAAA;;AACNrB,IAAAA,YAAY,CAAC9P,OAAb,CAAqB,UAACoR,WAAD,EAAiB;AACpC,UAAIA,WAAW,CAACF,IAAD,CAAf,EAAuB;AACrBE,QAAAA,WAAW,CAACF,IAAD,CAAX,OAAAE,WAAW,EAAW/R,IAAX,CAAX;AACD;AACF,KAJD;;AAMA,QAAI8R,qBAAJ,EAA2B;AAAA;;AACzB,yBAAAnL,QAAQ,CAAC5C,KAAT,EAAe8N,IAAf,yBAAwB7R,IAAxB;AACD;AACF;;AAED,WAASgS,0BAAT,GAA4C;AAC1C,QAAOjI,IAAP,GAAepD,QAAQ,CAAC5C,KAAxB,CAAOgG,IAAP;;AAEA,QAAI,CAACA,IAAI,CAACR,OAAV,EAAmB;AACjB;AACD;;AAED,QAAM0I,IAAI,aAAWlI,IAAI,CAACR,OAA1B;AACA,QAAMmG,EAAE,GAAGhC,MAAM,CAACgC,EAAlB;AACA,QAAMwC,KAAK,GAAGlR,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AAEA0P,IAAAA,KAAK,CAACvR,OAAN,CAAc,UAACoN,IAAD,EAAU;AACtB,UAAMoE,YAAY,GAAGpE,IAAI,CAACxB,YAAL,CAAkB0F,IAAlB,CAArB;;AAEA,UAAItL,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5BkH,QAAAA,IAAI,CAAC7K,YAAL,CAAkB+O,IAAlB,EAAwBE,YAAY,GAAMA,YAAN,SAAsBzC,EAAtB,GAA6BA,EAAjE;AACD,OAFD,MAEO;AACL,YAAM0C,SAAS,GAAGD,YAAY,IAAIA,YAAY,CAAC3K,OAAb,CAAqBkI,EAArB,EAAyB,EAAzB,EAA6BjI,IAA7B,EAAlC;;AAEA,YAAI2K,SAAJ,EAAe;AACbrE,UAAAA,IAAI,CAAC7K,YAAL,CAAkB+O,IAAlB,EAAwBG,SAAxB;AACD,SAFD,MAEO;AACLrE,UAAAA,IAAI,CAACM,eAAL,CAAqB4D,IAArB;AACD;AACF;AACF,KAdD;AAeD;;AAED,WAASnB,2BAAT,GAA6C;AAC3C,QAAIH,eAAe,IAAI,CAAChK,QAAQ,CAAC5C,KAAT,CAAegG,IAAf,CAAoBC,QAA5C,EAAsD;AACpD;AACD;;AAED,QAAMkI,KAAK,GAAGlR,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AAEA0P,IAAAA,KAAK,CAACvR,OAAN,CAAc,UAACoN,IAAD,EAAU;AACtB,UAAIpH,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;AAC9ByD,QAAAA,IAAI,CAAC7K,YAAL,CACE,eADF,EAEEyD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IAA4BkH,IAAI,KAAKuD,gBAAgB,EAArD,GACI,MADJ,GAEI,OAJN;AAMD,OAPD,MAOO;AACLvD,QAAAA,IAAI,CAACM,eAAL,CAAqB,eAArB;AACD;AACF,KAXD;AAYD;;AAED,WAASgE,gCAAT,GAAkD;AAChDnB,IAAAA,WAAW,GAAG1K,mBAAd,CAAkC,WAAlC,EAA+C+I,oBAA/C;AACAd,IAAAA,kBAAkB,GAAGA,kBAAkB,CAAC3N,MAAnB,CACnB,UAACwE,QAAD;AAAA,aAAcA,QAAQ,KAAKiK,oBAA3B;AAAA,KADmB,CAArB;AAGD;;AAED,WAAS+C,eAAT,CAAyB7O,KAAzB,EAA+D;AAC7D;AACA,QAAIsC,YAAY,CAACC,OAAjB,EAA0B;AACxB,UAAIiJ,YAAY,IAAIxL,KAAK,CAAC9D,IAAN,KAAe,WAAnC,EAAgD;AAC9C;AACD;AACF;;AAED,QAAM4S,YAAY,GACf9O,KAAK,CAAC+O,YAAN,IAAsB/O,KAAK,CAAC+O,YAAN,GAAqB,CAArB,CAAvB,IAAmD/O,KAAK,CAACkC,MAD3D,CAR6D;;AAY7D,QACEgB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA9E,cAAc,CAACkI,MAAD,EAAS6E,YAAT,CAFhB,EAGE;AACA;AACD,KAjB4D;;;AAoB7D,QACEvR,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAAhB,CAA4DL,IAA5D,CAAiE,UAACU,EAAD;AAAA,aAC/D2C,cAAc,CAAC3C,EAAD,EAAK0P,YAAL,CADiD;AAAA,KAAjE,CADF,EAIE;AACA,UAAIxM,YAAY,CAACC,OAAjB,EAA0B;AACxB;AACD;;AAED,UACEW,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,IACAF,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAF7C,EAGE;AACA;AACD;AACF,KAfD,MAeO;AACLkR,MAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACrK,QAAD,EAAWlD,KAAX,CAAnB,CAAV;AACD;;AAED,QAAIkD,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,IAAnC,EAAyC;AACvCzD,MAAAA,QAAQ,CAACqJ,kBAAT;AACArJ,MAAAA,QAAQ,CAACwJ,IAAT,GAFuC;AAKvC;AACA;;AACAnB,MAAAA,6BAA6B,GAAG,IAAhC;AACAzO,MAAAA,UAAU,CAAC,YAAM;AACfyO,QAAAA,6BAA6B,GAAG,KAAhC;AACD,OAFS,CAAV,CARuC;AAavC;AACA;;AACA,UAAI,CAACrI,QAAQ,CAAC1D,KAAT,CAAe6M,SAApB,EAA+B;AAC7B2C,QAAAA,mBAAmB;AACpB;AACF;AACF;;AAED,WAASC,WAAT,GAA6B;AAC3BzD,IAAAA,YAAY,GAAG,IAAf;AACD;;AAED,WAAS0D,YAAT,GAA8B;AAC5B1D,IAAAA,YAAY,GAAG,KAAf;AACD;;AAED,WAAS2D,gBAAT,GAAkC;AAChC,QAAMC,GAAG,GAAG3B,WAAW,EAAvB;AACA2B,IAAAA,GAAG,CAACxM,gBAAJ,CAAqB,WAArB,EAAkCiM,eAAlC,EAAmD,IAAnD;AACAO,IAAAA,GAAG,CAACxM,gBAAJ,CAAqB,UAArB,EAAiCiM,eAAjC,EAAkD7T,aAAlD;AACAoU,IAAAA,GAAG,CAACxM,gBAAJ,CAAqB,YAArB,EAAmCsM,YAAnC,EAAiDlU,aAAjD;AACAoU,IAAAA,GAAG,CAACxM,gBAAJ,CAAqB,WAArB,EAAkCqM,WAAlC,EAA+CjU,aAA/C;AACD;;AAED,WAASgU,mBAAT,GAAqC;AACnC,QAAMI,GAAG,GAAG3B,WAAW,EAAvB;AACA2B,IAAAA,GAAG,CAACrM,mBAAJ,CAAwB,WAAxB,EAAqC8L,eAArC,EAAsD,IAAtD;AACAO,IAAAA,GAAG,CAACrM,mBAAJ,CAAwB,UAAxB,EAAoC8L,eAApC,EAAqD7T,aAArD;AACAoU,IAAAA,GAAG,CAACrM,mBAAJ,CAAwB,YAAxB,EAAsCmM,YAAtC,EAAoDlU,aAApD;AACAoU,IAAAA,GAAG,CAACrM,mBAAJ,CAAwB,WAAxB,EAAqCkM,WAArC,EAAkDjU,aAAlD;AACD;;AAED,WAASqU,iBAAT,CAA2B5I,QAA3B,EAA6C6I,QAA7C,EAAyE;AACvEC,IAAAA,eAAe,CAAC9I,QAAD,EAAW,YAAM;AAC9B,UACE,CAACvD,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IACA6G,MAAM,CAAC6D,UADP,IAEA7D,MAAM,CAAC6D,UAAP,CAAkB3L,QAAlB,CAA2B8H,MAA3B,CAHF,EAIE;AACAqF,QAAAA,QAAQ;AACT;AACF,KARc,CAAf;AASD;;AAED,WAASE,gBAAT,CAA0B/I,QAA1B,EAA4C6I,QAA5C,EAAwE;AACtEC,IAAAA,eAAe,CAAC9I,QAAD,EAAW6I,QAAX,CAAf;AACD;;AAED,WAASC,eAAT,CAAyB9I,QAAzB,EAA2C6I,QAA3C,EAAuE;AACrE,QAAM3N,GAAG,GAAGoM,0BAA0B,GAAGpM,GAAzC;;AAEA,aAASE,QAAT,CAAkB7B,KAAlB,EAAgD;AAC9C,UAAIA,KAAK,CAACkC,MAAN,KAAiBP,GAArB,EAA0B;AACxBD,QAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBE,QAAhB,CAA3B;AACAyN,QAAAA,QAAQ;AACT;AACF,KARoE;AAWrE;;;AACA,QAAI7I,QAAQ,KAAK,CAAjB,EAAoB;AAClB,aAAO6I,QAAQ,EAAf;AACD;;AAED5N,IAAAA,2BAA2B,CAACC,GAAD,EAAM,QAAN,EAAgBgK,4BAAhB,CAA3B;AACAjK,IAAAA,2BAA2B,CAACC,GAAD,EAAM,KAAN,EAAaE,QAAb,CAA3B;AAEA8J,IAAAA,4BAA4B,GAAG9J,QAA/B;AACD;;AAED,WAAS4N,EAAT,CACEC,SADF,EAEEC,OAFF,EAGEC,OAHF,EAIQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADuC,KACvC;AAAA;;AACN,QAAMnB,KAAK,GAAGlR,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA9B;AACA0P,IAAAA,KAAK,CAACvR,OAAN,CAAc,UAACoN,IAAD,EAAU;AACtBA,MAAAA,IAAI,CAAC1H,gBAAL,CAAsB8M,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACA/D,MAAAA,SAAS,CAAClO,IAAV,CAAe;AAAC2M,QAAAA,IAAI,EAAJA,IAAD;AAAOoF,QAAAA,SAAS,EAATA,SAAP;AAAkBC,QAAAA,OAAO,EAAPA,OAAlB;AAA2BC,QAAAA,OAAO,EAAPA;AAA3B,OAAf;AACD,KAHD;AAID;;AAED,WAASxC,YAAT,GAA8B;AAC5B,QAAIO,wBAAwB,EAA5B,EAAgC;AAC9B8B,MAAAA,EAAE,CAAC,YAAD,EAAehI,SAAf,EAA0B;AAACxM,QAAAA,OAAO,EAAE;AAAV,OAA1B,CAAF;AACAwU,MAAAA,EAAE,CAAC,UAAD,EAAaI,YAAb,EAA4C;AAAC5U,QAAAA,OAAO,EAAE;AAAV,OAA5C,CAAF;AACD;;AAEDkC,IAAAA,aAAa,CAAC+F,QAAQ,CAAC5C,KAAT,CAAe2H,OAAhB,CAAb,CAAsC/K,OAAtC,CAA8C,UAACwS,SAAD,EAAe;AAC3D,UAAIA,SAAS,KAAK,QAAlB,EAA4B;AAC1B;AACD;;AAEDD,MAAAA,EAAE,CAACC,SAAD,EAAYjI,SAAZ,CAAF;;AAEA,cAAQiI,SAAR;AACE,aAAK,YAAL;AACED,UAAAA,EAAE,CAAC,YAAD,EAAeI,YAAf,CAAF;AACA;;AACF,aAAK,OAAL;AACEJ,UAAAA,EAAE,CAAClM,MAAM,GAAG,UAAH,GAAgB,MAAvB,EAA+BuM,gBAA/B,CAAF;AACA;;AACF,aAAK,SAAL;AACEL,UAAAA,EAAE,CAAC,UAAD,EAAaK,gBAAb,CAAF;AACA;AATJ;AAWD,KAlBD;AAmBD;;AAED,WAASC,eAAT,GAAiC;AAC/BlE,IAAAA,SAAS,CAAC3O,OAAV,CAAkB,gBAAyD;AAAA,UAAvDoN,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDoF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEtF,MAAAA,IAAI,CAACvH,mBAAL,CAAyB2M,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGA/D,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAASpE,SAAT,CAAmBzH,KAAnB,EAAuC;AAAA;;AACrC,QAAIgQ,uBAAuB,GAAG,KAA9B;;AAEA,QACE,CAAC9M,QAAQ,CAAC1D,KAAT,CAAe2M,SAAhB,IACA8D,sBAAsB,CAACjQ,KAAD,CADtB,IAEAuL,6BAHF,EAIE;AACA;AACD;;AAED,QAAM2E,UAAU,GAAG,sBAAAxE,gBAAgB,SAAhB,8BAAkBxP,IAAlB,MAA2B,OAA9C;AAEAwP,IAAAA,gBAAgB,GAAG1L,KAAnB;AACAgM,IAAAA,aAAa,GAAGhM,KAAK,CAACgM,aAAtB;AAEAqB,IAAAA,2BAA2B;;AAE3B,QAAI,CAACnK,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BxE,YAAY,CAACoB,KAAD,CAA7C,EAAsD;AACpD;AACA;AACA;AACA;AACAgL,MAAAA,kBAAkB,CAAC9N,OAAnB,CAA2B,UAAC2E,QAAD;AAAA,eAAcA,QAAQ,CAAC7B,KAAD,CAAtB;AAAA,OAA3B;AACD,KAxBoC;;;AA2BrC,QACEA,KAAK,CAAC9D,IAAN,KAAe,OAAf,KACCgH,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,IAA+C,CAA/C,IACCiP,kBAFF,KAGApI,QAAQ,CAAC5C,KAAT,CAAeqG,WAAf,KAA+B,KAH/B,IAIAzD,QAAQ,CAAC1D,KAAT,CAAe4D,SALjB,EAME;AACA4M,MAAAA,uBAAuB,GAAG,IAA1B;AACD,KARD,MAQO;AACLxC,MAAAA,YAAY,CAACxN,KAAD,CAAZ;AACD;;AAED,QAAIA,KAAK,CAAC9D,IAAN,KAAe,OAAnB,EAA4B;AAC1BoP,MAAAA,kBAAkB,GAAG,CAAC0E,uBAAtB;AACD;;AAED,QAAIA,uBAAuB,IAAI,CAACE,UAAhC,EAA4C;AAC1CC,MAAAA,YAAY,CAACnQ,KAAD,CAAZ;AACD;AACF;;AAED,WAAS+L,WAAT,CAAqB/L,KAArB,EAA8C;AAC5C,QAAMkC,MAAM,GAAGlC,KAAK,CAACkC,MAArB;AACA,QAAMkO,6BAA6B,GACjCvC,gBAAgB,GAAG1L,QAAnB,CAA4BD,MAA5B,KAAuC+H,MAAM,CAAC9H,QAAP,CAAgBD,MAAhB,CADzC;;AAGA,QAAIlC,KAAK,CAAC9D,IAAN,KAAe,WAAf,IAA8BkU,6BAAlC,EAAiE;AAC/D;AACD;;AAED,QAAMrQ,cAAc,GAAGsQ,mBAAmB,GACvC7S,MADoB,CACbyM,MADa,EAEpBgD,GAFoB,CAEhB,UAAChD,MAAD,EAAY;AAAA;;AACf,UAAM/G,QAAQ,GAAG+G,MAAM,CAACnL,MAAxB;AACA,UAAMU,KAAK,4BAAG0D,QAAQ,CAACgJ,cAAZ,qBAAG,sBAAyB1M,KAAvC;;AAEA,UAAIA,KAAJ,EAAW;AACT,eAAO;AACLY,UAAAA,UAAU,EAAE6J,MAAM,CAACqG,qBAAP,EADP;AAELjQ,UAAAA,WAAW,EAAEb,KAFR;AAGLc,UAAAA,KAAK,EAALA;AAHK,SAAP;AAKD;;AAED,aAAO,IAAP;AACD,KAfoB,EAgBpBjD,MAhBoB,CAgBbC,OAhBa,CAAvB;;AAkBA,QAAIwC,gCAAgC,CAACC,cAAD,EAAiBC,KAAjB,CAApC,EAA6D;AAC3D4O,MAAAA,gCAAgC;AAChCuB,MAAAA,YAAY,CAACnQ,KAAD,CAAZ;AACD;AACF;;AAED,WAAS6P,YAAT,CAAsB7P,KAAtB,EAA+C;AAC7C,QAAMuQ,UAAU,GACdN,sBAAsB,CAACjQ,KAAD,CAAtB,IACCkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAA3C,IAAgDiP,kBAFnD;;AAIA,QAAIiF,UAAJ,EAAgB;AACd;AACD;;AAED,QAAIrN,QAAQ,CAAC5C,KAAT,CAAeuG,WAAnB,EAAgC;AAC9B3D,MAAAA,QAAQ,CAACyJ,qBAAT,CAA+B3M,KAA/B;AACA;AACD;;AAEDmQ,IAAAA,YAAY,CAACnQ,KAAD,CAAZ;AACD;;AAED,WAAS8P,gBAAT,CAA0B9P,KAA1B,EAAmD;AACjD,QACEkD,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,SAA/B,IAA4C,CAA5C,IACA2D,KAAK,CAACkC,MAAN,KAAiB2L,gBAAgB,EAFnC,EAGE;AACA;AACD,KANgD;;;AASjD,QACE3K,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACA7G,KAAK,CAACwQ,aADN,IAEAvG,MAAM,CAAC9H,QAAP,CAAgBnC,KAAK,CAACwQ,aAAtB,CAHF,EAIE;AACA;AACD;;AAEDL,IAAAA,YAAY,CAACnQ,KAAD,CAAZ;AACD;;AAED,WAASiQ,sBAAT,CAAgCjQ,KAAhC,EAAuD;AACrD,WAAOsC,YAAY,CAACC,OAAb,GACHoL,wBAAwB,OAAO3N,KAAK,CAAC9D,IAAN,CAAWG,OAAX,CAAmB,OAAnB,KAA+B,CAD3D,GAEH,KAFJ;AAGD;;AAED,WAASoU,oBAAT,GAAsC;AACpCC,IAAAA,qBAAqB;AAErB,2BAMIxN,QAAQ,CAAC5C,KANb;AAAA,QACEuH,aADF,oBACEA,aADF;AAAA,QAEE9J,SAFF,oBAEEA,SAFF;AAAA,QAGE4C,MAHF,oBAGEA,MAHF;AAAA,QAIE+F,sBAJF,oBAIEA,sBAJF;AAAA,QAKEK,cALF,oBAKEA,cALF;AAQA,QAAMlB,KAAK,GAAG+H,oBAAoB,KAAK5D,WAAW,CAACC,MAAD,CAAX,CAAoBpE,KAAzB,GAAiC,IAAnE;AAEA,QAAM8K,iBAAiB,GAAGjK,sBAAsB,GAC5C;AACE4J,MAAAA,qBAAqB,EAAE5J,sBADzB;AAEEkK,MAAAA,cAAc,EACZlK,sBAAsB,CAACkK,cAAvB,IAAyC/C,gBAAgB;AAH7D,KAD4C,GAM5C9O,SANJ;AAQA,QAAM8R,aAA2D,GAAG;AAClEnI,MAAAA,IAAI,EAAE,SAD4D;AAElEoI,MAAAA,OAAO,EAAE,IAFyD;AAGlEC,MAAAA,KAAK,EAAE,aAH2D;AAIlEC,MAAAA,QAAQ,EAAE,CAAC,eAAD,CAJwD;AAKlEvU,MAAAA,EALkE,qBAKtD;AAAA,YAAR+C,KAAQ,SAARA,KAAQ;;AACV,YAAIoO,oBAAoB,EAAxB,EAA4B;AAC1B,sCAAcG,0BAA0B,EAAxC;AAAA,cAAOpM,GAAP,yBAAOA,GAAP;;AAEA,WAAC,WAAD,EAAc,kBAAd,EAAkC,SAAlC,EAA6CzE,OAA7C,CAAqD,UAACsR,IAAD,EAAU;AAC7D,gBAAIA,IAAI,KAAK,WAAb,EAA0B;AACxB7M,cAAAA,GAAG,CAAClC,YAAJ,CAAiB,gBAAjB,EAAmCD,KAAK,CAACzB,SAAzC;AACD,aAFD,MAEO;AACL,kBAAIyB,KAAK,CAACyR,UAAN,CAAiBhH,MAAjB,kBAAuCuE,IAAvC,CAAJ,EAAoD;AAClD7M,gBAAAA,GAAG,CAAClC,YAAJ,WAAyB+O,IAAzB,EAAiC,EAAjC;AACD,eAFD,MAEO;AACL7M,gBAAAA,GAAG,CAACiJ,eAAJ,WAA4B4D,IAA5B;AACD;AACF;AACF,WAVD;AAYAhP,UAAAA,KAAK,CAACyR,UAAN,CAAiBhH,MAAjB,GAA0B,EAA1B;AACD;AACF;AAvBiE,KAApE;AA6BA,QAAMiH,SAAmC,GAAG,CAC1C;AACExI,MAAAA,IAAI,EAAE,QADR;AAEEkH,MAAAA,OAAO,EAAE;AACPjP,QAAAA,MAAM,EAANA;AADO;AAFX,KAD0C,EAO1C;AACE+H,MAAAA,IAAI,EAAE,iBADR;AAEEkH,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AACPtQ,UAAAA,GAAG,EAAE,CADE;AAEPG,UAAAA,MAAM,EAAE,CAFD;AAGPE,UAAAA,IAAI,EAAE,CAHC;AAIPG,UAAAA,KAAK,EAAE;AAJA;AADF;AAFX,KAP0C,EAkB1C;AACEqH,MAAAA,IAAI,EAAE,MADR;AAEEkH,MAAAA,OAAO,EAAE;AACPuB,QAAAA,OAAO,EAAE;AADF;AAFX,KAlB0C,EAwB1C;AACEzI,MAAAA,IAAI,EAAE,eADR;AAEEkH,MAAAA,OAAO,EAAE;AACPwB,QAAAA,QAAQ,EAAE,CAACrK;AADJ;AAFX,KAxB0C,EA8B1C8J,aA9B0C,CAA5C;;AAiCA,QAAIjD,oBAAoB,MAAM/H,KAA9B,EAAqC;AACnCqL,MAAAA,SAAS,CAACvT,IAAV,CAAe;AACb+K,QAAAA,IAAI,EAAE,OADO;AAEbkH,QAAAA,OAAO,EAAE;AACPhQ,UAAAA,OAAO,EAAEiG,KADF;AAEPsL,UAAAA,OAAO,EAAE;AAFF;AAFI,OAAf;AAOD;;AAEDD,IAAAA,SAAS,CAACvT,IAAV,OAAAuT,SAAS,EAAU,CAAArJ,aAAa,QAAb,YAAAA,aAAa,CAAEqJ,SAAf,KAA4B,EAAtC,CAAT;AAEAhO,IAAAA,QAAQ,CAACgJ,cAAT,GAA0BmF,iBAAY,CACpCV,iBADoC,EAEpC1G,MAFoC,oBAI/BpC,aAJ+B;AAKlC9J,MAAAA,SAAS,EAATA,SALkC;AAMlC6N,MAAAA,aAAa,EAAbA,aANkC;AAOlCsF,MAAAA,SAAS,EAATA;AAPkC,OAAtC;AAUD;;AAED,WAASR,qBAAT,GAAuC;AACrC,QAAIxN,QAAQ,CAACgJ,cAAb,EAA6B;AAC3BhJ,MAAAA,QAAQ,CAACgJ,cAAT,CAAwBa,OAAxB;AACA7J,MAAAA,QAAQ,CAACgJ,cAAT,GAA0B,IAA1B;AACD;AACF;;AAED,WAASoF,KAAT,GAAuB;AACrB,QAAOjL,QAAP,GAAmBnD,QAAQ,CAAC5C,KAA5B,CAAO+F,QAAP;AAEA,QAAIyH,UAAJ,CAHqB;AAMrB;AACA;AACA;AACA;;AACA,QAAMxD,IAAI,GAAGuD,gBAAgB,EAA7B;;AAEA,QACG3K,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IAA8BR,QAAQ,KAAKlL,uBAA5C,IACAkL,QAAQ,KAAK,QAFf,EAGE;AACAyH,MAAAA,UAAU,GAAGxD,IAAI,CAACwD,UAAlB;AACD,KALD,MAKO;AACLA,MAAAA,UAAU,GAAGxR,sBAAsB,CAAC+J,QAAD,EAAW,CAACiE,IAAD,CAAX,CAAnC;AACD,KAnBoB;AAsBrB;;;AACA,QAAI,CAACwD,UAAU,CAAC3L,QAAX,CAAoB8H,MAApB,CAAL,EAAkC;AAChC6D,MAAAA,UAAU,CAACjE,WAAX,CAAuBI,MAAvB;AACD;;AAED/G,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6M,SAAf,GAA2B,IAA3B;AAEAoE,IAAAA,oBAAoB;AAEpB;;AACA,+CAAa;AACX;AACAlM,MAAAA,QAAQ,CACNrB,QAAQ,CAAC5C,KAAT,CAAeuG,WAAf,IACER,QAAQ,KAAKD,YAAY,CAACC,QAD5B,IAEEiE,IAAI,CAACiH,kBAAL,KAA4BtH,MAHxB,EAIN,CACE,8DADF,EAEE,mEAFF,EAGE,0BAHF,EAIE,MAJF,EAKE,kEALF,EAME,mDANF,EAOE,MAPF,EAQE,oEARF,EASE,6DATF,EAUE,sBAVF,EAWE,MAXF,EAYE,wEAZF,EAaEtG,IAbF,CAaO,GAbP,CAJM,CAAR;AAmBD;AACF;;AAED,WAAS0M,mBAAT,GAAgD;AAC9C,WAAOrS,SAAS,CACdiM,MAAM,CAAChL,gBAAP,CAAwB,mBAAxB,CADc,CAAhB;AAGD;;AAED,WAASuO,YAAT,CAAsBxN,KAAtB,EAA2C;AACzCkD,IAAAA,QAAQ,CAACqJ,kBAAT;;AAEA,QAAIvM,KAAJ,EAAW;AACTuN,MAAAA,UAAU,CAAC,WAAD,EAAc,CAACrK,QAAD,EAAWlD,KAAX,CAAd,CAAV;AACD;;AAEDmP,IAAAA,gBAAgB;AAEhB,QAAI3I,KAAK,GAAGwH,QAAQ,CAAC,IAAD,CAApB;;AACA,gCAAiCN,0BAA0B,EAA3D;AAAA,QAAO8D,UAAP;AAAA,QAAmBC,UAAnB;;AAEA,QAAInP,YAAY,CAACC,OAAb,IAAwBiP,UAAU,KAAK,MAAvC,IAAiDC,UAArD,EAAiE;AAC/DjL,MAAAA,KAAK,GAAGiL,UAAR;AACD;;AAED,QAAIjL,KAAJ,EAAW;AACT2E,MAAAA,WAAW,GAAGrO,UAAU,CAAC,YAAM;AAC7BoG,QAAAA,QAAQ,CAACuJ,IAAT;AACD,OAFuB,EAErBjG,KAFqB,CAAxB;AAGD,KAJD,MAIO;AACLtD,MAAAA,QAAQ,CAACuJ,IAAT;AACD;AACF;;AAED,WAAS0D,YAAT,CAAsBnQ,KAAtB,EAA0C;AACxCkD,IAAAA,QAAQ,CAACqJ,kBAAT;AAEAgB,IAAAA,UAAU,CAAC,aAAD,EAAgB,CAACrK,QAAD,EAAWlD,KAAX,CAAhB,CAAV;;AAEA,QAAI,CAACkD,QAAQ,CAAC1D,KAAT,CAAe4D,SAApB,EAA+B;AAC7B4L,MAAAA,mBAAmB;AAEnB;AACD,KATuC;AAYxC;AACA;AACA;;;AACA,QACE9L,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,YAA/B,KAAgD,CAAhD,IACA6G,QAAQ,CAAC5C,KAAT,CAAe2H,OAAf,CAAuB5L,OAAvB,CAA+B,OAA/B,KAA2C,CAD3C,IAEA,CAAC,YAAD,EAAe,WAAf,EAA4BA,OAA5B,CAAoC2D,KAAK,CAAC9D,IAA1C,KAAmD,CAFnD,IAGAoP,kBAJF,EAKE;AACA;AACD;;AAED,QAAM9E,KAAK,GAAGwH,QAAQ,CAAC,KAAD,CAAtB;;AAEA,QAAIxH,KAAJ,EAAW;AACT4E,MAAAA,WAAW,GAAGtO,UAAU,CAAC,YAAM;AAC7B,YAAIoG,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5BF,UAAAA,QAAQ,CAACwJ,IAAT;AACD;AACF,OAJuB,EAIrBlG,KAJqB,CAAxB;AAKD,KAND,MAMO;AACL;AACA;AACA6E,MAAAA,0BAA0B,GAAGqG,qBAAqB,CAAC,YAAM;AACvDxO,QAAAA,QAAQ,CAACwJ,IAAT;AACD,OAFiD,CAAlD;AAGD;AACF,GA3wBS;AA8wBV;AACA;;;AACA,WAASE,MAAT,GAAwB;AACtB1J,IAAAA,QAAQ,CAAC1D,KAAT,CAAe2M,SAAf,GAA2B,IAA3B;AACD;;AAED,WAASU,OAAT,GAAyB;AACvB;AACA;AACA3J,IAAAA,QAAQ,CAACwJ,IAAT;AACAxJ,IAAAA,QAAQ,CAAC1D,KAAT,CAAe2M,SAAf,GAA2B,KAA3B;AACD;;AAED,WAASI,kBAAT,GAAoC;AAClC1P,IAAAA,YAAY,CAACsO,WAAD,CAAZ;AACAtO,IAAAA,YAAY,CAACuO,WAAD,CAAZ;AACAuG,IAAAA,oBAAoB,CAACtG,0BAAD,CAApB;AACD;;AAED,WAASmB,QAAT,CAAkBnE,YAAlB,EAAsD;AACpD;AACA,+CAAa;AACX9D,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe4M,WAAhB,EAA6B3I,uBAAuB,CAAC,UAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe4M,WAAnB,EAAgC;AAC9B;AACD;;AAEDmB,IAAAA,UAAU,CAAC,gBAAD,EAAmB,CAACrK,QAAD,EAAWmF,YAAX,CAAnB,CAAV;AAEA0H,IAAAA,eAAe;AAEf,QAAMrF,SAAS,GAAGxH,QAAQ,CAAC5C,KAA3B;AACA,QAAMqK,SAAS,GAAGzB,aAAa,CAACnK,SAAD,oBAC1B2L,SAD0B,EAE1BxM,oBAAoB,CAACmK,YAAD,CAFM;AAG7BzB,MAAAA,gBAAgB,EAAE;AAHW,OAA/B;AAMA1D,IAAAA,QAAQ,CAAC5C,KAAT,GAAiBqK,SAAjB;AAEAyC,IAAAA,YAAY;;AAEZ,QAAI1C,SAAS,CAAC5D,mBAAV,KAAkC6D,SAAS,CAAC7D,mBAAhD,EAAqE;AACnE8H,MAAAA,gCAAgC;AAChC9C,MAAAA,oBAAoB,GAAGtP,QAAQ,CAC7BuP,WAD6B,EAE7BpB,SAAS,CAAC7D,mBAFmB,CAA/B;AAID,KA/BmD;;;AAkCpD,QAAI4D,SAAS,CAACxC,aAAV,IAA2B,CAACyC,SAAS,CAACzC,aAA1C,EAAyD;AACvD3K,MAAAA,gBAAgB,CAACmN,SAAS,CAACxC,aAAX,CAAhB,CAA0ChL,OAA1C,CAAkD,UAACoN,IAAD,EAAU;AAC1DA,QAAAA,IAAI,CAACM,eAAL,CAAqB,eAArB;AACD,OAFD;AAGD,KAJD,MAIO,IAAID,SAAS,CAACzC,aAAd,EAA6B;AAClCnJ,MAAAA,SAAS,CAAC6L,eAAV,CAA0B,eAA1B;AACD;;AAEDyC,IAAAA,2BAA2B;AAC3BC,IAAAA,YAAY;;AAEZ,QAAI7C,QAAJ,EAAc;AACZA,MAAAA,QAAQ,CAACC,SAAD,EAAYC,SAAZ,CAAR;AACD;;AAED,QAAIzH,QAAQ,CAACgJ,cAAb,EAA6B;AAC3BuE,MAAAA,oBAAoB,GADO;AAI3B;AACA;AACA;;AACAJ,MAAAA,mBAAmB,GAAGnT,OAAtB,CAA8B,UAAC0U,YAAD,EAAkB;AAC9C;AACA;AACAF,QAAAA,qBAAqB,CAACE,YAAY,CAAC9S,MAAb,CAAqBoN,cAArB,CAAqC2F,WAAtC,CAArB;AACD,OAJD;AAKD;;AAEDtE,IAAAA,UAAU,CAAC,eAAD,EAAkB,CAACrK,QAAD,EAAWmF,YAAX,CAAlB,CAAV;AACD;;AAED,WAASyB,UAAT,CAAoBhE,OAApB,EAA4C;AAC1C5C,IAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAAC1G,MAAAA,OAAO,EAAPA;AAAD,KAAlB;AACD;;AAED,WAAS2G,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXlI,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe4M,WAAhB,EAA6B3I,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAMqO,gBAAgB,GAAG5O,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;AACA,QAAMgJ,WAAW,GAAGlJ,QAAQ,CAAC1D,KAAT,CAAe4M,WAAnC;AACA,QAAM2F,UAAU,GAAG,CAAC7O,QAAQ,CAAC1D,KAAT,CAAe2M,SAAnC;AACA,QAAM6F,uBAAuB,GAC3B1P,YAAY,CAACC,OAAb,IAAwB,CAACW,QAAQ,CAAC5C,KAAT,CAAe0H,KAD1C;AAEA,QAAMvB,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;AAMA,QACEqL,gBAAgB,IAChB1F,WADA,IAEA2F,UAFA,IAGAC,uBAJF,EAKE;AACA;AACD,KAzBmB;AA4BpB;AACA;;;AACA,QAAInE,gBAAgB,GAAGV,YAAnB,CAAgC,UAAhC,CAAJ,EAAiD;AAC/C;AACD;;AAEDI,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACrK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAeiH,MAAf,CAAsBrE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,IAA3B;;AAEA,QAAIwK,oBAAoB,EAAxB,EAA4B;AAC1B3D,MAAAA,MAAM,CAAC5K,KAAP,CAAa4S,UAAb,GAA0B,SAA1B;AACD;;AAED3E,IAAAA,YAAY;AACZ6B,IAAAA,gBAAgB;;AAEhB,QAAI,CAACjM,QAAQ,CAAC1D,KAAT,CAAe6M,SAApB,EAA+B;AAC7BpC,MAAAA,MAAM,CAAC5K,KAAP,CAAa6S,UAAb,GAA0B,MAA1B;AACD,KAlDmB;AAqDpB;;;AACA,QAAItE,oBAAoB,EAAxB,EAA4B;AAC1B,mCAAuBG,0BAA0B,EAAjD;AAAA,UAAOpM,GAAP,0BAAOA,GAAP;AAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;AACA5G,MAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,CAAjB,CAArB;AACD;;AAED8F,IAAAA,aAAa,GAAG,yBAAY;AAAA;;AAC1B,UAAI,CAAC1I,QAAQ,CAAC1D,KAAT,CAAe4D,SAAhB,IAA6BqI,mBAAjC,EAAsD;AACpD;AACD;;AAEDA,MAAAA,mBAAmB,GAAG,IAAtB,CAL0B;;AAQ1B,WAAKxB,MAAM,CAACkI,YAAZ;AAEAlI,MAAAA,MAAM,CAAC5K,KAAP,CAAa6S,UAAb,GAA0BhP,QAAQ,CAAC5C,KAAT,CAAeyG,cAAzC;;AAEA,UAAI6G,oBAAoB,MAAM1K,QAAQ,CAAC5C,KAAT,CAAesF,SAA7C,EAAwD;AACtD,qCAAuBmI,0BAA0B,EAAjD;AAAA,YAAOpM,IAAP,0BAAOA,GAAP;AAAA,YAAYmE,QAAZ,0BAAYA,OAAZ;;AACA5G,QAAAA,qBAAqB,CAAC,CAACyC,IAAD,EAAMmE,QAAN,CAAD,EAAiBW,QAAjB,CAArB;AACAlH,QAAAA,kBAAkB,CAAC,CAACoC,IAAD,EAAMmE,QAAN,CAAD,EAAiB,SAAjB,CAAlB;AACD;;AAEDyI,MAAAA,0BAA0B;AAC1BlB,MAAAA,2BAA2B;AAE3B5P,MAAAA,YAAY,CAACwN,gBAAD,EAAmB/H,QAAnB,CAAZ,CArB0B;AAwB1B;;AACA,gCAAAA,QAAQ,CAACgJ,cAAT,4CAAyB2F,WAAzB;AAEAtE,MAAAA,UAAU,CAAC,SAAD,EAAY,CAACrK,QAAD,CAAZ,CAAV;;AAEA,UAAIA,QAAQ,CAAC5C,KAAT,CAAesF,SAAf,IAA4BgI,oBAAoB,EAApD,EAAwD;AACtD4B,QAAAA,gBAAgB,CAAC/I,QAAD,EAAW,YAAM;AAC/BvD,UAAAA,QAAQ,CAAC1D,KAAT,CAAe8M,OAAf,GAAyB,IAAzB;AACAiB,UAAAA,UAAU,CAAC,SAAD,EAAY,CAACrK,QAAD,CAAZ,CAAV;AACD,SAHe,CAAhB;AAID;AACF,KAnCD;;AAqCAoO,IAAAA,KAAK;AACN;;AAED,WAAS5E,IAAT,GAAsB;AACpB;AACA,+CAAa;AACXnI,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe4M,WAAhB,EAA6B3I,uBAAuB,CAAC,MAAD,CAApD,CAAR;AACD,KAJmB;;;AAOpB,QAAM2O,eAAe,GAAG,CAAClP,QAAQ,CAAC1D,KAAT,CAAe4D,SAAxC;AACA,QAAMgJ,WAAW,GAAGlJ,QAAQ,CAAC1D,KAAT,CAAe4M,WAAnC;AACA,QAAM2F,UAAU,GAAG,CAAC7O,QAAQ,CAAC1D,KAAT,CAAe2M,SAAnC;AACA,QAAM1F,QAAQ,GAAG/K,uBAAuB,CACtCwH,QAAQ,CAAC5C,KAAT,CAAemG,QADuB,EAEtC,CAFsC,EAGtCL,YAAY,CAACK,QAHyB,CAAxC;;AAMA,QAAI2L,eAAe,IAAIhG,WAAnB,IAAkC2F,UAAtC,EAAkD;AAChD;AACD;;AAEDxE,IAAAA,UAAU,CAAC,QAAD,EAAW,CAACrK,QAAD,CAAX,EAAuB,KAAvB,CAAV;;AACA,QAAIA,QAAQ,CAAC5C,KAAT,CAAe+G,MAAf,CAAsBnE,QAAtB,MAAoC,KAAxC,EAA+C;AAC7C;AACD;;AAEDA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4D,SAAf,GAA2B,KAA3B;AACAF,IAAAA,QAAQ,CAAC1D,KAAT,CAAe8M,OAAf,GAAyB,KAAzB;AACAb,IAAAA,mBAAmB,GAAG,KAAtB;AACAH,IAAAA,kBAAkB,GAAG,KAArB;;AAEA,QAAIsC,oBAAoB,EAAxB,EAA4B;AAC1B3D,MAAAA,MAAM,CAAC5K,KAAP,CAAa4S,UAAb,GAA0B,QAA1B;AACD;;AAEDrD,IAAAA,gCAAgC;AAChCI,IAAAA,mBAAmB;AACnB1B,IAAAA,YAAY,CAAC,IAAD,CAAZ;;AAEA,QAAIM,oBAAoB,EAAxB,EAA4B;AAC1B,mCAAuBG,0BAA0B,EAAjD;AAAA,UAAOpM,GAAP,0BAAOA,GAAP;AAAA,UAAYmE,OAAZ,0BAAYA,OAAZ;;AAEA,UAAI5C,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;AAC5B1G,QAAAA,qBAAqB,CAAC,CAACyC,GAAD,EAAMmE,OAAN,CAAD,EAAiBW,QAAjB,CAArB;AACAlH,QAAAA,kBAAkB,CAAC,CAACoC,GAAD,EAAMmE,OAAN,CAAD,EAAiB,QAAjB,CAAlB;AACD;AACF;;AAEDyI,IAAAA,0BAA0B;AAC1BlB,IAAAA,2BAA2B;;AAE3B,QAAInK,QAAQ,CAAC5C,KAAT,CAAesF,SAAnB,EAA8B;AAC5B,UAAIgI,oBAAoB,EAAxB,EAA4B;AAC1ByB,QAAAA,iBAAiB,CAAC5I,QAAD,EAAWvD,QAAQ,CAAC4J,OAApB,CAAjB;AACD;AACF,KAJD,MAIO;AACL5J,MAAAA,QAAQ,CAAC4J,OAAT;AACD;AACF;;AAED,WAASH,qBAAT,CAA+B3M,KAA/B,EAAwD;AACtD;AACA,+CAAa;AACXuE,MAAAA,QAAQ,CACNrB,QAAQ,CAAC1D,KAAT,CAAe4M,WADT,EAEN3I,uBAAuB,CAAC,uBAAD,CAFjB,CAAR;AAID;;AAEDgK,IAAAA,WAAW,GAAG7K,gBAAd,CAA+B,WAA/B,EAA4CkJ,oBAA5C;AACArO,IAAAA,YAAY,CAACuN,kBAAD,EAAqBc,oBAArB,CAAZ;AACAA,IAAAA,oBAAoB,CAAC9L,KAAD,CAApB;AACD;;AAED,WAAS8M,OAAT,GAAyB;AACvB;AACA,+CAAa;AACXvI,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe4M,WAAhB,EAA6B3I,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe4D,SAAnB,EAA8B;AAC5BF,MAAAA,QAAQ,CAACwJ,IAAT;AACD;;AAED,QAAI,CAACxJ,QAAQ,CAAC1D,KAAT,CAAe6M,SAApB,EAA+B;AAC7B;AACD;;AAEDqE,IAAAA,qBAAqB,GAdE;AAiBvB;AACA;;AACAL,IAAAA,mBAAmB,GAAGnT,OAAtB,CAA8B,UAAC0U,YAAD,EAAkB;AAC9CA,MAAAA,YAAY,CAAC9S,MAAb,CAAqBgO,OAArB;AACD,KAFD;;AAIA,QAAI7C,MAAM,CAAC6D,UAAX,EAAuB;AACrB7D,MAAAA,MAAM,CAAC6D,UAAP,CAAkBjD,WAAlB,CAA8BZ,MAA9B;AACD;;AAEDgB,IAAAA,gBAAgB,GAAGA,gBAAgB,CAAC5N,MAAjB,CAAwB,UAACgV,CAAD;AAAA,aAAOA,CAAC,KAAKnP,QAAb;AAAA,KAAxB,CAAnB;AAEAA,IAAAA,QAAQ,CAAC1D,KAAT,CAAe6M,SAAf,GAA2B,KAA3B;AACAkB,IAAAA,UAAU,CAAC,UAAD,EAAa,CAACrK,QAAD,CAAb,CAAV;AACD;;AAED,WAAS6J,OAAT,GAAyB;AACvB;AACA,+CAAa;AACXxI,MAAAA,QAAQ,CAACrB,QAAQ,CAAC1D,KAAT,CAAe4M,WAAhB,EAA6B3I,uBAAuB,CAAC,SAAD,CAApD,CAAR;AACD;;AAED,QAAIP,QAAQ,CAAC1D,KAAT,CAAe4M,WAAnB,EAAgC;AAC9B;AACD;;AAEDlJ,IAAAA,QAAQ,CAACqJ,kBAAT;AACArJ,IAAAA,QAAQ,CAAC4J,OAAT;AAEAiD,IAAAA,eAAe;AAEf,WAAOhR,SAAS,CAACD,MAAjB;AAEAoE,IAAAA,QAAQ,CAAC1D,KAAT,CAAe4M,WAAf,GAA6B,IAA7B;AAEAmB,IAAAA,UAAU,CAAC,WAAD,EAAc,CAACrK,QAAD,CAAd,CAAV;AACD;AACF;;AC/mCD,SAASoP,KAAT,CACEtN,OADF,EAEEuN,aAFF,EAGyB;AAAA,MADvBA,aACuB;AADvBA,IAAAA,aACuB,GADS,EACT;AAAA;;AACvB,MAAM3K,OAAO,GAAGxB,YAAY,CAACwB,OAAb,CAAqBpK,MAArB,CAA4B+U,aAAa,CAAC3K,OAAd,IAAyB,EAArD,CAAhB;AAEA;;AACA,6CAAa;AACX7C,IAAAA,eAAe,CAACC,OAAD,CAAf;AACAsD,IAAAA,aAAa,CAACiK,aAAD,EAAgB3K,OAAhB,CAAb;AACD;;AAEDvE,EAAAA,wBAAwB;AAExB,MAAMmF,WAA2B,qBAAO+J,aAAP;AAAsB3K,IAAAA,OAAO,EAAPA;AAAtB,IAAjC;AAEA,MAAM4K,QAAQ,GAAGxT,kBAAkB,CAACgG,OAAD,CAAnC;AAEA;;AACA,6CAAa;AACX,QAAMyN,sBAAsB,GAAGhU,SAAS,CAAC+J,WAAW,CAAC1C,OAAb,CAAxC;AACA,QAAM4M,6BAA6B,GAAGF,QAAQ,CAACjJ,MAAT,GAAkB,CAAxD;AACAhF,IAAAA,QAAQ,CACNkO,sBAAsB,IAAIC,6BADpB,EAEN,CACE,oEADF,EAEE,mEAFF,EAGE,mEAHF,EAIE,MAJF,EAKE,qEALF,EAME,kDANF,EAOE,MAPF,EAQE,iCARF,EASE,2CATF,EAUE/O,IAVF,CAUO,GAVP,CAFM,CAAR;AAcD;;AAED,MAAMgP,SAAS,GAAGH,QAAQ,CAACpU,MAAT,CAChB,UAACC,GAAD,EAAMU,SAAN,EAAgC;AAC9B,QAAMmE,QAAQ,GAAGnE,SAAS,IAAImM,WAAW,CAACnM,SAAD,EAAYyJ,WAAZ,CAAzC;;AAEA,QAAItF,QAAJ,EAAc;AACZ7E,MAAAA,GAAG,CAACV,IAAJ,CAASuF,QAAT;AACD;;AAED,WAAO7E,GAAP;AACD,GATe,EAUhB,EAVgB,CAAlB;AAaA,SAAOI,SAAS,CAACuG,OAAD,CAAT,GAAqB2N,SAAS,CAAC,CAAD,CAA9B,GAAoCA,SAA3C;AACD;;AAEDL,KAAK,CAAClM,YAAN,GAAqBA,YAArB;AACAkM,KAAK,CAAClK,eAAN,GAAwBA,eAAxB;AACAkK,KAAK,CAAChQ,YAAN,GAAqBA,YAArB;AAEA,IAEasQ,OAAgB,GAAG,SAAnBA,OAAmB,QAGL;AAAA,gCAAP,EAAO;AAAA,MAFhBC,2BAEgB,QAFzBC,OAEyB;AAAA,MADzBrM,QACyB,QADzBA,QACyB;;AACzBwE,EAAAA,gBAAgB,CAAC/N,OAAjB,CAAyB,UAACgG,QAAD,EAAc;AACrC,QAAI6P,UAAU,GAAG,KAAjB;;AAEA,QAAIF,2BAAJ,EAAiC;AAC/BE,MAAAA,UAAU,GAAGlU,kBAAkB,CAACgU,2BAAD,CAAlB,GACT3P,QAAQ,CAACnE,SAAT,KAAuB8T,2BADd,GAET3P,QAAQ,CAAC+G,MAAT,KAAqB4I,2BAAD,CAA0C5I,MAFlE;AAGD;;AAED,QAAI,CAAC8I,UAAL,EAAiB;AACf,UAAMC,gBAAgB,GAAG9P,QAAQ,CAAC5C,KAAT,CAAemG,QAAxC;AAEAvD,MAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAAC/F,QAAAA,QAAQ,EAARA;AAAD,OAAlB;AACAvD,MAAAA,QAAQ,CAACwJ,IAAT;;AAEA,UAAI,CAACxJ,QAAQ,CAAC1D,KAAT,CAAe4M,WAApB,EAAiC;AAC/BlJ,QAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAAC/F,UAAAA,QAAQ,EAAEuM;AAAX,SAAlB;AACD;AACF;AACF,GAnBD;AAoBD,CAxBM;;ACrDP;AACA;AACA;;AACA,IAAMC,mBAAqE,qBACtEC,gBADsE;AAEzEC,EAAAA,MAFyE,wBAEzD;AAAA,QAAR3T,KAAQ,QAARA,KAAQ;AACd,QAAM4T,aAAa,GAAG;AACpBnJ,MAAAA,MAAM,EAAE;AACNoJ,QAAAA,QAAQ,EAAE7T,KAAK,CAACoQ,OAAN,CAAc0D,QADlB;AAENpS,QAAAA,IAAI,EAAE,GAFA;AAGNL,QAAAA,GAAG,EAAE,GAHC;AAIN0S,QAAAA,MAAM,EAAE;AAJF,OADY;AAOpB1N,MAAAA,KAAK,EAAE;AACLwN,QAAAA,QAAQ,EAAE;AADL,OAPa;AAUpBtU,MAAAA,SAAS,EAAE;AAVS,KAAtB;AAaAZ,IAAAA,MAAM,CAACqV,MAAP,CAAchU,KAAK,CAACgT,QAAN,CAAevI,MAAf,CAAsB5K,KAApC,EAA2C+T,aAAa,CAACnJ,MAAzD;AACAzK,IAAAA,KAAK,CAACiU,MAAN,GAAeL,aAAf;;AAEA,QAAI5T,KAAK,CAACgT,QAAN,CAAe3M,KAAnB,EAA0B;AACxB1H,MAAAA,MAAM,CAACqV,MAAP,CAAchU,KAAK,CAACgT,QAAN,CAAe3M,KAAf,CAAqBxG,KAAnC,EAA0C+T,aAAa,CAACvN,KAAxD;AACD,KAnBa;AAsBd;;AACD;AAzBwE,EAA3E;;AA4BA,IAAM6N,eAAgC,GAAG,SAAnCA,eAAmC,CACvCC,cADuC,EAEvCpB,aAFuC,EAGpC;AAAA;;AAAA,MADHA,aACG;AADHA,IAAAA,aACG,GADa,EACb;AAAA;;AACH;AACA,6CAAa;AACX1N,IAAAA,SAAS,CACP,CAAC/I,KAAK,CAACC,OAAN,CAAc4X,cAAd,CADM,EAEP,CACE,oEADF,EAEE,uCAFF,EAGEvO,MAAM,CAACuO,cAAD,CAHR,EAIEhQ,IAJF,CAIO,GAJP,CAFO,CAAT;AAQD;;AAED,MAAIiQ,mBAAmB,GAAGD,cAA1B;AACA,MAAIE,UAAmC,GAAG,EAA1C;AACA,MAAIC,cAA8B,GAAG,EAArC;AACA,MAAI9H,aAAJ;AACA,MAAI+H,SAAS,GAAGxB,aAAa,CAACwB,SAA9B;AACA,MAAIC,yBAA4C,GAAG,EAAnD;AACA,MAAIC,aAAa,GAAG,KAApB;;AAEA,WAASC,iBAAT,GAAmC;AACjCJ,IAAAA,cAAc,GAAGF,mBAAmB,CACjC3G,GADc,CACV,UAAC/J,QAAD;AAAA,aACH3F,gBAAgB,CAAC2F,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgChF,QAAQ,CAACnE,SAA1C,CADb;AAAA,KADU,EAIdX,MAJc,CAIP,UAACC,GAAD,EAAMR,IAAN;AAAA,aAAeQ,GAAG,CAACb,MAAJ,CAAWK,IAAX,CAAf;AAAA,KAJO,EAI0B,EAJ1B,CAAjB;AAKD;;AAED,WAASsW,aAAT,GAA+B;AAC7BN,IAAAA,UAAU,GAAGD,mBAAmB,CAAC3G,GAApB,CAAwB,UAAC/J,QAAD;AAAA,aAAcA,QAAQ,CAACnE,SAAvB;AAAA,KAAxB,CAAb;AACD;;AAED,WAASqV,eAAT,CAAyBjI,SAAzB,EAAmD;AACjDyH,IAAAA,mBAAmB,CAAC1W,OAApB,CAA4B,UAACgG,QAAD,EAAc;AACxC,UAAIiJ,SAAJ,EAAe;AACbjJ,QAAAA,QAAQ,CAAC0J,MAAT;AACD,OAFD,MAEO;AACL1J,QAAAA,QAAQ,CAAC2J,OAAT;AACD;AACF,KAND;AAOD;;AAED,WAASwH,iBAAT,CAA2BC,SAA3B,EAAmE;AACjE,WAAOV,mBAAmB,CAAC3G,GAApB,CAAwB,UAAC/J,QAAD,EAAc;AAC3C,UAAMqR,gBAAgB,GAAGrR,QAAQ,CAACsJ,QAAlC;;AAEAtJ,MAAAA,QAAQ,CAACsJ,QAAT,GAAoB,UAAClM,KAAD,EAAiB;AACnCiU,QAAAA,gBAAgB,CAACjU,KAAD,CAAhB;;AAEA,YAAI4C,QAAQ,CAACnE,SAAT,KAAuBiN,aAA3B,EAA0C;AACxCsI,UAAAA,SAAS,CAAC9H,QAAV,CAAmBlM,KAAnB;AACD;AACF,OAND;;AAQA,aAAO,YAAY;AACjB4C,QAAAA,QAAQ,CAACsJ,QAAT,GAAoB+H,gBAApB;AACD,OAFD;AAGD,KAdM,CAAP;AAeD,GA3DE;;;AA8DH,WAASC,eAAT,CACEF,SADF,EAEEpS,MAFF,EAGQ;AACN,QAAMtG,KAAK,GAAGkY,cAAc,CAACzX,OAAf,CAAuB6F,MAAvB,CAAd,CADM;;AAIN,QAAIA,MAAM,KAAK8J,aAAf,EAA8B;AAC5B;AACD;;AAEDA,IAAAA,aAAa,GAAG9J,MAAhB;AAEA,QAAMuS,aAA6B,GAAG,CAACV,SAAS,IAAI,EAAd,EACnCvW,MADmC,CAC5B,SAD4B,EAEnCY,MAFmC,CAE5B,UAACC,GAAD,EAAM+K,IAAN,EAAe;AACpB/K,MAAAA,GAAD,CAAa+K,IAAb,IAAqBwK,mBAAmB,CAAChY,KAAD,CAAnB,CAA2B0E,KAA3B,CAAiC8I,IAAjC,CAArB;AACA,aAAO/K,GAAP;AACD,KALmC,EAKjC,EALiC,CAAtC;AAOAiW,IAAAA,SAAS,CAAC9H,QAAV,mBACKiI,aADL;AAEE/N,MAAAA,sBAAsB,EACpB,OAAO+N,aAAa,CAAC/N,sBAArB,KAAgD,UAAhD,GACI+N,aAAa,CAAC/N,sBADlB,GAEI;AAAA;;AAAA,oCAAkBmN,UAAU,CAACjY,KAAD,CAA5B,qBAAkB,kBAAmB0U,qBAAnB,EAAlB;AAAA;AALR;AAOD;;AAED8D,EAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,EAAAA,aAAa;AACbD,EAAAA,iBAAiB;AAEjB,MAAMzL,MAAc,GAAG;AACrBhM,IAAAA,EADqB,gBAChB;AACH,aAAO;AACL0K,QAAAA,SADK,uBACa;AAChBiN,UAAAA,eAAe,CAAC,IAAD,CAAf;AACD,SAHI;AAILhN,QAAAA,QAJK,sBAIY;AACf4E,UAAAA,aAAa,GAAG,IAAhB;AACD,SANI;AAOLrE,QAAAA,cAPK,0BAOUzE,QAPV,EAO0B;AAC7B,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAACkM,aAApC,EAAmD;AACjDA,YAAAA,aAAa,GAAG,IAAhB;AACAjI,YAAAA,aAAa,GAAG,IAAhB;AACD;AACF,SAZI;AAaLzE,QAAAA,MAbK,kBAaErE,QAbF,EAakB;AACrB,cAAIA,QAAQ,CAAC5C,KAAT,CAAeyH,YAAf,IAA+B,CAACkM,aAApC,EAAmD;AACjDA,YAAAA,aAAa,GAAG,IAAhB;AACAO,YAAAA,eAAe,CAACtR,QAAD,EAAW2Q,UAAU,CAAC,CAAD,CAArB,CAAf;AACD;AACF,SAlBI;AAmBLpM,QAAAA,SAnBK,qBAmBKvE,QAnBL,EAmBelD,KAnBf,EAmB4B;AAC/BwU,UAAAA,eAAe,CAACtR,QAAD,EAAWlD,KAAK,CAACgM,aAAjB,CAAf;AACD;AArBI,OAAP;AAuBD;AAzBoB,GAAvB;AA4BA,MAAMsI,SAAS,GAAGhC,KAAK,CAAC/T,GAAG,EAAJ,oBAClBxB,gBAAgB,CAACwV,aAAD,EAAgB,CAAC,WAAD,CAAhB,CADE;AAErB3K,IAAAA,OAAO,GAAGa,MAAH,SAAe8J,aAAa,CAAC3K,OAAd,IAAyB,EAAxC,CAFc;AAGrBM,IAAAA,aAAa,EAAE4L,cAHM;AAIrBjM,IAAAA,aAAa,oBACR0K,aAAa,CAAC1K,aADN;AAEXqJ,MAAAA,SAAS,YACH,0BAAAqB,aAAa,CAAC1K,aAAd,2CAA6BqJ,SAA7B,KAA0C,EADvC,GAEP+B,mBAFO;AAFE;AAJQ,KAAvB;AAaA,MAAMyB,YAAY,GAAGJ,SAAS,CAAC7H,IAA/B;;AAEA6H,EAAAA,SAAS,CAAC7H,IAAV,GAAiB,UAACvK,MAAD,EAAyD;AACxEwS,IAAAA,YAAY,GAD4D;AAIxE;;AACA,QAAI,CAAC1I,aAAD,IAAkB9J,MAAM,IAAI,IAAhC,EAAsC;AACpC,aAAOsS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAAC,CAAD,CAAtB,CAAtB;AACD,KAPuE;AAUxE;;;AACA,QAAI7H,aAAa,IAAI9J,MAAM,IAAI,IAA/B,EAAqC;AACnC;AACD,KAbuE;;;AAgBxE,QAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,aACE2R,UAAU,CAAC3R,MAAD,CAAV,IAAsBsS,eAAe,CAACF,SAAD,EAAYT,UAAU,CAAC3R,MAAD,CAAtB,CADvC;AAGD,KApBuE;;;AAuBxE,QAAI0R,mBAAmB,CAACvX,OAApB,CAA4B6F,MAA5B,KAAmD,CAAvD,EAA0D;AACxD,UAAMyS,GAAG,GAAIzS,MAAD,CAAqBnD,SAAjC;AACA,aAAOyV,eAAe,CAACF,SAAD,EAAYK,GAAZ,CAAtB;AACD,KA1BuE;;;AA6BxE,QAAId,UAAU,CAACxX,OAAX,CAAmB6F,MAAnB,KAAkD,CAAtD,EAAyD;AACvD,aAAOsS,eAAe,CAACF,SAAD,EAAYpS,MAAZ,CAAtB;AACD;AACF,GAhCD;;AAkCAoS,EAAAA,SAAS,CAACM,QAAV,GAAqB,YAAY;AAC/B,QAAMC,KAAK,GAAGhB,UAAU,CAAC,CAAD,CAAxB;;AACA,QAAI,CAAC7H,aAAL,EAAoB;AAClB,aAAOsI,SAAS,CAAC7H,IAAV,CAAe,CAAf,CAAP;AACD;;AACD,QAAM7Q,KAAK,GAAGiY,UAAU,CAACxX,OAAX,CAAmB2P,aAAnB,CAAd;AACAsI,IAAAA,SAAS,CAAC7H,IAAV,CAAeoH,UAAU,CAACjY,KAAK,GAAG,CAAT,CAAV,IAAyBiZ,KAAxC;AACD,GAPD;;AASAP,EAAAA,SAAS,CAACQ,YAAV,GAAyB,YAAY;AACnC,QAAMC,IAAI,GAAGlB,UAAU,CAACA,UAAU,CAACtK,MAAX,GAAoB,CAArB,CAAvB;;AACA,QAAI,CAACyC,aAAL,EAAoB;AAClB,aAAOsI,SAAS,CAAC7H,IAAV,CAAesI,IAAf,CAAP;AACD;;AACD,QAAMnZ,KAAK,GAAGiY,UAAU,CAACxX,OAAX,CAAmB2P,aAAnB,CAAd;AACA,QAAM9J,MAAM,GAAG2R,UAAU,CAACjY,KAAK,GAAG,CAAT,CAAV,IAAyBmZ,IAAxC;AACAT,IAAAA,SAAS,CAAC7H,IAAV,CAAevK,MAAf;AACD,GARD;;AAUA,MAAMqS,gBAAgB,GAAGD,SAAS,CAAC9H,QAAnC;;AAEA8H,EAAAA,SAAS,CAAC9H,QAAV,GAAqB,UAAClM,KAAD,EAAiB;AACpCyT,IAAAA,SAAS,GAAGzT,KAAK,CAACyT,SAAN,IAAmBA,SAA/B;AACAQ,IAAAA,gBAAgB,CAACjU,KAAD,CAAhB;AACD,GAHD;;AAKAgU,EAAAA,SAAS,CAACU,YAAV,GAAyB,UAACC,aAAD,EAAyB;AAChDb,IAAAA,eAAe,CAAC,IAAD,CAAf;AACAJ,IAAAA,yBAAyB,CAAC9W,OAA1B,CAAkC,UAACT,EAAD;AAAA,aAAQA,EAAE,EAAV;AAAA,KAAlC;AAEAmX,IAAAA,mBAAmB,GAAGqB,aAAtB;AAEAb,IAAAA,eAAe,CAAC,KAAD,CAAf;AACAD,IAAAA,aAAa;AACbD,IAAAA,iBAAiB;AACjBF,IAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;AAEAA,IAAAA,SAAS,CAAC9H,QAAV,CAAmB;AAACtE,MAAAA,aAAa,EAAE4L;AAAhB,KAAnB;AACD,GAZD;;AAcAE,EAAAA,yBAAyB,GAAGK,iBAAiB,CAACC,SAAD,CAA7C;AAEA,SAAOA,SAAP;AACD,CA1ND;;ACvCA,IAAMY,mBAAmB,GAAG;AAC1BC,EAAAA,SAAS,EAAE,YADe;AAE1BC,EAAAA,OAAO,EAAE,OAFiB;AAG1BC,EAAAA,KAAK,EAAE;AAHmB,CAA5B;AAMA;AACA;AACA;AACA;;AACA,SAASC,QAAT,CACEtQ,OADF,EAEE1E,KAFF,EAGyB;AACvB;AACA,6CAAa;AACXuE,IAAAA,SAAS,CACP,EAAEvE,KAAK,IAAIA,KAAK,CAAC4B,MAAjB,CADO,EAEP,CACE,4EADF,EAEE,kDAFF,EAGEyB,IAHF,CAGO,GAHP,CAFO,CAAT;AAOD;;AAED,MAAIkI,SAA2B,GAAG,EAAlC;AACA,MAAI0J,mBAA+B,GAAG,EAAtC;AACA,MAAIC,QAAQ,GAAG,KAAf;AAEA,MAAOtT,MAAP,GAAiB5B,KAAjB,CAAO4B,MAAP;AAEA,MAAMuT,WAAW,GAAG1Y,gBAAgB,CAACuD,KAAD,EAAQ,CAAC,QAAD,CAAR,CAApC;AACA,MAAMoV,WAAW,qBAAOD,WAAP;AAAoBxN,IAAAA,OAAO,EAAE,QAA7B;AAAuCD,IAAAA,KAAK,EAAE;AAA9C,IAAjB;AACA,MAAM2N,UAAU;AACd3N,IAAAA,KAAK,EAAE5B,YAAY,CAAC4B;AADN,KAEXyN,WAFW;AAGd1N,IAAAA,YAAY,EAAE;AAHA,IAAhB;AAMA,MAAM6N,WAAW,GAAGtD,KAAK,CAACtN,OAAD,EAAU0Q,WAAV,CAAzB;AACA,MAAMG,qBAAqB,GAAGtY,gBAAgB,CAACqY,WAAD,CAA9C;;AAEA,WAASnO,SAAT,CAAmBzH,KAAnB,EAAuC;AACrC,QAAI,CAACA,KAAK,CAACkC,MAAP,IAAiBsT,QAArB,EAA+B;AAC7B;AACD;;AAED,QAAMM,UAAU,GAAI9V,KAAK,CAACkC,MAAP,CAA0B6T,OAA1B,CAAkC7T,MAAlC,CAAnB;;AAEA,QAAI,CAAC4T,UAAL,EAAiB;AACf;AACD,KAToC;AAYrC;AACA;AACA;;;AACA,QAAM7N,OAAO,GACX6N,UAAU,CAAChN,YAAX,CAAwB,oBAAxB,KACAxI,KAAK,CAAC2H,OADN,IAEA7B,YAAY,CAAC6B,OAHf,CAfqC;;AAqBrC,QAAI6N,UAAU,CAAChX,MAAf,EAAuB;AACrB;AACD;;AAED,QAAIkB,KAAK,CAAC9D,IAAN,KAAe,YAAf,IAA+B,OAAOyZ,UAAU,CAAC3N,KAAlB,KAA4B,SAA/D,EAA0E;AACxE;AACD;;AAED,QACEhI,KAAK,CAAC9D,IAAN,KAAe,YAAf,IACA+L,OAAO,CAAC5L,OAAR,CAAiB6Y,mBAAD,CAA6BlV,KAAK,CAAC9D,IAAnC,CAAhB,IAA4D,CAF9D,EAGE;AACA;AACD;;AAED,QAAMgH,QAAQ,GAAGoP,KAAK,CAACwD,UAAD,EAAaH,UAAb,CAAtB;;AAEA,QAAIzS,QAAJ,EAAc;AACZqS,MAAAA,mBAAmB,GAAGA,mBAAmB,CAAC/X,MAApB,CAA2B0F,QAA3B,CAAtB;AACD;AACF;;AAED,WAASuM,EAAT,CACEnF,IADF,EAEEoF,SAFF,EAGEC,OAHF,EAIEC,OAJF,EAKQ;AAAA,QADNA,OACM;AADNA,MAAAA,OACM,GADuC,KACvC;AAAA;;AACNtF,IAAAA,IAAI,CAAC1H,gBAAL,CAAsB8M,SAAtB,EAAiCC,OAAjC,EAA0CC,OAA1C;AACA/D,IAAAA,SAAS,CAAClO,IAAV,CAAe;AAAC2M,MAAAA,IAAI,EAAJA,IAAD;AAAOoF,MAAAA,SAAS,EAATA,SAAP;AAAkBC,MAAAA,OAAO,EAAPA,OAAlB;AAA2BC,MAAAA,OAAO,EAAPA;AAA3B,KAAf;AACD;;AAED,WAASoG,iBAAT,CAA2B9S,QAA3B,EAAqD;AACnD,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;AAEA0Q,IAAAA,EAAE,CAAC1Q,SAAD,EAAY,YAAZ,EAA0B0I,SAA1B,EAAqCzM,aAArC,CAAF;AACAyU,IAAAA,EAAE,CAAC1Q,SAAD,EAAY,WAAZ,EAAyB0I,SAAzB,CAAF;AACAgI,IAAAA,EAAE,CAAC1Q,SAAD,EAAY,SAAZ,EAAuB0I,SAAvB,CAAF;AACAgI,IAAAA,EAAE,CAAC1Q,SAAD,EAAY,OAAZ,EAAqB0I,SAArB,CAAF;AACD;;AAED,WAASwO,oBAAT,GAAsC;AACpCpK,IAAAA,SAAS,CAAC3O,OAAV,CAAkB,gBAAyD;AAAA,UAAvDoN,IAAuD,QAAvDA,IAAuD;AAAA,UAAjDoF,SAAiD,QAAjDA,SAAiD;AAAA,UAAtCC,OAAsC,QAAtCA,OAAsC;AAAA,UAA7BC,OAA6B,QAA7BA,OAA6B;AACzEtF,MAAAA,IAAI,CAACvH,mBAAL,CAAyB2M,SAAzB,EAAoCC,OAApC,EAA6CC,OAA7C;AACD,KAFD;AAGA/D,IAAAA,SAAS,GAAG,EAAZ;AACD;;AAED,WAASqK,cAAT,CAAwBhT,QAAxB,EAAkD;AAChD,QAAMiT,eAAe,GAAGjT,QAAQ,CAAC6J,OAAjC;AACA,QAAMqJ,cAAc,GAAGlT,QAAQ,CAAC0J,MAAhC;AACA,QAAMyJ,eAAe,GAAGnT,QAAQ,CAAC2J,OAAjC;;AAEA3J,IAAAA,QAAQ,CAAC6J,OAAT,GAAmB,UAACuJ,2BAAD,EAA8C;AAAA,UAA7CA,2BAA6C;AAA7CA,QAAAA,2BAA6C,GAAf,IAAe;AAAA;;AAC/D,UAAIA,2BAAJ,EAAiC;AAC/Bf,QAAAA,mBAAmB,CAACrY,OAApB,CAA4B,UAACgG,QAAD,EAAc;AACxCA,UAAAA,QAAQ,CAAC6J,OAAT;AACD,SAFD;AAGD;;AAEDwI,MAAAA,mBAAmB,GAAG,EAAtB;AAEAU,MAAAA,oBAAoB;AACpBE,MAAAA,eAAe;AAChB,KAXD;;AAaAjT,IAAAA,QAAQ,CAAC0J,MAAT,GAAkB,YAAY;AAC5BwJ,MAAAA,cAAc;AACdb,MAAAA,mBAAmB,CAACrY,OAApB,CAA4B,UAACgG,QAAD;AAAA,eAAcA,QAAQ,CAAC0J,MAAT,EAAd;AAAA,OAA5B;AACA4I,MAAAA,QAAQ,GAAG,KAAX;AACD,KAJD;;AAMAtS,IAAAA,QAAQ,CAAC2J,OAAT,GAAmB,YAAY;AAC7BwJ,MAAAA,eAAe;AACfd,MAAAA,mBAAmB,CAACrY,OAApB,CAA4B,UAACgG,QAAD;AAAA,eAAcA,QAAQ,CAAC2J,OAAT,EAAd;AAAA,OAA5B;AACA2I,MAAAA,QAAQ,GAAG,IAAX;AACD,KAJD;;AAMAQ,IAAAA,iBAAiB,CAAC9S,QAAD,CAAjB;AACD;;AAED2S,EAAAA,qBAAqB,CAAC3Y,OAAtB,CAA8BgZ,cAA9B;AAEA,SAAON,WAAP;AACD;;ACrJD,IAAMtQ,WAAwB,GAAG;AAC/BoD,EAAAA,IAAI,EAAE,aADyB;AAE/B7M,EAAAA,YAAY,EAAE,KAFiB;AAG/BY,EAAAA,EAH+B,cAG5ByG,QAH4B,EAGlB;AAAA;;AACX;AACA,QAAI,2BAACA,QAAQ,CAAC5C,KAAT,CAAewH,MAAhB,aAAC,sBAAuBgD,OAAxB,CAAJ,EAAqC;AACnC,iDAAa;AACXjG,QAAAA,SAAS,CACP3B,QAAQ,CAAC5C,KAAT,CAAegF,WADR,EAEP,gEAFO,CAAT;AAID;;AAED,aAAO,EAAP;AACD;;AAED,uBAAuB0E,WAAW,CAAC9G,QAAQ,CAAC+G,MAAV,CAAlC;AAAA,QAAOtI,GAAP,gBAAOA,GAAP;AAAA,QAAYmE,OAAZ,gBAAYA,OAAZ;;AAEA,QAAM0E,QAAQ,GAAGtH,QAAQ,CAAC5C,KAAT,CAAegF,WAAf,GACbiR,qBAAqB,EADR,GAEb,IAFJ;AAIA,WAAO;AACLrP,MAAAA,QADK,sBACY;AACf,YAAIsD,QAAJ,EAAc;AACZ7I,UAAAA,GAAG,CAAC6U,YAAJ,CAAiBhM,QAAjB,EAA2B7I,GAAG,CAACuI,iBAA/B;AACAvI,UAAAA,GAAG,CAAClC,YAAJ,CAAiB,kBAAjB,EAAqC,EAArC;AACAkC,UAAAA,GAAG,CAACtC,KAAJ,CAAUoX,QAAV,GAAqB,QAArB;AAEAvT,UAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAAC3G,YAAAA,KAAK,EAAE,KAAR;AAAeD,YAAAA,SAAS,EAAE;AAA1B,WAAlB;AACD;AACF,OATI;AAUL0B,MAAAA,OAVK,qBAUW;AACd,YAAIkD,QAAJ,EAAc;AACZ,cAAOlL,kBAAP,GAA6BqC,GAAG,CAACtC,KAAjC,CAAOC,kBAAP;AACA,cAAMmH,QAAQ,GAAGiQ,MAAM,CAACpX,kBAAkB,CAACyE,OAAnB,CAA2B,IAA3B,EAAiC,EAAjC,CAAD,CAAvB,CAFY;AAKZ;AACA;;AACA+B,UAAAA,OAAO,CAACzG,KAAR,CAAcsX,eAAd,GAAmCC,IAAI,CAACC,KAAL,CAAWpQ,QAAQ,GAAG,EAAtB,CAAnC;AAEA+D,UAAAA,QAAQ,CAACnL,KAAT,CAAeC,kBAAf,GAAoCA,kBAApC;AACAC,UAAAA,kBAAkB,CAAC,CAACiL,QAAD,CAAD,EAAa,SAAb,CAAlB;AACD;AACF,OAvBI;AAwBLjD,MAAAA,MAxBK,oBAwBU;AACb,YAAIiD,QAAJ,EAAc;AACZA,UAAAA,QAAQ,CAACnL,KAAT,CAAeC,kBAAf,GAAoC,KAApC;AACD;AACF,OA5BI;AA6BL+H,MAAAA,MA7BK,oBA6BU;AACb,YAAImD,QAAJ,EAAc;AACZjL,UAAAA,kBAAkB,CAAC,CAACiL,QAAD,CAAD,EAAa,QAAb,CAAlB;AACD;AACF;AAjCI,KAAP;AAmCD;AAzD8B,CAAjC;AA4DA;AAEA,SAAS+L,qBAAT,GAAiD;AAC/C,MAAM/L,QAAQ,GAAGjM,GAAG,EAApB;AACAiM,EAAAA,QAAQ,CAACZ,SAAT,GAAqB/O,cAArB;AACA0E,EAAAA,kBAAkB,CAAC,CAACiL,QAAD,CAAD,EAAa,QAAb,CAAlB;AACA,SAAOA,QAAP;AACD;;ACtED,IAAIsM,WAAW,GAAG;AAAC7W,EAAAA,OAAO,EAAE,CAAV;AAAaC,EAAAA,OAAO,EAAE;AAAtB,CAAlB;AACA,IAAI6W,eAA2D,GAAG,EAAlE;;AAEA,SAASC,gBAAT,OAAgE;AAAA,MAArC/W,OAAqC,QAArCA,OAAqC;AAAA,MAA5BC,OAA4B,QAA5BA,OAA4B;AAC9D4W,EAAAA,WAAW,GAAG;AAAC7W,IAAAA,OAAO,EAAPA,OAAD;AAAUC,IAAAA,OAAO,EAAPA;AAAV,GAAd;AACD;;AAED,SAAS+W,sBAAT,CAAgC7H,GAAhC,EAAqD;AACnDA,EAAAA,GAAG,CAACxM,gBAAJ,CAAqB,WAArB,EAAkCoU,gBAAlC;AACD;;AAED,SAASE,yBAAT,CAAmC9H,GAAnC,EAAwD;AACtDA,EAAAA,GAAG,CAACrM,mBAAJ,CAAwB,WAAxB,EAAqCiU,gBAArC;AACD;;AAED,IAAMzR,YAA0B,GAAG;AACjCmD,EAAAA,IAAI,EAAE,cAD2B;AAEjC7M,EAAAA,YAAY,EAAE,KAFmB;AAGjCY,EAAAA,EAHiC,cAG9ByG,QAH8B,EAGpB;AACX,QAAMnE,SAAS,GAAGmE,QAAQ,CAACnE,SAA3B;AACA,QAAMqQ,GAAG,GAAG1P,gBAAgB,CAACwD,QAAQ,CAAC5C,KAAT,CAAe4H,aAAf,IAAgCnJ,SAAjC,CAA5B;AAEA,QAAIoY,gBAAgB,GAAG,KAAvB;AACA,QAAIC,aAAa,GAAG,KAApB;AACA,QAAIC,WAAW,GAAG,IAAlB;AACA,QAAI3M,SAAS,GAAGxH,QAAQ,CAAC5C,KAAzB;;AAEA,aAASgX,oBAAT,GAAyC;AACvC,aACEpU,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,KAAgC,SAAhC,IAA6CrC,QAAQ,CAAC1D,KAAT,CAAe4D,SAD9D;AAGD;;AAED,aAASmU,WAAT,GAA6B;AAC3BnI,MAAAA,GAAG,CAACxM,gBAAJ,CAAqB,WAArB,EAAkCmJ,WAAlC;AACD;;AAED,aAASyL,cAAT,GAAgC;AAC9BpI,MAAAA,GAAG,CAACrM,mBAAJ,CAAwB,WAAxB,EAAqCgJ,WAArC;AACD;;AAED,aAAS0L,2BAAT,GAA6C;AAC3CN,MAAAA,gBAAgB,GAAG,IAAnB;AACAjU,MAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAAC9F,QAAAA,sBAAsB,EAAE;AAAzB,OAAlB;AACAyQ,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAASpL,WAAT,CAAqB/L,KAArB,EAA8C;AAC5C;AACA;AACA,UAAM0X,qBAAqB,GAAG1X,KAAK,CAACkC,MAAN,GAC1BnD,SAAS,CAACoD,QAAV,CAAmBnC,KAAK,CAACkC,MAAzB,CAD0B,GAE1B,IAFJ;AAGA,UAAOqD,YAAP,GAAuBrC,QAAQ,CAAC5C,KAAhC,CAAOiF,YAAP;AACA,UAAOtF,OAAP,GAA2BD,KAA3B,CAAOC,OAAP;AAAA,UAAgBC,OAAhB,GAA2BF,KAA3B,CAAgBE,OAAhB;AAEA,UAAMyX,IAAI,GAAG5Y,SAAS,CAACuR,qBAAV,EAAb;AACA,UAAMsH,SAAS,GAAG3X,OAAO,GAAG0X,IAAI,CAACzW,IAAjC;AACA,UAAM2W,SAAS,GAAG3X,OAAO,GAAGyX,IAAI,CAAC9W,GAAjC;;AAEA,UAAI6W,qBAAqB,IAAI,CAACxU,QAAQ,CAAC5C,KAAT,CAAeuG,WAA7C,EAA0D;AACxD3D,QAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAChB;AACA9F,UAAAA,sBAFgB,oCAES;AACvB,gBAAMiR,IAAI,GAAG5Y,SAAS,CAACuR,qBAAV,EAAb;AAEA,gBAAInP,CAAC,GAAGlB,OAAR;AACA,gBAAIa,CAAC,GAAGZ,OAAR;;AAEA,gBAAIqF,YAAY,KAAK,SAArB,EAAgC;AAC9BpE,cAAAA,CAAC,GAAGwW,IAAI,CAACzW,IAAL,GAAY0W,SAAhB;AACA9W,cAAAA,CAAC,GAAG6W,IAAI,CAAC9W,GAAL,GAAWgX,SAAf;AACD;;AAED,gBAAMhX,GAAG,GAAG0E,YAAY,KAAK,YAAjB,GAAgCoS,IAAI,CAAC9W,GAArC,GAA2CC,CAAvD;AACA,gBAAMO,KAAK,GAAGkE,YAAY,KAAK,UAAjB,GAA8BoS,IAAI,CAACtW,KAAnC,GAA2CF,CAAzD;AACA,gBAAMH,MAAM,GAAGuE,YAAY,KAAK,YAAjB,GAAgCoS,IAAI,CAAC3W,MAArC,GAA8CF,CAA7D;AACA,gBAAMI,IAAI,GAAGqE,YAAY,KAAK,UAAjB,GAA8BoS,IAAI,CAACzW,IAAnC,GAA0CC,CAAvD;AAEA,mBAAO;AACL2W,cAAAA,KAAK,EAAEzW,KAAK,GAAGH,IADV;AAEL6W,cAAAA,MAAM,EAAE/W,MAAM,GAAGH,GAFZ;AAGLA,cAAAA,GAAG,EAAHA,GAHK;AAILQ,cAAAA,KAAK,EAALA,KAJK;AAKLL,cAAAA,MAAM,EAANA,MALK;AAMLE,cAAAA,IAAI,EAAJA;AANK,aAAP;AAQD;AA1Be,SAAlB;AA4BD;AACF;;AAED,aAAS8W,MAAT,GAAwB;AACtB,UAAI9U,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;AAC/BwR,QAAAA,eAAe,CAACpZ,IAAhB,CAAqB;AAACuF,UAAAA,QAAQ,EAARA,QAAD;AAAWkM,UAAAA,GAAG,EAAHA;AAAX,SAArB;AACA6H,QAAAA,sBAAsB,CAAC7H,GAAD,CAAtB;AACD;AACF;;AAED,aAASrC,OAAT,GAAyB;AACvBgK,MAAAA,eAAe,GAAGA,eAAe,CAAC1Z,MAAhB,CAChB,UAAC4a,IAAD;AAAA,eAAUA,IAAI,CAAC/U,QAAL,KAAkBA,QAA5B;AAAA,OADgB,CAAlB;;AAIA,UAAI6T,eAAe,CAAC1Z,MAAhB,CAAuB,UAAC4a,IAAD;AAAA,eAAUA,IAAI,CAAC7I,GAAL,KAAaA,GAAvB;AAAA,OAAvB,EAAmD7F,MAAnD,KAA8D,CAAlE,EAAqE;AACnE2N,QAAAA,yBAAyB,CAAC9H,GAAD,CAAzB;AACD;AACF;;AAED,WAAO;AACLlI,MAAAA,QAAQ,EAAE8Q,MADL;AAEL7Q,MAAAA,SAAS,EAAE4F,OAFN;AAGL9F,MAAAA,cAHK,4BAGkB;AACrByD,QAAAA,SAAS,GAAGxH,QAAQ,CAAC5C,KAArB;AACD,OALI;AAML0G,MAAAA,aANK,yBAMSkR,CANT,SAMkC;AAAA,YAArB3S,YAAqB,SAArBA,YAAqB;;AACrC,YAAI4R,gBAAJ,EAAsB;AACpB;AACD;;AAED,YACE5R,YAAY,KAAKjH,SAAjB,IACAoM,SAAS,CAACnF,YAAV,KAA2BA,YAF7B,EAGE;AACAwH,UAAAA,OAAO;;AAEP,cAAIxH,YAAJ,EAAkB;AAChByS,YAAAA,MAAM;;AAEN,gBACE9U,QAAQ,CAAC1D,KAAT,CAAe6M,SAAf,IACA,CAAC+K,aADD,IAEA,CAACE,oBAAoB,EAHvB,EAIE;AACAC,cAAAA,WAAW;AACZ;AACF,WAVD,MAUO;AACLC,YAAAA,cAAc;AACdC,YAAAA,2BAA2B;AAC5B;AACF;AACF,OAhCI;AAiCLnQ,MAAAA,OAjCK,qBAiCW;AACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAf,IAA+B,CAAC6R,aAApC,EAAmD;AACjD,cAAIC,WAAJ,EAAiB;AACftL,YAAAA,WAAW,CAAC+K,WAAD,CAAX;AACAO,YAAAA,WAAW,GAAG,KAAd;AACD;;AAED,cAAI,CAACC,oBAAoB,EAAzB,EAA6B;AAC3BC,YAAAA,WAAW;AACZ;AACF;AACF,OA5CI;AA6CL9P,MAAAA,SA7CK,qBA6CKyQ,CA7CL,EA6CQlY,KA7CR,EA6CqB;AACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;AACvB8W,UAAAA,WAAW,GAAG;AAAC7W,YAAAA,OAAO,EAAED,KAAK,CAACC,OAAhB;AAAyBC,YAAAA,OAAO,EAAEF,KAAK,CAACE;AAAxC,WAAd;AACD;;AACDkX,QAAAA,aAAa,GAAGpX,KAAK,CAAC9D,IAAN,KAAe,OAA/B;AACD,OAlDI;AAmDLkL,MAAAA,QAnDK,sBAmDY;AACf,YAAIlE,QAAQ,CAAC5C,KAAT,CAAeiF,YAAnB,EAAiC;AAC/BkS,UAAAA,2BAA2B;AAC3BD,UAAAA,cAAc;AACdH,UAAAA,WAAW,GAAG,IAAd;AACD;AACF;AAzDI,KAAP;AA2DD;AAzJgC,CAAnC;;ACbA,SAASc,QAAT,CAAkB7X,KAAlB,EAAgC8X,QAAhC,EAA8E;AAAA;;AAC5E,SAAO;AACLvQ,IAAAA,aAAa,oBACRvH,KAAK,CAACuH,aADE;AAEXqJ,MAAAA,SAAS,YACJ,CAAC,yBAAA5Q,KAAK,CAACuH,aAAN,0CAAqBqJ,SAArB,KAAkC,EAAnC,EAAuC7T,MAAvC,CACD;AAAA,YAAEqL,IAAF,QAAEA,IAAF;AAAA,eAAYA,IAAI,KAAK0P,QAAQ,CAAC1P,IAA9B;AAAA,OADC,CADI,GAIP0P,QAJO;AAFE;AADR,GAAP;AAWD;;AAED,IAAM5S,iBAAoC,GAAG;AAC3CkD,EAAAA,IAAI,EAAE,mBADqC;AAE3C7M,EAAAA,YAAY,EAAE,KAF6B;AAG3CY,EAAAA,EAH2C,cAGxCyG,QAHwC,EAG9B;AACX,QAAOnE,SAAP,GAAoBmE,QAApB,CAAOnE,SAAP;;AAEA,aAASoN,SAAT,GAA8B;AAC5B,aAAO,CAAC,CAACjJ,QAAQ,CAAC5C,KAAT,CAAekF,iBAAxB;AACD;;AAED,QAAIzH,SAAJ;AACA,QAAIsa,eAAe,GAAG,CAAC,CAAvB;AACA,QAAIlB,gBAAgB,GAAG,KAAvB;AACA,QAAImB,eAA8B,GAAG,EAArC;AAEA,QAAMF,QAGL,GAAG;AACF1P,MAAAA,IAAI,EAAE,wBADJ;AAEFoI,MAAAA,OAAO,EAAE,IAFP;AAGFC,MAAAA,KAAK,EAAE,YAHL;AAIFtU,MAAAA,EAJE,qBAIU;AAAA,YAAR+C,KAAQ,SAARA,KAAQ;;AACV,YAAI2M,SAAS,EAAb,EAAiB;AACf,cAAImM,eAAe,CAACjc,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAAlD,EAAqD;AACnDua,YAAAA,eAAe,GAAG,EAAlB;AACD;;AAED,cACEva,SAAS,KAAKyB,KAAK,CAACzB,SAApB,IACAua,eAAe,CAACjc,OAAhB,CAAwBmD,KAAK,CAACzB,SAA9B,MAA6C,CAAC,CAFhD,EAGE;AACAua,YAAAA,eAAe,CAAC3a,IAAhB,CAAqB6B,KAAK,CAACzB,SAA3B;AACAmF,YAAAA,QAAQ,CAACsJ,QAAT,CAAkB;AAChB;AACA9F,cAAAA,sBAAsB,EAAE;AAAA,uBACtBA,uBAAsB,CAAClH,KAAK,CAACzB,SAAP,CADA;AAAA;AAFR,aAAlB;AAKD;;AAEDA,UAAAA,SAAS,GAAGyB,KAAK,CAACzB,SAAlB;AACD;AACF;AAxBC,KAHJ;;AA8BA,aAAS2I,uBAAT,CAAgC3I,SAAhC,EAAwE;AACtE,aAAOwa,2BAA2B,CAChCza,gBAAgB,CAACC,SAAD,CADgB,EAEhCgB,SAAS,CAACuR,qBAAV,EAFgC,EAGhCtS,SAAS,CAACe,SAAS,CAACyZ,cAAV,EAAD,CAHuB,EAIhCH,eAJgC,CAAlC;AAMD;;AAED,aAASI,gBAAT,CAA0BpQ,YAA1B,EAA8D;AAC5D8O,MAAAA,gBAAgB,GAAG,IAAnB;AACAjU,MAAAA,QAAQ,CAACsJ,QAAT,CAAkBnE,YAAlB;AACA8O,MAAAA,gBAAgB,GAAG,KAAnB;AACD;;AAED,aAASuB,WAAT,GAA6B;AAC3B,UAAI,CAACvB,gBAAL,EAAuB;AACrBsB,QAAAA,gBAAgB,CAACN,QAAQ,CAACjV,QAAQ,CAAC5C,KAAV,EAAiB8X,QAAjB,CAAT,CAAhB;AACD;AACF;;AAED,WAAO;AACLlR,MAAAA,QAAQ,EAAEwR,WADL;AAEL1R,MAAAA,aAAa,EAAE0R,WAFV;AAGLjR,MAAAA,SAHK,qBAGKyQ,CAHL,EAGQlY,KAHR,EAGqB;AACxB,YAAIpB,YAAY,CAACoB,KAAD,CAAhB,EAAyB;AACvB,cAAM2Y,KAAK,GAAG3a,SAAS,CAACkF,QAAQ,CAACnE,SAAT,CAAmByZ,cAAnB,EAAD,CAAvB;AACA,cAAMI,UAAU,GAAGD,KAAK,CAACtO,IAAN,CACjB,UAACsN,IAAD;AAAA,mBACEA,IAAI,CAACzW,IAAL,GAAY,CAAZ,IAAiBlB,KAAK,CAACC,OAAvB,IACA0X,IAAI,CAACtW,KAAL,GAAa,CAAb,IAAkBrB,KAAK,CAACC,OADxB,IAEA0X,IAAI,CAAC9W,GAAL,GAAW,CAAX,IAAgBb,KAAK,CAACE,OAFtB,IAGAyX,IAAI,CAAC3W,MAAL,GAAc,CAAd,IAAmBhB,KAAK,CAACE,OAJ3B;AAAA,WADiB,CAAnB;AAOA,cAAMtE,KAAK,GAAG+c,KAAK,CAACtc,OAAN,CAAcuc,UAAd,CAAd;AACAP,UAAAA,eAAe,GAAGzc,KAAK,GAAG,CAAC,CAAT,GAAaA,KAAb,GAAqByc,eAAvC;AACD;AACF,OAhBI;AAiBLjR,MAAAA,QAjBK,sBAiBY;AACfiR,QAAAA,eAAe,GAAG,CAAC,CAAnB;AACD;AAnBI,KAAP;AAqBD;AAvF0C,CAA7C;AA0FA,AAEO,SAASE,2BAAT,CACLM,oBADK,EAELC,YAFK,EAGLC,WAHK,EAILV,eAJK,EAYL;AACA;AACA,MAAIU,WAAW,CAACxP,MAAZ,GAAqB,CAArB,IAA0BsP,oBAAoB,KAAK,IAAvD,EAA6D;AAC3D,WAAOC,YAAP;AACD,GAJD;;;AAOA,MACEC,WAAW,CAACxP,MAAZ,KAAuB,CAAvB,IACA8O,eAAe,IAAI,CADnB,IAEAU,WAAW,CAAC,CAAD,CAAX,CAAe7X,IAAf,GAAsB6X,WAAW,CAAC,CAAD,CAAX,CAAe1X,KAHvC,EAIE;AACA,WAAO0X,WAAW,CAACV,eAAD,CAAX,IAAgCS,YAAvC;AACD;;AAED,UAAQD,oBAAR;AACE,SAAK,KAAL;AACA,SAAK,QAAL;AAAe;AACb,YAAMG,SAAS,GAAGD,WAAW,CAAC,CAAD,CAA7B;AACA,YAAME,QAAQ,GAAGF,WAAW,CAACA,WAAW,CAACxP,MAAZ,GAAqB,CAAtB,CAA5B;AACA,YAAM2P,KAAK,GAAGL,oBAAoB,KAAK,KAAvC;AAEA,YAAMhY,GAAG,GAAGmY,SAAS,CAACnY,GAAtB;AACA,YAAMG,MAAM,GAAGiY,QAAQ,CAACjY,MAAxB;AACA,YAAME,IAAI,GAAGgY,KAAK,GAAGF,SAAS,CAAC9X,IAAb,GAAoB+X,QAAQ,CAAC/X,IAA/C;AACA,YAAMG,KAAK,GAAG6X,KAAK,GAAGF,SAAS,CAAC3X,KAAb,GAAqB4X,QAAQ,CAAC5X,KAAjD;AACA,YAAMyW,KAAK,GAAGzW,KAAK,GAAGH,IAAtB;AACA,YAAM6W,MAAM,GAAG/W,MAAM,GAAGH,GAAxB;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,GAAD;AAAMG,UAAAA,MAAM,EAANA,MAAN;AAAcE,UAAAA,IAAI,EAAJA,IAAd;AAAoBG,UAAAA,KAAK,EAALA,KAApB;AAA2ByW,UAAAA,KAAK,EAALA,KAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD,SAAK,MAAL;AACA,SAAK,OAAL;AAAc;AACZ,YAAMoB,OAAO,GAAGvC,IAAI,CAACwC,GAAL,OAAAxC,IAAI,EAAQmC,WAAW,CAAC9L,GAAZ,CAAgB,UAAC0L,KAAD;AAAA,iBAAWA,KAAK,CAACzX,IAAjB;AAAA,SAAhB,CAAR,CAApB;AACA,YAAMmY,QAAQ,GAAGzC,IAAI,CAAC0C,GAAL,OAAA1C,IAAI,EAAQmC,WAAW,CAAC9L,GAAZ,CAAgB,UAAC0L,KAAD;AAAA,iBAAWA,KAAK,CAACtX,KAAjB;AAAA,SAAhB,CAAR,CAArB;AACA,YAAMkY,YAAY,GAAGR,WAAW,CAAC1b,MAAZ,CAAmB,UAACsa,IAAD;AAAA,iBACtCkB,oBAAoB,KAAK,MAAzB,GACIlB,IAAI,CAACzW,IAAL,KAAciY,OADlB,GAEIxB,IAAI,CAACtW,KAAL,KAAegY,QAHmB;AAAA,SAAnB,CAArB;AAMA,YAAMxY,IAAG,GAAG0Y,YAAY,CAAC,CAAD,CAAZ,CAAgB1Y,GAA5B;AACA,YAAMG,OAAM,GAAGuY,YAAY,CAACA,YAAY,CAAChQ,MAAb,GAAsB,CAAvB,CAAZ,CAAsCvI,MAArD;AACA,YAAME,KAAI,GAAGiY,OAAb;AACA,YAAM9X,MAAK,GAAGgY,QAAd;;AACA,YAAMvB,MAAK,GAAGzW,MAAK,GAAGH,KAAtB;;AACA,YAAM6W,OAAM,GAAG/W,OAAM,GAAGH,IAAxB;;AAEA,eAAO;AAACA,UAAAA,GAAG,EAAHA,IAAD;AAAMG,UAAAA,MAAM,EAANA,OAAN;AAAcE,UAAAA,IAAI,EAAJA,KAAd;AAAoBG,UAAAA,KAAK,EAALA,MAApB;AAA2ByW,UAAAA,KAAK,EAALA,MAA3B;AAAkCC,UAAAA,MAAM,EAANA;AAAlC,SAAP;AACD;;AACD;AAAS;AACP,eAAOe,YAAP;AACD;AArCH;AAuCD;;AC9KD,IAAMrT,MAAc,GAAG;AACrBiD,EAAAA,IAAI,EAAE,QADe;AAErB7M,EAAAA,YAAY,EAAE,KAFO;AAGrBY,EAAAA,EAHqB,cAGlByG,QAHkB,EAGR;AACX,QAAOnE,SAAP,GAA4BmE,QAA5B,CAAOnE,SAAP;AAAA,QAAkBkL,MAAlB,GAA4B/G,QAA5B,CAAkB+G,MAAlB;;AAEA,aAASuP,YAAT,GAA2D;AACzD,aAAOtW,QAAQ,CAACgJ,cAAT,GACHhJ,QAAQ,CAACgJ,cAAT,CAAwB1M,KAAxB,CAA8BgT,QAA9B,CAAuCzT,SADpC,GAEHA,SAFJ;AAGD;;AAED,aAAS0a,WAAT,CAAqB9d,KAArB,EAA6D;AAC3D,aAAOuH,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B,IAA1B,IAAkCvC,QAAQ,CAAC5C,KAAT,CAAemF,MAAf,KAA0B9J,KAAnE;AACD;;AAED,QAAI+d,WAA8B,GAAG,IAArC;AACA,QAAIC,WAA8B,GAAG,IAArC;;AAEA,aAASC,cAAT,GAAgC;AAC9B,UAAMC,cAAc,GAAGJ,WAAW,CAAC,WAAD,CAAX,GACnBD,YAAY,GAAGlJ,qBAAf,EADmB,GAEnB,IAFJ;AAGA,UAAMwJ,cAAc,GAAGL,WAAW,CAAC,QAAD,CAAX,GACnBxP,MAAM,CAACqG,qBAAP,EADmB,GAEnB,IAFJ;;AAIA,UACGuJ,cAAc,IAAIE,iBAAiB,CAACL,WAAD,EAAcG,cAAd,CAApC,IACCC,cAAc,IAAIC,iBAAiB,CAACJ,WAAD,EAAcG,cAAd,CAFtC,EAGE;AACA,YAAI5W,QAAQ,CAACgJ,cAAb,EAA6B;AAC3BhJ,UAAAA,QAAQ,CAACgJ,cAAT,CAAwB8N,MAAxB;AACD;AACF;;AAEDN,MAAAA,WAAW,GAAGG,cAAd;AACAF,MAAAA,WAAW,GAAGG,cAAd;;AAEA,UAAI5W,QAAQ,CAAC1D,KAAT,CAAe6M,SAAnB,EAA8B;AAC5BqF,QAAAA,qBAAqB,CAACkI,cAAD,CAArB;AACD;AACF;;AAED,WAAO;AACLtS,MAAAA,OADK,qBACW;AACd,YAAIpE,QAAQ,CAAC5C,KAAT,CAAemF,MAAnB,EAA2B;AACzBmU,UAAAA,cAAc;AACf;AACF;AALI,KAAP;AAOD;AAnDoB,CAAvB;AAsDA;AAEA,SAASG,iBAAT,CACEE,KADF,EAEEC,KAFF,EAGW;AACT,MAAID,KAAK,IAAIC,KAAb,EAAoB;AAClB,WACED,KAAK,CAACpZ,GAAN,KAAcqZ,KAAK,CAACrZ,GAApB,IACAoZ,KAAK,CAAC5Y,KAAN,KAAgB6Y,KAAK,CAAC7Y,KADtB,IAEA4Y,KAAK,CAACjZ,MAAN,KAAiBkZ,KAAK,CAAClZ,MAFvB,IAGAiZ,KAAK,CAAC/Y,IAAN,KAAegZ,KAAK,CAAChZ,IAJvB;AAMD;;AAED,SAAO,IAAP;AACD;;ACtEDoR,KAAK,CAAClK,eAAN,CAAsB;AAACN,EAAAA,MAAM,EAANA;AAAD,CAAtB;;;;;;;;;;;;"}