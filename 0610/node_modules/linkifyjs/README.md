linkifyjs
===

[![npm version](https://badge.fury.io/js/linkifyjs.svg)](https://www.npmjs.com/package/linkifyjs)

Core [Linkify](https://linkify.js.org/) JavaScript library. Use Linkify and its
related packages to detect URLs, email addresses and more in plain-text strings and convert them to HTML `<a>` anchor tags.

## Installation

Install from the command line with NPM

```
npm install linkifyjs
```

Import into your JavaScript with `require`
```js
const linkify = require('linkifyjs');
```
or with ES modules

```js
import * as linkify from 'linkifyjs';
```

Separate packages are available for each of the following features:
- [HTML strings](../linkify-html)
- [React component](../linkify-react)
- [jQuery plugin](../linkify-jquery)
- [DOM Elements](../linkify-element)
- [Plain-text](../linkify-string)
- [#hashtag plugin](../linkify-plugin-hashtag)
- [@mention plugin](../linkify-plugin-mention)
- [#ticket plugin](../linkify-plugin-ticket)
- [IP address plugin](../linkify-plugin-ip)
- [Keyword plugin](../linkify-plugin-keyword)

## Usage

[Read the full documentation](https://linkify.js.org/docs/linkifyjs.html).

## License

MIT
