var linkify=function(e){"use strict";const t=(e,t)=>{for(const n in t)e[n]=t[n];return e},n="numeric",i="ascii",r="alpha",s="asciinumeric",o="alphanumeric",a="domain",l="emoji",u="scheme",c="slashscheme",g="whitespace";function d(e,t){return e in t||(t[e]=[]),t[e]}function h(e,t,u){t[n]&&(t[s]=!0,t[o]=!0),t[i]&&(t[s]=!0,t[r]=!0),t[s]&&(t[o]=!0),t[r]&&(t[o]=!0),t[o]&&(t[a]=!0),t[l]&&(t[a]=!0);for(const n in t){const t=d(n,u);t.indexOf(e)<0&&t.push(e)}}function f(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}f.groups={},f.prototype={accepts(){return!!this.t},go(e){const t=this,n=t.j[e];if(n)return n;for(let n=0;n<t.jr.length;n++){const i=t.jr[n][0],r=t.jr[n][1];if(r&&i.test(e))return r}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,i){for(let r=0;r<e.length;r++)this.tt(e[r],t,n,i)},tr(e,t,n,i){let r;return i=i||f.groups,t&&t.j?r=t:(r=new f(t),n&&i&&h(t,n,i)),this.jr.push([e,r]),r},ts(e,t,n,i){let r=this;const s=e.length;if(!s)return r;for(let t=0;t<s-1;t++)r=r.tt(e[t]);return r.tt(e[s-1],t,n,i)},tt(e,n,i,r){r=r||f.groups;const s=this;if(n&&n.j)return s.j[e]=n,n;const o=n;let a,l=s.go(e);if(l?(a=new f,t(a.j,l.j),a.jr.push.apply(a.jr,l.jr),a.jd=l.jd,a.t=l.t):a=new f,o){if(r)if(a.t&&"string"==typeof a.t){const e=t(function(e,t){const n={};for(const i in t)t[i].indexOf(e)>=0&&(n[i]=!0);return n}(a.t,r),i);h(o,e,r)}else i&&h(o,i,r);a.t=o}return s.j[e]=a,a}};const p=(e,t,n,i,r)=>e.ta(t,n,i,r),m=(e,t,n,i,r)=>e.tr(t,n,i,r),E=(e,t,n,i,r)=>e.ts(t,n,i,r),y=(e,t,n,i,r)=>e.tt(t,n,i,r),k="WORD",b="UWORD",T="ASCIINUMERICAL",L="ALPHANUMERICAL",A="LOCALHOST",R="TLD",O="UTLD",C="SCHEME",v="SLASH_SCHEME",S="NUM",w="WS",j="NL",I="OPENBRACE",N="CLOSEBRACE",H="OPENBRACKET",x="CLOSEBRACKET",P="OPENPAREN",z="CLOSEPAREN",D="OPENANGLEBRACKET",U="CLOSEANGLEBRACKET",M="FULLWIDTHLEFTPAREN",B="FULLWIDTHRIGHTPAREN",K="LEFTCORNERBRACKET",F="RIGHTCORNERBRACKET",_="LEFTWHITECORNERBRACKET",Q="RIGHTWHITECORNERBRACKET",W="FULLWIDTHLESSTHAN",$="FULLWIDTHGREATERTHAN",G="AMPERSAND",q="APOSTROPHE",Y="ASTERISK",J="AT",X="BACKSLASH",V="BACKTICK",Z="CARET",ee="COLON",te="COMMA",ne="DOLLAR",ie="DOT",re="EQUALS",se="EXCLAMATION",oe="HYPHEN",ae="PERCENT",le="PIPE",ue="PLUS",ce="POUND",ge="QUERY",de="QUOTE",he="FULLWIDTHMIDDLEDOT",fe="SEMI",pe="SLASH",me="TILDE",Ee="UNDERSCORE",ye="EMOJI",ke="SYM";var be=Object.freeze({__proto__:null,WORD:k,UWORD:b,ASCIINUMERICAL:T,ALPHANUMERICAL:L,LOCALHOST:A,TLD:R,UTLD:O,SCHEME:C,SLASH_SCHEME:v,NUM:S,WS:w,NL:j,OPENBRACE:I,CLOSEBRACE:N,OPENBRACKET:H,CLOSEBRACKET:x,OPENPAREN:P,CLOSEPAREN:z,OPENANGLEBRACKET:D,CLOSEANGLEBRACKET:U,FULLWIDTHLEFTPAREN:M,FULLWIDTHRIGHTPAREN:B,LEFTCORNERBRACKET:K,RIGHTCORNERBRACKET:F,LEFTWHITECORNERBRACKET:_,RIGHTWHITECORNERBRACKET:Q,FULLWIDTHLESSTHAN:W,FULLWIDTHGREATERTHAN:$,AMPERSAND:G,APOSTROPHE:q,ASTERISK:Y,AT:J,BACKSLASH:X,BACKTICK:V,CARET:Z,COLON:ee,COMMA:te,DOLLAR:ne,DOT:ie,EQUALS:re,EXCLAMATION:se,HYPHEN:oe,PERCENT:ae,PIPE:le,PLUS:ue,POUND:ce,QUERY:ge,QUOTE:de,FULLWIDTHMIDDLEDOT:he,SEMI:fe,SLASH:pe,TILDE:me,UNDERSCORE:Ee,EMOJI:ye,SYM:ke});const Te=/[a-z]/,Le=/\p{L}/u,Ae=/\p{Emoji}/u,Re=/\d/,Oe=/\s/;var Ce=Object.freeze({__proto__:null,ASCII_LETTER:Te,LETTER:Le,EMOJI:Ae,EMOJI_VARIATION:/\ufe0f/,DIGIT:Re,SPACE:Oe});let ve=null,Se=null;function we(e,t){const n=je(t.replace(/[A-Z]/g,(e=>e.toLowerCase()))),i=n.length,r=[];let s=0,o=0;for(;o<i;){let a=e,l=null,u=0,c=null,g=-1,d=-1;for(;o<i&&(l=a.go(n[o]));)a=l,a.accepts()?(g=0,d=0,c=a):g>=0&&(g+=n[o].length,d++),u+=n[o].length,s+=n[o].length,o++;s-=g,o-=d,u-=g,r.push({t:c.t,v:t.slice(s-u,s),s:s-u,e:s})}return r}function je(e){const t=[],n=e.length;let i=0;for(;i<n;){let r,s=e.charCodeAt(i),o=s<55296||s>56319||i+1===n||(r=e.charCodeAt(i+1))<56320||r>57343?e[i]:e.slice(i,i+2);t.push(o),i+=o.length}return t}function Ie(e,t,n,i,r){let s;const o=t.length;for(let n=0;n<o-1;n++){const o=t[n];e.j[o]?s=e.j[o]:(s=new f(i),s.jr=r.slice(),e.j[o]=s),e=s}return s=new f(n),s.jr=r.slice(),e.j[t[o-1]]=s,s}function Ne(e){const t=[],n=[];let i=0;for(;i<e.length;){let r=0;for(;"0123456789".indexOf(e[i+r])>=0;)r++;if(r>0){t.push(n.join(""));for(let t=parseInt(e.substring(i,i+r),10);t>0;t--)n.pop();i+=r}else n.push(e[i]),i++}return t}const He={defaultProtocol:"http",events:null,format:Pe,formatHref:Pe,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function xe(e,n=null){let i=t({},He);e&&(i=t(i,e instanceof xe?e.o:e));const r=i.ignoreTags,s=[];for(let e=0;e<r.length;e++)s.push(r[e].toUpperCase());this.o=i,n&&(this.defaultRender=n),this.ignoreTags=s}function Pe(e){return e}xe.prototype={o:He,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){const i=null!=t;let r=this.o[e];return r?("object"==typeof r?(r=n.t in r?r[n.t]:He[e],"function"==typeof r&&i&&(r=r(t,n))):"function"==typeof r&&i&&(r=r(t,n.t,n)),r):r},getObj(e,t,n){let i=this.o[e];return"function"==typeof i&&null!=t&&(i=i(t,n.t,n)),i},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}};var ze=Object.freeze({__proto__:null,defaults:He,Options:xe,assign:t});function De(e,t){this.t="token",this.v=e,this.tk=t}function Ue(e,t){class n extends De{constructor(t,n){super(t,n),this.t=e}}for(const e in t)n.prototype[e]=t[e];return n.t=e,n}De.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),n=e.get("truncate",t,this),i=e.get("format",t,this);return n&&i.length>n?i.substring(0,n)+"…":i},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=He.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const n=this,i=this.toHref(e.get("defaultProtocol")),r=e.get("formatHref",i,this),s=e.get("tagName",i,n),o=this.toFormattedString(e),a={},l=e.get("className",i,n),u=e.get("target",i,n),c=e.get("rel",i,n),g=e.getObj("attributes",i,n),d=e.getObj("events",i,n);return a.href=r,l&&(a.class=l),u&&(a.target=u),c&&(a.rel=c),g&&t(a,g),{tagName:s,attributes:a,content:o,eventListeners:d}}};const Me=Ue("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Be=Ue("text"),Ke=Ue("nl"),Fe=Ue("url",{isLink:!0,toHref(e=He.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==A&&e[1].t===ee}});var _e=Object.freeze({__proto__:null,MultiToken:De,Base:De,createTokenClass:Ue,Email:Me,Text:Be,Nl:Ke,Url:Fe});const Qe=e=>new f(e);function We(e,t,n){const i=n[0].s,r=n[n.length-1].e;return new e(t.slice(i,r),n)}const $e="undefined"!=typeof console&&console&&console.warn||(()=>{}),Ge="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",qe={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Ye(){qe.scanner=function(e=[]){const d={};f.groups=d;const p=new f;null==ve&&(ve=Ne("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2ntley5rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6logistics9properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3ncaster6d0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2psy3ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==Se&&(Se=Ne("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),y(p,"'",q),y(p,"{",I),y(p,"}",N),y(p,"[",H),y(p,"]",x),y(p,"(",P),y(p,")",z),y(p,"<",D),y(p,">",U),y(p,"（",M),y(p,"）",B),y(p,"「",K),y(p,"」",F),y(p,"『",_),y(p,"』",Q),y(p,"＜",W),y(p,"＞",$),y(p,"&",G),y(p,"*",Y),y(p,"@",J),y(p,"`",V),y(p,"^",Z),y(p,":",ee),y(p,",",te),y(p,"$",ne),y(p,".",ie),y(p,"=",re),y(p,"!",se),y(p,"-",oe),y(p,"%",ae),y(p,"|",le),y(p,"+",ue),y(p,"#",ce),y(p,"?",ge),y(p,'"',de),y(p,"/",pe),y(p,";",fe),y(p,"~",me),y(p,"_",Ee),y(p,"\\",X),y(p,"・",he);const Ce=m(p,Re,S,{[n]:!0});m(Ce,Re,Ce);const we=m(Ce,Te,T,{[s]:!0}),je=m(Ce,Le,L,{[o]:!0}),He=m(p,Te,k,{[i]:!0});m(He,Re,we),m(He,Te,He),m(we,Re,we),m(we,Te,we);const xe=m(p,Le,b,{[r]:!0});m(xe,Te),m(xe,Re,je),m(xe,Le,xe),m(je,Re,je),m(je,Te),m(je,Le,je);const Pe=y(p,"\n",j,{[g]:!0}),ze=y(p,"\r",w,{[g]:!0}),De=m(p,Oe,w,{[g]:!0});y(p,"￼",De),y(ze,"\n",Pe),y(ze,"￼",De),m(ze,Oe,De),y(De,"\r"),y(De,"\n"),m(De,Oe,De),y(De,"￼",De);const Ue=m(p,Ae,ye,{[l]:!0});y(Ue,"#"),m(Ue,Ae,Ue),y(Ue,"️",Ue);const Me=y(Ue,"‍");y(Me,"#"),m(Me,Ae,Ue);const Be=[[Te,He],[Re,we]],Ke=[[Te,null],[Le,xe],[Re,je]];for(let e=0;e<ve.length;e++)Ie(p,ve[e],R,k,Be);for(let e=0;e<Se.length;e++)Ie(p,Se[e],O,b,Ke);h(R,{tld:!0,ascii:!0},d),h(O,{utld:!0,alpha:!0},d),Ie(p,"file",C,k,Be),Ie(p,"mailto",C,k,Be),Ie(p,"http",v,k,Be),Ie(p,"https",v,k,Be),Ie(p,"ftp",v,k,Be),Ie(p,"ftps",v,k,Be),h(C,{scheme:!0,ascii:!0},d),h(v,{slashscheme:!0,ascii:!0},d),e=e.sort(((e,t)=>e[0]>t[0]?1:-1));for(let t=0;t<e.length;t++){const r=e[t][0],o=e[t][1]?{[u]:!0}:{[c]:!0};r.indexOf("-")>=0?o[a]=!0:Te.test(r)?Re.test(r)?o[s]=!0:o[i]=!0:o[n]=!0,E(p,r,r,o)}return E(p,"localhost",A,{ascii:!0}),p.jd=new f(ke),{start:p,tokens:t({groups:d},be)}}(qe.customSchemes);for(let e=0;e<qe.tokenQueue.length;e++)qe.tokenQueue[e][1]({scanner:qe.scanner});qe.parser=function({groups:e}){const t=e.domain.concat([G,Y,J,X,V,Z,ne,re,oe,S,ae,le,ue,ce,pe,ke,me,Ee]),n=[ee,te,ie,se,ae,ge,de,fe,D,U,I,N,x,H,P,z,M,B,K,F,_,Q,W,$],i=[G,q,Y,X,V,Z,ne,re,oe,I,N,ae,le,ue,ce,ge,pe,ke,me,Ee],r=Qe(),s=y(r,me);p(s,i,s),p(s,e.domain,s);const o=Qe(),a=Qe(),l=Qe();p(r,e.domain,o),p(r,e.scheme,a),p(r,e.slashscheme,l),p(o,i,s),p(o,e.domain,o);const u=y(o,J);y(s,J,u),y(a,J,u),y(l,J,u);const c=y(s,ie);p(c,i,s),p(c,e.domain,s);const g=Qe();p(u,e.domain,g),p(g,e.domain,g);const d=y(g,ie);p(d,e.domain,g);const h=Qe(Me);p(d,e.tld,h),p(d,e.utld,h),y(u,A,h);const f=y(g,oe);y(f,oe,f),p(f,e.domain,g),p(h,e.domain,g),y(h,ie,d),y(h,oe,f);const m=y(h,ee);p(m,e.numeric,Me);const E=y(o,oe),k=y(o,ie);y(E,oe,E),p(E,e.domain,o),p(k,i,s),p(k,e.domain,o);const b=Qe(Fe);p(k,e.tld,b),p(k,e.utld,b),p(b,e.domain,o),p(b,i,s),y(b,ie,k),y(b,oe,E),y(b,J,u);const T=y(b,ee),L=Qe(Fe);p(T,e.numeric,L);const R=Qe(Fe),O=Qe();p(R,t,R),p(R,n,O),p(O,t,R),p(O,n,O),y(b,pe,R),y(L,pe,R);const C=y(a,ee),v=y(l,ee),w=y(v,pe),he=y(w,pe);p(a,e.domain,o),y(a,ie,k),y(a,oe,E),p(l,e.domain,o),y(l,ie,k),y(l,oe,E),p(C,e.domain,R),y(C,pe,R),y(C,ge,R),p(he,e.domain,R),p(he,t,R),y(he,pe,R);const ye=[[I,N],[H,x],[P,z],[D,U],[M,B],[K,F],[_,Q],[W,$]];for(let e=0;e<ye.length;e++){const[i,r]=ye[e],s=y(R,i);y(O,i,s),y(s,r,R);const o=Qe(Fe);p(s,t,o);const a=Qe();p(s,n),p(o,t,o),p(o,n,a),p(a,t,o),p(a,n,a),y(o,r,R),y(a,r,R)}return y(r,A,b),y(r,j,Ke),{start:r,tokens:be}}(qe.scanner.tokens);for(let e=0;e<qe.pluginQueue.length;e++)qe.pluginQueue[e][1]({scanner:qe.scanner,parser:qe.parser});return qe.initialized=!0,qe}function Je(e){return qe.initialized||Ye(),function(e,t,n){let i=n.length,r=0,s=[],o=[];for(;r<i;){let a=e,l=null,u=null,c=0,g=null,d=-1;for(;r<i&&!(l=a.go(n[r].t));)o.push(n[r++]);for(;r<i&&(u=l||a.go(n[r].t));)l=null,a=u,a.accepts()?(d=0,g=a):d>=0&&d++,r++,c++;if(d<0)r-=c,r<i&&(o.push(n[r]),r++);else{o.length>0&&(s.push(We(Be,t,o)),o=[]),r-=d,c-=d;const e=g.t,i=n.slice(r-c,r);s.push(We(e,t,i))}}return o.length>0&&s.push(We(Be,t,o)),s}(qe.parser.start,e,we(qe.scanner.start,e))}return Je.scan=we,e.MultiToken=De,e.Options=xe,e.State=f,e.createTokenClass=Ue,e.find=function(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}const i=new xe(n),r=Je(e),s=[];for(let e=0;e<r.length;e++){const n=r[e];!n.isLink||t&&n.t!==t||!i.check(n)||s.push(n.toFormattedObject(i))}return s},e.init=Ye,e.multi=_e,e.options=ze,e.regexp=Ce,e.registerCustomProtocol=function(e,t=!1){if(qe.initialized&&$e(`linkifyjs: already initialized - will not register custom scheme "${e}" ${Ge}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error('linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or "-"\n2. Cannot start or end with "-"\n3. "-" cannot repeat');qe.customSchemes.push([e,t])},e.registerPlugin=function(e,t){if("function"!=typeof t)throw new Error(`linkifyjs: Invalid plugin ${t} (expects function)`);for(let n=0;n<qe.pluginQueue.length;n++)if(e===qe.pluginQueue[n][0])return $e(`linkifyjs: plugin "${e}" already registered - will be overwritten`),void(qe.pluginQueue[n]=[e,t]);qe.pluginQueue.push([e,t]),qe.initialized&&$e(`linkifyjs: already initialized - will not register plugin "${e}" ${Ge}`)},e.registerTokenPlugin=function(e,t){if("function"!=typeof t)throw new Error(`linkifyjs: Invalid token plugin ${t} (expects function)`);for(let n=0;n<qe.tokenQueue.length;n++)if(e===qe.tokenQueue[n][0])return $e(`linkifyjs: token plugin "${e}" already registered - will be overwritten`),void(qe.tokenQueue[n]=[e,t]);qe.tokenQueue.push([e,t]),qe.initialized&&$e(`linkifyjs: already initialized - will not register token plugin "${e}" ${Ge}`)},e.reset=function(){return f.groups={},qe.scanner=null,qe.parser=null,qe.tokenQueue=[],qe.pluginQueue=[],qe.customSchemes=[],qe.initialized=!1,qe},e.stringToArray=je,e.test=function(e,t=null){const n=Je(e);return 1===n.length&&n[0].isLink&&(!t||n[0].t===t)},e.text=_e,e.tokenize=Je,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
