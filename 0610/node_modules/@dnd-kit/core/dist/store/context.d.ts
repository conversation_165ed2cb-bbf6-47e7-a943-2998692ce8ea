/// <reference types="react" />
import type { InternalContextDescriptor, PublicContextDescriptor } from './types';
export declare const defaultPublicContext: PublicContextDescriptor;
export declare const defaultInternalContext: InternalContextDescriptor;
export declare const InternalContext: import("react").Context<InternalContextDescriptor>;
export declare const PublicContext: import("react").Context<PublicContextDescriptor>;
