{"name": "oauth4webapi", "version": "2.17.0", "description": "Low-Level OAuth 2 / OpenID Connect Client API for JavaScript Runtimes", "keywords": ["access token", "auth", "authentication", "authorization", "basic", "browser", "bun", "certified", "client", "cloudflare", "deno", "edge", "electron", "fapi", "javascript", "jwt", "netlify", "next", "nextjs", "node", "nodejs", "o<PERSON>h", "oauth2", "oidc", "openid-connect", "openid", "vercel", "workerd", "workers"], "homepage": "https://github.com/panva/oauth4webapi", "repository": "panva/oauth4webapi", "funding": {"url": "https://github.com/sponsors/panva"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "main": "./build/index.js", "types": "./build/index.d.ts", "files": ["build/index.js", "build/index.d.ts"], "scripts": {"_format": "find src test tap examples conformance -type f -name '*.ts' -o -name '*.mjs' -o -name '*.cjs' | xargs prettier", "build": "rm -rf build && tsc && tsc --declaration true --emitDeclarationOnly true --removeComments false && tsc -p test && tsc -p examples && tsc -p conformance && tsc -p tap && npx --yes jsr publish --dry-run --allow-dirty", "conformance": "bash -c 'source .node_flags.sh && ava --config conformance/ava.config.ts'", "docs": "patch-package && typedoc", "format": "npm run _format -- --write", "format-check": "npm run _format -- --check", "tap:browsers": "./tap/.browsers.sh", "tap:bun": "./tap/.bun.sh", "tap:deno": "./tap/.deno.sh", "tap:edge-runtime": "./tap/.edge-runtime.sh", "tap:electron": "./tap/.electron.sh", "tap:node": "bash -c './tap/.node.sh'", "tap:workerd": "./tap/.workerd.sh", "test": "bash -c 'source .node_flags.sh && ava'"}, "devDependencies": {"@koa/cors": "^5.0.0", "@types/koa__cors": "^5.0.0", "@types/node": "^20.16.5", "@types/qunit": "^2.19.10", "archiver": "^7.0.1", "ava": "^6.1.3", "chrome-launcher": "^1.1.2", "edge-runtime": "^3.0.3", "esbuild": "^0.23.1", "jose": "^5.9.2", "oidc-provider": "^8.5.1", "patch-package": "^8.0.0", "prettier": "^3.3.3", "prettier-plugin-jsdoc": "^1.3.0", "puppeteer-core": "^23.3.0", "qunit": "^2.22.0", "raw-body": "^3.0.0", "selfsigned": "^2.4.1", "timekeeper": "^2.3.1", "tsx": "^4.19.1", "typedoc": "^0.26.7", "typedoc-plugin-markdown": "^4.2.7", "typedoc-plugin-mdn-links": "^3.2.12", "typescript": "~5.5.4", "undici": "^6.19.8"}}