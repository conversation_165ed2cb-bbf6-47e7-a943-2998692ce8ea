{"version": 3, "file": "column.js", "names": ["_", "require", "Enums", "co<PERSON><PERSON><PERSON>", "DEFAULT_COLUMN_WIDTH", "Column", "constructor", "worksheet", "number", "defn", "_worksheet", "_number", "letter", "n2l", "isCustomWidth", "width", "undefined", "header", "_header", "key", "style", "hidden", "outlineLevel", "value", "_hidden", "_key", "headers", "Array", "for<PERSON>ach", "text", "index", "getCell", "column", "getColumnKey", "deleteColumnKey", "setColumnKey", "_outlineLevel", "collapsed", "properties", "outlineLevelCol", "toString", "JSON", "stringify", "length", "equivalentTo", "other", "isEqual", "isDefault", "s", "font", "numFmt", "alignment", "border", "fill", "protection", "headerCount", "eachCell", "options", "iteratee", "colNumber", "eachRow", "row", "rowNumber", "values", "v", "cell", "type", "ValueType", "<PERSON><PERSON>", "offset", "hasOwnProperty", "_applyStyle", "name", "toModel", "columns", "cols", "col", "min", "max", "push", "fromModel", "count", "sort", "pre", "next", "module", "exports"], "sources": ["../../../lib/doc/column.js"], "sourcesContent": ["'use strict';\n\nconst _ = require('../utils/under-dash');\n\nconst Enums = require('./enums');\nconst colCache = require('../utils/col-cache');\n\nconst DEFAULT_COLUMN_WIDTH = 9;\n\n// Column defines the column properties for 1 column.\n// This includes header rows, widths, key, (style), etc.\n// Worksheet will condense the columns as appropriate during serialization\nclass Column {\n  constructor(worksheet, number, defn) {\n    this._worksheet = worksheet;\n    this._number = number;\n    if (defn !== false) {\n      // sometimes defn will follow\n      this.defn = defn;\n    }\n  }\n\n  get number() {\n    return this._number;\n  }\n\n  get worksheet() {\n    return this._worksheet;\n  }\n\n  get letter() {\n    return colCache.n2l(this._number);\n  }\n\n  get isCustomWidth() {\n    return this.width !== undefined && this.width !== DEFAULT_COLUMN_WIDTH;\n  }\n\n  get defn() {\n    return {\n      header: this._header,\n      key: this.key,\n      width: this.width,\n      style: this.style,\n      hidden: this.hidden,\n      outlineLevel: this.outlineLevel,\n    };\n  }\n\n  set defn(value) {\n    if (value) {\n      this.key = value.key;\n      this.width = value.width !== undefined ? value.width : DEFAULT_COLUMN_WIDTH;\n      this.outlineLevel = value.outlineLevel;\n      if (value.style) {\n        this.style = value.style;\n      } else {\n        this.style = {};\n      }\n\n      // headers must be set after style\n      this.header = value.header;\n      this._hidden = !!value.hidden;\n    } else {\n      delete this._header;\n      delete this._key;\n      delete this.width;\n      this.style = {};\n      this.outlineLevel = 0;\n    }\n  }\n\n  get headers() {\n    return this._header && this._header instanceof Array ? this._header : [this._header];\n  }\n\n  get header() {\n    return this._header;\n  }\n\n  set header(value) {\n    if (value !== undefined) {\n      this._header = value;\n      this.headers.forEach((text, index) => {\n        this._worksheet.getCell(index + 1, this.number).value = text;\n      });\n    } else {\n      this._header = undefined;\n    }\n  }\n\n  get key() {\n    return this._key;\n  }\n\n  set key(value) {\n    const column = this._key && this._worksheet.getColumnKey(this._key);\n    if (column === this) {\n      this._worksheet.deleteColumnKey(this._key);\n    }\n\n    this._key = value;\n    if (value) {\n      this._worksheet.setColumnKey(this._key, this);\n    }\n  }\n\n  get hidden() {\n    return !!this._hidden;\n  }\n\n  set hidden(value) {\n    this._hidden = value;\n  }\n\n  get outlineLevel() {\n    return this._outlineLevel || 0;\n  }\n\n  set outlineLevel(value) {\n    this._outlineLevel = value;\n  }\n\n  get collapsed() {\n    return !!(\n      this._outlineLevel && this._outlineLevel >= this._worksheet.properties.outlineLevelCol\n    );\n  }\n\n  toString() {\n    return JSON.stringify({\n      key: this.key,\n      width: this.width,\n      headers: this.headers.length ? this.headers : undefined,\n    });\n  }\n\n  equivalentTo(other) {\n    return (\n      this.width === other.width &&\n      this.hidden === other.hidden &&\n      this.outlineLevel === other.outlineLevel &&\n      _.isEqual(this.style, other.style)\n    );\n  }\n\n  get isDefault() {\n    if (this.isCustomWidth) {\n      return false;\n    }\n    if (this.hidden) {\n      return false;\n    }\n    if (this.outlineLevel) {\n      return false;\n    }\n    const s = this.style;\n    if (s && (s.font || s.numFmt || s.alignment || s.border || s.fill || s.protection)) {\n      return false;\n    }\n    return true;\n  }\n\n  get headerCount() {\n    return this.headers.length;\n  }\n\n  eachCell(options, iteratee) {\n    const colNumber = this.number;\n    if (!iteratee) {\n      iteratee = options;\n      options = null;\n    }\n    this._worksheet.eachRow(options, (row, rowNumber) => {\n      iteratee(row.getCell(colNumber), rowNumber);\n    });\n  }\n\n  get values() {\n    const v = [];\n    this.eachCell((cell, rowNumber) => {\n      if (cell && cell.type !== Enums.ValueType.Null) {\n        v[rowNumber] = cell.value;\n      }\n    });\n    return v;\n  }\n\n  set values(v) {\n    if (!v) {\n      return;\n    }\n    const colNumber = this.number;\n    let offset = 0;\n    if (v.hasOwnProperty('0')) {\n      // assume contiguous array, start at row 1\n      offset = 1;\n    }\n    v.forEach((value, index) => {\n      this._worksheet.getCell(index + offset, colNumber).value = value;\n    });\n  }\n\n  // =========================================================================\n  // styles\n  _applyStyle(name, value) {\n    this.style[name] = value;\n    this.eachCell(cell => {\n      cell[name] = value;\n    });\n    return value;\n  }\n\n  get numFmt() {\n    return this.style.numFmt;\n  }\n\n  set numFmt(value) {\n    this._applyStyle('numFmt', value);\n  }\n\n  get font() {\n    return this.style.font;\n  }\n\n  set font(value) {\n    this._applyStyle('font', value);\n  }\n\n  get alignment() {\n    return this.style.alignment;\n  }\n\n  set alignment(value) {\n    this._applyStyle('alignment', value);\n  }\n\n  get protection() {\n    return this.style.protection;\n  }\n\n  set protection(value) {\n    this._applyStyle('protection', value);\n  }\n\n  get border() {\n    return this.style.border;\n  }\n\n  set border(value) {\n    this._applyStyle('border', value);\n  }\n\n  get fill() {\n    return this.style.fill;\n  }\n\n  set fill(value) {\n    this._applyStyle('fill', value);\n  }\n\n  // =============================================================================\n  // static functions\n\n  static toModel(columns) {\n    // Convert array of Column into compressed list cols\n    const cols = [];\n    let col = null;\n    if (columns) {\n      columns.forEach((column, index) => {\n        if (column.isDefault) {\n          if (col) {\n            col = null;\n          }\n        } else if (!col || !column.equivalentTo(col)) {\n          col = {\n            min: index + 1,\n            max: index + 1,\n            width: column.width !== undefined ? column.width : DEFAULT_COLUMN_WIDTH,\n            style: column.style,\n            isCustomWidth: column.isCustomWidth,\n            hidden: column.hidden,\n            outlineLevel: column.outlineLevel,\n            collapsed: column.collapsed,\n          };\n          cols.push(col);\n        } else {\n          col.max = index + 1;\n        }\n      });\n    }\n    return cols.length ? cols : undefined;\n  }\n\n  static fromModel(worksheet, cols) {\n    cols = cols || [];\n    const columns = [];\n    let count = 1;\n    let index = 0;\n    /**\n     * sort cols by min\n     * If it is not sorted, the subsequent column configuration will be overwritten\n     * */\n    cols = cols.sort(function(pre, next)  {\n      return pre.min - next.min;\n    });\n    while (index < cols.length) {\n      const col = cols[index++];\n      while (count < col.min) {\n        columns.push(new Column(worksheet, count++));\n      }\n      while (count <= col.max) {\n        columns.push(new Column(worksheet, count++, col));\n      }\n    }\n    return columns.length ? columns : null;\n  }\n}\n\nmodule.exports = Column;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAExC,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,MAAME,QAAQ,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAE9C,MAAMG,oBAAoB,GAAG,CAAC;;AAE9B;AACA;AACA;AACA,MAAMC,MAAM,CAAC;EACXC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAE;IACnC,IAAI,CAACC,UAAU,GAAGH,SAAS;IAC3B,IAAI,CAACI,OAAO,GAAGH,MAAM;IACrB,IAAIC,IAAI,KAAK,KAAK,EAAE;MAClB;MACA,IAAI,CAACA,IAAI,GAAGA,IAAI;IAClB;EACF;EAEA,IAAID,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACG,OAAO;EACrB;EAEA,IAAIJ,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACG,UAAU;EACxB;EAEA,IAAIE,MAAMA,CAAA,EAAG;IACX,OAAOT,QAAQ,CAACU,GAAG,CAAC,IAAI,CAACF,OAAO,CAAC;EACnC;EAEA,IAAIG,aAAaA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,KAAK,KAAKC,SAAS,IAAI,IAAI,CAACD,KAAK,KAAKX,oBAAoB;EACxE;EAEA,IAAIK,IAAIA,CAAA,EAAG;IACT,OAAO;MACLQ,MAAM,EAAE,IAAI,CAACC,OAAO;MACpBC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBK,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBC,YAAY,EAAE,IAAI,CAACA;IACrB,CAAC;EACH;EAEA,IAAIb,IAAIA,CAACc,KAAK,EAAE;IACd,IAAIA,KAAK,EAAE;MACT,IAAI,CAACJ,GAAG,GAAGI,KAAK,CAACJ,GAAG;MACpB,IAAI,CAACJ,KAAK,GAAGQ,KAAK,CAACR,KAAK,KAAKC,SAAS,GAAGO,KAAK,CAACR,KAAK,GAAGX,oBAAoB;MAC3E,IAAI,CAACkB,YAAY,GAAGC,KAAK,CAACD,YAAY;MACtC,IAAIC,KAAK,CAACH,KAAK,EAAE;QACf,IAAI,CAACA,KAAK,GAAGG,KAAK,CAACH,KAAK;MAC1B,CAAC,MAAM;QACL,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC;MACjB;;MAEA;MACA,IAAI,CAACH,MAAM,GAAGM,KAAK,CAACN,MAAM;MAC1B,IAAI,CAACO,OAAO,GAAG,CAAC,CAACD,KAAK,CAACF,MAAM;IAC/B,CAAC,MAAM;MACL,OAAO,IAAI,CAACH,OAAO;MACnB,OAAO,IAAI,CAACO,IAAI;MAChB,OAAO,IAAI,CAACV,KAAK;MACjB,IAAI,CAACK,KAAK,GAAG,CAAC,CAAC;MACf,IAAI,CAACE,YAAY,GAAG,CAAC;IACvB;EACF;EAEA,IAAII,OAAOA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACR,OAAO,IAAI,IAAI,CAACA,OAAO,YAAYS,KAAK,GAAG,IAAI,CAACT,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO,CAAC;EACtF;EAEA,IAAID,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,OAAO;EACrB;EAEA,IAAID,MAAMA,CAACM,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAKP,SAAS,EAAE;MACvB,IAAI,CAACE,OAAO,GAAGK,KAAK;MACpB,IAAI,CAACG,OAAO,CAACE,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;QACpC,IAAI,CAACpB,UAAU,CAACqB,OAAO,CAACD,KAAK,GAAG,CAAC,EAAE,IAAI,CAACtB,MAAM,CAAC,CAACe,KAAK,GAAGM,IAAI;MAC9D,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACX,OAAO,GAAGF,SAAS;IAC1B;EACF;EAEA,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACM,IAAI;EAClB;EAEA,IAAIN,GAAGA,CAACI,KAAK,EAAE;IACb,MAAMS,MAAM,GAAG,IAAI,CAACP,IAAI,IAAI,IAAI,CAACf,UAAU,CAACuB,YAAY,CAAC,IAAI,CAACR,IAAI,CAAC;IACnE,IAAIO,MAAM,KAAK,IAAI,EAAE;MACnB,IAAI,CAACtB,UAAU,CAACwB,eAAe,CAAC,IAAI,CAACT,IAAI,CAAC;IAC5C;IAEA,IAAI,CAACA,IAAI,GAAGF,KAAK;IACjB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACb,UAAU,CAACyB,YAAY,CAAC,IAAI,CAACV,IAAI,EAAE,IAAI,CAAC;IAC/C;EACF;EAEA,IAAIJ,MAAMA,CAAA,EAAG;IACX,OAAO,CAAC,CAAC,IAAI,CAACG,OAAO;EACvB;EAEA,IAAIH,MAAMA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACC,OAAO,GAAGD,KAAK;EACtB;EAEA,IAAID,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACc,aAAa,IAAI,CAAC;EAChC;EAEA,IAAId,YAAYA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACa,aAAa,GAAGb,KAAK;EAC5B;EAEA,IAAIc,SAASA,CAAA,EAAG;IACd,OAAO,CAAC,EACN,IAAI,CAACD,aAAa,IAAI,IAAI,CAACA,aAAa,IAAI,IAAI,CAAC1B,UAAU,CAAC4B,UAAU,CAACC,eAAe,CACvF;EACH;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAOC,IAAI,CAACC,SAAS,CAAC;MACpBvB,GAAG,EAAE,IAAI,CAACA,GAAG;MACbJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBW,OAAO,EAAE,IAAI,CAACA,OAAO,CAACiB,MAAM,GAAG,IAAI,CAACjB,OAAO,GAAGV;IAChD,CAAC,CAAC;EACJ;EAEA4B,YAAYA,CAACC,KAAK,EAAE;IAClB,OACE,IAAI,CAAC9B,KAAK,KAAK8B,KAAK,CAAC9B,KAAK,IAC1B,IAAI,CAACM,MAAM,KAAKwB,KAAK,CAACxB,MAAM,IAC5B,IAAI,CAACC,YAAY,KAAKuB,KAAK,CAACvB,YAAY,IACxCtB,CAAC,CAAC8C,OAAO,CAAC,IAAI,CAAC1B,KAAK,EAAEyB,KAAK,CAACzB,KAAK,CAAC;EAEtC;EAEA,IAAI2B,SAASA,CAAA,EAAG;IACd,IAAI,IAAI,CAACjC,aAAa,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACO,MAAM,EAAE;MACf,OAAO,KAAK;IACd;IACA,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,OAAO,KAAK;IACd;IACA,MAAM0B,CAAC,GAAG,IAAI,CAAC5B,KAAK;IACpB,IAAI4B,CAAC,KAAKA,CAAC,CAACC,IAAI,IAAID,CAAC,CAACE,MAAM,IAAIF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,MAAM,IAAIJ,CAAC,CAACK,IAAI,IAAIL,CAAC,CAACM,UAAU,CAAC,EAAE;MAClF,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEA,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC7B,OAAO,CAACiB,MAAM;EAC5B;EAEAa,QAAQA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,SAAS,GAAG,IAAI,CAACnD,MAAM;IAC7B,IAAI,CAACkD,QAAQ,EAAE;MACbA,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAG,IAAI;IAChB;IACA,IAAI,CAAC/C,UAAU,CAACkD,OAAO,CAACH,OAAO,EAAE,CAACI,GAAG,EAAEC,SAAS,KAAK;MACnDJ,QAAQ,CAACG,GAAG,CAAC9B,OAAO,CAAC4B,SAAS,CAAC,EAAEG,SAAS,CAAC;IAC7C,CAAC,CAAC;EACJ;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX,MAAMC,CAAC,GAAG,EAAE;IACZ,IAAI,CAACR,QAAQ,CAAC,CAACS,IAAI,EAAEH,SAAS,KAAK;MACjC,IAAIG,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKhE,KAAK,CAACiE,SAAS,CAACC,IAAI,EAAE;QAC9CJ,CAAC,CAACF,SAAS,CAAC,GAAGG,IAAI,CAAC1C,KAAK;MAC3B;IACF,CAAC,CAAC;IACF,OAAOyC,CAAC;EACV;EAEA,IAAID,MAAMA,CAACC,CAAC,EAAE;IACZ,IAAI,CAACA,CAAC,EAAE;MACN;IACF;IACA,MAAML,SAAS,GAAG,IAAI,CAACnD,MAAM;IAC7B,IAAI6D,MAAM,GAAG,CAAC;IACd,IAAIL,CAAC,CAACM,cAAc,CAAC,GAAG,CAAC,EAAE;MACzB;MACAD,MAAM,GAAG,CAAC;IACZ;IACAL,CAAC,CAACpC,OAAO,CAAC,CAACL,KAAK,EAAEO,KAAK,KAAK;MAC1B,IAAI,CAACpB,UAAU,CAACqB,OAAO,CAACD,KAAK,GAAGuC,MAAM,EAAEV,SAAS,CAAC,CAACpC,KAAK,GAAGA,KAAK;IAClE,CAAC,CAAC;EACJ;;EAEA;EACA;EACAgD,WAAWA,CAACC,IAAI,EAAEjD,KAAK,EAAE;IACvB,IAAI,CAACH,KAAK,CAACoD,IAAI,CAAC,GAAGjD,KAAK;IACxB,IAAI,CAACiC,QAAQ,CAACS,IAAI,IAAI;MACpBA,IAAI,CAACO,IAAI,CAAC,GAAGjD,KAAK;IACpB,CAAC,CAAC;IACF,OAAOA,KAAK;EACd;EAEA,IAAI2B,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC9B,KAAK,CAAC8B,MAAM;EAC1B;EAEA,IAAIA,MAAMA,CAAC3B,KAAK,EAAE;IAChB,IAAI,CAACgD,WAAW,CAAC,QAAQ,EAAEhD,KAAK,CAAC;EACnC;EAEA,IAAI0B,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7B,KAAK,CAAC6B,IAAI;EACxB;EAEA,IAAIA,IAAIA,CAAC1B,KAAK,EAAE;IACd,IAAI,CAACgD,WAAW,CAAC,MAAM,EAAEhD,KAAK,CAAC;EACjC;EAEA,IAAI4B,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC/B,KAAK,CAAC+B,SAAS;EAC7B;EAEA,IAAIA,SAASA,CAAC5B,KAAK,EAAE;IACnB,IAAI,CAACgD,WAAW,CAAC,WAAW,EAAEhD,KAAK,CAAC;EACtC;EAEA,IAAI+B,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI,CAAClC,KAAK,CAACkC,UAAU;EAC9B;EAEA,IAAIA,UAAUA,CAAC/B,KAAK,EAAE;IACpB,IAAI,CAACgD,WAAW,CAAC,YAAY,EAAEhD,KAAK,CAAC;EACvC;EAEA,IAAI6B,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAAChC,KAAK,CAACgC,MAAM;EAC1B;EAEA,IAAIA,MAAMA,CAAC7B,KAAK,EAAE;IAChB,IAAI,CAACgD,WAAW,CAAC,QAAQ,EAAEhD,KAAK,CAAC;EACnC;EAEA,IAAI8B,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACjC,KAAK,CAACiC,IAAI;EACxB;EAEA,IAAIA,IAAIA,CAAC9B,KAAK,EAAE;IACd,IAAI,CAACgD,WAAW,CAAC,MAAM,EAAEhD,KAAK,CAAC;EACjC;;EAEA;EACA;;EAEA,OAAOkD,OAAOA,CAACC,OAAO,EAAE;IACtB;IACA,MAAMC,IAAI,GAAG,EAAE;IACf,IAAIC,GAAG,GAAG,IAAI;IACd,IAAIF,OAAO,EAAE;MACXA,OAAO,CAAC9C,OAAO,CAAC,CAACI,MAAM,EAAEF,KAAK,KAAK;QACjC,IAAIE,MAAM,CAACe,SAAS,EAAE;UACpB,IAAI6B,GAAG,EAAE;YACPA,GAAG,GAAG,IAAI;UACZ;QACF,CAAC,MAAM,IAAI,CAACA,GAAG,IAAI,CAAC5C,MAAM,CAACY,YAAY,CAACgC,GAAG,CAAC,EAAE;UAC5CA,GAAG,GAAG;YACJC,GAAG,EAAE/C,KAAK,GAAG,CAAC;YACdgD,GAAG,EAAEhD,KAAK,GAAG,CAAC;YACdf,KAAK,EAAEiB,MAAM,CAACjB,KAAK,KAAKC,SAAS,GAAGgB,MAAM,CAACjB,KAAK,GAAGX,oBAAoB;YACvEgB,KAAK,EAAEY,MAAM,CAACZ,KAAK;YACnBN,aAAa,EAAEkB,MAAM,CAAClB,aAAa;YACnCO,MAAM,EAAEW,MAAM,CAACX,MAAM;YACrBC,YAAY,EAAEU,MAAM,CAACV,YAAY;YACjCe,SAAS,EAAEL,MAAM,CAACK;UACpB,CAAC;UACDsC,IAAI,CAACI,IAAI,CAACH,GAAG,CAAC;QAChB,CAAC,MAAM;UACLA,GAAG,CAACE,GAAG,GAAGhD,KAAK,GAAG,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;IACA,OAAO6C,IAAI,CAAChC,MAAM,GAAGgC,IAAI,GAAG3D,SAAS;EACvC;EAEA,OAAOgE,SAASA,CAACzE,SAAS,EAAEoE,IAAI,EAAE;IAChCA,IAAI,GAAGA,IAAI,IAAI,EAAE;IACjB,MAAMD,OAAO,GAAG,EAAE;IAClB,IAAIO,KAAK,GAAG,CAAC;IACb,IAAInD,KAAK,GAAG,CAAC;IACb;AACJ;AACA;AACA;IACI6C,IAAI,GAAGA,IAAI,CAACO,IAAI,CAAC,UAASC,GAAG,EAAEC,IAAI,EAAG;MACpC,OAAOD,GAAG,CAACN,GAAG,GAAGO,IAAI,CAACP,GAAG;IAC3B,CAAC,CAAC;IACF,OAAO/C,KAAK,GAAG6C,IAAI,CAAChC,MAAM,EAAE;MAC1B,MAAMiC,GAAG,GAAGD,IAAI,CAAC7C,KAAK,EAAE,CAAC;MACzB,OAAOmD,KAAK,GAAGL,GAAG,CAACC,GAAG,EAAE;QACtBH,OAAO,CAACK,IAAI,CAAC,IAAI1E,MAAM,CAACE,SAAS,EAAE0E,KAAK,EAAE,CAAC,CAAC;MAC9C;MACA,OAAOA,KAAK,IAAIL,GAAG,CAACE,GAAG,EAAE;QACvBJ,OAAO,CAACK,IAAI,CAAC,IAAI1E,MAAM,CAACE,SAAS,EAAE0E,KAAK,EAAE,EAAEL,GAAG,CAAC,CAAC;MACnD;IACF;IACA,OAAOF,OAAO,CAAC/B,MAAM,GAAG+B,OAAO,GAAG,IAAI;EACxC;AACF;AAEAW,MAAM,CAACC,OAAO,GAAGjF,MAAM"}