{"version": 3, "file": "note.js", "names": ["_", "require", "Note", "constructor", "note", "model", "value", "type", "texts", "text", "deepMerge", "DEFAULT_CONFIGS", "length", "Object", "keys", "fromModel", "margins", "insetmode", "inset", "protection", "locked", "lockText", "editAs", "module", "exports"], "sources": ["../../../lib/doc/note.js"], "sourcesContent": ["const _ = require('../utils/under-dash');\n\nclass Note {\n  constructor(note) {\n    this.note = note;\n  }\n\n  get model() {\n    let value = null;\n    switch (typeof this.note) {\n      case 'string':\n        value = {\n          type: 'note',\n          note: {\n            texts: [\n              {\n                text: this.note,\n              },\n            ],\n          },\n        };\n        break;\n      default:\n        value = {\n          type: 'note',\n          note: this.note,\n        };\n        break;\n    }\n    // Suitable for all cell comments\n    return _.deepMerge({}, Note.DEFAULT_CONFIGS, value);\n  }\n\n  set model(value) {\n    const {note} = value;\n    const {texts} = note;\n    if (texts.length === 1 && Object.keys(texts[0]).length === 1) {\n      this.note = texts[0].text;\n    } else {\n      this.note = note;\n    }\n  }\n\n  static fromModel(model) {\n    const note = new Note();\n    note.model = model;\n    return note;\n  }\n}\n\nNote.DEFAULT_CONFIGS = {\n  note: {\n    margins: {\n      insetmode: 'auto',\n      inset: [0.13, 0.13, 0.25, 0.25],\n    },\n    protection: {\n      locked: 'True',\n      lockText: 'True',\n    },\n    editAs: 'absolute',\n  },\n};\n\nmodule.exports = Note;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAExC,MAAMC,IAAI,CAAC;EACTC,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACA,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAIC,KAAKA,CAAA,EAAG;IACV,IAAIC,KAAK,GAAG,IAAI;IAChB,QAAQ,OAAO,IAAI,CAACF,IAAI;MACtB,KAAK,QAAQ;QACXE,KAAK,GAAG;UACNC,IAAI,EAAE,MAAM;UACZH,IAAI,EAAE;YACJI,KAAK,EAAE,CACL;cACEC,IAAI,EAAE,IAAI,CAACL;YACb,CAAC;UAEL;QACF,CAAC;QACD;MACF;QACEE,KAAK,GAAG;UACNC,IAAI,EAAE,MAAM;UACZH,IAAI,EAAE,IAAI,CAACA;QACb,CAAC;QACD;IACJ;IACA;IACA,OAAOJ,CAAC,CAACU,SAAS,CAAC,CAAC,CAAC,EAAER,IAAI,CAACS,eAAe,EAAEL,KAAK,CAAC;EACrD;EAEA,IAAID,KAAKA,CAACC,KAAK,EAAE;IACf,MAAM;MAACF;IAAI,CAAC,GAAGE,KAAK;IACpB,MAAM;MAACE;IAAK,CAAC,GAAGJ,IAAI;IACpB,IAAII,KAAK,CAACI,MAAM,KAAK,CAAC,IAAIC,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;MAC5D,IAAI,CAACR,IAAI,GAAGI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI;IAC3B,CAAC,MAAM;MACL,IAAI,CAACL,IAAI,GAAGA,IAAI;IAClB;EACF;EAEA,OAAOW,SAASA,CAACV,KAAK,EAAE;IACtB,MAAMD,IAAI,GAAG,IAAIF,IAAI,CAAC,CAAC;IACvBE,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,OAAOD,IAAI;EACb;AACF;AAEAF,IAAI,CAACS,eAAe,GAAG;EACrBP,IAAI,EAAE;IACJY,OAAO,EAAE;MACPC,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;IAChC,CAAC;IACDC,UAAU,EAAE;MACVC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;EACV;AACF,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGtB,IAAI"}