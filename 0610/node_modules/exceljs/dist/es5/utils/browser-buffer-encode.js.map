{"version": 3, "file": "browser-buffer-encode.js", "names": ["textEncoder", "TextEncoder", "<PERSON><PERSON><PERSON>", "require", "string<PERSON>o<PERSON>uffer", "str", "from", "encode", "buffer", "exports"], "sources": ["../../../lib/utils/browser-buffer-encode.js"], "sourcesContent": ["// eslint-disable-next-line node/no-unsupported-features/node-builtins\nconst textEncoder = typeof TextEncoder === 'undefined' ? null : new TextEncoder('utf-8');\nconst {Buffer} = require('buffer');\n\nfunction stringToBuffer(str) {\n  if (typeof str !== 'string') {\n    return str;\n  }\n  if (textEncoder) {\n    return Buffer.from(textEncoder.encode(str).buffer);\n  }\n  return Buffer.from(str);\n}\n\nexports.stringToBuffer = stringToBuffer;\n"], "mappings": ";;AAAA;AACA,MAAMA,WAAW,GAAG,OAAOC,WAAW,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,WAAW,CAAC,OAAO,CAAC;AACxF,MAAM;EAACC;AAAM,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAElC,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAOA,GAAG;EACZ;EACA,IAAIL,WAAW,EAAE;IACf,OAAOE,MAAM,CAACI,IAAI,CAACN,WAAW,CAACO,MAAM,CAACF,GAAG,CAAC,CAACG,MAAM,CAAC;EACpD;EACA,OAAON,MAAM,CAACI,IAAI,CAACD,GAAG,CAAC;AACzB;AAEAI,OAAO,CAACL,cAAc,GAAGA,cAAc"}