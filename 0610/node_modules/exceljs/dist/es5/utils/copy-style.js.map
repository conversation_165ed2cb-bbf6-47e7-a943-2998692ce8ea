{"version": 3, "file": "copy-style.js", "names": ["oneDepthCopy", "obj", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "memo", "key", "setIfExists", "src", "dst", "arguments", "length", "undefined", "isEmptyObj", "Object", "keys", "copyStyle", "style", "copied", "border", "fill", "stops", "map", "s", "exports"], "sources": ["../../../lib/utils/copy-style.js"], "sourcesContent": ["const oneDepthCopy = (obj, nestKeys) => ({\n  ...obj,\n  ...nestKeys.reduce((memo, key) => {\n    if (obj[key]) memo[key] = {...obj[key]};\n    return memo;\n  }, {}),\n});\n\nconst setIfExists = (src, dst, key, nestKeys = []) => {\n  if (src[key]) dst[key] = oneDepthCopy(src[key], nestKeys);\n};\n\nconst isEmptyObj = obj => Object.keys(obj).length === 0;\n\nconst copyStyle = style => {\n  if (!style) return style;\n  if (isEmptyObj(style)) return {};\n\n  const copied = {...style};\n\n  setIfExists(style, copied, 'font', ['color']);\n  setIfExists(style, copied, 'alignment');\n  setIfExists(style, copied, 'protection');\n  if (style.border) {\n    setIfExists(style, copied, 'border');\n    setIfExists(style.border, copied.border, 'top', ['color']);\n    setIfExists(style.border, copied.border, 'left', ['color']);\n    setIfExists(style.border, copied.border, 'bottom', ['color']);\n    setIfExists(style.border, copied.border, 'right', ['color']);\n    setIfExists(style.border, copied.border, 'diagonal', ['color']);\n  }\n\n  if (style.fill) {\n    setIfExists(style, copied, 'fill', ['fgColor', 'bgColor', 'center']);\n    if (style.fill.stops) {\n      copied.fill.stops = style.fill.stops.map(s => oneDepthCopy(s, ['color']));\n    }\n  }\n\n  return copied;\n};\n\nexports.copyStyle = copyStyle;\n"], "mappings": ";;AAAA,MAAMA,YAAY,GAAGA,CAACC,GAAG,EAAEC,QAAQ,MAAM;EACvC,GAAGD,GAAG;EACN,GAAGC,QAAQ,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;IAChC,IAAIJ,GAAG,CAACI,GAAG,CAAC,EAAED,IAAI,CAACC,GAAG,CAAC,GAAG;MAAC,GAAGJ,GAAG,CAACI,GAAG;IAAC,CAAC;IACvC,OAAOD,IAAI;EACb,CAAC,EAAE,CAAC,CAAC;AACP,CAAC,CAAC;AAEF,MAAME,WAAW,GAAG,SAAAA,CAACC,GAAG,EAAEC,GAAG,EAAEH,GAAG,EAAoB;EAAA,IAAlBH,QAAQ,GAAAO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EAC/C,IAAIF,GAAG,CAACF,GAAG,CAAC,EAAEG,GAAG,CAACH,GAAG,CAAC,GAAGL,YAAY,CAACO,GAAG,CAACF,GAAG,CAAC,EAAEH,QAAQ,CAAC;AAC3D,CAAC;AAED,MAAMU,UAAU,GAAGX,GAAG,IAAIY,MAAM,CAACC,IAAI,CAACb,GAAG,CAAC,CAACS,MAAM,KAAK,CAAC;AAEvD,MAAMK,SAAS,GAAGC,KAAK,IAAI;EACzB,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;EACxB,IAAIJ,UAAU,CAACI,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;EAEhC,MAAMC,MAAM,GAAG;IAAC,GAAGD;EAAK,CAAC;EAEzBV,WAAW,CAACU,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;EAC7CX,WAAW,CAACU,KAAK,EAAEC,MAAM,EAAE,WAAW,CAAC;EACvCX,WAAW,CAACU,KAAK,EAAEC,MAAM,EAAE,YAAY,CAAC;EACxC,IAAID,KAAK,CAACE,MAAM,EAAE;IAChBZ,WAAW,CAACU,KAAK,EAAEC,MAAM,EAAE,QAAQ,CAAC;IACpCX,WAAW,CAACU,KAAK,CAACE,MAAM,EAAED,MAAM,CAACC,MAAM,EAAE,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC;IAC1DZ,WAAW,CAACU,KAAK,CAACE,MAAM,EAAED,MAAM,CAACC,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC;IAC3DZ,WAAW,CAACU,KAAK,CAACE,MAAM,EAAED,MAAM,CAACC,MAAM,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAC;IAC7DZ,WAAW,CAACU,KAAK,CAACE,MAAM,EAAED,MAAM,CAACC,MAAM,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;IAC5DZ,WAAW,CAACU,KAAK,CAACE,MAAM,EAAED,MAAM,CAACC,MAAM,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;EACjE;EAEA,IAAIF,KAAK,CAACG,IAAI,EAAE;IACdb,WAAW,CAACU,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACpE,IAAID,KAAK,CAACG,IAAI,CAACC,KAAK,EAAE;MACpBH,MAAM,CAACE,IAAI,CAACC,KAAK,GAAGJ,KAAK,CAACG,IAAI,CAACC,KAAK,CAACC,GAAG,CAACC,CAAC,IAAItB,YAAY,CAACsB,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3E;EACF;EAEA,OAAOL,MAAM;AACf,CAAC;AAEDM,OAAO,CAACR,SAAS,GAAGA,SAAS"}