{"version": 3, "file": "auto-drain.js", "names": ["EventEmitter", "require", "AutoDrain", "write", "chunk", "emit", "end", "module", "exports"], "sources": ["../../../lib/utils/auto-drain.js"], "sourcesContent": ["const {EventEmitter} = require('events');\n\n// =============================================================================\n// AutoDrain - kind of /dev/null\nclass AutoDrain extends EventEmitter {\n  write(chunk) {\n    this.emit('data', chunk);\n  }\n\n  end() {\n    this.emit('end');\n  }\n}\n\nmodule.exports = AutoDrain;\n"], "mappings": ";;AAAA,MAAM;EAACA;AAAY,CAAC,GAAGC,OAAO,CAAC,QAAQ,CAAC;;AAExC;AACA;AACA,MAAMC,SAAS,SAASF,YAAY,CAAC;EACnCG,KAAKA,CAACC,KAAK,EAAE;IACX,IAAI,CAACC,IAAI,CAAC,MAAM,EAAED,KAAK,CAAC;EAC1B;EAEAE,GAAGA,CAAA,EAAG;IACJ,IAAI,CAACD,IAAI,CAAC,KAAK,CAAC;EAClB;AACF;AAEAE,MAAM,CAACC,OAAO,GAAGN,SAAS"}