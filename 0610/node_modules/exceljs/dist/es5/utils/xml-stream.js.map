{"version": 3, "file": "xml-stream.js", "names": ["_", "require", "utils", "OPEN_ANGLE", "CLOSE_ANGLE", "OPEN_ANGLE_SLASH", "CLOSE_SLASH_ANGLE", "pushAttribute", "xml", "name", "value", "push", "xmlEncode", "toString", "pushAttributes", "attributes", "tmp", "each", "undefined", "join", "XmlStream", "constructor", "_xml", "_stack", "_rollbacks", "tos", "length", "cursor", "openXml", "docAttributes", "openNode", "parent", "open", "leaf", "addAttribute", "Error", "addAttributes", "attrs", "writeText", "text", "writeXml", "closeNode", "node", "pop", "leafNode", "closeAll", "add<PERSON><PERSON><PERSON>", "stack", "commit", "rollback", "r", "splice", "StdDocAttributes", "version", "encoding", "standalone", "module", "exports"], "sources": ["../../../lib/utils/xml-stream.js"], "sourcesContent": ["const _ = require('./under-dash');\n\nconst utils = require('./utils');\n\n// constants\nconst OPEN_ANGLE = '<';\nconst CLOSE_ANGLE = '>';\nconst OPEN_ANGLE_SLASH = '</';\nconst CLOSE_SLASH_ANGLE = '/>';\n\nfunction pushAttribute(xml, name, value) {\n  xml.push(` ${name}=\"${utils.xmlEncode(value.toString())}\"`);\n}\nfunction pushAttributes(xml, attributes) {\n  if (attributes) {\n    const tmp = [];\n    _.each(attributes, (value, name) => {\n      if (value !== undefined) {\n        pushAttribute(tmp, name, value);\n      }\n    });\n    xml.push(tmp.join(\"\"));\n  }\n}\n\nclass XmlStream {\n  constructor() {\n    this._xml = [];\n    this._stack = [];\n    this._rollbacks = [];\n  }\n\n  get tos() {\n    return this._stack.length ? this._stack[this._stack.length - 1] : undefined;\n  }\n\n  get cursor() {\n    // handy way to track whether anything has been added\n    return this._xml.length;\n  }\n\n  openXml(docAttributes) {\n    const xml = this._xml;\n    // <?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n    xml.push('<?xml');\n    pushAttributes(xml, docAttributes);\n    xml.push('?>\\n');\n  }\n\n  openNode(name, attributes) {\n    const parent = this.tos;\n    const xml = this._xml;\n    if (parent && this.open) {\n      xml.push(CLOSE_ANGLE);\n    }\n\n    this._stack.push(name);\n\n    // start streaming node\n    xml.push(OPEN_ANGLE);\n    xml.push(name);\n    pushAttributes(xml, attributes);\n    this.leaf = true;\n    this.open = true;\n  }\n\n  addAttribute(name, value) {\n    if (!this.open) {\n      throw new Error('Cannot write attributes to node if it is not open');\n    }\n    if (value !== undefined) {\n      pushAttribute(this._xml, name, value);\n    }\n  }\n\n  addAttributes(attrs) {\n    if (!this.open) {\n      throw new Error('Cannot write attributes to node if it is not open');\n    }\n    pushAttributes(this._xml, attrs);\n  }\n\n  writeText(text) {\n    const xml = this._xml;\n    if (this.open) {\n      xml.push(CLOSE_ANGLE);\n      this.open = false;\n    }\n    this.leaf = false;\n    xml.push(utils.xmlEncode(text.toString()));\n  }\n\n  writeXml(xml) {\n    if (this.open) {\n      this._xml.push(CLOSE_ANGLE);\n      this.open = false;\n    }\n    this.leaf = false;\n    this._xml.push(xml);\n  }\n\n  closeNode() {\n    const node = this._stack.pop();\n    const xml = this._xml;\n    if (this.leaf) {\n      xml.push(CLOSE_SLASH_ANGLE);\n    } else {\n      xml.push(OPEN_ANGLE_SLASH);\n      xml.push(node);\n      xml.push(CLOSE_ANGLE);\n    }\n    this.open = false;\n    this.leaf = false;\n  }\n\n  leafNode(name, attributes, text) {\n    this.openNode(name, attributes);\n    if (text !== undefined) {\n      // zeros need to be written\n      this.writeText(text);\n    }\n    this.closeNode();\n  }\n\n  closeAll() {\n    while (this._stack.length) {\n      this.closeNode();\n    }\n  }\n\n  addRollback() {\n    this._rollbacks.push({\n      xml: this._xml.length,\n      stack: this._stack.length,\n      leaf: this.leaf,\n      open: this.open,\n    });\n    return this.cursor;\n  }\n\n  commit() {\n    this._rollbacks.pop();\n  }\n\n  rollback() {\n    const r = this._rollbacks.pop();\n    if (this._xml.length > r.xml) {\n      this._xml.splice(r.xml, this._xml.length - r.xml);\n    }\n    if (this._stack.length > r.stack) {\n      this._stack.splice(r.stack, this._stack.length - r.stack);\n    }\n    this.leaf = r.leaf;\n    this.open = r.open;\n  }\n\n  get xml() {\n    this.closeAll();\n    return this._xml.join('');\n  }\n}\n\nXmlStream.StdDocAttributes = {\n  version: '1.0',\n  encoding: 'UTF-8',\n  standalone: 'yes',\n};\n\nmodule.exports = XmlStream;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEjC,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAEhC;AACA,MAAME,UAAU,GAAG,GAAG;AACtB,MAAMC,WAAW,GAAG,GAAG;AACvB,MAAMC,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,iBAAiB,GAAG,IAAI;AAE9B,SAASC,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACvCF,GAAG,CAACG,IAAI,CAAE,IAAGF,IAAK,KAAIP,KAAK,CAACU,SAAS,CAACF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAE,GAAE,CAAC;AAC7D;AACA,SAASC,cAAcA,CAACN,GAAG,EAAEO,UAAU,EAAE;EACvC,IAAIA,UAAU,EAAE;IACd,MAAMC,GAAG,GAAG,EAAE;IACdhB,CAAC,CAACiB,IAAI,CAACF,UAAU,EAAE,CAACL,KAAK,EAAED,IAAI,KAAK;MAClC,IAAIC,KAAK,KAAKQ,SAAS,EAAE;QACvBX,aAAa,CAACS,GAAG,EAAEP,IAAI,EAAEC,KAAK,CAAC;MACjC;IACF,CAAC,CAAC;IACFF,GAAG,CAACG,IAAI,CAACK,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC,CAAC;EACxB;AACF;AAEA,MAAMC,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,UAAU,GAAG,EAAE;EACtB;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,MAAM,CAACG,MAAM,GAAG,IAAI,CAACH,MAAM,CAAC,IAAI,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC,GAAGR,SAAS;EAC7E;EAEA,IAAIS,MAAMA,CAAA,EAAG;IACX;IACA,OAAO,IAAI,CAACL,IAAI,CAACI,MAAM;EACzB;EAEAE,OAAOA,CAACC,aAAa,EAAE;IACrB,MAAMrB,GAAG,GAAG,IAAI,CAACc,IAAI;IACrB;IACAd,GAAG,CAACG,IAAI,CAAC,OAAO,CAAC;IACjBG,cAAc,CAACN,GAAG,EAAEqB,aAAa,CAAC;IAClCrB,GAAG,CAACG,IAAI,CAAC,MAAM,CAAC;EAClB;EAEAmB,QAAQA,CAACrB,IAAI,EAAEM,UAAU,EAAE;IACzB,MAAMgB,MAAM,GAAG,IAAI,CAACN,GAAG;IACvB,MAAMjB,GAAG,GAAG,IAAI,CAACc,IAAI;IACrB,IAAIS,MAAM,IAAI,IAAI,CAACC,IAAI,EAAE;MACvBxB,GAAG,CAACG,IAAI,CAACP,WAAW,CAAC;IACvB;IAEA,IAAI,CAACmB,MAAM,CAACZ,IAAI,CAACF,IAAI,CAAC;;IAEtB;IACAD,GAAG,CAACG,IAAI,CAACR,UAAU,CAAC;IACpBK,GAAG,CAACG,IAAI,CAACF,IAAI,CAAC;IACdK,cAAc,CAACN,GAAG,EAAEO,UAAU,CAAC;IAC/B,IAAI,CAACkB,IAAI,GAAG,IAAI;IAChB,IAAI,CAACD,IAAI,GAAG,IAAI;EAClB;EAEAE,YAAYA,CAACzB,IAAI,EAAEC,KAAK,EAAE;IACxB,IAAI,CAAC,IAAI,CAACsB,IAAI,EAAE;MACd,MAAM,IAAIG,KAAK,CAAC,mDAAmD,CAAC;IACtE;IACA,IAAIzB,KAAK,KAAKQ,SAAS,EAAE;MACvBX,aAAa,CAAC,IAAI,CAACe,IAAI,EAAEb,IAAI,EAAEC,KAAK,CAAC;IACvC;EACF;EAEA0B,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAI,CAAC,IAAI,CAACL,IAAI,EAAE;MACd,MAAM,IAAIG,KAAK,CAAC,mDAAmD,CAAC;IACtE;IACArB,cAAc,CAAC,IAAI,CAACQ,IAAI,EAAEe,KAAK,CAAC;EAClC;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,MAAM/B,GAAG,GAAG,IAAI,CAACc,IAAI;IACrB,IAAI,IAAI,CAACU,IAAI,EAAE;MACbxB,GAAG,CAACG,IAAI,CAACP,WAAW,CAAC;MACrB,IAAI,CAAC4B,IAAI,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,IAAI,GAAG,KAAK;IACjBzB,GAAG,CAACG,IAAI,CAACT,KAAK,CAACU,SAAS,CAAC2B,IAAI,CAAC1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C;EAEA2B,QAAQA,CAAChC,GAAG,EAAE;IACZ,IAAI,IAAI,CAACwB,IAAI,EAAE;MACb,IAAI,CAACV,IAAI,CAACX,IAAI,CAACP,WAAW,CAAC;MAC3B,IAAI,CAAC4B,IAAI,GAAG,KAAK;IACnB;IACA,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACX,IAAI,CAACX,IAAI,CAACH,GAAG,CAAC;EACrB;EAEAiC,SAASA,CAAA,EAAG;IACV,MAAMC,IAAI,GAAG,IAAI,CAACnB,MAAM,CAACoB,GAAG,CAAC,CAAC;IAC9B,MAAMnC,GAAG,GAAG,IAAI,CAACc,IAAI;IACrB,IAAI,IAAI,CAACW,IAAI,EAAE;MACbzB,GAAG,CAACG,IAAI,CAACL,iBAAiB,CAAC;IAC7B,CAAC,MAAM;MACLE,GAAG,CAACG,IAAI,CAACN,gBAAgB,CAAC;MAC1BG,GAAG,CAACG,IAAI,CAAC+B,IAAI,CAAC;MACdlC,GAAG,CAACG,IAAI,CAACP,WAAW,CAAC;IACvB;IACA,IAAI,CAAC4B,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,IAAI,GAAG,KAAK;EACnB;EAEAW,QAAQA,CAACnC,IAAI,EAAEM,UAAU,EAAEwB,IAAI,EAAE;IAC/B,IAAI,CAACT,QAAQ,CAACrB,IAAI,EAAEM,UAAU,CAAC;IAC/B,IAAIwB,IAAI,KAAKrB,SAAS,EAAE;MACtB;MACA,IAAI,CAACoB,SAAS,CAACC,IAAI,CAAC;IACtB;IACA,IAAI,CAACE,SAAS,CAAC,CAAC;EAClB;EAEAI,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAACtB,MAAM,CAACG,MAAM,EAAE;MACzB,IAAI,CAACe,SAAS,CAAC,CAAC;IAClB;EACF;EAEAK,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACtB,UAAU,CAACb,IAAI,CAAC;MACnBH,GAAG,EAAE,IAAI,CAACc,IAAI,CAACI,MAAM;MACrBqB,KAAK,EAAE,IAAI,CAACxB,MAAM,CAACG,MAAM;MACzBO,IAAI,EAAE,IAAI,CAACA,IAAI;MACfD,IAAI,EAAE,IAAI,CAACA;IACb,CAAC,CAAC;IACF,OAAO,IAAI,CAACL,MAAM;EACpB;EAEAqB,MAAMA,CAAA,EAAG;IACP,IAAI,CAACxB,UAAU,CAACmB,GAAG,CAAC,CAAC;EACvB;EAEAM,QAAQA,CAAA,EAAG;IACT,MAAMC,CAAC,GAAG,IAAI,CAAC1B,UAAU,CAACmB,GAAG,CAAC,CAAC;IAC/B,IAAI,IAAI,CAACrB,IAAI,CAACI,MAAM,GAAGwB,CAAC,CAAC1C,GAAG,EAAE;MAC5B,IAAI,CAACc,IAAI,CAAC6B,MAAM,CAACD,CAAC,CAAC1C,GAAG,EAAE,IAAI,CAACc,IAAI,CAACI,MAAM,GAAGwB,CAAC,CAAC1C,GAAG,CAAC;IACnD;IACA,IAAI,IAAI,CAACe,MAAM,CAACG,MAAM,GAAGwB,CAAC,CAACH,KAAK,EAAE;MAChC,IAAI,CAACxB,MAAM,CAAC4B,MAAM,CAACD,CAAC,CAACH,KAAK,EAAE,IAAI,CAACxB,MAAM,CAACG,MAAM,GAAGwB,CAAC,CAACH,KAAK,CAAC;IAC3D;IACA,IAAI,CAACd,IAAI,GAAGiB,CAAC,CAACjB,IAAI;IAClB,IAAI,CAACD,IAAI,GAAGkB,CAAC,CAAClB,IAAI;EACpB;EAEA,IAAIxB,GAAGA,CAAA,EAAG;IACR,IAAI,CAACqC,QAAQ,CAAC,CAAC;IACf,OAAO,IAAI,CAACvB,IAAI,CAACH,IAAI,CAAC,EAAE,CAAC;EAC3B;AACF;AAEAC,SAAS,CAACgC,gBAAgB,GAAG;EAC3BC,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,OAAO;EACjBC,UAAU,EAAE;AACd,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGrC,SAAS"}