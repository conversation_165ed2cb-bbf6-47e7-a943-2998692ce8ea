{"version": 3, "file": "exceljs.browser.js", "names": ["require", "ExcelJS", "Workbook", "Enums", "Object", "keys", "for<PERSON>ach", "key", "module", "exports"], "sources": ["../../lib/exceljs.browser.js"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies,node/no-unpublished-require */\nrequire('core-js/modules/es.promise');\nrequire('core-js/modules/es.promise.finally');\nrequire('core-js/modules/es.object.assign');\nrequire('core-js/modules/es.object.keys');\nrequire('core-js/modules/es.object.values');\nrequire('core-js/modules/es.symbol');\nrequire('core-js/modules/es.symbol.async-iterator');\n// required by core-js/modules/es.promise Promise.all\nrequire('core-js/modules/es.array.iterator');\n// required by node_modules/saxes/saxes.js SaxesParser.captureTo\nrequire('core-js/modules/es.array.includes');\n// required by lib/doc/workbook.js Workbook.model\nrequire('core-js/modules/es.array.find-index');\n// required by lib/doc/workbook.js Workbook.addWorksheet and Workbook.getWorksheet\nrequire('core-js/modules/es.array.find');\n// required by node_modules/saxes/saxes.js SaxesParser.getCode10\nrequire('core-js/modules/es.string.from-code-point');\n// required by lib/xlsx/xform/sheet/data-validations-xform.js DataValidationsXform.parseClose\nrequire('core-js/modules/es.string.includes');\n// required by lib/utils/utils.js utils.validInt and lib/csv/csv.js CSV.read\nrequire('core-js/modules/es.number.is-nan');\nrequire('regenerator-runtime/runtime');\n\nconst ExcelJS = {\n  Workbook: require('./doc/workbook'),\n};\n\n// Object.assign mono-fill\nconst Enums = require('./doc/enums');\n\nObject.keys(Enums).forEach(key => {\n  ExcelJS[key] = Enums[key];\n});\n\nmodule.exports = ExcelJS;\n"], "mappings": ";;AAAA;AACAA,OAAO,CAAC,4BAA4B,CAAC;AACrCA,OAAO,CAAC,oCAAoC,CAAC;AAC7CA,OAAO,CAAC,kCAAkC,CAAC;AAC3CA,OAAO,CAAC,gCAAgC,CAAC;AACzCA,OAAO,CAAC,kCAAkC,CAAC;AAC3CA,OAAO,CAAC,2BAA2B,CAAC;AACpCA,OAAO,CAAC,0CAA0C,CAAC;AACnD;AACAA,OAAO,CAAC,mCAAmC,CAAC;AAC5C;AACAA,OAAO,CAAC,mCAAmC,CAAC;AAC5C;AACAA,OAAO,CAAC,qCAAqC,CAAC;AAC9C;AACAA,OAAO,CAAC,+BAA+B,CAAC;AACxC;AACAA,OAAO,CAAC,2CAA2C,CAAC;AACpD;AACAA,OAAO,CAAC,oCAAoC,CAAC;AAC7C;AACAA,OAAO,CAAC,kCAAkC,CAAC;AAC3CA,OAAO,CAAC,6BAA6B,CAAC;AAEtC,MAAMC,OAAO,GAAG;EACdC,QAAQ,EAAEF,OAAO,CAAC,gBAAgB;AACpC,CAAC;;AAED;AACA,MAAMG,KAAK,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEpCI,MAAM,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;EAChCN,OAAO,CAACM,GAAG,CAAC,GAAGJ,KAAK,CAACI,GAAG,CAAC;AAC3B,CAAC,CAAC;AAEFC,MAAM,CAACC,OAAO,GAAGR,OAAO"}