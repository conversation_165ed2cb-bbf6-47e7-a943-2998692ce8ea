{"version": 3, "file": "vml-protection-xform.js", "names": ["BaseXform", "require", "VmlProtectionXform", "constructor", "model", "_model", "tag", "render", "xmlStream", "leafNode", "parseOpen", "node", "name", "text", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/comment/style/vml-protection-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass VmlProtectionXform extends BaseXform {\n  constructor(model) {\n    super();\n    this._model = model;\n  }\n\n  get tag() {\n    return this._model && this._model.tag;\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, null, model);\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.text = '';\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    this.text = text;\n  }\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = VmlProtectionXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,kBAAkB,SAASF,SAAS,CAAC;EACzCG,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,MAAM,GAAGD,KAAK;EACrB;EAEA,IAAIE,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,GAAG;EACvC;EAEAC,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvBI,SAAS,CAACC,QAAQ,CAAC,IAAI,CAACH,GAAG,EAAE,IAAI,EAAEF,KAAK,CAAC;EAC3C;EAEAM,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAACN,GAAG;QACX,IAAI,CAACO,IAAI,GAAG,EAAE;QACd,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAACD,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EAClB;EAEAE,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGf,kBAAkB"}