{"version": 3, "file": "vml-notes-xform.js", "names": ["XmlStream", "require", "BaseXform", "VmlShapeXform", "VmlNotesXform", "constructor", "map", "tag", "render", "xmlStream", "model", "openXml", "StdDocAttributes", "openNode", "DRAWING_ATTRIBUTES", "leafNode", "data", "closeNode", "id", "coordsize", "path", "joinstyle", "gradientshapeok", "comments", "for<PERSON>ach", "item", "index", "parseOpen", "node", "parser", "name", "reset", "parseText", "text", "parseClose", "push", "undefined", "reconcile", "options", "anchors", "anchor", "br", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/comment/vml-notes-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\nconst VmlShapeXform = require('./vml-shape-xform');\n\n// This class is (currently) single purposed to insert the triangle\n// drawing icons on commented cells\nclass VmlNotesXform extends BaseXform {\n  constructor() {\n    super();\n    this.map = {\n      'v:shape': new VmlShapeXform(),\n    };\n  }\n\n  get tag() {\n    return 'xml';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n    xmlStream.openNode(this.tag, VmlNotesXform.DRAWING_ATTRIBUTES);\n\n    xmlStream.openNode('o:shapelayout', {'v:ext': 'edit'});\n    xmlStream.leafNode('o:idmap', {'v:ext': 'edit', data: 1});\n    xmlStream.closeNode();\n\n    xmlStream.openNode('v:shapetype', {\n      id: '_x0000_t202',\n      coordsize: '21600,21600',\n      'o:spt': 202,\n      path: 'm,l,21600r21600,l21600,xe',\n    });\n    xmlStream.leafNode('v:stroke', {joinstyle: 'miter'});\n    xmlStream.leafNode('v:path', {gradientshapeok: 't', 'o:connecttype': 'rect'});\n    xmlStream.closeNode();\n\n    model.comments.forEach((item, index) => {\n      this.map['v:shape'].render(xmlStream, item, index);\n    });\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case this.tag:\n        this.reset();\n        this.model = {\n          comments: [],\n        };\n        break;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        break;\n    }\n    return true;\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.model.comments.push(this.parser.model);\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case this.tag:\n        return false;\n      default:\n        // could be some unrecognised tags\n        return true;\n    }\n  }\n\n  reconcile(model, options) {\n    model.anchors.forEach(anchor => {\n      if (anchor.br) {\n        this.map['xdr:twoCellAnchor'].reconcile(anchor, options);\n      } else {\n        this.map['xdr:oneCellAnchor'].reconcile(anchor, options);\n      }\n    });\n  }\n}\n\nVmlNotesXform.DRAWING_ATTRIBUTES = {\n  'xmlns:v': 'urn:schemas-microsoft-com:vml',\n  'xmlns:o': 'urn:schemas-microsoft-com:office:office',\n  'xmlns:x': 'urn:schemas-microsoft-com:office:excel',\n};\n\nmodule.exports = VmlNotesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAME,aAAa,GAAGF,OAAO,CAAC,mBAAmB,CAAC;;AAElD;AACA;AACA,MAAMG,aAAa,SAASF,SAAS,CAAC;EACpCG,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,GAAG,GAAG;MACT,SAAS,EAAE,IAAIH,aAAa,CAAC;IAC/B,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,OAAO,CAACX,SAAS,CAACY,gBAAgB,CAAC;IAC7CH,SAAS,CAACI,QAAQ,CAAC,IAAI,CAACN,GAAG,EAAEH,aAAa,CAACU,kBAAkB,CAAC;IAE9DL,SAAS,CAACI,QAAQ,CAAC,eAAe,EAAE;MAAC,OAAO,EAAE;IAAM,CAAC,CAAC;IACtDJ,SAAS,CAACM,QAAQ,CAAC,SAAS,EAAE;MAAC,OAAO,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAC,CAAC,CAAC;IACzDP,SAAS,CAACQ,SAAS,CAAC,CAAC;IAErBR,SAAS,CAACI,QAAQ,CAAC,aAAa,EAAE;MAChCK,EAAE,EAAE,aAAa;MACjBC,SAAS,EAAE,aAAa;MACxB,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE;IACR,CAAC,CAAC;IACFX,SAAS,CAACM,QAAQ,CAAC,UAAU,EAAE;MAACM,SAAS,EAAE;IAAO,CAAC,CAAC;IACpDZ,SAAS,CAACM,QAAQ,CAAC,QAAQ,EAAE;MAACO,eAAe,EAAE,GAAG;MAAE,eAAe,EAAE;IAAM,CAAC,CAAC;IAC7Eb,SAAS,CAACQ,SAAS,CAAC,CAAC;IAErBP,KAAK,CAACa,QAAQ,CAACC,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MACtC,IAAI,CAACpB,GAAG,CAAC,SAAS,CAAC,CAACE,MAAM,CAACC,SAAS,EAAEgB,IAAI,EAAEC,KAAK,CAAC;IACpD,CAAC,CAAC;IAEFjB,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAU,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,IAAI,CAACvB,GAAG;QACX,IAAI,CAACwB,KAAK,CAAC,CAAC;QACZ,IAAI,CAACrB,KAAK,GAAG;UACXa,QAAQ,EAAE;QACZ,CAAC;QACD;MACF;QACE,IAAI,CAACM,MAAM,GAAG,IAAI,CAACvB,GAAG,CAACsB,IAAI,CAACE,IAAI,CAAC;QACjC,IAAI,IAAI,CAACD,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA;IACJ;IACA,OAAO,IAAI;EACb;EAEAI,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACG,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACJ,IAAI,EAAE;IACf,IAAI,IAAI,CAACD,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACK,UAAU,CAACJ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACpB,KAAK,CAACa,QAAQ,CAACY,IAAI,CAAC,IAAI,CAACN,MAAM,CAACnB,KAAK,CAAC;QAC3C,IAAI,CAACmB,MAAM,GAAGO,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQN,IAAI;MACV,KAAK,IAAI,CAACvB,GAAG;QACX,OAAO,KAAK;MACd;QACE;QACA,OAAO,IAAI;IACf;EACF;EAEA8B,SAASA,CAAC3B,KAAK,EAAE4B,OAAO,EAAE;IACxB5B,KAAK,CAAC6B,OAAO,CAACf,OAAO,CAACgB,MAAM,IAAI;MAC9B,IAAIA,MAAM,CAACC,EAAE,EAAE;QACb,IAAI,CAACnC,GAAG,CAAC,mBAAmB,CAAC,CAAC+B,SAAS,CAACG,MAAM,EAAEF,OAAO,CAAC;MAC1D,CAAC,MAAM;QACL,IAAI,CAAChC,GAAG,CAAC,mBAAmB,CAAC,CAAC+B,SAAS,CAACG,MAAM,EAAEF,OAAO,CAAC;MAC1D;IACF,CAAC,CAAC;EACJ;AACF;AAEAlC,aAAa,CAACU,kBAAkB,GAAG;EACjC,SAAS,EAAE,+BAA+B;EAC1C,SAAS,EAAE,yCAAyC;EACpD,SAAS,EAAE;AACb,CAAC;AAED4B,MAAM,CAACC,OAAO,GAAGvC,aAAa"}