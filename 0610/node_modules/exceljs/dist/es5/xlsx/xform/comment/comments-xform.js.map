{"version": 3, "file": "comments-xform.js", "names": ["XmlStream", "require", "utils", "BaseXform", "CommentXform", "CommentsXform", "module", "exports", "map", "comment", "inherits", "COMMENTS_ATTRIBUTES", "xmlns", "render", "xmlStream", "model", "openXml", "StdDocAttributes", "openNode", "leafNode", "closeNode", "comments", "for<PERSON>ach", "parseOpen", "node", "parser", "name", "parseText", "text", "parseClose", "push", "undefined"], "sources": ["../../../../../lib/xlsx/xform/comment/comments-xform.js"], "sourcesContent": ["const XmlStream = require('../../../utils/xml-stream');\nconst utils = require('../../../utils/utils');\nconst BaseXform = require('../base-xform');\n\nconst CommentXform = require('./comment-xform');\n\nconst CommentsXform = (module.exports = function() {\n  this.map = {\n    comment: new CommentXform(),\n  };\n});\n\nutils.inherits(\n  CommentsXform,\n  BaseXform,\n  {\n    COMMENTS_ATTRIBUTES: {\n      xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n    },\n  },\n  {\n    render(xmlStream, model) {\n      model = model || this.model;\n      xmlStream.openXml(XmlStream.StdDocAttributes);\n      xmlStream.openNode('comments', CommentsXform.COMMENTS_ATTRIBUTES);\n\n      // authors\n      // TODO: support authors properly\n      xmlStream.openNode('authors');\n      xmlStream.leafNode('author', null, 'Author');\n      xmlStream.closeNode();\n\n      // comments\n      xmlStream.openNode('commentList');\n      model.comments.forEach(comment => {\n        this.map.comment.render(xmlStream, comment);\n      });\n      xmlStream.closeNode();\n      xmlStream.closeNode();\n    },\n\n    parseOpen(node) {\n      if (this.parser) {\n        this.parser.parseOpen(node);\n        return true;\n      }\n      switch (node.name) {\n        case 'commentList':\n          this.model = {\n            comments: [],\n          };\n          return true;\n        case 'comment':\n          this.parser = this.map.comment;\n          this.parser.parseOpen(node);\n          return true;\n        default:\n          return false;\n      }\n    },\n    parseText(text) {\n      if (this.parser) {\n        this.parser.parseText(text);\n      }\n    },\n    parseClose(name) {\n      switch (name) {\n        case 'commentList':\n          return false;\n        case 'comment':\n          this.model.comments.push(this.parser.model);\n          this.parser = undefined;\n          return true;\n        default:\n          if (this.parser) {\n            this.parser.parseClose(name);\n          }\n          return true;\n      }\n    },\n  }\n);\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AACtD,MAAMC,KAAK,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AAC7C,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAE/C,MAAMI,aAAa,GAAIC,MAAM,CAACC,OAAO,GAAG,YAAW;EACjD,IAAI,CAACC,GAAG,GAAG;IACTC,OAAO,EAAE,IAAIL,YAAY,CAAC;EAC5B,CAAC;AACH,CAAE;AAEFF,KAAK,CAACQ,QAAQ,CACZL,aAAa,EACbF,SAAS,EACT;EACEQ,mBAAmB,EAAE;IACnBC,KAAK,EAAE;EACT;AACF,CAAC,EACD;EACEC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK;IAC3BD,SAAS,CAACE,OAAO,CAAChB,SAAS,CAACiB,gBAAgB,CAAC;IAC7CH,SAAS,CAACI,QAAQ,CAAC,UAAU,EAAEb,aAAa,CAACM,mBAAmB,CAAC;;IAEjE;IACA;IACAG,SAAS,CAACI,QAAQ,CAAC,SAAS,CAAC;IAC7BJ,SAAS,CAACK,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC;IAC5CL,SAAS,CAACM,SAAS,CAAC,CAAC;;IAErB;IACAN,SAAS,CAACI,QAAQ,CAAC,aAAa,CAAC;IACjCH,KAAK,CAACM,QAAQ,CAACC,OAAO,CAACb,OAAO,IAAI;MAChC,IAAI,CAACD,GAAG,CAACC,OAAO,CAACI,MAAM,CAACC,SAAS,EAAEL,OAAO,CAAC;IAC7C,CAAC,CAAC;IACFK,SAAS,CAACM,SAAS,CAAC,CAAC;IACrBN,SAAS,CAACM,SAAS,CAAC,CAAC;EACvB,CAAC;EAEDG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACE,IAAI;MACf,KAAK,aAAa;QAChB,IAAI,CAACX,KAAK,GAAG;UACXM,QAAQ,EAAE;QACZ,CAAC;QACD,OAAO,IAAI;MACb,KAAK,SAAS;QACZ,IAAI,CAACI,MAAM,GAAG,IAAI,CAACjB,GAAG,CAACC,OAAO;QAC9B,IAAI,CAACgB,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC3B,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EACDG,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACH,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACE,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF,CAAC;EACDC,UAAUA,CAACH,IAAI,EAAE;IACf,QAAQA,IAAI;MACV,KAAK,aAAa;QAChB,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,IAAI,CAACX,KAAK,CAACM,QAAQ,CAACS,IAAI,CAAC,IAAI,CAACL,MAAM,CAACV,KAAK,CAAC;QAC3C,IAAI,CAACU,MAAM,GAAGM,SAAS;QACvB,OAAO,IAAI;MACb;QACE,IAAI,IAAI,CAACN,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACI,UAAU,CAACH,IAAI,CAAC;QAC9B;QACA,OAAO,IAAI;IACf;EACF;AACF,CACF,CAAC"}