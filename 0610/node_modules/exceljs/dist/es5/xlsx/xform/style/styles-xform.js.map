{"version": 3, "file": "styles-xform.js", "names": ["Enums", "require", "XmlStream", "BaseXform", "StaticXform", "ListXform", "FontXform", "FillXform", "BorderXform", "NumFmtXform", "StyleXform", "DxfXform", "NUMFMT_BASE", "StylesXform", "constructor", "initialise", "map", "numFmts", "tag", "count", "childXform", "fonts", "$", "fills", "borders", "cellStyleXfs", "cellXfs", "xfId", "dxfs", "always", "numFmt", "font", "fill", "border", "style", "cellStyles", "STATIC_XFORMS", "tableStyles", "extLst", "init", "initIndex", "index", "numFmtNextId", "model", "styles", "_addBorder", "_addStyle", "numFmtId", "fontId", "fillId", "borderId", "_addFill", "type", "pattern", "weakMap", "WeakMap", "render", "xmlStream", "openXml", "StdDocAttributes", "openNode", "STYLESHEET_ATTRIBUTES", "length", "for<PERSON>ach", "numFmtXml", "writeXml", "closeNode", "_addFont", "size", "color", "theme", "name", "family", "scheme", "fontXml", "fillXml", "borderXml", "styleXml", "parseOpen", "node", "parser", "parseText", "text", "parseClose", "undefined", "add", "propName", "xform", "numFmtIndex", "id", "formatCode", "addStyleModel", "cellType", "has", "get", "ValueType", "Number", "_addNumFmtStr", "Date", "alignment", "protection", "styleId", "set", "getStyleModel", "getDefaultFmtCode", "addStyle", "group", "part", "addDxfStyle", "push", "getDxfStyle", "xml", "toXml", "getDefaultFmtId", "xmlns", "c", "builtinId", "defaultTableStyle", "defaultPivotStyle", "uri", "defaultSlicerStyle", "defaultTimelineStyle", "StylesXformMock", "parseStream", "stream", "autodrain", "Promise", "resolve", "dateStyleId", "_dateStyleId", "dateStyle", "<PERSON><PERSON>", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/styles-xform.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst Enums = require('../../../doc/enums');\nconst XmlStream = require('../../../utils/xml-stream');\n\nconst BaseXform = require('../base-xform');\nconst StaticXform = require('../static-xform');\nconst ListXform = require('../list-xform');\nconst FontXform = require('./font-xform');\nconst FillXform = require('./fill-xform');\nconst BorderXform = require('./border-xform');\nconst NumFmtXform = require('./numfmt-xform');\nconst StyleXform = require('./style-xform');\nconst DxfXform = require('./dxf-xform');\n\n// custom numfmt ids start here\nconst NUMFMT_BASE = 164;\n\n// =============================================================================\n// StylesXform is used to generate and parse the styles.xml file\n// it manages the collections of fonts, number formats, alignments, etc\nclass StylesXform extends BaseXform {\n  constructor(initialise) {\n    super();\n\n    this.map = {\n      numFmts: new ListXform({tag: 'numFmts', count: true, childXform: new NumFmtXform()}),\n      fonts: new ListXform({\n        tag: 'fonts',\n        count: true,\n        childXform: new FontXform(),\n        $: {'x14ac:knownFonts': 1},\n      }),\n      fills: new ListXform({tag: 'fills', count: true, childXform: new FillXform()}),\n      borders: new ListXform({tag: 'borders', count: true, childXform: new BorderXform()}),\n      cellStyleXfs: new ListXform({tag: 'cellStyleXfs', count: true, childXform: new StyleXform()}),\n      cellXfs: new ListXform({\n        tag: 'cellXfs',\n        count: true,\n        childXform: new StyleXform({xfId: true}),\n      }),\n      dxfs: new ListXform({tag: 'dxfs', always: true, count: true, childXform: new DxfXform()}),\n\n      // for style manager\n      numFmt: new NumFmtXform(),\n      font: new FontXform(),\n      fill: new FillXform(),\n      border: new BorderXform(),\n      style: new StyleXform({xfId: true}),\n\n      cellStyles: StylesXform.STATIC_XFORMS.cellStyles,\n      tableStyles: StylesXform.STATIC_XFORMS.tableStyles,\n      extLst: StylesXform.STATIC_XFORMS.extLst,\n    };\n\n    if (initialise) {\n      // StylesXform also acts as style manager and is used to build up styles-model during worksheet processing\n      this.init();\n    }\n  }\n\n  initIndex() {\n    this.index = {\n      style: {},\n      numFmt: {},\n      numFmtNextId: 164, // start custom format ids here\n      font: {},\n      border: {},\n      fill: {},\n    };\n  }\n\n  init() {\n    // Prepare for Style Manager role\n    this.model = {\n      styles: [],\n      numFmts: [],\n      fonts: [],\n      borders: [],\n      fills: [],\n      dxfs: [],\n    };\n\n    this.initIndex();\n\n    // default (zero) border\n    this._addBorder({});\n\n    // add default (all zero) style\n    this._addStyle({numFmtId: 0, fontId: 0, fillId: 0, borderId: 0, xfId: 0});\n\n    // add default fills\n    this._addFill({type: 'pattern', pattern: 'none'});\n    this._addFill({type: 'pattern', pattern: 'gray125'});\n\n    this.weakMap = new WeakMap();\n  }\n\n  render(xmlStream, model) {\n    model = model || this.model;\n    //\n    //   <fonts count=\"2\" x14ac:knownFonts=\"1\">\n    xmlStream.openXml(XmlStream.StdDocAttributes);\n\n    xmlStream.openNode('styleSheet', StylesXform.STYLESHEET_ATTRIBUTES);\n\n    if (this.index) {\n      // model has been built by style manager role (contains xml)\n      if (model.numFmts && model.numFmts.length) {\n        xmlStream.openNode('numFmts', {count: model.numFmts.length});\n        model.numFmts.forEach(numFmtXml => {\n          xmlStream.writeXml(numFmtXml);\n        });\n        xmlStream.closeNode();\n      }\n\n      if (!model.fonts.length) {\n        // default (zero) font\n        this._addFont({size: 11, color: {theme: 1}, name: 'Calibri', family: 2, scheme: 'minor'});\n      }\n      xmlStream.openNode('fonts', {count: model.fonts.length, 'x14ac:knownFonts': 1});\n      model.fonts.forEach(fontXml => {\n        xmlStream.writeXml(fontXml);\n      });\n      xmlStream.closeNode();\n\n      xmlStream.openNode('fills', {count: model.fills.length});\n      model.fills.forEach(fillXml => {\n        xmlStream.writeXml(fillXml);\n      });\n      xmlStream.closeNode();\n\n      xmlStream.openNode('borders', {count: model.borders.length});\n      model.borders.forEach(borderXml => {\n        xmlStream.writeXml(borderXml);\n      });\n      xmlStream.closeNode();\n\n      this.map.cellStyleXfs.render(xmlStream, [{numFmtId: 0, fontId: 0, fillId: 0, borderId: 0, xfId: 0}]);\n\n      xmlStream.openNode('cellXfs', {count: model.styles.length});\n      model.styles.forEach(styleXml => {\n        xmlStream.writeXml(styleXml);\n      });\n      xmlStream.closeNode();\n    } else {\n      // model is plain JSON and needs to be xformed\n      this.map.numFmts.render(xmlStream, model.numFmts);\n      this.map.fonts.render(xmlStream, model.fonts);\n      this.map.fills.render(xmlStream, model.fills);\n      this.map.borders.render(xmlStream, model.borders);\n      this.map.cellStyleXfs.render(xmlStream, [{numFmtId: 0, fontId: 0, fillId: 0, borderId: 0, xfId: 0}]);\n      this.map.cellXfs.render(xmlStream, model.styles);\n    }\n\n    StylesXform.STATIC_XFORMS.cellStyles.render(xmlStream);\n\n    this.map.dxfs.render(xmlStream, model.dxfs);\n\n    StylesXform.STATIC_XFORMS.tableStyles.render(xmlStream);\n    StylesXform.STATIC_XFORMS.extLst.render(xmlStream);\n\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    if (this.parser) {\n      this.parser.parseOpen(node);\n      return true;\n    }\n    switch (node.name) {\n      case 'styleSheet':\n        this.initIndex();\n        return true;\n      default:\n        this.parser = this.map[node.name];\n        if (this.parser) {\n          this.parser.parseOpen(node);\n        }\n        return true;\n    }\n  }\n\n  parseText(text) {\n    if (this.parser) {\n      this.parser.parseText(text);\n    }\n  }\n\n  parseClose(name) {\n    if (this.parser) {\n      if (!this.parser.parseClose(name)) {\n        this.parser = undefined;\n      }\n      return true;\n    }\n    switch (name) {\n      case 'styleSheet': {\n        this.model = {};\n        const add = (propName, xform) => {\n          if (xform.model && xform.model.length) {\n            this.model[propName] = xform.model;\n          }\n        };\n        add('numFmts', this.map.numFmts);\n        add('fonts', this.map.fonts);\n        add('fills', this.map.fills);\n        add('borders', this.map.borders);\n        add('styles', this.map.cellXfs);\n        add('dxfs', this.map.dxfs);\n\n        // index numFmts\n        this.index = {\n          model: [],\n          numFmt: [],\n        };\n        if (this.model.numFmts) {\n          const numFmtIndex = this.index.numFmt;\n          this.model.numFmts.forEach(numFmt => {\n            numFmtIndex[numFmt.id] = numFmt.formatCode;\n          });\n        }\n\n        return false;\n      }\n      default:\n        // not quite sure how we get here!\n        return true;\n    }\n  }\n\n  // add a cell's style model to the collection\n  // each style property is processed and cross-referenced, etc.\n  // the styleId is returned. Note: cellType is used when numFmt not defined\n  addStyleModel(model, cellType) {\n    if (!model) {\n      return 0;\n    }\n\n    // if we have no default font, add it here now\n    if (!this.model.fonts.length) {\n      // default (zero) font\n      this._addFont({size: 11, color: {theme: 1}, name: 'Calibri', family: 2, scheme: 'minor'});\n    }\n\n    // if we have seen this style object before, assume it has the same styleId\n    if (this.weakMap && this.weakMap.has(model)) {\n      return this.weakMap.get(model);\n    }\n\n    const style = {};\n    cellType = cellType || Enums.ValueType.Number;\n\n    if (model.numFmt) {\n      style.numFmtId = this._addNumFmtStr(model.numFmt);\n    } else {\n      switch (cellType) {\n        case Enums.ValueType.Number:\n          style.numFmtId = this._addNumFmtStr('General');\n          break;\n        case Enums.ValueType.Date:\n          style.numFmtId = this._addNumFmtStr('mm-dd-yy');\n          break;\n        default:\n          break;\n      }\n    }\n\n    if (model.font) {\n      style.fontId = this._addFont(model.font);\n    }\n\n    if (model.border) {\n      style.borderId = this._addBorder(model.border);\n    }\n\n    if (model.fill) {\n      style.fillId = this._addFill(model.fill);\n    }\n\n    if (model.alignment) {\n      style.alignment = model.alignment;\n    }\n\n    if (model.protection) {\n      style.protection = model.protection;\n    }\n\n    const styleId = this._addStyle(style);\n    if (this.weakMap) {\n      this.weakMap.set(model, styleId);\n    }\n    return styleId;\n  }\n\n  // given a styleId (i.e. s=\"n\"), get the cell's style model\n  // objects are shared where possible.\n  getStyleModel(id) {\n    // if the style doesn't exist return null\n    const style = this.model.styles[id];\n    if (!style) return null;\n\n    // have we built this model before?\n    let model = this.index.model[id];\n    if (model) return model;\n\n    // build a new model\n    model = this.index.model[id] = {};\n\n    // -------------------------------------------------------\n    // number format\n    if (style.numFmtId) {\n      const numFmt = this.index.numFmt[style.numFmtId] || NumFmtXform.getDefaultFmtCode(style.numFmtId);\n      if (numFmt) {\n        model.numFmt = numFmt;\n      }\n    }\n\n    function addStyle(name, group, styleId) {\n      if (styleId || styleId === 0) {\n        const part = group[styleId];\n        if (part) {\n          model[name] = part;\n        }\n      }\n    }\n\n    addStyle('font', this.model.fonts, style.fontId);\n    addStyle('border', this.model.borders, style.borderId);\n    addStyle('fill', this.model.fills, style.fillId);\n\n    // -------------------------------------------------------\n    // alignment\n    if (style.alignment) {\n      model.alignment = style.alignment;\n    }\n\n    // -------------------------------------------------------\n    // protection\n    if (style.protection) {\n      model.protection = style.protection;\n    }\n\n    return model;\n  }\n\n  addDxfStyle(style) {\n    if (style.numFmt) {\n      // register numFmtId to use it during dxf-xform rendering\n      style.numFmtId = this._addNumFmtStr(style.numFmt);\n    }\n\n    this.model.dxfs.push(style);\n    return this.model.dxfs.length - 1;\n  }\n\n  getDxfStyle(id) {\n    return this.model.dxfs[id];\n  }\n\n  // =========================================================================\n  // Private Interface\n  _addStyle(style) {\n    const xml = this.map.style.toXml(style);\n    let index = this.index.style[xml];\n    if (index === undefined) {\n      index = this.index.style[xml] = this.model.styles.length;\n      this.model.styles.push(xml);\n    }\n    return index;\n  }\n\n  // =========================================================================\n  // Number Formats\n  _addNumFmtStr(formatCode) {\n    // check if default format\n    let index = NumFmtXform.getDefaultFmtId(formatCode);\n    if (index !== undefined) return index;\n\n    // check if already in\n    index = this.index.numFmt[formatCode];\n    if (index !== undefined) return index;\n\n    index = this.index.numFmt[formatCode] = NUMFMT_BASE + this.model.numFmts.length;\n    const xml = this.map.numFmt.toXml({id: index, formatCode});\n    this.model.numFmts.push(xml);\n    return index;\n  }\n\n  // =========================================================================\n  // Fonts\n  _addFont(font) {\n    const xml = this.map.font.toXml(font);\n    let index = this.index.font[xml];\n    if (index === undefined) {\n      index = this.index.font[xml] = this.model.fonts.length;\n      this.model.fonts.push(xml);\n    }\n    return index;\n  }\n\n  // =========================================================================\n  // Borders\n  _addBorder(border) {\n    const xml = this.map.border.toXml(border);\n    let index = this.index.border[xml];\n    if (index === undefined) {\n      index = this.index.border[xml] = this.model.borders.length;\n      this.model.borders.push(xml);\n    }\n    return index;\n  }\n\n  // =========================================================================\n  // Fills\n  _addFill(fill) {\n    const xml = this.map.fill.toXml(fill);\n    let index = this.index.fill[xml];\n    if (index === undefined) {\n      index = this.index.fill[xml] = this.model.fills.length;\n      this.model.fills.push(xml);\n    }\n    return index;\n  }\n\n  // =========================================================================\n}\n\nStylesXform.STYLESHEET_ATTRIBUTES = {\n  xmlns: 'http://schemas.openxmlformats.org/spreadsheetml/2006/main',\n  'xmlns:mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',\n  'mc:Ignorable': 'x14ac x16r2',\n  'xmlns:x14ac': 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac',\n  'xmlns:x16r2': 'http://schemas.microsoft.com/office/spreadsheetml/2015/02/main',\n};\nStylesXform.STATIC_XFORMS = {\n  cellStyles: new StaticXform({\n    tag: 'cellStyles',\n    $: {count: 1},\n    c: [{tag: 'cellStyle', $: {name: 'Normal', xfId: 0, builtinId: 0}}],\n  }),\n  dxfs: new StaticXform({tag: 'dxfs', $: {count: 0}}),\n  tableStyles: new StaticXform({\n    tag: 'tableStyles',\n    $: {count: 0, defaultTableStyle: 'TableStyleMedium2', defaultPivotStyle: 'PivotStyleLight16'},\n  }),\n  extLst: new StaticXform({\n    tag: 'extLst',\n    c: [\n      {\n        tag: 'ext',\n        $: {\n          uri: '{EB79DEF2-80B8-43e5-95BD-54CBDDF9020C}',\n          'xmlns:x14': 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/main',\n        },\n        c: [{tag: 'x14:slicerStyles', $: {defaultSlicerStyle: 'SlicerStyleLight1'}}],\n      },\n      {\n        tag: 'ext',\n        $: {\n          uri: '{9260A510-F301-46a8-8635-F512D64BE5F5}',\n          'xmlns:x15': 'http://schemas.microsoft.com/office/spreadsheetml/2010/11/main',\n        },\n        c: [{tag: 'x15:timelineStyles', $: {defaultTimelineStyle: 'TimeSlicerStyleLight1'}}],\n      },\n    ],\n  }),\n};\n\n// the stylemanager mock acts like StyleManager except that it always returns 0 or {}\nclass StylesXformMock extends StylesXform {\n  constructor() {\n    super();\n\n    this.model = {\n      styles: [{numFmtId: 0, fontId: 0, fillId: 0, borderId: 0, xfId: 0}],\n      numFmts: [],\n      fonts: [{size: 11, color: {theme: 1}, name: 'Calibri', family: 2, scheme: 'minor'}],\n      borders: [{}],\n      fills: [\n        {type: 'pattern', pattern: 'none'},\n        {type: 'pattern', pattern: 'gray125'},\n      ],\n    };\n  }\n\n  // =========================================================================\n  // Style Manager Interface\n\n  // override normal behaviour - consume and dispose\n  parseStream(stream) {\n    stream.autodrain();\n    return Promise.resolve();\n  }\n\n  // add a cell's style model to the collection\n  // each style property is processed and cross-referenced, etc.\n  // the styleId is returned. Note: cellType is used when numFmt not defined\n  addStyleModel(model, cellType) {\n    switch (cellType) {\n      case Enums.ValueType.Date:\n        return this.dateStyleId;\n      default:\n        return 0;\n    }\n  }\n\n  get dateStyleId() {\n    if (!this._dateStyleId) {\n      const dateStyle = {\n        numFmtId: NumFmtXform.getDefaultFmtId('mm-dd-yy'),\n      };\n      this._dateStyleId = this.model.styles.length;\n      this.model.styles.push(dateStyle);\n    }\n    return this._dateStyleId;\n  }\n\n  // given a styleId (i.e. s=\"n\"), get the cell's style model\n  // objects are shared where possible.\n  getStyleModel(/* id */) {\n    return {};\n  }\n}\n\nStylesXform.Mock = StylesXformMock;\n\nmodule.exports = StylesXform;\n"], "mappings": ";;AAAA;AACA,MAAMA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAC3C,MAAMC,SAAS,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEtD,MAAME,SAAS,GAAGF,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMG,WAAW,GAAGH,OAAO,CAAC,iBAAiB,CAAC;AAC9C,MAAMI,SAAS,GAAGJ,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMK,SAAS,GAAGL,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMM,SAAS,GAAGN,OAAO,CAAC,cAAc,CAAC;AACzC,MAAMO,WAAW,GAAGP,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMQ,WAAW,GAAGR,OAAO,CAAC,gBAAgB,CAAC;AAC7C,MAAMS,UAAU,GAAGT,OAAO,CAAC,eAAe,CAAC;AAC3C,MAAMU,QAAQ,GAAGV,OAAO,CAAC,aAAa,CAAC;;AAEvC;AACA,MAAMW,WAAW,GAAG,GAAG;;AAEvB;AACA;AACA;AACA,MAAMC,WAAW,SAASV,SAAS,CAAC;EAClCW,WAAWA,CAACC,UAAU,EAAE;IACtB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,OAAO,EAAE,IAAIZ,SAAS,CAAC;QAACa,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIX,WAAW,CAAC;MAAC,CAAC,CAAC;MACpFY,KAAK,EAAE,IAAIhB,SAAS,CAAC;QACnBa,GAAG,EAAE,OAAO;QACZC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAId,SAAS,CAAC,CAAC;QAC3BgB,CAAC,EAAE;UAAC,kBAAkB,EAAE;QAAC;MAC3B,CAAC,CAAC;MACFC,KAAK,EAAE,IAAIlB,SAAS,CAAC;QAACa,GAAG,EAAE,OAAO;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIb,SAAS,CAAC;MAAC,CAAC,CAAC;MAC9EiB,OAAO,EAAE,IAAInB,SAAS,CAAC;QAACa,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIZ,WAAW,CAAC;MAAC,CAAC,CAAC;MACpFiB,YAAY,EAAE,IAAIpB,SAAS,CAAC;QAACa,GAAG,EAAE,cAAc;QAAEC,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIV,UAAU,CAAC;MAAC,CAAC,CAAC;MAC7FgB,OAAO,EAAE,IAAIrB,SAAS,CAAC;QACrBa,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,IAAI;QACXC,UAAU,EAAE,IAAIV,UAAU,CAAC;UAACiB,IAAI,EAAE;QAAI,CAAC;MACzC,CAAC,CAAC;MACFC,IAAI,EAAE,IAAIvB,SAAS,CAAC;QAACa,GAAG,EAAE,MAAM;QAAEW,MAAM,EAAE,IAAI;QAAEV,KAAK,EAAE,IAAI;QAAEC,UAAU,EAAE,IAAIT,QAAQ,CAAC;MAAC,CAAC,CAAC;MAEzF;MACAmB,MAAM,EAAE,IAAIrB,WAAW,CAAC,CAAC;MACzBsB,IAAI,EAAE,IAAIzB,SAAS,CAAC,CAAC;MACrB0B,IAAI,EAAE,IAAIzB,SAAS,CAAC,CAAC;MACrB0B,MAAM,EAAE,IAAIzB,WAAW,CAAC,CAAC;MACzB0B,KAAK,EAAE,IAAIxB,UAAU,CAAC;QAACiB,IAAI,EAAE;MAAI,CAAC,CAAC;MAEnCQ,UAAU,EAAEtB,WAAW,CAACuB,aAAa,CAACD,UAAU;MAChDE,WAAW,EAAExB,WAAW,CAACuB,aAAa,CAACC,WAAW;MAClDC,MAAM,EAAEzB,WAAW,CAACuB,aAAa,CAACE;IACpC,CAAC;IAED,IAAIvB,UAAU,EAAE;MACd;MACA,IAAI,CAACwB,IAAI,CAAC,CAAC;IACb;EACF;EAEAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG;MACXP,KAAK,EAAE,CAAC,CAAC;MACTJ,MAAM,EAAE,CAAC,CAAC;MACVY,YAAY,EAAE,GAAG;MAAE;MACnBX,IAAI,EAAE,CAAC,CAAC;MACRE,MAAM,EAAE,CAAC,CAAC;MACVD,IAAI,EAAE,CAAC;IACT,CAAC;EACH;EAEAO,IAAIA,CAAA,EAAG;IACL;IACA,IAAI,CAACI,KAAK,GAAG;MACXC,MAAM,EAAE,EAAE;MACV3B,OAAO,EAAE,EAAE;MACXI,KAAK,EAAE,EAAE;MACTG,OAAO,EAAE,EAAE;MACXD,KAAK,EAAE,EAAE;MACTK,IAAI,EAAE;IACR,CAAC;IAED,IAAI,CAACY,SAAS,CAAC,CAAC;;IAEhB;IACA,IAAI,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,SAAS,CAAC;MAACC,QAAQ,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,QAAQ,EAAE,CAAC;MAAEvB,IAAI,EAAE;IAAC,CAAC,CAAC;;IAEzE;IACA,IAAI,CAACwB,QAAQ,CAAC;MAACC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAM,CAAC,CAAC;IACjD,IAAI,CAACF,QAAQ,CAAC;MAACC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE;IAAS,CAAC,CAAC;IAEpD,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;EAC9B;EAEAC,MAAMA,CAACC,SAAS,EAAEd,KAAK,EAAE;IACvBA,KAAK,GAAGA,KAAK,IAAI,IAAI,CAACA,KAAK;IAC3B;IACA;IACAc,SAAS,CAACC,OAAO,CAACxD,SAAS,CAACyD,gBAAgB,CAAC;IAE7CF,SAAS,CAACG,QAAQ,CAAC,YAAY,EAAE/C,WAAW,CAACgD,qBAAqB,CAAC;IAEnE,IAAI,IAAI,CAACpB,KAAK,EAAE;MACd;MACA,IAAIE,KAAK,CAAC1B,OAAO,IAAI0B,KAAK,CAAC1B,OAAO,CAAC6C,MAAM,EAAE;QACzCL,SAAS,CAACG,QAAQ,CAAC,SAAS,EAAE;UAACzC,KAAK,EAAEwB,KAAK,CAAC1B,OAAO,CAAC6C;QAAM,CAAC,CAAC;QAC5DnB,KAAK,CAAC1B,OAAO,CAAC8C,OAAO,CAACC,SAAS,IAAI;UACjCP,SAAS,CAACQ,QAAQ,CAACD,SAAS,CAAC;QAC/B,CAAC,CAAC;QACFP,SAAS,CAACS,SAAS,CAAC,CAAC;MACvB;MAEA,IAAI,CAACvB,KAAK,CAACtB,KAAK,CAACyC,MAAM,EAAE;QACvB;QACA,IAAI,CAACK,QAAQ,CAAC;UAACC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE;YAACC,KAAK,EAAE;UAAC,CAAC;UAAEC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAO,CAAC,CAAC;MAC3F;MACAhB,SAAS,CAACG,QAAQ,CAAC,OAAO,EAAE;QAACzC,KAAK,EAAEwB,KAAK,CAACtB,KAAK,CAACyC,MAAM;QAAE,kBAAkB,EAAE;MAAC,CAAC,CAAC;MAC/EnB,KAAK,CAACtB,KAAK,CAAC0C,OAAO,CAACW,OAAO,IAAI;QAC7BjB,SAAS,CAACQ,QAAQ,CAACS,OAAO,CAAC;MAC7B,CAAC,CAAC;MACFjB,SAAS,CAACS,SAAS,CAAC,CAAC;MAErBT,SAAS,CAACG,QAAQ,CAAC,OAAO,EAAE;QAACzC,KAAK,EAAEwB,KAAK,CAACpB,KAAK,CAACuC;MAAM,CAAC,CAAC;MACxDnB,KAAK,CAACpB,KAAK,CAACwC,OAAO,CAACY,OAAO,IAAI;QAC7BlB,SAAS,CAACQ,QAAQ,CAACU,OAAO,CAAC;MAC7B,CAAC,CAAC;MACFlB,SAAS,CAACS,SAAS,CAAC,CAAC;MAErBT,SAAS,CAACG,QAAQ,CAAC,SAAS,EAAE;QAACzC,KAAK,EAAEwB,KAAK,CAACnB,OAAO,CAACsC;MAAM,CAAC,CAAC;MAC5DnB,KAAK,CAACnB,OAAO,CAACuC,OAAO,CAACa,SAAS,IAAI;QACjCnB,SAAS,CAACQ,QAAQ,CAACW,SAAS,CAAC;MAC/B,CAAC,CAAC;MACFnB,SAAS,CAACS,SAAS,CAAC,CAAC;MAErB,IAAI,CAAClD,GAAG,CAACS,YAAY,CAAC+B,MAAM,CAACC,SAAS,EAAE,CAAC;QAACV,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAC,CAAC,CAAC,CAAC;MAEpG8B,SAAS,CAACG,QAAQ,CAAC,SAAS,EAAE;QAACzC,KAAK,EAAEwB,KAAK,CAACC,MAAM,CAACkB;MAAM,CAAC,CAAC;MAC3DnB,KAAK,CAACC,MAAM,CAACmB,OAAO,CAACc,QAAQ,IAAI;QAC/BpB,SAAS,CAACQ,QAAQ,CAACY,QAAQ,CAAC;MAC9B,CAAC,CAAC;MACFpB,SAAS,CAACS,SAAS,CAAC,CAAC;IACvB,CAAC,MAAM;MACL;MACA,IAAI,CAAClD,GAAG,CAACC,OAAO,CAACuC,MAAM,CAACC,SAAS,EAAEd,KAAK,CAAC1B,OAAO,CAAC;MACjD,IAAI,CAACD,GAAG,CAACK,KAAK,CAACmC,MAAM,CAACC,SAAS,EAAEd,KAAK,CAACtB,KAAK,CAAC;MAC7C,IAAI,CAACL,GAAG,CAACO,KAAK,CAACiC,MAAM,CAACC,SAAS,EAAEd,KAAK,CAACpB,KAAK,CAAC;MAC7C,IAAI,CAACP,GAAG,CAACQ,OAAO,CAACgC,MAAM,CAACC,SAAS,EAAEd,KAAK,CAACnB,OAAO,CAAC;MACjD,IAAI,CAACR,GAAG,CAACS,YAAY,CAAC+B,MAAM,CAACC,SAAS,EAAE,CAAC;QAACV,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAC,CAAC,CAAC,CAAC;MACpG,IAAI,CAACX,GAAG,CAACU,OAAO,CAAC8B,MAAM,CAACC,SAAS,EAAEd,KAAK,CAACC,MAAM,CAAC;IAClD;IAEA/B,WAAW,CAACuB,aAAa,CAACD,UAAU,CAACqB,MAAM,CAACC,SAAS,CAAC;IAEtD,IAAI,CAACzC,GAAG,CAACY,IAAI,CAAC4B,MAAM,CAACC,SAAS,EAAEd,KAAK,CAACf,IAAI,CAAC;IAE3Cf,WAAW,CAACuB,aAAa,CAACC,WAAW,CAACmB,MAAM,CAACC,SAAS,CAAC;IACvD5C,WAAW,CAACuB,aAAa,CAACE,MAAM,CAACkB,MAAM,CAACC,SAAS,CAAC;IAElDA,SAAS,CAACS,SAAS,CAAC,CAAC;EACvB;EAEAY,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;MAC3B,OAAO,IAAI;IACb;IACA,QAAQA,IAAI,CAACR,IAAI;MACf,KAAK,YAAY;QACf,IAAI,CAAC/B,SAAS,CAAC,CAAC;QAChB,OAAO,IAAI;MACb;QACE,IAAI,CAACwC,MAAM,GAAG,IAAI,CAAChE,GAAG,CAAC+D,IAAI,CAACR,IAAI,CAAC;QACjC,IAAI,IAAI,CAACS,MAAM,EAAE;UACf,IAAI,CAACA,MAAM,CAACF,SAAS,CAACC,IAAI,CAAC;QAC7B;QACA,OAAO,IAAI;IACf;EACF;EAEAE,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,IAAI,CAACF,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACC,SAAS,CAACC,IAAI,CAAC;IAC7B;EACF;EAEAC,UAAUA,CAACZ,IAAI,EAAE;IACf,IAAI,IAAI,CAACS,MAAM,EAAE;MACf,IAAI,CAAC,IAAI,CAACA,MAAM,CAACG,UAAU,CAACZ,IAAI,CAAC,EAAE;QACjC,IAAI,CAACS,MAAM,GAAGI,SAAS;MACzB;MACA,OAAO,IAAI;IACb;IACA,QAAQb,IAAI;MACV,KAAK,YAAY;QAAE;UACjB,IAAI,CAAC5B,KAAK,GAAG,CAAC,CAAC;UACf,MAAM0C,GAAG,GAAGA,CAACC,QAAQ,EAAEC,KAAK,KAAK;YAC/B,IAAIA,KAAK,CAAC5C,KAAK,IAAI4C,KAAK,CAAC5C,KAAK,CAACmB,MAAM,EAAE;cACrC,IAAI,CAACnB,KAAK,CAAC2C,QAAQ,CAAC,GAAGC,KAAK,CAAC5C,KAAK;YACpC;UACF,CAAC;UACD0C,GAAG,CAAC,SAAS,EAAE,IAAI,CAACrE,GAAG,CAACC,OAAO,CAAC;UAChCoE,GAAG,CAAC,OAAO,EAAE,IAAI,CAACrE,GAAG,CAACK,KAAK,CAAC;UAC5BgE,GAAG,CAAC,OAAO,EAAE,IAAI,CAACrE,GAAG,CAACO,KAAK,CAAC;UAC5B8D,GAAG,CAAC,SAAS,EAAE,IAAI,CAACrE,GAAG,CAACQ,OAAO,CAAC;UAChC6D,GAAG,CAAC,QAAQ,EAAE,IAAI,CAACrE,GAAG,CAACU,OAAO,CAAC;UAC/B2D,GAAG,CAAC,MAAM,EAAE,IAAI,CAACrE,GAAG,CAACY,IAAI,CAAC;;UAE1B;UACA,IAAI,CAACa,KAAK,GAAG;YACXE,KAAK,EAAE,EAAE;YACTb,MAAM,EAAE;UACV,CAAC;UACD,IAAI,IAAI,CAACa,KAAK,CAAC1B,OAAO,EAAE;YACtB,MAAMuE,WAAW,GAAG,IAAI,CAAC/C,KAAK,CAACX,MAAM;YACrC,IAAI,CAACa,KAAK,CAAC1B,OAAO,CAAC8C,OAAO,CAACjC,MAAM,IAAI;cACnC0D,WAAW,CAAC1D,MAAM,CAAC2D,EAAE,CAAC,GAAG3D,MAAM,CAAC4D,UAAU;YAC5C,CAAC,CAAC;UACJ;UAEA,OAAO,KAAK;QACd;MACA;QACE;QACA,OAAO,IAAI;IACf;EACF;;EAEA;EACA;EACA;EACAC,aAAaA,CAAChD,KAAK,EAAEiD,QAAQ,EAAE;IAC7B,IAAI,CAACjD,KAAK,EAAE;MACV,OAAO,CAAC;IACV;;IAEA;IACA,IAAI,CAAC,IAAI,CAACA,KAAK,CAACtB,KAAK,CAACyC,MAAM,EAAE;MAC5B;MACA,IAAI,CAACK,QAAQ,CAAC;QAACC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;UAACC,KAAK,EAAE;QAAC,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAC,CAAC;IAC3F;;IAEA;IACA,IAAI,IAAI,CAACnB,OAAO,IAAI,IAAI,CAACA,OAAO,CAACuC,GAAG,CAAClD,KAAK,CAAC,EAAE;MAC3C,OAAO,IAAI,CAACW,OAAO,CAACwC,GAAG,CAACnD,KAAK,CAAC;IAChC;IAEA,MAAMT,KAAK,GAAG,CAAC,CAAC;IAChB0D,QAAQ,GAAGA,QAAQ,IAAI5F,KAAK,CAAC+F,SAAS,CAACC,MAAM;IAE7C,IAAIrD,KAAK,CAACb,MAAM,EAAE;MAChBI,KAAK,CAACa,QAAQ,GAAG,IAAI,CAACkD,aAAa,CAACtD,KAAK,CAACb,MAAM,CAAC;IACnD,CAAC,MAAM;MACL,QAAQ8D,QAAQ;QACd,KAAK5F,KAAK,CAAC+F,SAAS,CAACC,MAAM;UACzB9D,KAAK,CAACa,QAAQ,GAAG,IAAI,CAACkD,aAAa,CAAC,SAAS,CAAC;UAC9C;QACF,KAAKjG,KAAK,CAAC+F,SAAS,CAACG,IAAI;UACvBhE,KAAK,CAACa,QAAQ,GAAG,IAAI,CAACkD,aAAa,CAAC,UAAU,CAAC;UAC/C;QACF;UACE;MACJ;IACF;IAEA,IAAItD,KAAK,CAACZ,IAAI,EAAE;MACdG,KAAK,CAACc,MAAM,GAAG,IAAI,CAACmB,QAAQ,CAACxB,KAAK,CAACZ,IAAI,CAAC;IAC1C;IAEA,IAAIY,KAAK,CAACV,MAAM,EAAE;MAChBC,KAAK,CAACgB,QAAQ,GAAG,IAAI,CAACL,UAAU,CAACF,KAAK,CAACV,MAAM,CAAC;IAChD;IAEA,IAAIU,KAAK,CAACX,IAAI,EAAE;MACdE,KAAK,CAACe,MAAM,GAAG,IAAI,CAACE,QAAQ,CAACR,KAAK,CAACX,IAAI,CAAC;IAC1C;IAEA,IAAIW,KAAK,CAACwD,SAAS,EAAE;MACnBjE,KAAK,CAACiE,SAAS,GAAGxD,KAAK,CAACwD,SAAS;IACnC;IAEA,IAAIxD,KAAK,CAACyD,UAAU,EAAE;MACpBlE,KAAK,CAACkE,UAAU,GAAGzD,KAAK,CAACyD,UAAU;IACrC;IAEA,MAAMC,OAAO,GAAG,IAAI,CAACvD,SAAS,CAACZ,KAAK,CAAC;IACrC,IAAI,IAAI,CAACoB,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACgD,GAAG,CAAC3D,KAAK,EAAE0D,OAAO,CAAC;IAClC;IACA,OAAOA,OAAO;EAChB;;EAEA;EACA;EACAE,aAAaA,CAACd,EAAE,EAAE;IAChB;IACA,MAAMvD,KAAK,GAAG,IAAI,CAACS,KAAK,CAACC,MAAM,CAAC6C,EAAE,CAAC;IACnC,IAAI,CAACvD,KAAK,EAAE,OAAO,IAAI;;IAEvB;IACA,IAAIS,KAAK,GAAG,IAAI,CAACF,KAAK,CAACE,KAAK,CAAC8C,EAAE,CAAC;IAChC,IAAI9C,KAAK,EAAE,OAAOA,KAAK;;IAEvB;IACAA,KAAK,GAAG,IAAI,CAACF,KAAK,CAACE,KAAK,CAAC8C,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEjC;IACA;IACA,IAAIvD,KAAK,CAACa,QAAQ,EAAE;MAClB,MAAMjB,MAAM,GAAG,IAAI,CAACW,KAAK,CAACX,MAAM,CAACI,KAAK,CAACa,QAAQ,CAAC,IAAItC,WAAW,CAAC+F,iBAAiB,CAACtE,KAAK,CAACa,QAAQ,CAAC;MACjG,IAAIjB,MAAM,EAAE;QACVa,KAAK,CAACb,MAAM,GAAGA,MAAM;MACvB;IACF;IAEA,SAAS2E,QAAQA,CAAClC,IAAI,EAAEmC,KAAK,EAAEL,OAAO,EAAE;MACtC,IAAIA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;QAC5B,MAAMM,IAAI,GAAGD,KAAK,CAACL,OAAO,CAAC;QAC3B,IAAIM,IAAI,EAAE;UACRhE,KAAK,CAAC4B,IAAI,CAAC,GAAGoC,IAAI;QACpB;MACF;IACF;IAEAF,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9D,KAAK,CAACtB,KAAK,EAAEa,KAAK,CAACc,MAAM,CAAC;IAChDyD,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC9D,KAAK,CAACnB,OAAO,EAAEU,KAAK,CAACgB,QAAQ,CAAC;IACtDuD,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC9D,KAAK,CAACpB,KAAK,EAAEW,KAAK,CAACe,MAAM,CAAC;;IAEhD;IACA;IACA,IAAIf,KAAK,CAACiE,SAAS,EAAE;MACnBxD,KAAK,CAACwD,SAAS,GAAGjE,KAAK,CAACiE,SAAS;IACnC;;IAEA;IACA;IACA,IAAIjE,KAAK,CAACkE,UAAU,EAAE;MACpBzD,KAAK,CAACyD,UAAU,GAAGlE,KAAK,CAACkE,UAAU;IACrC;IAEA,OAAOzD,KAAK;EACd;EAEAiE,WAAWA,CAAC1E,KAAK,EAAE;IACjB,IAAIA,KAAK,CAACJ,MAAM,EAAE;MAChB;MACAI,KAAK,CAACa,QAAQ,GAAG,IAAI,CAACkD,aAAa,CAAC/D,KAAK,CAACJ,MAAM,CAAC;IACnD;IAEA,IAAI,CAACa,KAAK,CAACf,IAAI,CAACiF,IAAI,CAAC3E,KAAK,CAAC;IAC3B,OAAO,IAAI,CAACS,KAAK,CAACf,IAAI,CAACkC,MAAM,GAAG,CAAC;EACnC;EAEAgD,WAAWA,CAACrB,EAAE,EAAE;IACd,OAAO,IAAI,CAAC9C,KAAK,CAACf,IAAI,CAAC6D,EAAE,CAAC;EAC5B;;EAEA;EACA;EACA3C,SAASA,CAACZ,KAAK,EAAE;IACf,MAAM6E,GAAG,GAAG,IAAI,CAAC/F,GAAG,CAACkB,KAAK,CAAC8E,KAAK,CAAC9E,KAAK,CAAC;IACvC,IAAIO,KAAK,GAAG,IAAI,CAACA,KAAK,CAACP,KAAK,CAAC6E,GAAG,CAAC;IACjC,IAAItE,KAAK,KAAK2C,SAAS,EAAE;MACvB3C,KAAK,GAAG,IAAI,CAACA,KAAK,CAACP,KAAK,CAAC6E,GAAG,CAAC,GAAG,IAAI,CAACpE,KAAK,CAACC,MAAM,CAACkB,MAAM;MACxD,IAAI,CAACnB,KAAK,CAACC,MAAM,CAACiE,IAAI,CAACE,GAAG,CAAC;IAC7B;IACA,OAAOtE,KAAK;EACd;;EAEA;EACA;EACAwD,aAAaA,CAACP,UAAU,EAAE;IACxB;IACA,IAAIjD,KAAK,GAAGhC,WAAW,CAACwG,eAAe,CAACvB,UAAU,CAAC;IACnD,IAAIjD,KAAK,KAAK2C,SAAS,EAAE,OAAO3C,KAAK;;IAErC;IACAA,KAAK,GAAG,IAAI,CAACA,KAAK,CAACX,MAAM,CAAC4D,UAAU,CAAC;IACrC,IAAIjD,KAAK,KAAK2C,SAAS,EAAE,OAAO3C,KAAK;IAErCA,KAAK,GAAG,IAAI,CAACA,KAAK,CAACX,MAAM,CAAC4D,UAAU,CAAC,GAAG9E,WAAW,GAAG,IAAI,CAAC+B,KAAK,CAAC1B,OAAO,CAAC6C,MAAM;IAC/E,MAAMiD,GAAG,GAAG,IAAI,CAAC/F,GAAG,CAACc,MAAM,CAACkF,KAAK,CAAC;MAACvB,EAAE,EAAEhD,KAAK;MAAEiD;IAAU,CAAC,CAAC;IAC1D,IAAI,CAAC/C,KAAK,CAAC1B,OAAO,CAAC4F,IAAI,CAACE,GAAG,CAAC;IAC5B,OAAOtE,KAAK;EACd;;EAEA;EACA;EACA0B,QAAQA,CAACpC,IAAI,EAAE;IACb,MAAMgF,GAAG,GAAG,IAAI,CAAC/F,GAAG,CAACe,IAAI,CAACiF,KAAK,CAACjF,IAAI,CAAC;IACrC,IAAIU,KAAK,GAAG,IAAI,CAACA,KAAK,CAACV,IAAI,CAACgF,GAAG,CAAC;IAChC,IAAItE,KAAK,KAAK2C,SAAS,EAAE;MACvB3C,KAAK,GAAG,IAAI,CAACA,KAAK,CAACV,IAAI,CAACgF,GAAG,CAAC,GAAG,IAAI,CAACpE,KAAK,CAACtB,KAAK,CAACyC,MAAM;MACtD,IAAI,CAACnB,KAAK,CAACtB,KAAK,CAACwF,IAAI,CAACE,GAAG,CAAC;IAC5B;IACA,OAAOtE,KAAK;EACd;;EAEA;EACA;EACAI,UAAUA,CAACZ,MAAM,EAAE;IACjB,MAAM8E,GAAG,GAAG,IAAI,CAAC/F,GAAG,CAACiB,MAAM,CAAC+E,KAAK,CAAC/E,MAAM,CAAC;IACzC,IAAIQ,KAAK,GAAG,IAAI,CAACA,KAAK,CAACR,MAAM,CAAC8E,GAAG,CAAC;IAClC,IAAItE,KAAK,KAAK2C,SAAS,EAAE;MACvB3C,KAAK,GAAG,IAAI,CAACA,KAAK,CAACR,MAAM,CAAC8E,GAAG,CAAC,GAAG,IAAI,CAACpE,KAAK,CAACnB,OAAO,CAACsC,MAAM;MAC1D,IAAI,CAACnB,KAAK,CAACnB,OAAO,CAACqF,IAAI,CAACE,GAAG,CAAC;IAC9B;IACA,OAAOtE,KAAK;EACd;;EAEA;EACA;EACAU,QAAQA,CAACnB,IAAI,EAAE;IACb,MAAM+E,GAAG,GAAG,IAAI,CAAC/F,GAAG,CAACgB,IAAI,CAACgF,KAAK,CAAChF,IAAI,CAAC;IACrC,IAAIS,KAAK,GAAG,IAAI,CAACA,KAAK,CAACT,IAAI,CAAC+E,GAAG,CAAC;IAChC,IAAItE,KAAK,KAAK2C,SAAS,EAAE;MACvB3C,KAAK,GAAG,IAAI,CAACA,KAAK,CAACT,IAAI,CAAC+E,GAAG,CAAC,GAAG,IAAI,CAACpE,KAAK,CAACpB,KAAK,CAACuC,MAAM;MACtD,IAAI,CAACnB,KAAK,CAACpB,KAAK,CAACsF,IAAI,CAACE,GAAG,CAAC;IAC5B;IACA,OAAOtE,KAAK;EACd;;EAEA;AACF;;AAEA5B,WAAW,CAACgD,qBAAqB,GAAG;EAClCqD,KAAK,EAAE,2DAA2D;EAClE,UAAU,EAAE,6DAA6D;EACzE,cAAc,EAAE,aAAa;EAC7B,aAAa,EAAE,6DAA6D;EAC5E,aAAa,EAAE;AACjB,CAAC;AACDrG,WAAW,CAACuB,aAAa,GAAG;EAC1BD,UAAU,EAAE,IAAI/B,WAAW,CAAC;IAC1Bc,GAAG,EAAE,YAAY;IACjBI,CAAC,EAAE;MAACH,KAAK,EAAE;IAAC,CAAC;IACbgG,CAAC,EAAE,CAAC;MAACjG,GAAG,EAAE,WAAW;MAAEI,CAAC,EAAE;QAACiD,IAAI,EAAE,QAAQ;QAAE5C,IAAI,EAAE,CAAC;QAAEyF,SAAS,EAAE;MAAC;IAAC,CAAC;EACpE,CAAC,CAAC;EACFxF,IAAI,EAAE,IAAIxB,WAAW,CAAC;IAACc,GAAG,EAAE,MAAM;IAAEI,CAAC,EAAE;MAACH,KAAK,EAAE;IAAC;EAAC,CAAC,CAAC;EACnDkB,WAAW,EAAE,IAAIjC,WAAW,CAAC;IAC3Bc,GAAG,EAAE,aAAa;IAClBI,CAAC,EAAE;MAACH,KAAK,EAAE,CAAC;MAAEkG,iBAAiB,EAAE,mBAAmB;MAAEC,iBAAiB,EAAE;IAAmB;EAC9F,CAAC,CAAC;EACFhF,MAAM,EAAE,IAAIlC,WAAW,CAAC;IACtBc,GAAG,EAAE,QAAQ;IACbiG,CAAC,EAAE,CACD;MACEjG,GAAG,EAAE,KAAK;MACVI,CAAC,EAAE;QACDiG,GAAG,EAAE,wCAAwC;QAC7C,WAAW,EAAE;MACf,CAAC;MACDJ,CAAC,EAAE,CAAC;QAACjG,GAAG,EAAE,kBAAkB;QAAEI,CAAC,EAAE;UAACkG,kBAAkB,EAAE;QAAmB;MAAC,CAAC;IAC7E,CAAC,EACD;MACEtG,GAAG,EAAE,KAAK;MACVI,CAAC,EAAE;QACDiG,GAAG,EAAE,wCAAwC;QAC7C,WAAW,EAAE;MACf,CAAC;MACDJ,CAAC,EAAE,CAAC;QAACjG,GAAG,EAAE,oBAAoB;QAAEI,CAAC,EAAE;UAACmG,oBAAoB,EAAE;QAAuB;MAAC,CAAC;IACrF,CAAC;EAEL,CAAC;AACH,CAAC;;AAED;AACA,MAAMC,eAAe,SAAS7G,WAAW,CAAC;EACxCC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAAC6B,KAAK,GAAG;MACXC,MAAM,EAAE,CAAC;QAACG,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC;QAAEvB,IAAI,EAAE;MAAC,CAAC,CAAC;MACnEV,OAAO,EAAE,EAAE;MACXI,KAAK,EAAE,CAAC;QAAC+C,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE;UAACC,KAAK,EAAE;QAAC,CAAC;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAC,CAAC;MACnFjD,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;MACbD,KAAK,EAAE,CACL;QAAC6B,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAM,CAAC,EAClC;QAACD,IAAI,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,CAAC;IAEzC,CAAC;EACH;;EAEA;EACA;;EAEA;EACAsE,WAAWA,CAACC,MAAM,EAAE;IAClBA,MAAM,CAACC,SAAS,CAAC,CAAC;IAClB,OAAOC,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;EACA;EACA;EACApC,aAAaA,CAAChD,KAAK,EAAEiD,QAAQ,EAAE;IAC7B,QAAQA,QAAQ;MACd,KAAK5F,KAAK,CAAC+F,SAAS,CAACG,IAAI;QACvB,OAAO,IAAI,CAAC8B,WAAW;MACzB;QACE,OAAO,CAAC;IACZ;EACF;EAEA,IAAIA,WAAWA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACtB,MAAMC,SAAS,GAAG;QAChBnF,QAAQ,EAAEtC,WAAW,CAACwG,eAAe,CAAC,UAAU;MAClD,CAAC;MACD,IAAI,CAACgB,YAAY,GAAG,IAAI,CAACtF,KAAK,CAACC,MAAM,CAACkB,MAAM;MAC5C,IAAI,CAACnB,KAAK,CAACC,MAAM,CAACiE,IAAI,CAACqB,SAAS,CAAC;IACnC;IACA,OAAO,IAAI,CAACD,YAAY;EAC1B;;EAEA;EACA;EACA1B,aAAaA,CAAA,CAAC;EAAA,EAAU;IACtB,OAAO,CAAC,CAAC;EACX;AACF;AAEA1F,WAAW,CAACsH,IAAI,GAAGT,eAAe;AAElCU,MAAM,CAACC,OAAO,GAAGxH,WAAW"}