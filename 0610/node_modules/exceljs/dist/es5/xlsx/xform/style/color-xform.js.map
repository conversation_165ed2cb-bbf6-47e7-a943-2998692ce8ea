{"version": 3, "file": "color-xform.js", "names": ["BaseXform", "require", "ColorXform", "constructor", "name", "tag", "render", "xmlStream", "model", "openNode", "argb", "addAttribute", "theme", "undefined", "tint", "indexed", "closeNode", "parseOpen", "node", "attributes", "rgb", "parseInt", "parseFloat", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/style/color-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\n// Color encapsulates translation from color model to/from xlsx\nclass ColorXform extends BaseXform {\n  constructor(name) {\n    super();\n\n    // this.name controls the xm node name\n    this.name = name || 'color';\n  }\n\n  get tag() {\n    return this.name;\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.openNode(this.name);\n      if (model.argb) {\n        xmlStream.addAttribute('rgb', model.argb);\n      } else if (model.theme !== undefined) {\n        xmlStream.addAttribute('theme', model.theme);\n        if (model.tint !== undefined) {\n          xmlStream.addAttribute('tint', model.tint);\n        }\n      } else if (model.indexed !== undefined) {\n        xmlStream.addAttribute('indexed', model.indexed);\n      } else {\n        xmlStream.addAttribute('auto', '1');\n      }\n      xmlStream.closeNode();\n      return true;\n    }\n    return false;\n  }\n\n  parseOpen(node) {\n    if (node.name === this.name) {\n      if (node.attributes.rgb) {\n        this.model = {argb: node.attributes.rgb};\n      } else if (node.attributes.theme) {\n        this.model = {theme: parseInt(node.attributes.theme, 10)};\n        if (node.attributes.tint) {\n          this.model.tint = parseFloat(node.attributes.tint);\n        }\n      } else if (node.attributes.indexed) {\n        this.model = {indexed: parseInt(node.attributes.indexed, 10)};\n      } else {\n        this.model = undefined;\n      }\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = ColorXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA,MAAMC,UAAU,SAASF,SAAS,CAAC;EACjCG,WAAWA,CAACC,IAAI,EAAE;IAChB,KAAK,CAAC,CAAC;;IAEP;IACA,IAAI,CAACA,IAAI,GAAGA,IAAI,IAAI,OAAO;EAC7B;EAEA,IAAIC,GAAGA,CAAA,EAAG;IACR,OAAO,IAAI,CAACD,IAAI;EAClB;EAEAE,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACL,IAAI,CAAC;MAC7B,IAAII,KAAK,CAACE,IAAI,EAAE;QACdH,SAAS,CAACI,YAAY,CAAC,KAAK,EAAEH,KAAK,CAACE,IAAI,CAAC;MAC3C,CAAC,MAAM,IAAIF,KAAK,CAACI,KAAK,KAAKC,SAAS,EAAE;QACpCN,SAAS,CAACI,YAAY,CAAC,OAAO,EAAEH,KAAK,CAACI,KAAK,CAAC;QAC5C,IAAIJ,KAAK,CAACM,IAAI,KAAKD,SAAS,EAAE;UAC5BN,SAAS,CAACI,YAAY,CAAC,MAAM,EAAEH,KAAK,CAACM,IAAI,CAAC;QAC5C;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,KAAKF,SAAS,EAAE;QACtCN,SAAS,CAACI,YAAY,CAAC,SAAS,EAAEH,KAAK,CAACO,OAAO,CAAC;MAClD,CAAC,MAAM;QACLR,SAAS,CAACI,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;MACrC;MACAJ,SAAS,CAACS,SAAS,CAAC,CAAC;MACrB,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACd,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MAC3B,IAAIc,IAAI,CAACC,UAAU,CAACC,GAAG,EAAE;QACvB,IAAI,CAACZ,KAAK,GAAG;UAACE,IAAI,EAAEQ,IAAI,CAACC,UAAU,CAACC;QAAG,CAAC;MAC1C,CAAC,MAAM,IAAIF,IAAI,CAACC,UAAU,CAACP,KAAK,EAAE;QAChC,IAAI,CAACJ,KAAK,GAAG;UAACI,KAAK,EAAES,QAAQ,CAACH,IAAI,CAACC,UAAU,CAACP,KAAK,EAAE,EAAE;QAAC,CAAC;QACzD,IAAIM,IAAI,CAACC,UAAU,CAACL,IAAI,EAAE;UACxB,IAAI,CAACN,KAAK,CAACM,IAAI,GAAGQ,UAAU,CAACJ,IAAI,CAACC,UAAU,CAACL,IAAI,CAAC;QACpD;MACF,CAAC,MAAM,IAAII,IAAI,CAACC,UAAU,CAACJ,OAAO,EAAE;QAClC,IAAI,CAACP,KAAK,GAAG;UAACO,OAAO,EAAEM,QAAQ,CAACH,IAAI,CAACC,UAAU,CAACJ,OAAO,EAAE,EAAE;QAAC,CAAC;MAC/D,CAAC,MAAM;QACL,IAAI,CAACP,KAAK,GAAGK,SAAS;MACxB;MACA,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAU,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGxB,UAAU"}