{"version": 3, "file": "defined-name-xform.js", "names": ["BaseXform", "require", "co<PERSON><PERSON><PERSON>", "DefinedNamesXform", "render", "xmlStream", "model", "openNode", "name", "localSheetId", "writeText", "ranges", "join", "closeNode", "parseOpen", "node", "_parsedName", "attributes", "_parsedLocalSheetId", "_parsedText", "parseText", "text", "push", "parseClose", "extractRanges", "undefined", "parseInt", "isValidRange", "range", "decodeEx", "err", "parsedText", "quotesOpened", "last", "split", "for<PERSON>ach", "item", "quotes", "match", "length", "quotesEven", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/defined-name-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\nconst colCache = require('../../../utils/col-cache');\n\nclass DefinedNamesXform extends BaseXform {\n  render(xmlStream, model) {\n    // <definedNames>\n    //   <definedName name=\"name\">name.ranges.join(',')</definedName>\n    //   <definedName name=\"_xlnm.Print_Area\" localSheetId=\"0\">name.ranges.join(',')</definedName>\n    // </definedNames>\n    xmlStream.openNode('definedName', {\n      name: model.name,\n      localSheetId: model.localSheetId,\n    });\n    xmlStream.writeText(model.ranges.join(','));\n    xmlStream.closeNode();\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case 'definedName':\n        this._parsedName = node.attributes.name;\n        this._parsedLocalSheetId = node.attributes.localSheetId;\n        this._parsedText = [];\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText(text) {\n    this._parsedText.push(text);\n  }\n\n  parseClose() {\n    this.model = {\n      name: this._parsedName,\n      ranges: extractRanges(this._parsedText.join('')),\n    };\n    if (this._parsedLocalSheetId !== undefined) {\n      this.model.localSheetId = parseInt(this._parsedLocalSheetId, 10);\n    }\n    return false;\n  }\n}\n\nfunction isValidRange(range) {\n  try {\n    colCache.decodeEx(range);\n    return true;\n  } catch (err) {\n    return false;\n  }\n}\n\nfunction extractRanges(parsedText) {\n  const ranges = [];\n  let quotesOpened = false;\n  let last = '';\n  parsedText.split(',').forEach(item => {\n    if (!item) {\n      return;\n    }\n    const quotes = (item.match(/'/g) || []).length;\n\n    if (!quotes) {\n      if (quotesOpened) {\n        last += `${item},`;\n      } else if (isValidRange(item)) {\n        ranges.push(item);\n      }\n      return;\n    }\n    const quotesEven = quotes % 2 === 0;\n\n    if (!quotesOpened && quotesEven && isValidRange(item)) {\n      ranges.push(item);\n    } else if (quotesOpened && !quotesEven) {\n      quotesOpened = false;\n      if (isValidRange(last + item)) {\n        ranges.push(last + item);\n      }\n      last = '';\n    } else {\n      quotesOpened = true;\n      last += `${item},`;\n    }\n  });\n  return ranges;\n}\n\nmodule.exports = DefinedNamesXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,MAAMC,QAAQ,GAAGD,OAAO,CAAC,0BAA0B,CAAC;AAEpD,MAAME,iBAAiB,SAASH,SAAS,CAAC;EACxCI,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB;IACA;IACA;IACA;IACAD,SAAS,CAACE,QAAQ,CAAC,aAAa,EAAE;MAChCC,IAAI,EAAEF,KAAK,CAACE,IAAI;MAChBC,YAAY,EAAEH,KAAK,CAACG;IACtB,CAAC,CAAC;IACFJ,SAAS,CAACK,SAAS,CAACJ,KAAK,CAACK,MAAM,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3CP,SAAS,CAACQ,SAAS,CAAC,CAAC;EACvB;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACP,IAAI;MACf,KAAK,aAAa;QAChB,IAAI,CAACQ,WAAW,GAAGD,IAAI,CAACE,UAAU,CAACT,IAAI;QACvC,IAAI,CAACU,mBAAmB,GAAGH,IAAI,CAACE,UAAU,CAACR,YAAY;QACvD,IAAI,CAACU,WAAW,GAAG,EAAE;QACrB,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAC,SAASA,CAACC,IAAI,EAAE;IACd,IAAI,CAACF,WAAW,CAACG,IAAI,CAACD,IAAI,CAAC;EAC7B;EAEAE,UAAUA,CAAA,EAAG;IACX,IAAI,CAACjB,KAAK,GAAG;MACXE,IAAI,EAAE,IAAI,CAACQ,WAAW;MACtBL,MAAM,EAAEa,aAAa,CAAC,IAAI,CAACL,WAAW,CAACP,IAAI,CAAC,EAAE,CAAC;IACjD,CAAC;IACD,IAAI,IAAI,CAACM,mBAAmB,KAAKO,SAAS,EAAE;MAC1C,IAAI,CAACnB,KAAK,CAACG,YAAY,GAAGiB,QAAQ,CAAC,IAAI,CAACR,mBAAmB,EAAE,EAAE,CAAC;IAClE;IACA,OAAO,KAAK;EACd;AACF;AAEA,SAASS,YAAYA,CAACC,KAAK,EAAE;EAC3B,IAAI;IACF1B,QAAQ,CAAC2B,QAAQ,CAACD,KAAK,CAAC;IACxB,OAAO,IAAI;EACb,CAAC,CAAC,OAAOE,GAAG,EAAE;IACZ,OAAO,KAAK;EACd;AACF;AAEA,SAASN,aAAaA,CAACO,UAAU,EAAE;EACjC,MAAMpB,MAAM,GAAG,EAAE;EACjB,IAAIqB,YAAY,GAAG,KAAK;EACxB,IAAIC,IAAI,GAAG,EAAE;EACbF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,IAAI,IAAI;IACpC,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IACA,MAAMC,MAAM,GAAG,CAACD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAEC,MAAM;IAE9C,IAAI,CAACF,MAAM,EAAE;MACX,IAAIL,YAAY,EAAE;QAChBC,IAAI,IAAK,GAAEG,IAAK,GAAE;MACpB,CAAC,MAAM,IAAIT,YAAY,CAACS,IAAI,CAAC,EAAE;QAC7BzB,MAAM,CAACW,IAAI,CAACc,IAAI,CAAC;MACnB;MACA;IACF;IACA,MAAMI,UAAU,GAAGH,MAAM,GAAG,CAAC,KAAK,CAAC;IAEnC,IAAI,CAACL,YAAY,IAAIQ,UAAU,IAAIb,YAAY,CAACS,IAAI,CAAC,EAAE;MACrDzB,MAAM,CAACW,IAAI,CAACc,IAAI,CAAC;IACnB,CAAC,MAAM,IAAIJ,YAAY,IAAI,CAACQ,UAAU,EAAE;MACtCR,YAAY,GAAG,KAAK;MACpB,IAAIL,YAAY,CAACM,IAAI,GAAGG,IAAI,CAAC,EAAE;QAC7BzB,MAAM,CAACW,IAAI,CAACW,IAAI,GAAGG,IAAI,CAAC;MAC1B;MACAH,IAAI,GAAG,EAAE;IACX,CAAC,MAAM;MACLD,YAAY,GAAG,IAAI;MACnBC,IAAI,IAAK,GAAEG,IAAK,GAAE;IACpB;EACF,CAAC,CAAC;EACF,OAAOzB,MAAM;AACf;AAEA8B,MAAM,CAACC,OAAO,GAAGvC,iBAAiB"}