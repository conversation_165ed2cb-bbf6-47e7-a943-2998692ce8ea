{"version": 3, "file": "workbook-view-xform.js", "names": ["BaseXform", "require", "WorkbookViewXform", "render", "xmlStream", "model", "attributes", "xWindow", "x", "yWindow", "y", "windowWidth", "width", "windowHeight", "height", "firstSheet", "activeTab", "visibility", "leafNode", "parseOpen", "node", "name", "addS", "value", "dflt", "s", "undefined", "addN", "n", "parseInt", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/book/workbook-view-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass WorkbookViewXform extends BaseXform {\n  render(xmlStream, model) {\n    const attributes = {\n      xWindow: model.x || 0,\n      yWindow: model.y || 0,\n      windowWidth: model.width || 12000,\n      windowHeight: model.height || 24000,\n      firstSheet: model.firstSheet,\n      activeTab: model.activeTab,\n    };\n    if (model.visibility && model.visibility !== 'visible') {\n      attributes.visibility = model.visibility;\n    }\n    xmlStream.leafNode('workbookView', attributes);\n  }\n\n  parseOpen(node) {\n    if (node.name === 'workbookView') {\n      const model = (this.model = {});\n      const addS = function(name, value, dflt) {\n        const s = value !== undefined ? (model[name] = value) : dflt;\n        if (s !== undefined) {\n          model[name] = s;\n        }\n      };\n      const addN = function(name, value, dflt) {\n        const n = value !== undefined ? (model[name] = parseInt(value, 10)) : dflt;\n        if (n !== undefined) {\n          model[name] = n;\n        }\n      };\n      addN('x', node.attributes.xWindow, 0);\n      addN('y', node.attributes.yWindow, 0);\n      addN('width', node.attributes.windowWidth, 25000);\n      addN('height', node.attributes.windowHeight, 10000);\n      addS('visibility', node.attributes.visibility, 'visible');\n      addN('activeTab', node.attributes.activeTab, undefined);\n      addN('firstSheet', node.attributes.firstSheet, undefined);\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = WorkbookViewXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,iBAAiB,SAASF,SAAS,CAAC;EACxCG,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,MAAMC,UAAU,GAAG;MACjBC,OAAO,EAAEF,KAAK,CAACG,CAAC,IAAI,CAAC;MACrBC,OAAO,EAAEJ,KAAK,CAACK,CAAC,IAAI,CAAC;MACrBC,WAAW,EAAEN,KAAK,CAACO,KAAK,IAAI,KAAK;MACjCC,YAAY,EAAER,KAAK,CAACS,MAAM,IAAI,KAAK;MACnCC,UAAU,EAAEV,KAAK,CAACU,UAAU;MAC5BC,SAAS,EAAEX,KAAK,CAACW;IACnB,CAAC;IACD,IAAIX,KAAK,CAACY,UAAU,IAAIZ,KAAK,CAACY,UAAU,KAAK,SAAS,EAAE;MACtDX,UAAU,CAACW,UAAU,GAAGZ,KAAK,CAACY,UAAU;IAC1C;IACAb,SAAS,CAACc,QAAQ,CAAC,cAAc,EAAEZ,UAAU,CAAC;EAChD;EAEAa,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,cAAc,EAAE;MAChC,MAAMhB,KAAK,GAAI,IAAI,CAACA,KAAK,GAAG,CAAC,CAAE;MAC/B,MAAMiB,IAAI,GAAG,SAAAA,CAASD,IAAI,EAAEE,KAAK,EAAEC,IAAI,EAAE;QACvC,MAAMC,CAAC,GAAGF,KAAK,KAAKG,SAAS,GAAIrB,KAAK,CAACgB,IAAI,CAAC,GAAGE,KAAK,GAAIC,IAAI;QAC5D,IAAIC,CAAC,KAAKC,SAAS,EAAE;UACnBrB,KAAK,CAACgB,IAAI,CAAC,GAAGI,CAAC;QACjB;MACF,CAAC;MACD,MAAME,IAAI,GAAG,SAAAA,CAASN,IAAI,EAAEE,KAAK,EAAEC,IAAI,EAAE;QACvC,MAAMI,CAAC,GAAGL,KAAK,KAAKG,SAAS,GAAIrB,KAAK,CAACgB,IAAI,CAAC,GAAGQ,QAAQ,CAACN,KAAK,EAAE,EAAE,CAAC,GAAIC,IAAI;QAC1E,IAAII,CAAC,KAAKF,SAAS,EAAE;UACnBrB,KAAK,CAACgB,IAAI,CAAC,GAAGO,CAAC;QACjB;MACF,CAAC;MACDD,IAAI,CAAC,GAAG,EAAEP,IAAI,CAACd,UAAU,CAACC,OAAO,EAAE,CAAC,CAAC;MACrCoB,IAAI,CAAC,GAAG,EAAEP,IAAI,CAACd,UAAU,CAACG,OAAO,EAAE,CAAC,CAAC;MACrCkB,IAAI,CAAC,OAAO,EAAEP,IAAI,CAACd,UAAU,CAACK,WAAW,EAAE,KAAK,CAAC;MACjDgB,IAAI,CAAC,QAAQ,EAAEP,IAAI,CAACd,UAAU,CAACO,YAAY,EAAE,KAAK,CAAC;MACnDS,IAAI,CAAC,YAAY,EAAEF,IAAI,CAACd,UAAU,CAACW,UAAU,EAAE,SAAS,CAAC;MACzDU,IAAI,CAAC,WAAW,EAAEP,IAAI,CAACd,UAAU,CAACU,SAAS,EAAEU,SAAS,CAAC;MACvDC,IAAI,CAAC,YAAY,EAAEP,IAAI,CAACd,UAAU,CAACS,UAAU,EAAEW,SAAS,CAAC;MACzD,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAI,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG/B,iBAAiB"}