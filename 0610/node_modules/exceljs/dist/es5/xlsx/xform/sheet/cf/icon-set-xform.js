"use strict";

const BaseXform = require('../../base-xform');
const CompositeXform = require('../../composite-xform');
const CfvoXform = require('./cfvo-xform');
class IconSetXform extends CompositeXform {
  constructor() {
    super();
    this.map = {
      cfvo: this.cfvoXform = new CfvoXform()
    };
  }
  get tag() {
    return 'iconSet';
  }
  render(xmlStream, model) {
    xmlStream.openNode(this.tag, {
      iconSet: BaseXform.toStringAttribute(model.iconSet, '3TrafficLights'),
      reverse: BaseXform.toBoolAttribute(model.reverse, false),
      showValue: BaseXform.toBoolAttribute(model.showValue, true)
    });
    model.cfvo.forEach(cfvo => {
      this.cfvoXform.render(xmlStream, cfvo);
    });
    xmlStream.closeNode();
  }
  createNewModel(_ref) {
    let {
      attributes
    } = _ref;
    return {
      iconSet: BaseXform.toStringValue(attributes.iconSet, '3TrafficLights'),
      reverse: BaseXform.toBoolValue(attributes.reverse),
      showValue: BaseXform.toBoolValue(attributes.showValue),
      cfvo: []
    };
  }
  onParserClose(name, parser) {
    this.model[name].push(parser.model);
  }
}
module.exports = IconSetXform;
//# sourceMappingURL=icon-set-xform.js.map
