{"version": 3, "file": "ext-lst-xform.js", "names": ["CompositeXform", "require", "ConditionalFormattingsExt", "ExtXform", "constructor", "map", "conditionalFormattings", "tag", "<PERSON><PERSON><PERSON><PERSON>", "model", "prepare", "options", "render", "xmlStream", "openNode", "uri", "closeNode", "createNewModel", "onParserClose", "name", "parser", "ExtLstXform", "ext", "Object", "assign", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/ext-lst-xform.js"], "sourcesContent": ["/* eslint-disable max-classes-per-file */\nconst CompositeXform = require('../composite-xform');\n\nconst ConditionalFormattingsExt = require('./cf-ext/conditional-formattings-ext-xform');\n\nclass ExtXform extends CompositeXform {\n  constructor() {\n    super();\n    this.map = {\n      'x14:conditionalFormattings': (this.conditionalFormattings = new ConditionalFormattingsExt()),\n    };\n  }\n\n  get tag() {\n    return 'ext';\n  }\n\n  hasContent(model) {\n    return this.conditionalFormattings.hasContent(model.conditionalFormattings);\n  }\n\n  prepare(model, options) {\n    this.conditionalFormattings.prepare(model.conditionalFormattings, options);\n  }\n\n  render(xmlStream, model) {\n    xmlStream.openNode('ext', {\n      uri: '{78C0D931-6437-407d-A8EE-F0AAD7539E65}',\n      'xmlns:x14': 'http://schemas.microsoft.com/office/spreadsheetml/2009/9/main',\n    });\n\n    this.conditionalFormattings.render(xmlStream, model.conditionalFormattings);\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {};\n  }\n\n  onParserClose(name, parser) {\n    this.model[name] = parser.model;\n  }\n}\n\nclass ExtLstXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      ext: (this.ext = new ExtXform()),\n    };\n  }\n\n  get tag() {\n    return 'extLst';\n  }\n\n  prepare(model, options) {\n    this.ext.prepare(model, options);\n  }\n\n  hasContent(model) {\n    return this.ext.hasContent(model);\n  }\n\n  render(xmlStream, model) {\n    if (!this.hasContent(model)) {\n      return;\n    }\n\n    xmlStream.openNode('extLst');\n    this.ext.render(xmlStream, model);\n    xmlStream.closeNode();\n  }\n\n  createNewModel() {\n    return {};\n  }\n\n  onParserClose(name, parser) {\n    Object.assign(this.model, parser.model);\n  }\n}\n\nmodule.exports = ExtLstXform;\n"], "mappings": ";;AAAA;AACA,MAAMA,cAAc,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AAEpD,MAAMC,yBAAyB,GAAGD,OAAO,CAAC,4CAA4C,CAAC;AAEvF,MAAME,QAAQ,SAASH,cAAc,CAAC;EACpCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,GAAG,GAAG;MACT,4BAA4B,EAAG,IAAI,CAACC,sBAAsB,GAAG,IAAIJ,yBAAyB,CAAC;IAC7F,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,KAAK;EACd;EAEAC,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI,CAACH,sBAAsB,CAACE,UAAU,CAACC,KAAK,CAACH,sBAAsB,CAAC;EAC7E;EAEAI,OAAOA,CAACD,KAAK,EAAEE,OAAO,EAAE;IACtB,IAAI,CAACL,sBAAsB,CAACI,OAAO,CAACD,KAAK,CAACH,sBAAsB,EAAEK,OAAO,CAAC;EAC5E;EAEAC,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvBI,SAAS,CAACC,QAAQ,CAAC,KAAK,EAAE;MACxBC,GAAG,EAAE,wCAAwC;MAC7C,WAAW,EAAE;IACf,CAAC,CAAC;IAEF,IAAI,CAACT,sBAAsB,CAACM,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAACH,sBAAsB,CAAC;IAE3EO,SAAS,CAACG,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC;EACX;EAEAC,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACX,KAAK,CAACU,IAAI,CAAC,GAAGC,MAAM,CAACX,KAAK;EACjC;AACF;AAEA,MAAMY,WAAW,SAASrB,cAAc,CAAC;EACvCI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTiB,GAAG,EAAG,IAAI,CAACA,GAAG,GAAG,IAAInB,QAAQ,CAAC;IAChC,CAAC;EACH;EAEA,IAAII,GAAGA,CAAA,EAAG;IACR,OAAO,QAAQ;EACjB;EAEAG,OAAOA,CAACD,KAAK,EAAEE,OAAO,EAAE;IACtB,IAAI,CAACW,GAAG,CAACZ,OAAO,CAACD,KAAK,EAAEE,OAAO,CAAC;EAClC;EAEAH,UAAUA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI,CAACa,GAAG,CAACd,UAAU,CAACC,KAAK,CAAC;EACnC;EAEAG,MAAMA,CAACC,SAAS,EAAEJ,KAAK,EAAE;IACvB,IAAI,CAAC,IAAI,CAACD,UAAU,CAACC,KAAK,CAAC,EAAE;MAC3B;IACF;IAEAI,SAAS,CAACC,QAAQ,CAAC,QAAQ,CAAC;IAC5B,IAAI,CAACQ,GAAG,CAACV,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAC;IACjCI,SAAS,CAACG,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAA,EAAG;IACf,OAAO,CAAC,CAAC;EACX;EAEAC,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1BG,MAAM,CAACC,MAAM,CAAC,IAAI,CAACf,KAAK,EAAEW,MAAM,CAACX,KAAK,CAAC;EACzC;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAGL,WAAW"}