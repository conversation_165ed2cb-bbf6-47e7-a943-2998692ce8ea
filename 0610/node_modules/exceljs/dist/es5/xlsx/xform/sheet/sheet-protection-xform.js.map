{"version": 3, "file": "sheet-protection-xform.js", "names": ["_", "require", "BaseXform", "booleanToXml", "model", "value", "undefined", "xmlToBoolean", "equals", "SheetProtectionXform", "tag", "render", "xmlStream", "attributes", "sheet", "selectLockedCells", "selectUnlockedCells", "formatCells", "formatColumns", "formatRows", "insertColumns", "insertRows", "insertHyperlinks", "deleteColumns", "deleteRows", "sort", "autoFilter", "pivotTables", "algorithmName", "hashValue", "saltValue", "spinCount", "objects", "scenarios", "some", "leafNode", "parseOpen", "node", "name", "parseInt", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/sheet-protection-xform.js"], "sourcesContent": ["const _ = require('../../../utils/under-dash');\nconst BaseXform = require('../base-xform');\n\nfunction booleanToXml(model, value) {\n  return model ? value : undefined;\n}\n\nfunction xmlToBoolean(value, equals) {\n  return value === equals ? true : undefined;\n}\n\nclass SheetProtectionXform extends BaseXform {\n  get tag() {\n    return 'sheetProtection';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      const attributes = {\n        sheet: booleanToXml(model.sheet, '1'),\n        selectLockedCells: model.selectLockedCells === false ? '1' : undefined,\n        selectUnlockedCells: model.selectUnlockedCells === false ? '1' : undefined,\n        formatCells: booleanToXml(model.formatCells, '0'),\n        formatColumns: booleanToXml(model.formatColumns, '0'),\n        formatRows: booleanToXml(model.formatRows, '0'),\n        insertColumns: booleanToXml(model.insertColumns, '0'),\n        insertRows: booleanToXml(model.insertRows, '0'),\n        insertHyperlinks: booleanToXml(model.insertHyperlinks, '0'),\n        deleteColumns: booleanToXml(model.deleteColumns, '0'),\n        deleteRows: booleanToXml(model.deleteRows, '0'),\n        sort: booleanToXml(model.sort, '0'),\n        autoFilter: booleanToXml(model.autoFilter, '0'),\n        pivotTables: booleanToXml(model.pivotTables, '0'),\n      };\n      if (model.sheet) {\n        attributes.algorithmName = model.algorithmName;\n        attributes.hashValue = model.hashValue;\n        attributes.saltValue = model.saltValue;\n        attributes.spinCount = model.spinCount;\n        attributes.objects = booleanToXml(model.objects === false, '1');\n        attributes.scenarios = booleanToXml(model.scenarios === false, '1');\n      }\n      if (_.some(attributes, value => value !== undefined)) {\n        xmlStream.leafNode(this.tag, attributes);\n      }\n    }\n  }\n\n  parseOpen(node) {\n    switch (node.name) {\n      case this.tag:\n        this.model = {\n          sheet: xmlToBoolean(node.attributes.sheet, '1'),\n          objects: node.attributes.objects === '1' ? false : undefined,\n          scenarios: node.attributes.scenarios === '1' ? false : undefined,\n          selectLockedCells: node.attributes.selectLockedCells === '1' ? false : undefined,\n          selectUnlockedCells: node.attributes.selectUnlockedCells === '1' ? false : undefined,\n          formatCells: xmlToBoolean(node.attributes.formatCells, '0'),\n          formatColumns: xmlToBoolean(node.attributes.formatColumns, '0'),\n          formatRows: xmlToBoolean(node.attributes.formatRows, '0'),\n          insertColumns: xmlToBoolean(node.attributes.insertColumns, '0'),\n          insertRows: xmlToBoolean(node.attributes.insertRows, '0'),\n          insertHyperlinks: xmlToBoolean(node.attributes.insertHyperlinks, '0'),\n          deleteColumns: xmlToBoolean(node.attributes.deleteColumns, '0'),\n          deleteRows: xmlToBoolean(node.attributes.deleteRows, '0'),\n          sort: xmlToBoolean(node.attributes.sort, '0'),\n          autoFilter: xmlToBoolean(node.attributes.autoFilter, '0'),\n          pivotTables: xmlToBoolean(node.attributes.pivotTables, '0'),\n        };\n        if (node.attributes.algorithmName) {\n          this.model.algorithmName = node.attributes.algorithmName;\n          this.model.hashValue = node.attributes.hashValue;\n          this.model.saltValue = node.attributes.saltValue;\n          this.model.spinCount = parseInt(node.attributes.spinCount, 10);\n        }\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = SheetProtectionXform;\n"], "mappings": ";;AAAA,MAAMA,CAAC,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC9C,MAAMC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAE1C,SAASE,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,OAAOD,KAAK,GAAGC,KAAK,GAAGC,SAAS;AAClC;AAEA,SAASC,YAAYA,CAACF,KAAK,EAAEG,MAAM,EAAE;EACnC,OAAOH,KAAK,KAAKG,MAAM,GAAG,IAAI,GAAGF,SAAS;AAC5C;AAEA,MAAMG,oBAAoB,SAASP,SAAS,CAAC;EAC3C,IAAIQ,GAAGA,CAAA,EAAG;IACR,OAAO,iBAAiB;EAC1B;EAEAC,MAAMA,CAACC,SAAS,EAAER,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACT,MAAMS,UAAU,GAAG;QACjBC,KAAK,EAAEX,YAAY,CAACC,KAAK,CAACU,KAAK,EAAE,GAAG,CAAC;QACrCC,iBAAiB,EAAEX,KAAK,CAACW,iBAAiB,KAAK,KAAK,GAAG,GAAG,GAAGT,SAAS;QACtEU,mBAAmB,EAAEZ,KAAK,CAACY,mBAAmB,KAAK,KAAK,GAAG,GAAG,GAAGV,SAAS;QAC1EW,WAAW,EAAEd,YAAY,CAACC,KAAK,CAACa,WAAW,EAAE,GAAG,CAAC;QACjDC,aAAa,EAAEf,YAAY,CAACC,KAAK,CAACc,aAAa,EAAE,GAAG,CAAC;QACrDC,UAAU,EAAEhB,YAAY,CAACC,KAAK,CAACe,UAAU,EAAE,GAAG,CAAC;QAC/CC,aAAa,EAAEjB,YAAY,CAACC,KAAK,CAACgB,aAAa,EAAE,GAAG,CAAC;QACrDC,UAAU,EAAElB,YAAY,CAACC,KAAK,CAACiB,UAAU,EAAE,GAAG,CAAC;QAC/CC,gBAAgB,EAAEnB,YAAY,CAACC,KAAK,CAACkB,gBAAgB,EAAE,GAAG,CAAC;QAC3DC,aAAa,EAAEpB,YAAY,CAACC,KAAK,CAACmB,aAAa,EAAE,GAAG,CAAC;QACrDC,UAAU,EAAErB,YAAY,CAACC,KAAK,CAACoB,UAAU,EAAE,GAAG,CAAC;QAC/CC,IAAI,EAAEtB,YAAY,CAACC,KAAK,CAACqB,IAAI,EAAE,GAAG,CAAC;QACnCC,UAAU,EAAEvB,YAAY,CAACC,KAAK,CAACsB,UAAU,EAAE,GAAG,CAAC;QAC/CC,WAAW,EAAExB,YAAY,CAACC,KAAK,CAACuB,WAAW,EAAE,GAAG;MAClD,CAAC;MACD,IAAIvB,KAAK,CAACU,KAAK,EAAE;QACfD,UAAU,CAACe,aAAa,GAAGxB,KAAK,CAACwB,aAAa;QAC9Cf,UAAU,CAACgB,SAAS,GAAGzB,KAAK,CAACyB,SAAS;QACtChB,UAAU,CAACiB,SAAS,GAAG1B,KAAK,CAAC0B,SAAS;QACtCjB,UAAU,CAACkB,SAAS,GAAG3B,KAAK,CAAC2B,SAAS;QACtClB,UAAU,CAACmB,OAAO,GAAG7B,YAAY,CAACC,KAAK,CAAC4B,OAAO,KAAK,KAAK,EAAE,GAAG,CAAC;QAC/DnB,UAAU,CAACoB,SAAS,GAAG9B,YAAY,CAACC,KAAK,CAAC6B,SAAS,KAAK,KAAK,EAAE,GAAG,CAAC;MACrE;MACA,IAAIjC,CAAC,CAACkC,IAAI,CAACrB,UAAU,EAAER,KAAK,IAAIA,KAAK,KAAKC,SAAS,CAAC,EAAE;QACpDM,SAAS,CAACuB,QAAQ,CAAC,IAAI,CAACzB,GAAG,EAAEG,UAAU,CAAC;MAC1C;IACF;EACF;EAEAuB,SAASA,CAACC,IAAI,EAAE;IACd,QAAQA,IAAI,CAACC,IAAI;MACf,KAAK,IAAI,CAAC5B,GAAG;QACX,IAAI,CAACN,KAAK,GAAG;UACXU,KAAK,EAAEP,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACC,KAAK,EAAE,GAAG,CAAC;UAC/CkB,OAAO,EAAEK,IAAI,CAACxB,UAAU,CAACmB,OAAO,KAAK,GAAG,GAAG,KAAK,GAAG1B,SAAS;UAC5D2B,SAAS,EAAEI,IAAI,CAACxB,UAAU,CAACoB,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG3B,SAAS;UAChES,iBAAiB,EAAEsB,IAAI,CAACxB,UAAU,CAACE,iBAAiB,KAAK,GAAG,GAAG,KAAK,GAAGT,SAAS;UAChFU,mBAAmB,EAAEqB,IAAI,CAACxB,UAAU,CAACG,mBAAmB,KAAK,GAAG,GAAG,KAAK,GAAGV,SAAS;UACpFW,WAAW,EAAEV,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACI,WAAW,EAAE,GAAG,CAAC;UAC3DC,aAAa,EAAEX,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACK,aAAa,EAAE,GAAG,CAAC;UAC/DC,UAAU,EAAEZ,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACM,UAAU,EAAE,GAAG,CAAC;UACzDC,aAAa,EAAEb,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACO,aAAa,EAAE,GAAG,CAAC;UAC/DC,UAAU,EAAEd,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACQ,UAAU,EAAE,GAAG,CAAC;UACzDC,gBAAgB,EAAEf,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACS,gBAAgB,EAAE,GAAG,CAAC;UACrEC,aAAa,EAAEhB,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACU,aAAa,EAAE,GAAG,CAAC;UAC/DC,UAAU,EAAEjB,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACW,UAAU,EAAE,GAAG,CAAC;UACzDC,IAAI,EAAElB,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACY,IAAI,EAAE,GAAG,CAAC;UAC7CC,UAAU,EAAEnB,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACa,UAAU,EAAE,GAAG,CAAC;UACzDC,WAAW,EAAEpB,YAAY,CAAC8B,IAAI,CAACxB,UAAU,CAACc,WAAW,EAAE,GAAG;QAC5D,CAAC;QACD,IAAIU,IAAI,CAACxB,UAAU,CAACe,aAAa,EAAE;UACjC,IAAI,CAACxB,KAAK,CAACwB,aAAa,GAAGS,IAAI,CAACxB,UAAU,CAACe,aAAa;UACxD,IAAI,CAACxB,KAAK,CAACyB,SAAS,GAAGQ,IAAI,CAACxB,UAAU,CAACgB,SAAS;UAChD,IAAI,CAACzB,KAAK,CAAC0B,SAAS,GAAGO,IAAI,CAACxB,UAAU,CAACiB,SAAS;UAChD,IAAI,CAAC1B,KAAK,CAAC2B,SAAS,GAAGQ,QAAQ,CAACF,IAAI,CAACxB,UAAU,CAACkB,SAAS,EAAE,EAAE,CAAC;QAChE;QACA,OAAO,IAAI;MACb;QACE,OAAO,KAAK;IAChB;EACF;EAEAS,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGlC,oBAAoB"}