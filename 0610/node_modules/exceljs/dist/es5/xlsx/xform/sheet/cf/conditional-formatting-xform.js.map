{"version": 3, "file": "conditional-formatting-xform.js", "names": ["CompositeXform", "require", "CfRuleXform", "ConditionalFormattingXform", "constructor", "map", "cfRule", "tag", "render", "xmlStream", "model", "rules", "some", "isPrimitive", "openNode", "sqref", "ref", "for<PERSON>ach", "rule", "closeNode", "createNewModel", "_ref", "attributes", "onParserClose", "name", "parser", "push", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf/conditional-formatting-xform.js"], "sourcesContent": ["const CompositeXform = require('../../composite-xform');\n\nconst CfRuleXform = require('./cf-rule-xform');\n\nclass ConditionalFormattingXform extends CompositeXform {\n  constructor() {\n    super();\n\n    this.map = {\n      cfRule: new CfRuleXform(),\n    };\n  }\n\n  get tag() {\n    return 'conditionalFormatting';\n  }\n\n  render(xmlStream, model) {\n    // if there are no primitive rules, exit now\n    if (!model.rules.some(CfRuleXform.isPrimitive)) {\n      return;\n    }\n\n    xmlStream.openNode(this.tag, {sqref: model.ref});\n\n    model.rules.forEach(rule => {\n      if (CfRuleXform.isPrimitive(rule)) {\n        rule.ref = model.ref;\n        this.map.cfRule.render(xmlStream, rule);\n      }\n    });\n\n    xmlStream.closeNode();\n  }\n\n  createNewModel({attributes}) {\n    return {\n      ref: attributes.sqref,\n      rules: [],\n    };\n  }\n\n  onParserClose(name, parser) {\n    this.model.rules.push(parser.model);\n  }\n}\n\nmodule.exports = ConditionalFormattingXform;\n"], "mappings": ";;AAAA,MAAMA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAEvD,MAAMC,WAAW,GAAGD,OAAO,CAAC,iBAAiB,CAAC;AAE9C,MAAME,0BAA0B,SAASH,cAAc,CAAC;EACtDI,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,GAAG,GAAG;MACTC,MAAM,EAAE,IAAIJ,WAAW,CAAC;IAC1B,CAAC;EACH;EAEA,IAAIK,GAAGA,CAAA,EAAG;IACR,OAAO,uBAAuB;EAChC;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB;IACA,IAAI,CAACA,KAAK,CAACC,KAAK,CAACC,IAAI,CAACV,WAAW,CAACW,WAAW,CAAC,EAAE;MAC9C;IACF;IAEAJ,SAAS,CAACK,QAAQ,CAAC,IAAI,CAACP,GAAG,EAAE;MAACQ,KAAK,EAAEL,KAAK,CAACM;IAAG,CAAC,CAAC;IAEhDN,KAAK,CAACC,KAAK,CAACM,OAAO,CAACC,IAAI,IAAI;MAC1B,IAAIhB,WAAW,CAACW,WAAW,CAACK,IAAI,CAAC,EAAE;QACjCA,IAAI,CAACF,GAAG,GAAGN,KAAK,CAACM,GAAG;QACpB,IAAI,CAACX,GAAG,CAACC,MAAM,CAACE,MAAM,CAACC,SAAS,EAAES,IAAI,CAAC;MACzC;IACF,CAAC,CAAC;IAEFT,SAAS,CAACU,SAAS,CAAC,CAAC;EACvB;EAEAC,cAAcA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACzB,OAAO;MACLL,GAAG,EAAEM,UAAU,CAACP,KAAK;MACrBJ,KAAK,EAAE;IACT,CAAC;EACH;EAEAY,aAAaA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAC1B,IAAI,CAACf,KAAK,CAACC,KAAK,CAACe,IAAI,CAACD,MAAM,CAACf,KAAK,CAAC;EACrC;AACF;AAEAiB,MAAM,CAACC,OAAO,GAAGzB,0BAA0B"}