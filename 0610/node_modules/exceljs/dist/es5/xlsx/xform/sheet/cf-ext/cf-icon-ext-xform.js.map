{"version": 3, "file": "cf-icon-ext-xform.js", "names": ["BaseXform", "require", "CfIconExtXform", "tag", "render", "xmlStream", "model", "leafNode", "iconSet", "iconId", "parseOpen", "_ref", "attributes", "toIntValue", "parseClose", "name", "module", "exports"], "sources": ["../../../../../../lib/xlsx/xform/sheet/cf-ext/cf-icon-ext-xform.js"], "sourcesContent": ["const BaseXform = require('../../base-xform');\n\nclass CfIconExtXform extends BaseXform {\n  get tag() {\n    return 'x14:cfIcon';\n  }\n\n  render(xmlStream, model) {\n    xmlStream.leafNode(this.tag, {\n      iconSet: model.iconSet,\n      iconId: model.iconId,\n    });\n  }\n\n  parseOpen({attributes}) {\n    this.model = {\n      iconSet: attributes.iconSet,\n      iconId: BaseXform.toIntValue(attributes.iconId),\n    };\n  }\n\n  parseClose(name) {\n    return name !== this.tag;\n  }\n}\n\nmodule.exports = CfIconExtXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAE7C,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,YAAY;EACrB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvBD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAACJ,GAAG,EAAE;MAC3BK,OAAO,EAAEF,KAAK,CAACE,OAAO;MACtBC,MAAM,EAAEH,KAAK,CAACG;IAChB,CAAC,CAAC;EACJ;EAEAC,SAASA,CAAAC,IAAA,EAAe;IAAA,IAAd;MAACC;IAAU,CAAC,GAAAD,IAAA;IACpB,IAAI,CAACL,KAAK,GAAG;MACXE,OAAO,EAAEI,UAAU,CAACJ,OAAO;MAC3BC,MAAM,EAAET,SAAS,CAACa,UAAU,CAACD,UAAU,CAACH,MAAM;IAChD,CAAC;EACH;EAEAK,UAAUA,CAACC,IAAI,EAAE;IACf,OAAOA,IAAI,KAAK,IAAI,CAACZ,GAAG;EAC1B;AACF;AAEAa,MAAM,CAACC,OAAO,GAAGf,cAAc"}