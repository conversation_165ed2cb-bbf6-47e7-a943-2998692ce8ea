{"version": 3, "file": "dimension-xform.js", "names": ["BaseXform", "require", "DimensionXform", "tag", "render", "xmlStream", "model", "leafNode", "ref", "parseOpen", "node", "name", "attributes", "parseText", "parseClose", "module", "exports"], "sources": ["../../../../../lib/xlsx/xform/sheet/dimension-xform.js"], "sourcesContent": ["const BaseXform = require('../base-xform');\n\nclass DimensionXform extends BaseXform {\n  get tag() {\n    return 'dimension';\n  }\n\n  render(xmlStream, model) {\n    if (model) {\n      xmlStream.leafNode('dimension', {ref: model});\n    }\n  }\n\n  parseOpen(node) {\n    if (node.name === 'dimension') {\n      this.model = node.attributes.ref;\n      return true;\n    }\n    return false;\n  }\n\n  parseText() {}\n\n  parseClose() {\n    return false;\n  }\n}\n\nmodule.exports = DimensionXform;\n"], "mappings": ";;AAAA,MAAMA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,MAAMC,cAAc,SAASF,SAAS,CAAC;EACrC,IAAIG,GAAGA,CAAA,EAAG;IACR,OAAO,WAAW;EACpB;EAEAC,MAAMA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACvB,IAAIA,KAAK,EAAE;MACTD,SAAS,CAACE,QAAQ,CAAC,WAAW,EAAE;QAACC,GAAG,EAAEF;MAAK,CAAC,CAAC;IAC/C;EACF;EAEAG,SAASA,CAACC,IAAI,EAAE;IACd,IAAIA,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;MAC7B,IAAI,CAACL,KAAK,GAAGI,IAAI,CAACE,UAAU,CAACJ,GAAG;MAChC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAK,SAASA,CAAA,EAAG,CAAC;EAEbC,UAAUA,CAAA,EAAG;IACX,OAAO,KAAK;EACd;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGd,cAAc"}