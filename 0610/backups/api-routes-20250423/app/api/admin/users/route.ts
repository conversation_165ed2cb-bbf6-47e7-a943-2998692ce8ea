/**
 * 管理员用户管理API
 * 处理管理员创建和管理用户的请求
 */

import { NextRequest, NextResponse } from "next/server"
import { checkPermission } from "@/lib/casbin/enforcer";
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { hash } from "bcryptjs"
import { hasResourcePermission } from "@/lib/abac/permission"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 创建用户
 * 管理员创建新用户，无需验证码
 *
 * @route POST /api/admin/users
 * @access 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取用户ID
    let userId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      userId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查用户是否有创建用户的权限
    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    const hasPermission = await checkPermission(request, 'users', 'create');
    
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限创建用户'
      }, { status: 403 })
    }

    // 解析请求体
    const body = await request.json()
    console.log('收到创建用户请求:', body)
    const { name, username, email, phone, password, accountType, creditLimit } = body

    // 验证必填字段
    if (!username || !email || !password) {
      return NextResponse.json({
        success: false,
        message: '用户名、邮箱和密码为必填项'
      }, { status: 400 })
    }

    // 检查用户名是否已存在
    const existingUsername = await prisma.user.findUnique({
      where: { username }
    })

    if (existingUsername) {
      return NextResponse.json({
        success: false,
        message: '用户名已存在'
      }, { status: 400 })
    }

    // 检查邮箱是否已存在
    const existingEmail = await prisma.user.findUnique({
      where: { email }
    })

    if (existingEmail) {
      return NextResponse.json({
        success: false,
        message: '邮箱已被注册'
      }, { status: 400 })
    }

    // 确定角色代码
    let roleCode = 'USER'
    if (accountType === 'admin') {
      roleCode = 'ADMIN'
    } else if (accountType === 'enterprise') {
      roleCode = 'ENTERPRISE'
    }

    // 加密密码
    const hashedPassword = await hash(password, 10)

    // 创建用户
    console.log('开始创建用户:', { username, email, name: name || username, roleCode })
    const user = await prisma.user.create({
      data: {
        username,
        email,
        password: hashedPassword,
        name: username, // 始终使用用户名作为昵称
        phone,
        roleCode,
        permissions: [],
        image: null,
        emailVerified: null,
        creditLimit: creditLimit || 0,
        createdById: userId, // 设置创建者ID
        status: 'active' // 确保新用户状态为激活
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        roleCode: true,
        creditLimit: true,
        createdAt: true,
        updatedAt: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      }
    })

    // 格式化返回数据，确保包含所有必要的字段
    const formattedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      phone: user.phone || "",
      roleCode: user.roleCode,
      accountType: accountType || "personal",
      role: user.roleCode,
      balance: 0,
      creditLimit: user.creditLimit || 0,
      status: "active",
      createdBy: user.createdBy ? {
        id: user.createdBy.id,
        name: user.createdBy.name || user.createdBy.username
      } : null,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString()
    }

    console.log('返回的用户数据:', formattedUser)

    return NextResponse.json({
      success: true,
      message: '用户创建成功',
      data: formattedUser
    }, { status: 201 })
  } catch (error) {
    console.error("创建用户错误:", error)
    // 详细记录错误信息
    if (error instanceof Error) {
      console.error('错误详情:', error.message)
      console.error('错误堆栈:', error.stack)
    }
    return NextResponse.json({
      success: false,
      message: '创建用户失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}


/**
 * 获取用户列表
 *
 * @route GET /api/admin/users
 * @access 需要管理员权限
 */
export async function GET(request: NextRequest) {
  try {
    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取用户ID
    let userId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      userId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查用户是否有查看用户列表的权限
    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '无效的管理员ID'
      }, { status: 401 })
    }

    // Pass request, resource, and action to checkPermission
    const hasPermission = await checkPermission(request, 'users', 'read');
    if (!hasPermission) {
      return NextResponse.json({
        success: false,
        message: '无权限查看用户列表'
      }, { status: 403 })
    }

    // 获取查询参数
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const search = url.searchParams.get('search') || ''

    // 计算分页
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where = search ? {
      OR: [
        { username: { contains: search } },
        { email: { contains: search } },
        // 注意：如果没有name字段，请删除这一行
        // { name: { contains: search } }
      ]
    } : {}

    // 查询用户总数
    const total = await prisma.user.count({ where })

    // 查询用户列表
    const users = await prisma.user.findMany({
      where,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        phone: true,
        image: true,
        roleCode: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        verificationStatus: true,
        verificationType: true,
        creditLimit: true,
        createdById: true,
        createdBy: {
          select: {
            id: true,
            name: true,
            username: true
          }
        },
        role: {
          select: {
            name: true,
            type: true
          }
        }
      },
      skip,
      take: pageSize,
      orderBy: { createdAt: 'desc' }
    })

    // 获取用户余额信息
    const userIds = users.map(user => user.id);
    const balances = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: {
        id: true,
        balance: true,
        status: true
      }
    });

    // 创建余额和状态查找映射
    const balanceMap = new Map();
    const statusMap = new Map();
    balances.forEach(item => {
      balanceMap.set(item.id, item.balance);
      statusMap.set(item.id, item.status);
    });

    // 获取用户费率信息
    const userRates = await prisma.rate.findMany({
      where: {
        customerId: { in: userIds }
      },
      select: {
        customerId: true,
        amount: true,
        businessType: true
      }
    });

    // 创建费率和业务类型查找映射
    const rateMap = new Map();
    const businessTypeMap = new Map();
    userRates.forEach(rate => {
      if (rate.customerId) {
        rateMap.set(rate.customerId, rate.amount);
        businessTypeMap.set(rate.customerId, rate.businessType);
      }
    });

    // 处理日期格式和转换为前端需要的格式
    const formattedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      phone: user.phone || "",
      image: user.image,
      roleCode: user.roleCode,
      accountType: user.role.type === "system" ? "admin" : user.role.type,
      personalVerification: user.verificationType === "personal" ? user.verificationStatus : null,
      enterpriseVerification: user.verificationType === "enterprise" ? user.verificationStatus : null,
      role: user.roleCode,
      balance: balanceMap.get(user.id) || 0,
      creditLimit: user.creditLimit || 0,
      rate: rateMap.get(user.id) || null,
      businessType: businessTypeMap.get(user.id) || null,
      createdBy: user.createdBy ? {
        id: user.createdBy.id,
        name: user.createdBy.name || user.createdBy.username
      } : null,
      status: statusMap.get(user.id) || "active",
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      lastLoginAt: user.lastLoginAt?.toISOString()
    }))

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize)

    return NextResponse.json({
      success: true,
      message: '获取用户列表成功',
      data: {
        users: formattedUsers,
        pagination: {
          page,
          pageSize,
          total,
          totalPages
        }
      }
    })
  } catch (error) {
    console.error("获取用户列表错误:", error)
    return NextResponse.json({
      success: false,
      message: '获取用户列表失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
