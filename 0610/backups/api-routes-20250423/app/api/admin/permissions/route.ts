import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/lib/prisma";

/**
 * 获取所有权限
 * @route GET /api/admin/permissions
 */
export async function GET(req: NextRequest) {
  try {
    // 检查用户是否登录和是否有管理员权限
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 }
      );
    }

    // 检查是否有管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
      include: { role: true }
    });

    if (!user || user.role?.code !== "admin") {
      return NextResponse.json(
        { success: false, message: "无权访问此资源" },
        { status: 403 }
      );
    }

    // 获取所有权限
    const permissions = await prisma.operation.findMany({
      orderBy: { id: "asc" }
    });

    return NextResponse.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    console.error("获取权限列表失败:", error);
    return NextResponse.json(
      { success: false, message: "获取权限列表失败" },
      { status: 500 }
    );
  }
}

/**
 * 创建新权限
 * @route POST /api/admin/permissions
 */
export async function POST(req: NextRequest) {
  try {
    // 检查用户是否登录和是否有管理员权限
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: "未授权访问" },
        { status: 401 }
      );
    }

    // 检查是否有管理员权限
    const user = await prisma.user.findUnique({
      where: { id: session.user.id as string },
      include: { role: true }
    });

    if (!user || user.role?.code !== "admin") {
      return NextResponse.json(
        { success: false, message: "无权访问此资源" },
        { status: 403 }
      );
    }

    // 解析请求体
    const body = await req.json();
    const { name, code, description } = body;

    // 验证必填字段
    if (!name || !code) {
      return NextResponse.json(
        { success: false, message: "名称和代码不能为空" },
        { status: 400 }
      );
    }

    // 检查代码是否已存在
    const existingPermission = await prisma.operation.findUnique({
      where: { code }
    });

    if (existingPermission) {
      return NextResponse.json(
        { success: false, message: "权限代码已存在" },
        { status: 400 }
      );
    }

    // 创建权限
    const permission = await prisma.operation.create({
      data: {
        name,
        code,
        type: "function", // 默认类型
        description
      }
    });

    return NextResponse.json({
      success: true,
      message: "创建权限成功",
      data: permission
    });
  } catch (error) {
    console.error("创建权限失败:", error);
    return NextResponse.json(
      { success: false, message: "创建权限失败" },
      { status: 500 }
    );
  }
}