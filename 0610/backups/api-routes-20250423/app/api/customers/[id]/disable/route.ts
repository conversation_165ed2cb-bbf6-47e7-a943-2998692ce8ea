/**
 * 停用客户API
 */

import { NextRequest, NextResponse } from "next/server"
import { cookies } from "next/headers"
import * as jose from 'jose'
import { prisma } from "@/lib/prisma"
import { createAccountDisabledNotification } from "@/app/lib/notification-helper"

// 定义JWT密钥
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-secret-key-here-minimum-32-chars'
)

/**
 * 停用客户账户
 *
 * @route POST /api/customers/[id]/disable
 * @access 需要管理员权限
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // 获取请求体
  const body = await request.json()
  const { reason } = body
  try {
    // 解码并清理用户ID
    let userId = decodeURIComponent(params.id)
    // 去除URL中可能的特殊字符
    userId = userId.trim()
    console.log('API接收到的用户ID:', userId)

    // 获取当前用户信息并验证权限
    const cookieStore = cookies()
    const token = cookieStore.get("token")?.value

    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 })
    }

    // 解析token获取管理员ID
    let adminId
    try {
      const { payload } = await jose.jwtVerify(token, JWT_SECRET)
      adminId = payload.sub
    } catch (error) {
      return NextResponse.json({
        success: false,
        message: '无效的认证信息'
      }, { status: 401 })
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 })
    }

    // 更新用户状态
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: 'inactive'
      }
    })

    // 记录状态变更日志
    await prisma.userStatusLog.create({
      data: {
        userId,
        adminId,
        status: 'inactive',
        reason: reason || '管理员停用账户'
      }
    })

    // 发送通知和邮件
    try {
      const notificationResult = await createAccountDisabledNotification(userId, reason || '管理员停用账户')
      console.log('禁用账户通知结果:', notificationResult)
    } catch (notificationError) {
      console.error('发送禁用账户通知失败:', notificationError)
      // 通知失败不影响主流程
    }

    return NextResponse.json({
      success: true,
      message: '用户已停用'
    })
  } catch (error) {
    console.error("停用用户错误:", error)
    return NextResponse.json({
      success: false,
      message: '停用用户失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 })
  }
}
