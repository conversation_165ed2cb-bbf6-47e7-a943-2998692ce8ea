import { NextRequest, NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"
import { CasbinService } from "@/lib/services/casbin-service"
import * as jose from 'jose'
import { v4 as uuidv4 } from 'uuid'
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { verifyJwtToken } from "@/lib/jwt"
import { prisma } from "@/lib/prisma"
import { checkPermission } from "@/lib/casbin/enforcer"
import { hasResourcePermissionWithCasbin } from "@/lib/abac/casbin-adapter"

// JWT密钥配置
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key")

/**
 * 权限检查API
 * 支持NextAuth和传统JWT认证
 */
export async function POST(request: NextRequest) {
  const requestId = uuidv4().substring(0, 8)
  console.log(`[权限检查API:${requestId}] 收到权限检查请求`)

  try {
    // 获取请求体
    const body = await request.json()
    const { resource, action = 'access' } = body
    
    if (!resource) {
      console.log(`[权限检查API:${requestId}] 缺少资源参数`)
      return NextResponse.json(
        { success: false, message: "请提供资源参数" },
        { status: 400 }
      )
    }
    
    // 1. 先尝试获取 NextAuth session
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    })

    let userId = token?.sub
    let userRole = token?.roleCode as string
    let hasPermission = false

    // 2. 如果没有 NextAuth session，尝试 JWT token
    if (!userId) {
      console.log(`[权限检查API:${requestId}] 未找到NextAuth会话，尝试JWT`)
      const jwtToken = request.cookies.get("token")?.value
      
      if (!jwtToken) {
        console.log(`[权限检查API:${requestId}] 未找到任何认证令牌`)
        return NextResponse.json(
          { success: false, message: "未登录", hasPermission: false },
          { status: 401 }
        )
      }

      try {
        // 验证JWT令牌
        const { payload } = await jose.jwtVerify(jwtToken, JWT_SECRET)
        
        if (!payload.sub) {
          console.log(`[权限检查API:${requestId}] JWT令牌无效，缺少sub字段`)
          return NextResponse.json(
            { success: false, message: "令牌无效", hasPermission: false },
            { status: 401 }
          )
        }

        userId = payload.sub as string
        userRole = payload.role as string
        
        console.log(`[权限检查API:${requestId}] 通过JWT认证，用户ID: ${userId}, 角色: ${userRole}`)
      } catch (error) {
        console.error(`[权限检查API:${requestId}] JWT令牌验证失败:`, error)
        return NextResponse.json(
          { success: false, message: "认证令牌已过期", hasPermission: false },
          { status: 401 }
        )
      }
    } else {
      console.log(`[权限检查API:${requestId}] 通过NextAuth认证，用户ID: ${userId}, 角色: ${userRole}`)
    }

    // 3. 使用jCasbin进行权限检查
    try {
      // 对用户进行权限检查
      hasPermission = await CasbinService.checkPermission(userId as string, resource, action)
      console.log(`[权限检查API:${requestId}] 用户级权限检查结果: ${hasPermission}`)
      
      // 如果用户没有权限，检查角色权限
      if (!hasPermission && userRole) {
        hasPermission = await CasbinService.checkPermission(userRole, resource, action)
        console.log(`[权限检查API:${requestId}] 角色级权限检查结果: ${hasPermission}`)
      }
    } catch (error) {
      console.error(`[权限检查API:${requestId}] 权限检查失败:`, error)
      hasPermission = false
    }

    return NextResponse.json({
      success: true,
      hasPermission,
      userId,
      resource,
      action
    })
  } catch (error) {
    console.error(`[权限检查API:${requestId}] 处理错误:`, error)
    return NextResponse.json(
      { 
        success: false, 
        message: "处理权限检查请求时发生错误",
        hasPermission: false 
      },
      { status: 500 }
    )
  }
}

// GET 方法保持不变，用于客户端权限检查
export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const url = new URL(request.url);
    const resource = url.searchParams.get("resource");
    const action = url.searchParams.get("action");

    if (!resource || !action) {
      return NextResponse.json(
        { success: false, message: "缺少必要参数" },
        { status: 400 }
      );
    }

    // 先尝试使用 NextAuth 会话
    const session = await getServerSession();
    let userId = session?.user?.id;

    // 如果没有 NextAuth 会话，回退到使用 JWT token
    if (!userId) {
      // 获取并验证 token
      const token = request.cookies.get("token")?.value
      if (!token) {
        return NextResponse.json(
          { success: false, message: "未登录", hasPermission: false },
          { status: 401 }
        )
      }

      const { valid, payload } = await verifyJwtToken(token)
      if (!valid || !payload) {
        return NextResponse.json(
          { success: false, message: "token 无效", hasPermission: false },
          { status: 401 }
        )
      }

      userId = payload.sub as string;
    }

    // 获取用户角色
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        role: {
          select: {
            code: true,
            type: true,
            permissions: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: "用户不存在", hasPermission: false },
        { status: 404 }
      )
    }

    // 检查是否是管理员或有 * 权限
    if (user.role.code === 'admin' || user.role.permissions.includes('*')) {
      return NextResponse.json({
        success: true,
        hasPermission: true
      })
    }

    // 使用 jCasbin 检查权限
    try {
      // 构建环境变量
      const env = {
        role: user.role.code,
        user: {
          id: userId,
          roleCode: user.role.code
        }
      };

      // 使用增强的 checkPermission 函数
      const casbinResult = await checkPermission(request, resource, action, env);
      if (casbinResult) {
        return NextResponse.json({
          success: true,
          hasPermission: true
        });
      }

      // 如果基本检查失败，尝试使用 ABAC 兼容模式
      const userObj = {
        id: userId,
        roleCode: user.role.code,
        role: user.role
      };

      const abacResult = await hasResourcePermissionWithCasbin(userObj, resource, action);
      if (abacResult) {
        return NextResponse.json({
          success: true,
          hasPermission: true
        });
      }
    } catch (error) {
      console.error("jCasbin 权限检查失败:", error);
      // 如果 jCasbin 检查失败，回退到使用原有的权限检查逻辑
    }

    // 回退到原有的权限检查逻辑
    const permissionString = `${resource}:${action}`;
    const hasPermission = user.role.permissions.includes(permissionString);

    return NextResponse.json({
      success: true,
      hasPermission
    });
  } catch (error) {
    console.error("权限检查失败:", error);
    return NextResponse.json(
      { success: false, message: "权限检查失败", hasPermission: false },
      { status: 500 }
    );
  }
}