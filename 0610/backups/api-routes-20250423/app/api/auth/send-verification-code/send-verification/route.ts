import { type NextRequest, NextResponse } from "next/server"
import { validateRequest } from "@/lib/middleware/validate-request"
import { errorHandler } from "@/lib/middleware/error-handler"
import { prisma } from "@/lib/prisma"
import * as yup from 'yup'
import verificationService from "@/lib/api/verification"
import emailService from "@/lib/api/email"

/**
 * 发送验证码响应接口
 */
interface SendVerificationResponse {
  code: string
  success: boolean
  message: string
  data?: null
}

/**
 * 验证码请求参数验证模式
 */
const sendVerificationSchema = yup.object().shape({
  email: yup.string()
    .required('邮箱不能为空')
    .email('邮箱格式不正确')
})

/**
 * 处理发送验证码请求
 * POST /api/auth/send-verification
 *
 * @param request - Next.js 请求对象
 * @returns Promise<NextResponse> 发送验证码响应
 *
 * 处理流程：
 * 1. 验证请求参数（邮箱）
 * 2. 检查邮箱是否已被注册
 * 3. 生成验证码
 * 4. 存储验证码（Redis，有效期5分钟）
 * 5. 发送验证码邮件
 *
 * 错误处理：
 * - 400: 参数验证失败、邮箱已被注册
 * - 500: 服务器内部错误
 *
 * @example
 * ```typescript
 * // 请求体示例
 * {
 *   "email": "<EMAIL>"
 * }
 *
 * // 成功响应示例
 * {
 *   "code": 200,
 *   "success": true,
 *   "message": "验证码已发送，请查收邮件",
 *   "data": null
 * }
 * ```
 */
export async function POST(request: NextRequest): Promise<NextResponse<SendVerificationResponse>> {
  try {
    // 验证请求参数
    const { email } = await validateRequest(request, sendVerificationSchema)

    // 检查邮箱是否已被注册
    const existingUser = await prisma.user.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json<SendVerificationResponse>(
        {
          code: "BAD_REQUEST",
          success: false,
          message: "邮箱已被注册",
          data: null
        },
        { status: 400 }
      )
    }

    // 生成验证码
    const code = verificationService.generateCode()

    // 存储验证码
    await verificationService.storeCode(email, code)

    // 发送验证码邮件
    await emailService.sendVerificationEmail(email, code)

    return NextResponse.json<SendVerificationResponse>(
      {
        code: "SUCCESS",
        success: true,
        message: "验证码已发送，请查收邮件",
        data: null
      },
      { status: 200 }
    )
  } catch (error) {
    return errorHandler(error)
  }
}