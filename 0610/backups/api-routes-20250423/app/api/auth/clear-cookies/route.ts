/**
 * 清理 Cookie API 路由
 * 用于清理所有认证相关的 Cookie，解决 HTTP 431 错误
 */

import { cookies } from "next/headers"
import { NextRequest, NextResponse } from "next/server"

/**
 * 处理清理 Cookie 请求
 */
export async function GET(request: NextRequest) {
  try {
    console.log("清理所有 Cookie")

    // 清除所有可能的认证相关 Cookie
    const cookieStore = cookies()
    
    // 获取所有 Cookie
    const allCookies = cookieStore.getAll()
    console.log(`发现 ${allCookies.length} 个 Cookie`)
    
    // 清除所有 Cookie
    for (const cookie of allCookies) {
      cookieStore.delete(cookie.name)
      console.log(`删除 Cookie: ${cookie.name}`)
    }

    // 返回清除前端存储的脚本
    const clearScript = `
      <script>
        try {
          // 清除所有本地存储
          localStorage.clear();
          sessionStorage.clear();

          // 清除所有 Cookie
          document.cookie.split(';').forEach(function(c) {
            document.cookie = c.trim().split('=')[0] + '=;' + 'expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
          });

          console.log('所有存储和会话数据已清除');

          // 延迟后跳转到登录页面
          setTimeout(function() {
            window.location.href = '/login?clear=' + Date.now();
          }, 100);
        } catch (e) {
          console.error('清除数据失败:', e);
          // 即使失败也跳转到登录页面
          window.location.href = '/login?clear=' + Date.now();
        }
      </script>
    `

    // 返回包含清除脚本的 HTML 响应
    return new Response(
      `<!DOCTYPE html>
      <html>
      <head>
        <title>正在清理数据...</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
      </head>
      <body>
        <h1>正在清理浏览器数据...</h1>
        <p>请稍等，正在清除您的会话数据和 Cookie...</p>
        <p>这将解决 HTTP 431 错误问题。</p>
        ${clearScript}
      </body>
      </html>`,
      {
        status: 200,
        headers: {
          "Content-Type": "text/html; charset=utf-8",
          "Cache-Control": "no-store, no-cache, must-revalidate, proxy-revalidate"
        }
      }
    )
  } catch (error) {
    console.error("清理 Cookie 失败:", error)
    return new Response(
      JSON.stringify({ success: false, error: "清理 Cookie 失败" }),
      { status: 500 }
    )
  }
}
