import { type NextRequest, NextResponse } from "next/server"
import { mockTasks } from "@/lib/api/mock"

export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id
    const body = await request.json()
    const { status } = body

    // 验证请求参数
    if (!status) {
      return NextResponse.json(
        {
          code: 400,
          success: false,
          message: "缺少状态参数",
          data: null,
        },
        { status: 400 },
      )
    }

    // 查找任务
    const taskIndex = mockTasks.findIndex((task) => task.id === id)

    if (taskIndex === -1) {
      return NextResponse.json(
        {
          code: 404,
          success: false,
          message: "任务不存在",
          data: null,
        },
        { status: 404 },
      )
    }

    // 更新任务状态（实际应用中应更新数据库）
    const task = mockTasks[taskIndex]

    // 模拟状态更新
    let statusMessage = ""
    if (status === "processing") {
      statusMessage = "任务启动成功"
    } else if (status === "paused") {
      statusMessage = "任务暂停成功"
    } else if (status === "completed") {
      statusMessage = "任务完成成功"
    } else {
      statusMessage = "状态更新成功"
    }

    // 返回响应
    return NextResponse.json(
      {
        code: 200,
        success: true,
        message: statusMessage,
        data: null,
      },
      { status: 200 },
    )
  } catch (error) {
    console.error("更新任务状态错误:", error)

    // 返回服务器错误响应
    return NextResponse.json(
      {
        code: 500,
        success: false,
        message: "服务器内部错误",
        data: null,
      },
      { status: 500 },
    )
  }
}

