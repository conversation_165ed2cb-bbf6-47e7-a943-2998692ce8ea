import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { createAccountDisabledNotification } from "@/app/lib/notification-helper";
import { sendDirectEmail } from "@/app/lib/direct-email";

/**
 * 调试API - 测试通知和邮件功能
 *
 * @route POST /api/debug/notification-test
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, reason, email } = body;

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '缺少用户ID参数'
      }, { status: 400 });
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    // 测试结果
    const results = {
      user: user,
      notificationResult: null as any,
      directEmailResult: null as any,
      databaseChecks: {
        notificationTypes: [] as any,
        notifications: [] as any,
        userNotifications: [] as any,
        emailQueue: [] as any
      }
    };

    // 1. 检查通知类型
    results.databaseChecks.notificationTypes = await prisma.notificationType.findMany();

    // 2. 创建测试通知
    try {
      results.notificationResult = await createAccountDisabledNotification(
        userId,
        reason || '测试禁用原因'
      );
    } catch (error) {
      results.notificationResult = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }

    // 3. 直接发送测试邮件
    if (email) {
      try {
        results.directEmailResult = await sendDirectEmail(
          email,
          '测试邮件 - 账户状态变更',
          `
          <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; color: #333;">
            <h2 style="color: #e53935; text-align: center; margin-bottom: 20px;">测试邮件</h2>
            <p>这是一封测试邮件，用于验证邮件发送功能是否正常工作。</p>
            <p>时间戳: ${new Date().toISOString()}</p>
          </div>
          `
        );
      } catch (error) {
        results.directEmailResult = {
          success: false,
          error: error instanceof Error ? error.message : '未知错误'
        };
      }
    }

    // 4. 检查数据库中的通知记录
    results.databaseChecks.notifications = await prisma.notification.findMany({
      where: {
        OR: [
          { sendToAll: true },
          {
            sendToAll: false,
            recipients: {
              array_contains: [userId]
            }
          }
        ]
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });

    // 5. 检查用户通知关联
    results.databaseChecks.userNotifications = await prisma.userNotification.findMany({
      where: {
        userId: userId
      },
      include: {
        notification: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    });

    // 6. 检查邮件队列
    if (user?.email || email) {
      results.databaseChecks.emailQueue = await prisma.emailQueue.findMany({
        where: {
          recipient: user?.email || email
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 5
      });
    }

    return NextResponse.json({
      success: true,
      message: '测试完成',
      data: results
    });
  } catch (error) {
    console.error('通知测试失败:', error);
    return NextResponse.json({
      success: false,
      message: '通知测试失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
