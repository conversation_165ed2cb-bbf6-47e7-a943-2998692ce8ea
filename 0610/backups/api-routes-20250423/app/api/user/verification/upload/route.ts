import { NextRequest, NextResponse } from "next/server"
import { join } from "path"
import { writeFile, mkdir } from "fs/promises"
import { existsSync } from "fs"
import { AuthService } from "@/lib/auth-service"
import { nanoid } from "nanoid"
import { UnifiedAuthService } from "@/lib/unified-auth-service"
import { prisma } from "@/lib/prisma"

// 允许的文件类型
const ALLOWED_IMAGE_TYPES = [".jpg", ".jpeg", ".png"]
const ALLOWED_DOC_TYPES = [".pdf", ".doc", ".docx"]

// 最大文件大小 (5MB)
const MAX_SIZE = 5 * 1024 * 1024

/**
 * 验证文件类型
 * @param filename 文件名
 * @param allowedTypes 允许的文件类型数组
 * @returns 是否为允许的文件类型
 */
function isAllowedFileType(filename: string, allowedTypes: string[]): boolean {
  const ext = `.${filename.split(".").pop()?.toLowerCase()}`
  return allowedTypes.includes(ext)
}

/**
 * 验证文件头部，确保是图片文件
 * @param buffer 文件缓冲区
 * @returns 是否为有效的图片文件
 */
function validateImageHeader(buffer: Buffer): boolean {
  // 简单的文件头检查
  // JPEG: 0xFF 0xD8 0xFF
  // PNG: 0x89 0x50 0x4E 0x47 0x0D 0x0A 0x1A 0x0A
  if (buffer.length < 8) return false

  // JPEG检查
  if (buffer[0] === 0xFF && buffer[1] === 0xD8 && buffer[2] === 0xFF) {
    return true
  }

  // PNG检查
  if (
    buffer[0] === 0x89 &&
    buffer[1] === 0x50 &&
    buffer[2] === 0x4E &&
    buffer[3] === 0x47 &&
    buffer[4] === 0x0D &&
    buffer[5] === 0x0A &&
    buffer[6] === 0x1A &&
    buffer[7] === 0x0A
  ) {
    return true
  }

  return false
}

/**
 * 处理认证资料文件上传请求
 * POST /api/user/verification/upload
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    let user = await UnifiedAuthService.getCurrentUser(request, "认证资料上传API")
    if (!user) {
      console.error("[认证资料上传API] 使用UnifiedAuthService获取用户失败");

      // 尝试使用NextAuth
      const { getServerSession } = await import("next-auth/next");
      const { authOptions } = await import("@/lib/auth");
      const session = await getServerSession(authOptions);

      if (!session?.user?.id) {
        console.error("[认证资料上传API] 使用NextAuth获取用户也失败");
        return NextResponse.json(
          { success: false, message: "未授权，请登录后再试", error: "Unauthorized" },
          { status: 401 }
        )
      }

      // 使用NextAuth的用户ID继续
      const dbUser = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { id: true, email: true, username: true, roleCode: true }
      });

      if (!dbUser) {
        return NextResponse.json(
          { success: false, message: "用户不存在", error: "NotFound" },
          { status: 404 }
        )
      }

      // 使用数据库中的用户信息
      user = {
        id: dbUser.id,
        email: dbUser.email,
        username: dbUser.username,
        roleCode: dbUser.roleCode
      };
    }

    // 解析表单数据
    const formData = await request.formData()
    const file = formData.get("file") as File
    const type = formData.get("type") as string

    if (!file) {
      return NextResponse.json(
        { success: false, message: "未找到上传文件", error: "BadRequest" },
        { status: 400 }
      )
    }

    if (!type) {
      return NextResponse.json(
        { success: false, message: "未指定文件类型", error: "BadRequest" },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > MAX_SIZE) {
      return NextResponse.json(
        { success: false, message: "文件大小超过限制（最大5MB）", error: "BadRequest" },
        { status: 400 }
      )
    }

    // 验证文件类型
    const isImage = type === "idCardFront" || type === "idCardBack" || type === "idCardHolding" || type === "businessLicense"
    const allowedTypes = isImage ? ALLOWED_IMAGE_TYPES : [...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOC_TYPES]

    if (!isAllowedFileType(file.name, allowedTypes)) {
      return NextResponse.json(
        { success: false, message: "不支持的文件类型", error: "BadRequest" },
        { status: 400 }
      )
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 如果是图片，验证文件头
    if (isImage && !validateImageHeader(buffer)) {
      return NextResponse.json(
        { success: false, message: "无效的图片文件", error: "BadRequest" },
        { status: 400 }
      )
    }

    // 确保上传目录存在
    const uploadDir = join(process.cwd(), "public", "uploads", "verification", user.id)
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 生成安全的文件名
    const ext = `.${file.name.split(".").pop()?.toLowerCase()}`
    const fileName = `${type}_${nanoid(10)}${ext}`
    const filePath = join(uploadDir, fileName)

    // 保存文件
    await writeFile(filePath, buffer)

    // 返回文件URL
    const fileUrl = `/uploads/verification/${user.id}/${fileName}`

    return NextResponse.json({
      success: true,
      message: "文件上传成功",
      data: {
        url: fileUrl,
        filename: file.name,
        type
      }
    })
  } catch (error) {
    console.error("文件上传失败:", error)
    return NextResponse.json(
      { success: false, message: "服务器错误，文件上传失败", error: "ServerError" },
      { status: 500 }
    )
  }
}
