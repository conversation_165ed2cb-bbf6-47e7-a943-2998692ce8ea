/**
 * 调试日志工具
 * 使用DEBUG环境变量控制日志输出
 */

// 日志级别类型
type LogLevel = "debug" | "info" | "warn" | "error"

// 日志类别类型
type LogCategory = "api" | "crypto" | "task" | "call" | "monitor" | "ui" | "general"

// 颜色映射
const colors = {
  debug: "\x1b[36m", // 青色
  info: "\x1b[32m", // 绿色
  warn: "\x1b[33m", // 黄色
  error: "\x1b[31m", // 红色
  reset: "\x1b[0m", // 重置
}

// 类别颜色映射
const categoryColors = {
  api: "\x1b[35m", // 紫色
  crypto: "\x1b[36m", // 青色
  task: "\x1b[32m", // 绿色
  call: "\x1b[33m", // 黄色
  monitor: "\x1b[34m", // 蓝色
  ui: "\x1b[36m", // 青色
  general: "\x1b[37m", // 白色
}

/**
 * 检查是否启用了调试模式
 * @param category 日志类别
 * @returns 是否启用调试
 */
function isDebugEnabled(category: LogCategory): boolean {
  const debugEnv = process.env.DEBUG || ""

  // 如果DEBUG=*，启用所有调试输出
  if (debugEnv === "*") return true

  // 如果DEBUG包含特定类别或"all"，启用该类别的调试输出
  const categories = debugEnv.split(",").map((c) => c.trim())
  return categories.includes(category) || categories.includes("all")
}

/**
 * 创建调试日志记录器
 * @param category 日志类别
 * @returns 日志记录器对象
 */
export function createLogger(category: LogCategory) {
  return {
    /**
     * 记录调试级别日志
     * @param message 日志消息
     * @param data 相关数据
     */
    debug(message: string, data?: any): void {
      logMessage("debug", category, message, data)
    },

    /**
     * 记录信息级别日志
     * @param message 日志消息
     * @param data 相关数据
     */
    info(message: string, data?: any): void {
      logMessage("info", category, message, data)
    },

    /**
     * 记录警告级别日志
     * @param message 日志消息
     * @param data 相关数据
     */
    warn(message: string, data?: any): void {
      logMessage("warn", category, message, data)
    },

    /**
     * 记录错误级别日志
     * @param message 日志消息
     * @param data 相关数据
     */
    error(message: string, data?: any): void {
      logMessage("error", category, message, data)
    },
  }
}

/**
 * 记录日志消息
 * @param level 日志级别
 * @param category 日志类别
 * @param message 日志消息
 * @param data 相关数据
 */
function logMessage(level: LogLevel, category: LogCategory, message: string, data?: any): void {
  // 检查是否启用了该类别的调试
  if (!isDebugEnabled(category) && level !== "error") {
    return
  }

  // 是否在浏览器环境
  const isBrowser = typeof window !== "undefined"

  // 获取时间戳
  const timestamp = new Date().toISOString()

  // 构建日志前缀
  const prefix = `[${timestamp}] [${category.toUpperCase()}] [${level.toUpperCase()}]`

  if (isBrowser) {
    // 浏览器环境使用console API
    switch (level) {
      case "debug":
        console.debug(prefix, message, data)
        break
      case "info":
        console.info(prefix, message, data)
        break
      case "warn":
        console.warn(prefix, message, data)
        break
      case "error":
        console.error(prefix, message, data)
        break
    }
  } else {
    // Node.js环境使用带颜色的输出
    const levelColor = colors[level]
    const catColor = categoryColors[category]

    const coloredPrefix = `${levelColor}${prefix}${colors.reset}`
    const coloredCategory = `${catColor}[${category}]${colors.reset}`

    if (data !== undefined) {
      console.log(`${coloredPrefix} ${coloredCategory} ${message}`, data)
    } else {
      console.log(`${coloredPrefix} ${coloredCategory} ${message}`)
    }
  }
}

// 预定义的日志记录器
export const apiLogger = createLogger("api")
export const cryptoLogger = createLogger("crypto")
export const taskLogger = createLogger("task")
export const callLogger = createLogger("call")
export const monitorLogger = createLogger("monitor")
export const uiLogger = createLogger("ui")
export const logger = createLogger("general")

export default {
  createLogger,
  apiLogger,
  cryptoLogger,
  taskLogger,
  callLogger,
  monitorLogger,
  uiLogger,
  logger,
}

