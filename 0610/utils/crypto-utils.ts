import CryptoJS from "crypto-js"
import { VIDEO_CALL_API_CONFIG } from "@/lib/api/config"

/**
 * AES加密工具类
 */
class AesUtils {
  /**
   * 生成签名
   */
  static generateSignature(data: string): string {
    const key = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesKey)
    const iv = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesIv)
    const encrypted = CryptoJS.AES.encrypt(data, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
  }

  /**
   * 解密数据
   */
  static decrypt(data: string): string {
    const key = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesKey)
    const iv = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesIv)
    const decrypted = CryptoJS.AES.decrypt(data, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  }

  /**
   * 生成认证头部
   */
  static generateAuthHeaders(): Record<string, string> {
    const signature = this.generateSignature(VIDEO_CALL_API_CONFIG.orgCode)
    return {
      "Content-Type": "application/json; charset=utf-8",
      "access-token": signature,
    }
  }

  /**
   * AES-CBC加密
   */
  static encrypt(plainText: string): string {
    const key = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesKey)
    const iv = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesIv)
    const encrypted = CryptoJS.AES.encrypt(plainText, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
  }

  /**
   * AES-CBC解密
   */
  static decrypt(cipherText: string): string {
    const key = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesKey)
    const iv = CryptoJS.enc.Utf8.parse(VIDEO_CALL_API_CONFIG.aesIv)
    const decrypted = CryptoJS.AES.decrypt(cipherText, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  }
}

// 导出类实例和单独的加密解密函数
export const aesUtils = AesUtils
export const aesEncrypt = AesUtils.encrypt
export const aesDecrypt = AesUtils.decrypt
export default AesUtils

