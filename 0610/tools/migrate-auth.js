import logger from '@/lib/utils/logger';

/**
 * 认证中间件迁移工具
 * 
 * 用于将API路由从传统认证方式迁移到统一认证服务和权限检查中间件
 * 
 * 使用方法：
 * node tools/migrate-auth.js <文件路径> [--admin|--auth|--role=ROLENAME]
 * 
 * 示例：
 * node tools/migrate-auth.js app/api/admin/users/route.ts --admin
 * node tools/migrate-auth.js app/api/user/profile/route.ts --auth
 * node tools/migrate-auth.js app/api/editor/content/route.ts --role=EDITOR
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取命令行参数
const filePath = process.argv[2];
const option = process.argv[3] || '--auth';

if (!filePath) {
  logger.error('请提供文件路径');
  logger.log('使用方法: node tools/migrate-auth.js <文件路径> [--admin|--auth|--role=ROLENAME]');
  process.exit(1);
}

// 检查文件是否存在
if (!fs.existsSync(filePath)) {
  logger.error(`文件不存在: ${filePath}`);
  process.exit(1);
}

// 创建备份
const backupPath = `${filePath}.bak.${new Date().toISOString().split('T')[0].replace(/-/g, '')}`;
fs.copyFileSync(filePath, backupPath);
logger.log(`已创建备份: ${backupPath}`);

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 分析API名称
const apiName = filePath
  .replace(/^app\/api\//, '')
  .replace(/\/route\.ts$/, '')
  .replace(/\//g, '-')
  .replace(/\[([^\]]+)\]/g, '$1')
  .replace(/-+/g, '-')
  .replace(/-$/, '') + 'API';

// 替换导入语句
content = content.replace(
  /import\s*{\s*getServerSession\s*}\s*from\s*["']next-auth\/next["']/g,
  ''
);
content = content.replace(
  /import\s*{\s*authOptions\s*}\s*from\s*["']@\/lib\/auth["']/g,
  ''
);

// 检查是否已经导入AuthMiddleware
if (!content.includes('AuthMiddleware')) {
  // 在导入语句后添加AuthMiddleware导入
  content = content.replace(
    /(import.*from.*['"].*['"];?(\r?\n|\r))/,
    '$1import { AuthMiddleware } from "@/lib/middleware/auth-middleware";\n'
  );
}

// 根据选项替换认证逻辑
if (option === '--admin') {
  // 替换管理员权限检查
  content = content.replace(
    /const\s+session\s*=\s*await\s+getServerSession\(authOptions\);?[\s\S]*?if\s*\(\s*(?:!session\?.user|session\?.user\.roleCode\s*!==\s*['"]ADMIN['"]|session\?.user\.role\?.code\s*!==\s*['"]admin['"]|session\?.user\.role\?.code\s*!==\s*['"]ADMIN['"])\s*\)\s*{[\s\S]*?return\s+NextResponse\.json\([\s\S]*?\)\s*;?\s*}/g,
    `// 使用权限检查中间件检查管理员权限\n  const { response, user } = await AuthMiddleware.requireAdmin(request, "${apiName}");\n  if (response) {\n    return response;\n  }`
  );
} else if (option.startsWith('--role=')) {
  // 替换特定角色权限检查
  const role = option.replace('--role=', '');
  content = content.replace(
    /const\s+session\s*=\s*await\s+getServerSession\(authOptions\);?[\s\S]*?if\s*\(\s*(?:!session\?.user|session\?.user\.roleCode\s*!==\s*['"][A-Z]+['"]|session\?.user\.role\?.code\s*!==\s*['"][a-zA-Z]+['"])\s*\)\s*{[\s\S]*?return\s+NextResponse\.json\([\s\S]*?\)\s*;?\s*}/g,
    `// 使用权限检查中间件检查用户是否有指定角色\n  const { response, user } = await AuthMiddleware.requireRole(request, "${role}", "${apiName}");\n  if (response) {\n    return response;\n  }`
  );
} else {
  // 替换基本认证检查
  content = content.replace(
    /const\s+session\s*=\s*await\s+getServerSession\(authOptions\);?[\s\S]*?if\s*\(\s*!session\?.user\s*\)\s*{[\s\S]*?return\s+NextResponse\.json\([\s\S]*?\)\s*;?\s*}/g,
    `// 使用权限检查中间件检查用户是否已登录\n  const { response, user } = await AuthMiddleware.requireAuth(request, "${apiName}");\n  if (response) {\n    return response;\n  }`
  );
}

// 替换session.user引用
content = content.replace(/session\.user/g, 'user');

// 写入文件
fs.writeFileSync(filePath, content);
logger.log(`已更新文件: ${filePath}`);

// 检查语法错误
try {
  execSync(`npx tsc --noEmit ${filePath}`);
  logger.log('语法检查通过');
} catch (error) {
  logger.error('语法检查失败，请手动修复错误');
  logger.error(error.stdout.toString());
}

logger.log('迁移完成');
