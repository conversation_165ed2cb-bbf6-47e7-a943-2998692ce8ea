# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=5gweb
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/5gweb?schema=public"
DIRECT_URL="postgresql://postgres:postgres@localhost:5432/5gweb?schema=public"

# JWT 配置
JWT_SECRET="your-secret-key"
JWT_REFRESH_SECRET="your_jwt_refresh_secret_key_here"
JWT_EXPIRES_IN="7d"

# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost
NODE_ENV=development
PORT=3000

# 邮件服务配置
EMAIL_HOST="smtp.qq.com"
EMAIL_PORT=465
EMAIL_USER="<EMAIL>"
EMAIL_PASS="vkssqikbptbabjad"

# Redis 配置
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=2gyZ3GDw3LHZQKDhPmPDL3sjREVRXPr8

# 确保JWT_SECRET与NEXTAUTH_SECRET一致
JWT_SECRET=2gyZ3GDw3LHZQKDhPmPDL3sjREVRXPr8

# 前端API配置 - 指向本地API
NEXT_PUBLIC_API_BASE_URL=/api

# 视频通话服务配置 - 外部服务配置
VIDEO_CALL_API_BASE_URL=https://services.vcrm.vip:8000
NEXT_PUBLIC_VIDEO_CALL_ORG_CODE=XUNMENGdorg
NEXT_PUBLIC_VIDEO_CALL_LOGIN_NAME=XUNMENG001
NEXT_PUBLIC_VIDEO_CALL_APP_ID=your-app-id-here
NEXT_PUBLIC_VIDEO_CALL_AES_KEY=f4d9145303db415a
NEXT_PUBLIC_VIDEO_CALL_AES_IV=6778fd576ed3dfff

# 回调接口配置
CALLBACK_BASE_URL=https://your-domain.com
CALL_RESULT_CALLBACK_PATH=/api/callbacks/call-result
SMS_STATUS_CALLBACK_PATH=/api/callbacks/sms-status