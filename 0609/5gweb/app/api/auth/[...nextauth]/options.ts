import logger from '@/lib/utils/logger';

import { PrismaAdapter } from "@auth/prisma-adapter"
import { compare } from "bcryptjs"
import { NextAuthOptions } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import { v4 as uuidv4 } from 'uuid'

import { prisma } from "@/lib/prisma"

// 自定义错误消息映射
const errorMessages = {
  CredentialsSignin: '用户名或密码错误',
  default: '登录失败，请稍后重试'
};

/**
 * NextAuth.js 配置选项
 */
export const options: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "账号密码",
      credentials: {
        username: { label: "用户名/邮箱", type: "text", placeholder: "请输入用户名或邮箱" },
        password: { label: "密码", type: "password", placeholder: "请输入密码" }
      },
      async authorize(credentials) {
        const requestId = uuidv4().substring(0, 8);
        logger.log(`[NextAuth:${requestId}] 开始验证用户凭据`, credentials?.username);

        if (!credentials?.username || !credentials?.password) {
          logger.log(`[NextAuth:${requestId}] 缺少必要的凭据`);
          throw new Error('CredentialsSignin');
        }

        try {
          // 查找用户
          logger.log(`[NextAuth:${requestId}] 开始查询用户:`, credentials.username);
          const user = await prisma.user.findFirst({
            where: {
              OR: [
                { username: credentials.username },
                { email: credentials.username }
              ]
            },
            include: {
              role: true
            }
          });

          logger.log(`[NextAuth:${requestId}] 查询结果:`, user ? '找到用户' : '未找到用户');

          if (!user) {
            logger.log(`[NextAuth:${requestId}] 用户不存在: ${credentials.username}`);
            throw new Error('CredentialsSignin');
          }

          // 验证密码
          logger.log(`[NextAuth:${requestId}] 开始验证密码`);
          const isPasswordValid = await compare(credentials.password, user.password);

          if (!isPasswordValid) {
            logger.log(`[NextAuth:${requestId}] 密码验证失败: ${credentials.username}`);
            throw new Error('CredentialsSignin');
          }

          // 提取用户权限
          const permissions = user.role?.permissions || [];

          logger.log(`[NextAuth:${requestId}] 用户验证成功: ${user.username}`);
          logger.log(`[NextAuth:${requestId}] 角色代码: ${user.roleCode}`);
          logger.log(`[NextAuth:${requestId}] 权限列表: ${permissions.join(', ')}`);

          // 返回用户信息给NextAuth，会加入到token和session中
          return {
            id: user.id,
            name: user.username,
            email: user.email,
            roleCode: user.roleCode,
            permissions
          };
        } catch (error) {
          logger.error(`[NextAuth:${requestId}] 认证过程发生错误:`, error);
          // 如果是我们抛出的错误，直接抛出
          if (error instanceof Error && error.message === 'CredentialsSignin') {
            throw error;
          }
          // 其他错误转换为通用错误
          throw new Error('CredentialsSignin');
        }
      }
    })
  ],
  callbacks: {
    // 自定义JWT令牌
    async jwt({ token, user, account }) {
      // 首次登录，将用户信息添加到令牌中
      if (user) {
        logger.log('将用户信息添加到JWT令牌:', user.id);
        token.sub = user.id;
        token.name = user.name;
        token.email = user.email;
        token.roleCode = user.roleCode;
        token.permissions = user.permissions;
        token.role = { code: user.roleCode, permissions: user.permissions };
      }
      return token;
    },
    // 自定义会话
    async session({ session, token }) {
      // 将令牌信息添加到会话中
      if (token) {
        logger.log('将令牌信息添加到会话:', token.sub);
        session.user.id = token.sub;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.roleCode = token.roleCode as string;
        session.user.permissions = token.permissions as string[];
        session.user.role = token.role as any;
      }
      return session;
    },
    // 自定义重定向回调
    async redirect({ url, baseUrl }) {
      logger.log('NextAuth重定向回调:', { url, baseUrl });

      // 如果URL是相对路径，使用baseUrl构建完整URL
      if (url.startsWith("/")) {
        const fullUrl = `${baseUrl}${url}`;
        logger.log('构建完整重定向URL:', fullUrl);
        return fullUrl;
      }

      // 如果URL包含localhost，替换为当前域名
      if (url.includes('localhost:3000')) {
        const correctedUrl = url.replace('http://localhost:3000', baseUrl);
        logger.log('修正重定向URL:', correctedUrl);
        return correctedUrl;
      }

      // 确保重定向URL使用正确的域名
      if (url.startsWith(baseUrl)) {
        logger.log('使用原始重定向URL:', url);
        return url;
      }

      // 默认重定向到dashboard
      const dashboardUrl = `${baseUrl}/dashboard`;
      logger.log('默认重定向到dashboard:', dashboardUrl);
      return dashboardUrl;
    },
    // 自定义错误处理
    async error(error, request, response) {
      logger.error('认证错误:', error);
      const errorType = error.type;
      const errorMessage = errorMessages[errorType] || errorMessages.default;
      logger.log('错误类型:', errorType, '错误消息:', errorMessage);
      return errorMessage;
    }
  },
  pages: {
    // 自定义登录页
    signIn: '/login',
    // 出错时显示的页面
    error: '/login',
  },
  session: {
    // 使用JWT作为会话策略，与已有系统兼容
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30天
  },
  secret: process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET,
  debug: process.env.NODE_ENV === 'development',
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NEXTAUTH_URL?.startsWith('https://') ?? true // 根据NEXTAUTH_URL判断是否使用HTTPS
      }
    }
  },
};
